ARG BASE_IMAGE
FROM ${BASE_IMAGE}

# git commit id
ARG COMMITID

# app_id
ARG APP_NAME

# git tag
ARG APP_TAG

# 时区
ARG TZ="Asia/Shanghai"

# 产物目录(相对于项目根目录如./xx/xxx/)
ARG ARTIFACT_DIR

LABEL COMMITID=${COMMITID} APPNAME=${APP_NAME} APPTAG=${APP_TAG} BASEIMAGE=${BASE_IMAGE}
ENV APPLICATION_VERSION=${APP_TAG} APP_NAME=${APP_NAME}

RUN ln -sf /usr/share/zoneinfo/${TZ} /etc/localtime \
&& echo ${TZ} > /etc/timezone \
&& echo "ok" > /usr/share/nginx/html/ping.html \
&& mkdir -p /app/${APP_NAME} && echo "${COMMITID}" > /app/${APP_NAME}/version \
&& ln -s /usr/share/nginx/html /app/${APP_NAME} \
&& ln -s /etc/nginx/conf.d/default.conf /app/${APP_NAME}/nginx_default.conf

COPY ${ARTIFACT_DIR}/ /app/${APP_NAME}/html/

WORKDIR /app/${APP_NAME}
