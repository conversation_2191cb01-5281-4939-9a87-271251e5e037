const glob = require("glob");
const path = require("path");
const pwd = process.cwd();
const fs = require("fs");

const getFiles = async () => {
	const fsList = await glob(path.join(pwd, "src/assets/**/*.svg"));
	console.log('fsList', fsList);
	return fsList;
};

const write = async () => {
	const fsList = await getFiles();

	const html = fsList
		.map(
			(f) =>
				`<div>
			<img src="file:\/\/${f}" alt="" width="40px" class="icon" data-name="${path.basename(
					f
				)}" data-path="${f.split('src')[1]}" />
				<p>${path.basename(f)}</p>
				</div>`
		)
		.join("");

	const body = `
		<!DOCTYPE html>
		<html lang="en">
			<head>
				<meta charset="UTF-8" />
				<meta name="viewport" content="width=device-width, initial-scale=1.0" />
				<title>Document</title>
			</head>
			<body>
				<div id="icon-wrap" style="width:800px;margin:0 auto; display: flex; flex-wrap: wrap; gap: 20px;padding: 20px;text-align: center;">
				${html}
				</div>
			</body>
				<script src="./page.js"></script>
		</html>
	`;

	fs.writeFile(path.join(pwd, "scripts/viewsvg/view.html"), body, (res) => {
		console.log(res);
	});
};

write();
