const path = require('path');
const fs = require('fs');
const UploadCore = require('weimob-uploadtocdn-core').default
const simpleGit = require('simple-git');
const semver = require('semver');

// const updateVersion = async ({mode} = {}) => {
// 	const git = simpleGit();
// 	const packageJsonPath = path.resolve(process.cwd(), 'package.json');
// 	const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));

// 	// 获取当前版本号和构建模式
// 	const currentVersion = packageJson.version;

// 	// 根据模式提升版本号
// 	let newVersion = currentVersion;
// 	if (mode === 'qa') {
// 		newVersion = semver.inc(currentVersion, 'patch');
// 	} else if (mode === 'online') {
// 		newVersion = semver.inc(currentVersion, 'minor');
// 	}

// 	// 检查远程tag是否存在
// 	async function tagExists(tag) {
// 		const tags = await git.tags();
// 		return tags.all.includes(tag);
// 	}
// 	// 更新 package.json 并提交更改
// 	async function updateVersionAndCommit(version) {
// 		// wdp流水线无法提交代码，所以注释掉了
// 		// packageJson.version = version;
// 		// fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2), 'utf-8');

// 		// await git.add(packageJsonPath);
// 		// await git.commit(`chore: bump version to ${version}`);
// 		// await git.push();
// 		await git.addTag(`${mode}-${version}`);
// 		await git.pushTags();
// 	}

// 	// 主函数
// 	async function main() {
// 		let versionToTry = newVersion;

// 		while (await tagExists(`${mode}-${versionToTry}`)) {
// 			console.log(`Tag ${mode}-${versionToTry} already exists. Incrementing version...`);
// 			if (mode === 'qa') {
// 				versionToTry = semver.inc(versionToTry, 'patch');
// 			} else if (mode === 'online') {
// 				versionToTry = semver.inc(versionToTry, 'minor');
// 			}
// 		}

// 		console.log(`Updating version to ${versionToTry} and creating tag ${mode}-${versionToTry}`);
// 		await updateVersionAndCommit(versionToTry);
// 		console.log(`Version updated to ${versionToTry} and tag ${mode}-${versionToTry} created successfully.`);
// 		return versionToTry;
// 	}

// 	// 运行主函数
// 	return main().catch(err => {
// 		console.error('Failed to update version and create tag:', err);
// 		process.exit(1);
// 	});
// }


const weimobUploadCdn = () => {
	let _base, _cdnPrefix, viteConfig
	return {
    name: 'vite-plugin-weimob-cdn-upload',

		config(config, { command }) {
			// 存储 config 以便在 closeBundle 中访问
      viteConfig = config;

      // 检查当前命令是否为 'build'
      if (command !== 'build') return;

			// const versionToTry =await updateVersion({mode: config.mode})
			// 获取 env.mode
      const envMode = config.mode;

      // 读取 package.json 中的版本号
      const packageJsonPath = path.resolve(process.cwd(), 'package.json');
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
      const version = packageJson.version;

      // 设置公共路径
			_base = `static/${packageJson.name}/@${envMode}/v${version}/`
			_cdnPrefix = `https://cdn2.weimob.com/${_base}`
      config.base = _cdnPrefix;
		},
		configResolved(config) {
			viteConfig = config;
		},
    // configResolved(config) {
		// 	// 存储 config 以便在 closeBundle 中访问
    //   viteConfig = config;

    //   // 检查当前命令是否为 'build'
    //   if (config.command !== 'build') return;

		// 	// const versionToTry =await updateVersion({mode: config.mode})
		// 	// 获取 env.mode
    //   const envMode = config.mode;

    //   // 读取 package.json 中的版本号
    //   const packageJsonPath = path.resolve(process.cwd(), 'package.json');
    //   const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
    //   const version = packageJson.version;

    //   // 设置公共路径
		// 	_base = `static/${packageJson.name}/@${envMode}/v${version}/`
		// 	_cdnPrefix = `https://cdn2.weimob.com/${_base}`

    //   config.base = _cdnPrefix;
    // },

		async closeBundle() {
			// 检查当前命令是否为 'build'
      if (viteConfig.command !== 'build') return;
			try {
				process.env.CI  = '1'
				console.log('Uploading assets to CDN...');

				const upload = new UploadCore({
					autoFresh: false,
					checkVersion: false,
					fullPath: true,
					// region: 'pioneer',
					prefix: _base,
					outputPath: path.resolve(process.cwd(), './dist')
				})

				const result1 = await upload.uploadToCDN([
					{
						filePath: path.resolve(process.cwd(), './dist')
					}
				])

				console.log('Assets uploaded to CDN successfully.');
			}  catch (error) {
				console.error('Failed to upload assets to CDN:');
				console.error(error);
				process.exit(1);
			}
    }
  };
}

module.exports = {
	weimobUploadCdn
}
