# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
.DS_Store

dist-ssr
coverage
*.local

/cypress/videos/
/cypress/screenshots/

# Editor directories and files
.vscode/*
!.vscode/settings.json
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
dist/
docs/.vuepress/.temp/
docs/.vuepress/.cache/
public/docs/
# Environment variables files

package-lock.json*
yarn.lock*
pnpm-lock.json*
pnpm-lock.yaml
#.vite
