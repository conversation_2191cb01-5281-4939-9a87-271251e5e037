{"compilerOptions": {"baseUrl": ".", "module": "esnext", "target": "es6", "lib": ["DOM", "ESNext"], "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "preserve", "moduleResolution": "node", "resolveJsonModule": true, "noUnusedLocals": false, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "noImplicitAny": false, "paths": {"@/*": ["./src/*"]}, "types": ["vite/client", "node"], "typeRoots": ["./node_modules/@types", "./src/types"]}, "exclude": ["node_modules", "dist", "service"], "include": ["./src/**/*"]}