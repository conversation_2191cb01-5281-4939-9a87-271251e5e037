<!DOCTYPE html>
<html lang="zh-cmn-Hans">

<head>
	<meta charset="UTF-8" />
	<link rel="icon" href="/favicon.ico" />
	<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
	<meta http-equiv="Pragma" content="no-cache" />
	<meta http-equiv="Expires" content="0" />
	<!-- <meta content="yes" name="apple-mobile-web-app-capable" /> -->
	<link rel="apple-touch-icon" href="/favicon.ico" />
	<meta name="viewport"
		content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover" />
	<title>AIWork365——国内领先的AI写作工具，让创作更简单，办公更轻松！</title>
	<meta name="keywords"
		content="AIWork365,AI写作,AI写作工具,AI写作助手,AI论文,AI聊天,AIPPT,AI智能写作,AI商务办公,AI智能办公,AI营销助手,AI方案计划书创作,AI研究报告创作,小说AI写作,AI新媒体创作,AI短视频创作,AI简历,AI图片生成,AI视频生成" />
	<meta name="description"
		content="AIWork365 是一款强大的 AI写作工具，覆盖全行业，适配全场景，提供高效、准确的写作服务，帮助用户快速生成优质内容。AI论文写作、制作PPT、绘画等功能帮助提升工作效率。" />
	<!-- <script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script> -->
	<!-- <script src="https://assets.weimob.com/rprm@latest/index.js"></script> -->
	<script src="https://cdn2.weimob.com/saas/@assets/rprm/latest/hound.js"></script>

	<link rel="stylesheet"
		href="https://cdn2.weimob.com/saas/saas-fe-sirius-orion-node/production/501/1.2.1731653662830/iconfont.css" />
	<style>
		[class^="icon-"],
		[class*=" icon-"] {
			line-height: 1;
		}
	</style>
	<script>
		if (![].at) {
			Array.prototype.at = function (pos) {
				return this.slice(pos, pos + 1)[0];
			};
		}
	</script>
	<script src="https://turing.captcha.qcloud.com/TJCaptcha.js"></script>
</head>

<body class="dark:bg-black">
	<script>
		if(!window.rprm){
			// 防止报错
			window.rprm = {
				init: function () {},
				public: function () {},
				rec: function () {}
			};
		}
		rprm.init({
			service: "wms", //APM系统中的服务名（无Node.js监控时需要填写）
			statType: "8290938202", //BI数据上报落库表名
			ancAPI: "", //BI数据上报地址（非私有化客户可以不配置）
			env: ['localhost', 'chat-qa.mjmobi.com'].includes(window.location.hostname) ? 'qa' : 'live', //可选环境变量为：dev、qa、pl、live
			anchor: true, //是否开启BI数据上报
			// public: {
			// 	subbusiness: 'huod',
			// 	bid: 10000130
			// }, //自定义全局变量
			debug: false, //是否在console内打印日志
			// errcodes: ['100200101002'], //不上报错误码为100200101002的接口（新云）
			whitelist: ['cdn.weimob.com', 'cdn2.weimob.com'], //不监控域名为cdn.weimob.com的请求
			apmRequestBody: true, //提供配置在apm上报时是否记录请求参数及返回值
			/**
			 * bi 上报时是否使用优先使用 fetch, 默认使用 img 标签上报
			 * 可通过 rprm.updateConfig({ biUseFetch: true }); 进行修改
			 */
			biUseFetch: false,
			apmRecordConsole: false
		});
		rprm.public({
			isproduction: !['localhost', 'chat-qa.mjmobi.com'].includes(window.location.hostname)
		})
	</script>
	<div id="app">
		<style>
			#app-loading {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: #fff;
				z-index: 999999;
				display: flex;
				align-items: center;
				justify-content: center;
				overflow: visible;
			}

			@keyframes app-loading {
				0% {
					/* transform: translate(0, 0) scale(1); */
					opacity: 1;
				}

				25% {
					/* transform: translate(0, 0) scale(1); */
					opacity: 1;
				}

				30% {
					/* transform: translate(0, -50%) scale(1.1); */
					opacity: 0;
				}

				45% {
					/* transform: translate(0, -50%) scale(1.1); */
					opacity: 1;
				}

				100% {
					/* transform: translate(0, 0) scale(1); */
					opacity: 1;
				}
			}

			#loading-svg-g>g {
				animation: app-loading 3s infinite;
			}

			#loading-svg-g>g:nth-child(2) {
				animation-delay: 0.1s;
			}

			#loading-svg-g>g:nth-child(3) {
				animation-delay: 0.2s;
			}

			#loading-svg-g>g:nth-child(4) {
				animation-delay: 0.3s;
			}

			#loading-svg-g>g:nth-child(5) {
				animation-delay: 0.4s;
			}

			#loading-svg-g>g:nth-child(6) {
				animation-delay: 0.5s;
			}

			#loading-svg-g>g:nth-child(7) {
				animation-delay: 0.6s;
			}

			#loading-svg-g>g:nth-child(8) {
				animation-delay: 0.7s;
			}

			#loading-svg-g>g:nth-child(9) {
				animation-delay: 0.8s;
			}
		</style>
		<div id="app-loading">
			<div class="app-loading_wrap">
				<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1"
					width="200" height="32" viewBox="0 0 104.38999938964844 14.479999542236328">
					<defs>
						<clipPath id="master_svg0_170_40007/136_35266/70_5631">
							<rect x="0" y="0" width="104.38999938964844" height="14.479999542236328" rx="0" />
						</clipPath>
					</defs>
					<g id="loading-svg-g" clip-path="url(#master_svg0_170_40007/136_35266/70_5631)">
						<g>
							<path
								d="M13.44,14.44L11.01,14.44L10.26,11.2L4.18,11.2L2.54,14.44L0,14.44L7.54,0L10,0L13.44,14.44ZM8.37,2.97L5.28,9.06L9.77,9.06L8.37,2.97Z"
								fill="#121519" fill-opacity="1" style="mix-blend-mode: passthrough" />
						</g>
						<g>
							<path
								d="M16.729999122619628,0L19.119999122619628,0L17.05999912261963,14.44L14.669999122619629,14.44L16.729999122619628,0Z"
								fill="#121519" fill-opacity="1" style="mix-blend-mode: passthrough" />
						</g>
						<g>
							<path
								d="M24.95,14.440000001788139L23.25,0.17000000178813934L25.59,0.17000000178813934L26.85,10.780000001788139L31.36,0.17000007629393935L33.76,0.17000007629393935L35.49,10.780000001788139L39.57,0.17000007629393935L42.2,0.17000007629393935L36.45,14.43000000178814L33.76,14.43000000178814L32.16,3.9400000017881394L27.72,14.43000000178814L24.95,14.43000000178814L24.95,14.440000001788139Z"
								fill="#121519" fill-opacity="1" style="mix-blend-mode: passthrough" />
						</g>
						<g>
							<path
								d="M46.130234375,14.48001220703125C43.160234375,14.48001220703125,40.740234375,12.06001220703125,40.740234375,9.09001220703125C40.740234375,6.12001220703125,43.160234375,3.70001220703125,46.130234375,3.70001220703125C49.100234375,3.70001220703125,51.520234375,6.12001220703125,51.520234375,9.09001220703125C51.520234375,12.06001220703125,49.110234375,14.48001220703125,46.130234375,14.48001220703125ZM46.130234375,6.10001220703125C44.480234375,6.10001220703125,43.140234375,7.44001220703125,43.140234375,9.09001220703125C43.140234375,10.74001220703125,44.480234375,12.08001220703125,46.130234375,12.08001220703125C47.780234375,12.08001220703125,49.120234375,10.74001220703125,49.120234375,9.09001220703125C49.120234375,7.44001220703125,47.780234375,6.10001220703125,46.130234375,6.10001220703125Z"
								fill="#0000FF" fill-opacity="1" style="mix-blend-mode: passthrough" />
						</g>
						<g>
							<path
								d="M52.5595703125,14.439999904632568L54.0295703125,4.079999904632569L56.3495703125,4.079999904632569L56.0295703125,6.159999904632569C56.6495703125,5.419999904632569,57.2995703125,4.859999904632568,57.9795703125,4.449999904632568C58.6595703125,4.0499999046325685,59.4295703125,3.8499999046325684,60.289570312500004,3.8499999046325684L60.4195703125,3.8499999046325684L60.079570312499996,6.229999904632568L59.9795803125,6.229999904632568C59.2795803125,6.229999904632568,58.6095803125,6.339999904632569,57.9695803125,6.569999904632569C57.3295803125,6.7999999046325685,56.7995803125,7.119999904632568,56.379580312499996,7.529999904632568C55.9595803125,7.939999904632568,55.7095803125,8.419999904632569,55.6395803125,8.96999990463257L54.879580312499996,14.439999904632568L52.5595703125,14.439999904632568Z"
								fill="#121519" fill-opacity="1" style="mix-blend-mode: passthrough" />
						</g>
						<g>
							<path
								d="M68.6204296875,4.080000534057618L71.3304296875,4.080000534057618L66.0404296875,8.770000534057617L70.0004296875,14.450000534057617L67.1404296875,14.450000534057617L63.5004296875,9.140000534057616L62.7604296875,14.450000534057617L60.4404296875,14.450000534057617L62.4604296875,0.1900005340576172L64.7704296875,0.1900005340576172L63.5904296875,8.400000534057618L68.6204296875,4.080000534057618Z"
								fill="#121519" fill-opacity="1" style="mix-blend-mode: passthrough" />
						</g>
						<g>
							<path
								d="M79.63016625,6.7600048828125C80.25016625,6.9700048828125,80.73016625,7.3400048828125,81.09016625,7.8600048828125C81.45016625,8.3800048828125,81.62016625,9.0100048828125,81.62016625,9.7300048828125C81.62016625,9.8500048828125,81.61015625,10.0400048828125,81.58016625,10.2800048828125C81.48016625,11.0900048828125,81.18015625,11.8100048828125,80.68015625,12.4400048828125C80.18015625,13.0700048828125,79.54015625,13.5600048828125,78.76016625,13.9100048828125C77.98016625,14.2600048828125,77.12016625,14.4400048828125,76.18015625,14.4400048828125C74.79015625,14.4400048828125,73.69016625,14.1000048828125,72.88015625,13.4300048828125C72.07016025,12.7600048828125,71.66015625,11.8600048828125,71.66015625,10.7300048828125C71.66015625,10.5000048828125,71.68015285,10.2500048828125,71.72015385,9.9900048828125L74.05015625,9.9900048828125C74.02015625,10.7700048828125,74.23015625,11.3600048828125,74.65015625,11.7600048828125C75.07015625,12.1700048828125,75.65015625,12.3700048828125,76.38015625,12.3700048828125C77.24015625,12.3700048828125,77.91015625,12.1600048828125,78.39015625,11.7300048828125C78.87016625,11.3100048828125,79.15016625,10.7100048828125,79.22016625,9.9500048828125C79.23016625,9.8800048828125,79.24015625,9.7700048828125,79.24015625,9.6400048828125C79.24015625,8.5100048828125,78.41015625,7.9400048828125,76.75015625,7.9400048828125L75.29015625,7.9400048828125L75.57015625,5.9500048828125L77.03015625,5.9500048828125C77.53015625,5.9500048828125,77.99015625,5.8500048828125,78.39015625,5.6500048828125C78.79015625,5.4500048828125,79.10015625,5.1900048828125,79.33016625,4.8500048828125C79.56016625,4.5100048828125,79.67015625,4.1500048828125,79.67015625,3.7500048828125C79.67015625,3.2900048828125,79.50015625,2.9200048828125,79.16015625,2.6300048828125C78.82015625,2.3400048828125,78.35015625,2.2000048828125,77.75015625,2.2000048828125C77.00015625,2.2000048828125,76.39015625,2.3800048828125,75.93015625,2.7500048828125C75.47015625,3.1100048828125,75.21015625,3.6000048828125,75.14015625,4.2000048828125L72.85014625,4.2000048828125C72.92014625,3.4200048828125,73.17014625,2.7300048828125,73.58015625,2.1100048828125C73.99015625,1.4900048828125,74.57015625,1.0100048828125,75.32015625,0.6600048828125C76.07015625,0.3100048828125,76.95014625,0.1300048828125,77.97015625,0.1300048828125C79.24014625,0.1300048828125,80.24014625,0.4300048828125,80.97015625,1.0300048828125C81.70015624999999,1.6300048828125,82.07015625,2.4200048828125,82.07015625,3.4200048828125C82.07015625,4.2000048828125,81.84015625,4.8700048828125,81.37015625000001,5.4400048828125C80.90016625,6.0100048828125,80.32016625,6.4500048828125,79.63016625,6.7600048828125Z"
								fill="#121519" fill-opacity="1" style="mix-blend-mode: passthrough" />
						</g>
						<g>
							<path
								d="M92.84015625,9.1100048828125C92.84015625,9.4000048828125,92.83015625,9.6200048828125,92.80015625,9.7700048828125C92.69015625,10.6800048828125,92.37015625000001,11.4900048828125,91.84015625,12.2000048828125C91.31015625,12.9100048828125,90.64015625,13.4600048828125,89.82015625,13.8500048828125C89.00015625,14.2400048828125,88.10015625,14.4400048828125,87.13015625,14.4400048828125C86.24015625,14.4400048828125,85.46015625,14.2700048828125,84.78015625,13.9400048828125C84.10015625,13.6100048828125,83.58016225,13.1400048828125,83.21015925,12.5400048828125C82.84015625,11.9400048828125,82.66015625,11.2400048828125,82.66015625,10.4600048828125C82.66015625,10.1700048828125,82.67015835,9.9500048828125,82.70015715,9.8000048828125C82.78015925,9.2100048828125,82.90015425,8.7000048828125,83.07016025,8.2600048828125C83.24015825,7.8200048828125,83.45015725,7.4100048828125,83.69016625,7.0400048828125C83.93015625,6.6600048828125,84.32015625,6.1000048828125,84.86015625,5.3500048828125L88.67015625,0.1300048828125L91.47016625,0.1300048828125L87.40016625,5.5200048828125C87.87016625,5.3600048828125,88.38016625,5.2800048828125,88.92015625,5.2800048828125C89.68015625,5.2800048828125,90.36015625,5.4400048828125,90.96015625,5.7600048828125C91.55015625,6.0800048828125,92.02015625,6.5300048828125,92.35015625,7.1000048828125C92.68015625,7.6700048828125,92.84015625,8.3400048828125,92.84015625,9.1100048828125ZM88.09015625,7.3800048828125C87.23015625,7.3800048828125,86.51015625,7.6400048828125,85.95015625,8.1700048828125C85.39015625,8.7000048828125,85.10015625,9.3700048828125,85.10015625,10.1800048828125C85.10015625,10.8200048828125,85.31015625,11.3400048828125,85.73015625,11.7300048828125C86.15015625,12.1200048828125,86.72015625,12.3200048828125,87.43015625,12.3200048828125C87.98015625,12.3200048828125,88.48015625,12.2000048828125,88.93015625,11.9500048828125C89.38014625,11.7000048828125,89.74014625,11.3600048828125,90.00015625,10.9300048828125C90.26015625,10.4900048828125,90.39015625,10.0000048828125,90.39015625,9.4500048828125C90.39015625,8.8200048828125,90.18015625,8.3200048828125,89.76015625,7.9500048828125C89.36015625,7.5700048828125,88.80016625,7.3800048828125,88.09015625,7.3800048828125Z"
								fill="#121519" fill-opacity="1" style="mix-blend-mode: passthrough" />
						</g>
						<g>
							<path
								d="M104.08984375,2.1999951171875L98.50985375,2.1999951171875L97.67985375,5.8399951171875C97.98985375,5.6499951171875,98.35985375,5.5099951171875,98.77985375,5.4199951171875C99.19985375,5.3299951171875,99.61985375,5.2799951171875,100.02985375,5.2799951171875C101.26985375,5.2799951171875,102.24985375,5.6199951171875,102.97985375,6.2999951171875C103.70985375000001,6.9799951171875,104.06984375,7.8899951171875,104.06984375,9.0299951171875C104.06984375,9.1499951171875,104.05984375,9.3699951171875,104.02984375,9.6699951171875C103.84984375,11.1099951171875,103.27984375,12.2599951171875,102.29984375000001,13.1299951171875C101.31984375,13.9999951171875,100.00984375,14.4299951171875,98.37984375,14.4299951171875C96.97984375,14.4299951171875,95.86984375,14.0899951171875,95.05984375,13.3999951171875C94.24984775,12.7099951171875,93.83984375,11.7899951171875,93.83984375,10.6399951171875C93.83984375,10.3799951171875,93.85984035,10.1099951171875,93.89984135,9.8499951171875L96.26984375,9.8499951171875C96.24984375,10.6299951171875,96.45984375,11.2299951171875,96.90984375,11.6699951171875C97.35984375,12.1099951171875,97.94984375,12.3299951171875,98.66984375,12.3299951171875C99.50984375,12.3299951171875,100.17984375,12.0999951171875,100.67984375,11.6399951171875C101.17984375,11.1799951171875,101.46984375,10.5599951171875,101.55984375,9.7999951171875C101.56984375,9.7099951171875,101.57984375,9.5899951171875,101.57984375,9.4399951171875C101.57984375,8.7499951171875,101.35984375,8.2199951171875,100.90984375,7.8399951171875C100.46984375,7.4599951171875,99.90984375,7.2799951171875,99.22984375,7.2799951171875C98.14984375,7.2799951171875,97.28984375,7.6799951171875,96.63984375,8.4799951171875L94.90984375,7.6499951171875L96.65984375,0.1199951171875L104.38984375,0.1199951171875L104.08984375,2.1999951171875Z"
								fill="#121519" fill-opacity="1" style="mix-blend-mode: passthrough" />
						</g>
					</g>
				</svg>
			</div>
		</div>
	</div>
	<script type="module" src="/src/main.ts"></script>
	<script>
		var _hmt = _hmt || [];
		(function () {
			var hm = document.createElement("script");
			hm.src = "https://hm.baidu.com/hm.js?0a90a20f104258cdf473b875e32518c5";
			var s = document.getElementsByTagName("script")[0];
			s.parentNode.insertBefore(hm, s);
		})();
	</script>
	<script>
		// vconsole
		if (location.search.indexOf("log=1") >= 0) {
			const script = document.createElement("script");
			script.src = "https://unpkg.com/vconsole@latest/dist/vconsole.min.js";
			script.onload = () => {
				var vConsole = new window.VConsole();
			};
			document.body.appendChild(script);
		}
	</script>
</body>

</html>
