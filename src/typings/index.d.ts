/// <reference path="./chat.d.ts" />

// 商品类型 ，默认ai，用于对话，paper 论文，ppt 用户ppt
interface BaseRechargeDetail {
	type: "ai" | "paper" | "ppt" | "ALL";
	categoryId?: number;
	memberType?: string;
	onSuccess?: () => void;
	onFail?: () => void;
}

export interface ReChargePaperDetail {
	type: "paper";
	paperId: number;
	[key as string]?: any;
}
export interface ReChargeAiDetail {
	type: "ai";
	categoryId?: 2 | 3 | 4 | 6;
	memberType?: string;
	[key as string]?: any;
}
export interface ReChargePptDetail {
	type: "ppt";
	[key as string]?: any;
}

export interface ReChargeFindPasswordOnceDetail {
	type: "findpasswordOnce";
	categoryId: 6;
	memberType: string;
	taskId?: number;
	[key as string]?: any;
}

export interface ReChargeCheckDetail {
	id?: string;
	type: string;
	categoryId: 5;
	title: string;
	author: string;
	file?: File;
	content?: string;
	[key as string]?: any;
}

export type RechargeDetailType =
	| ReChargePaperDetail
	| ReChargeAiDetail
	| ReChargePptDetail
	| ReChargeCheckDetail
	| ReChargeFindPasswordOnceDetail;

// 给window对象添加全局变量

export interface AiworkType {
	openActivity?: () => void;
	openLogin: () => Promise<any>;
	openRecharge: (detail: RechargeDetailType) => Promise<any>;
	landingUrl?: string; // 支付上报用
	openSelectTeam?: (afterLogin?: boolean) => void;
}
