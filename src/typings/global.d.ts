import { AiworkType } from ".";
declare global {
	interface Window {
		$loadingBar?: import("naive-ui").LoadingBarProviderInst;
		$dialog?: import("naive-ui").DialogProviderInst;
		$message?: import("naive-ui").MessageProviderInst;
		$notification?: import("naive-ui").NotificationProviderInst;
		$aiwork: AiworkType;
		$temptype?: string;
		landingUrl?: string;
		rprm: {
			rec: (params: any) => void;
			init: (params: any) => void;
			public: (params: any, type?: string) => void;
		};
	}
}
export {};
