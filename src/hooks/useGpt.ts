import { ref, watch } from "vue";
import { useRequest } from "vue-hooks-plus";
interface Node
	extends Pick<Chat.GptMessage, "id" | "parentMessageId" | "text"> {}
// 打字机队列
class Typewriter {
	private contentList: Node[] = [];
	private incrementalContentList: Node[] = [];
	private queue: string[] = [];
	private consuming = false;
	private timer: any;
	private startIndex = 0;
	constructor(private onConsume: (str?: string) => void) {}
	get started() {
		return this.consuming;
	}
	// 输出速度动态控制
	dynamicSpeed() {
		const baseSpeed = 2000 / this.queue.length;
		const randomFactor = 1; // Random factor between 0.8 and 1.2
		const speed = baseSpeed * randomFactor;
		return Math.min(speed, 200); // Limit the speed to a maximum of 200 milliseconds
	}

	// 添加字符串到队列
	add(data: Node[], incremental = false) {
		if (!data.length) return;
		if (!incremental) {
			const startIndex = this.contentList.length;
			this.contentList = data;
			this.incrementalContentList = data.slice(startIndex);
			this.queue.push(...this.incrementalContentList.map((item) => item.text));
		} else {
			this.queue.push(
				...data.map((item) => {
					if (item.text === "") {
						return "\n";
					}
					return item.text;
				})
			);
		}
	}
	// 消费
	consume() {
		const str = this.queue?.shift();
		this.onConsume(str);
		// console.log("Buffer length: ", this.queue?.length);
	}
	// 消费下一个
	next() {
		this.consume();
		// 根据队列中字符的数量来设置消耗每一帧的速度，用定时器消耗
		this.timer = setTimeout(() => {
			this.consume();
			this.next();
		}, this.dynamicSpeed());
	}
	// 开始消费队列
	start() {
		if (!this.consuming) {
			this.consuming = true;
			this.next();
		}
	}
	// 结束消费队列
	done() {
		this.consuming = false;
		const timer = setTimeout(() => {
			clearTimeout(this.timer);
			clearTimeout(timer);
		}, 500);
		// 把queue中剩下的字符一次性消费
		this.onConsume(this.queue.join(""));
		this.queue = [];
		this.incrementalContentList = [];
		this.contentList = [];
	}
}

interface UseGptOptions {
	// 接口
	api: (data: any) => Promise<any>;
	data?: any;
	onSuccess: (data: string) => void;
	onError: (error: any) => void;
}

export const useGpt = ({ api, onError, onSuccess }: UseGptOptions) => {
	const streamingText = ref("");
	const typewrite = new Typewriter((str?: string) => {
		streamingText.value += str || "";
	});
	const { run, loading, cancel } = useRequest(
		(params: any) =>
			api({
				...params,
				onDownloadProgress: ({ event }) => {
					const xhr = event.target;
					const { responseText } = xhr;
					try {
						typewrite.start();
						const chunkArr: Chat.GptMessage[] = (
							responseText.trim().split("\n") as string[]
						).map((item) => JSON.parse(item) || {}); // 将返回的数据按行分割转成数组
						typewrite.add(chunkArr);
						const lastItem = chunkArr[chunkArr.length - 1];
						const isCompleted = lastItem?.detail?.choices?.[0]?.finish_reason;
						if (isCompleted === "stop") {
							typewrite.done();
						}
					} catch (error) {}
				},
			}),
		{
			manual: true,
			onSuccess,
			onError,
		}
	);
	return {
		run: (args: any) => {
			streamingText.value = "";
			run(args);
		},
		cancel,
		loading,
		streamingText,
	};
};
/**
 * 长文生成
 */
export const useLongContentGpt = ({ api, onError }: UseGptOptions) => {
	const loading = ref(false);
	const streamingText = ref("");
	const typewrite = new Typewriter((str?: string) => {
		streamingText.value += str || "";
	});
	let controller
	const parameters = ref({});

	const run = async (data) => {
		loading.value = true;
		streamingText.value = "";
		parameters.value = data;
		controller = new AbortController();
		api({
			data,
			signal: controller,
			onError: handleError,
			onRepay: handleRepay,
			onDownloadProgress: ({ data }: { data: string }) => {
				typewrite.start();
				const message: Chat.GptMessage = JSON.parse(data);
				const isCompleted = message?.detail?.choices?.[0]?.finish_reason;
				typewrite.add([message], true);
				if (isCompleted === "stop") {
					loading.value = false;
					typewrite.done();
					controller.abort();
				}
			},
		});
	};
	const handleRepay = ({ type }) => {
		if (type === "buy") {
			// 购买
			run(parameters.value);
		}
	};
	const handleError = (error) => {
		controller.abort();
		onError(error);
		loading.value = false;
	};

	const cancel = () => {
		controller.abort();
		typewrite.done();
		loading.value = false;
		controller.abort();
	};
	return {
		run,
		cancel,
		loading,
		streamingText,
	};
};
