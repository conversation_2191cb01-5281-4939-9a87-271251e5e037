import { reactive } from "vue";

interface CurrentTime {
	total: number;
	days: string;
	hours: string;
	minutes: string;
	seconds: string;
	milliseconds: string;
}

interface CountDown {
	start: () => void;
	pause: () => void;
	reset: (totalTime: number, autoStart?: boolean) => void;
	current: CurrentTime;
}

interface UseCountDownOptions {
	time: number;
	autoStart?: boolean;
	millisecond?: boolean;
	onChange?: (current: CurrentTime) => void;
	onFinish?: () => void;
}
export const useCountDown = (options: UseCountDownOptions): CountDown => {
	let countdownTime: NodeJS.Timeout;
	const diff = options.millisecond ? 4 : 1000;
	const current = reactive({
		total: 0,
		days: "00",
		hours: "00",
		minutes: "00",
		seconds: "00",
		milliseconds: "00",
	});

	const timeDataFormat = () => {
		if (current.total >= 0) {
			current.days = Math.floor(current.total / (24 * 60 * 60 * 1000))
				.toString()
				.padStart(2, "0");
			current.hours = Math.floor(
				(current.total % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000)
			)
				.toString()
				.padStart(2, "0");
			current.minutes = Math.floor(
				(current.total % (60 * 60 * 1000)) / (60 * 1000)
			)
				.toString()
				.padStart(2, "0");
			current.seconds = Math.floor((current.total % 60000) / 1000)
				.toString()
				.padStart(2, "0");
			current.milliseconds = Math.floor(current.total % 1000)
				.toString()
				.padStart(2, "0");
		}
	};

	const start = () => {
		clearTimeout(countdownTime);
		current.total = current.total - diff;
		if (current.total >= 0) {
			timeDataFormat();
			options.onChange?.(current);
			countdownTime = setTimeout(() => {
				start();
			}, diff);
		} else {
			options.onFinish?.();
		}
	};

	const pause = () => {
		clearTimeout(countdownTime);
	};

	const reset = (totalTime: number, autoStart = false) => {
		clearTimeout(countdownTime);
		current.total = totalTime;
		timeDataFormat();
		if (autoStart) {
			start();
		}
	};

	reset(options.time, options.autoStart);

	return {
		start,
		pause,
		reset,
		current,
	};
};
