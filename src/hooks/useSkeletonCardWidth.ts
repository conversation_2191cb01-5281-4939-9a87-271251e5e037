import { ref, onMounted, onUnmounted } from "vue";

const useSkeletonCardWidth = () => {
	const containerWidth = ref<number>(0);
	const breakpoints = {
		"3xl": 1470,
		"2xl": 1270,
		xl: 980,
		lg: 724,
		md: 568,
		sm: 568,
	};

	const calcWidth = (percentage: number, gap: number, bp: string) => {
		const width = (breakpoints[bp] * percentage) / 100 - gap;
		return width;
	};

	let widthClasses = {
        "3xl": 0,
		"2xl": 0,
		xl: 0,
		lg: 0,
		md: 0,
		sm: 0,
	};

	const width = ref<number>(0);

	const updateWidth = (screenWidth: number) => {
		if (screenWidth >= breakpoints["3xl"]) {
			width.value = widthClasses["3xl"];
		} else if (screenWidth >= breakpoints["2xl"]) {
			width.value = widthClasses["2xl"];
		} else if (screenWidth >= breakpoints.xl) {
			width.value = widthClasses.xl;
		} else if (screenWidth >= breakpoints.lg) {
			width.value = widthClasses.lg;
		} else if (screenWidth >= breakpoints.md) {
			width.value = widthClasses.md;
		} else {
			width.value = widthClasses.sm;
		}
	};
	const setWidth = (w: number) => {
		containerWidth.value = w;
		widthClasses = {
			"3xl": calcWidth(25, 18, "3xl"),
			"2xl": calcWidth(25, 18, '2xl'),
			xl: calcWidth(25, 18, 'xl'),
			lg: calcWidth(33, 14, 'lg'),
			md: calcWidth(50, 13, 'md'),
			sm: calcWidth(100, 0, 'sm'),
		};
		console.log("widthClasses: ", widthClasses);
		updateWidth(w);
        console.log("width", width.value);
	};

	return {
		width,
		setWidth,
	};
};

export default useSkeletonCardWidth;
