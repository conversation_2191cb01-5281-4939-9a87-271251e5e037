import type { Ref } from 'vue'
import { nextTick, ref } from 'vue'

type ScrollElement = HTMLDivElement | null

interface ScrollReturn {
  scrollToBottom: () => Promise<void>
  scrollToTop: () => Promise<void>
  scrollToBottomIfAtBottom: () => Promise<void>
}

export function useScroll(): ScrollReturn {
  const scrollRef = document.getElementById('scrollRef')

  const scrollToBottom = async () => {
    await nextTick()
    if (scrollRef)
      scrollRef.scrollTop = scrollRef.scrollHeight
  }

  const scrollToTop = async () => {
    await nextTick()
    if (scrollRef)
      scrollRef.scrollTop = 0
  }

  const scrollToBottomIfAtBottom = async () => {
    await nextTick()
    const scrollRef = document.getElementById('scrollRef')
    if (scrollRef) {
      const threshold = 100 // 阈值，表示滚动条到底部的距离阈值
      const distanceToBottom = scrollRef.scrollHeight - scrollRef.scrollTop - scrollRef.clientHeight
      if (distanceToBottom <= threshold)
        scrollRef.scrollTop = scrollRef.scrollHeight
    }
  }

  return {
    scrollToBottom,
    scrollToTop,
    scrollToBottomIfAtBottom,
  }
}
