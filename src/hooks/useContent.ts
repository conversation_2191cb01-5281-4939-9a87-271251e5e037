import { computed, ref } from "vue";

interface AIMessage {
	role: string;
	id: string;
	parentMessageId: string;
	text: string;
	delta: string;
	detail: Detail;
}

interface Detail {
	choices: Choice[];
	created: number;
	id: string;
	model: string;
	object: string;
	usage: null;
}

interface Choice {
	delta: Delta;
	index: number;
}

interface Delta {
	content: string;
	role: string;
}

interface Node extends Pick<AIMessage, "id" | "parentMessageId" | "text"> {}

export function useContent() {
	const contentList = ref<Node[]>([]);
	const extraContentList = ref<Node[]>([]);
	const incrementalContentList = ref<Node[]>([]);
	const incrementalExtraContentList = ref<Node[]>([]);

	function insertContent(data: Node[]) {
		incrementalContentList.value = findIncrementalContentList(data, contentList);
	}
	function insertExtraContent(data: Node[]) {
		incrementalExtraContentList.value = findIncrementalContentList(
			data,
			extraContentList
		);
	}
	function insertRawContent(data?: string) {
		contentList.value.push({ id: "", parentMessageId: "", text: data || "" });
	}

	function findIncrementalContentList(data: Node[], tempList) {
		const list: Node[] = [];
		const startIndex = tempList.value.length;
		list.push(...data.slice(startIndex));
		tempList.value = data;
		return list;
	}

	function clearContentList() {
		contentList.value = [];
		incrementalContentList.value = [];
	}
	function clearExtraContentList() {
		extraContentList.value = [];
		incrementalExtraContentList.value = [];
	}
	const increaseContent = computed(() => {
		return contentList.value.map((item) => item.text).join("");
	});
	const incerateExtraContent = computed(() => {
		return incrementalExtraContentList.value.map((item) => item.text).join("");
	})

	return {
		increaseContent,
		incerateExtraContent,
		insertContent,
		insertRawContent,
		clearContentList,
		clearExtraContentList,
		insertExtraContent,
	};
}
