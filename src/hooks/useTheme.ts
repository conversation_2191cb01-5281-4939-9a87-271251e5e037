import type { GlobalThemeOverrides } from 'naive-ui'
import { computed, watch } from 'vue'
import { darkTheme, useOsTheme } from 'naive-ui'
import { useAppStore } from '@/store'

export function useTheme() {
  const appStore = useAppStore()

  const OsTheme = useOsTheme()

  const isDark = computed(() => {
    if (appStore.theme === 'auto')
      return OsTheme.value === 'dark'
    else
      return appStore.theme === 'dark'
  })

  const theme = computed(() => {
    return isDark.value ? darkTheme : undefined
  })

  const themeOverrides = computed<GlobalThemeOverrides>(() => {
    if (!isDark.value) {
      return {
        common: {
					primaryColor: "#3680F9",
					successColor: "#3dbaa1",
					errorColor: "#FF5100",
					borderRadius: "6px",
					// hoverColor: "#3dbaa1",
					primaryColorHover: "#3dbaa1",
					primaryColorPressed: "#3dbaa1",
					primaryColorFocus: "#3dbaa1",
				},
				Button: {
					colorPrimary: "#0066FE",
					colorHoverPrimary: "#0066FE",
					colorFocusPrimary: "#0066FE",
					colorPressedPrimary: "#0066FE",
					borderHoverPrimary: "#0066FE",
					textColorGhostHoverPrimary: "#0066FE",
					borderRadiusMedium: "4px",
					textColorHoverPrimary: "#6a9bec",
					textColorPressedPrimary: "#6a9bec",
					textColorFocusPrimary: "#6a9bec",
					textColorTextHoverPrimary: "#6a9bec",
					colorWarning: "#2F82FF",
					colorHoverWarning: "#2F82FF",
					colorFocusWarning: "#2F82FF",
					colorPressedWarning: "#2F82FF",
					colorDisabledWarning: "#2F82FF",
					borderWarning: "#2F82FF",
					borderHoverWarning: "#2F82FF",
				},
				Input: {
					borderHover: "#1874FF",
				},
				Dialog: {
					iconColorWarning: "#2F82FF",
					colorPressedWarning: "#2F82FF",
				},
				Message: {
					iconColorSuccess: "#0066FE",
				}
      }
    }
    return {}
  })

  watch(
    () => isDark.value,
    (dark) => {
      if (dark)
        document.documentElement.classList.add('dark')
      else
        document.documentElement.classList.remove('dark')
    },
    { immediate: true },
  )

  return { theme, themeOverrides }
}
