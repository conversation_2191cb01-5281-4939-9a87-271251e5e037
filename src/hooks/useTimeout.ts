import { watchEffect, Ref, unref, isRef, ref } from "vue";

function useTimeout(
	fn: () => void,
	delay?: Ref<number | undefined> | number,
	options: {
		immediate?: boolean;
	} = {}
) {
	const timerRef = ref<NodeJS.Timeout | null>(null);

	const clear = () => {
		if (timerRef.value) {
			clearTimeout(timerRef.value);
			timerRef.value = null;
		}
	};

	const stop = clear;

	const start = () => {
		if (timerRef.value) clear();

		const _delay = unref(delay);
		if (typeof _delay !== "number" || _delay < 0) return;

		timerRef.value = setTimeout(() => {
			fn();
			timerRef.value = null;
		}, _delay);
	};

	if (options.immediate) {
		fn();
	}

	watchEffect((onInvalidate) => {
		start();
		onInvalidate(() => {
			clear();
		});
	});

	return {
		start,
		stop,
		clear,
	};
}

export default useTimeout;
