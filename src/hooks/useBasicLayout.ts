import { breakpointsTailwind, useBreakpoints } from "@vueuse/core";
import { ref } from "vue";

/**
 * 检查用户是否使用移动设备访问
 * @returns {boolean} 如果用户是通过移动设备访问，则返回true；否则返回false
 */
const isMobileAgent = () => {
	return /(iPhone|Android|Windows Phone|Windows Mobile)/i.test(
		navigator.userAgent
	);
};

export function useBasicLayout() {
	const breakpoints = useBreakpoints(breakpointsTailwind);
	const isMobile = breakpoints.smaller("sm");
	const isIpad = breakpoints.between("sm", "lg");
	const isPC = breakpoints.greater("lg");

	const isMobileDevice = ref(isMobileAgent());
	const isWX = ref(/MicroMessenger/i.test(navigator.userAgent));

	return { isMobile, isIpad, isPC, isMobileDevice, isWX };
}
