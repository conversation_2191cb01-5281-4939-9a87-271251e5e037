export default {
  common: {
    add: '添加',
    addSuccess: '添加成功',
    edit: '编辑',
    editSuccess: '编辑成功',
    delete: '删除',
    deleteSuccess: '删除成功',
    save: '保存',
    saveSuccess: '保存成功',
    reset: '重置',
    action: '操作',
    export: '导出',
    exportSuccess: '导出成功',
    import: '导入',
    importSuccess: '导入成功',
    clear: '清空',
    clearSuccess: '清空成功',
    yes: '是',
    no: '否',
    confirm: '确定',
    download: '下载',
    noData: '暂无数据',
    wrong: '好像出错了，请稍后再试。',
    success: '操作成功',
    failed: '操作失败',
    verify: '登录',
    getCodeTips:"获取短信验证码",
    register: '点击注册',
    unauthorizedTips: '未经授权，请先进行登录。',
    registerTips: '请进行账户注册。',
    registerbtnTips: '没有账户？请点击注册',
  },
  chat: {
    newChatButton: '新建聊天',
		newChatMJButton: '新建MJ绘图',
    placeholder: '来说点什么吧...（Shift + Enter = 换行）',
    placeholderMobile: '来说点什么...',
		mJplaceholder: '请填写你的创意（建议先翻译为英语再创作效果更好）',
    copy: '复制',
    copied: '复制成功',
    copyCode: '复制代码',
    clearChat: '清空会话',
    clearChatConfirm: '是否清空会话?',
    exportImage: '保存会话到图片',
    exportImageConfirm: '是否将会话保存为图片?',
    exportSuccess: '保存成功',
    exportFailed: '保存失败',
    usingContext: '上下文模式',
    turnOnContext: '当前模式下, 发送消息会携带之前的聊天记录',
    turnOffContext: '当前模式下, 发送消息不会携带之前的聊天记录',
    deleteMessage: '删除消息',
    deleteMessageConfirm: '是否删除此消息?',
    deleteHistoryConfirm: '确定删除此记录?',
    clearHistoryConfirm: '确定清空聊天记录?',
    preview: '预览',
    showRawText: '显示原文',
  },
  setting: {
    setting: '设置',
    general: '总览',
    user: '个人信息',
    advanced: '高级',
    config: '配置',
    avatarLink: '头像链接',
    name: '名称',
    description: '描述',
    role: '角色设定',
    resetUserInfo: '重置用户信息',
    chatHistory: '聊天记录',
    theme: '主题',
    language: '语言',
    api: 'API',
    reverseProxy: '反向代理',
    timeout: '超时',
    socks: 'Socks',
    httpsProxy: 'HTTPS Proxy',
    balance: 'API余额',
  },
  store: {
    siderButton: '提示词商店',
    local: '本地',
    online: '在线',
    title: '标题',
    description: '描述',
    clearStoreConfirm: '是否清空数据？',
    importPlaceholder: '请粘贴 JSON 数据到此处',
    addRepeatTitleTips: '标题重复，请重新输入',
    addRepeatContentTips: '内容重复：{msg}，请重新输入',
    editRepeatTitleTips: '标题冲突，请重新修改',
    editRepeatContentTips: '内容冲突{msg} ，请重新修改',
    importError: '键值不匹配',
    importRepeatTitle: '标题重复跳过：{msg}',
    importRepeatContent: '内容重复跳过：{msg}',
    onlineImportWarning: '注意：请检查 JSON 文件来源！',
    downloadError: '请检查网络状态与 JSON 文件有效性',
  },
	vip: {
    siderButton: '升级VIP账户',
    title: '升级到VIP',
		monthOne: '1个月',
		priceOne: '￥28元',
		originOne: '原价￥88元',
		monthTwo: '3个月',
		priceTwo: '￥68元',
		originTwo: '原价￥198元',
		monthThree: '12个月',
		priceThree: '￥168元',
		originThree: '原价￥688元',
		payTitle: '支付方式',
		payAlipay: '支付宝支付',
    wxpay: '微信支付',
		remark1: '无限次数的问答',
		remark2: 'Plus付费账号接入',
		remark3: '急速响应',
		remark4: '使用新的特征',
		ok: '确定'
  },
	account: {
		title: '账号绑定',
		siderButton: '账号绑定',
	},
	login: {
		siderButton: '用户登录',
		logoutButton: '退出登录'
	},
	novice: {
		siderButton: '帮助文档',
	}
}
