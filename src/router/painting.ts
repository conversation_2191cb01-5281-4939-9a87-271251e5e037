import type { RouteRecordRaw } from "vue-router";
import Layout from "@/views/painting/Layout/index.vue";

const PaintingRoutes: RouteRecordRaw[] = [
	{
		path: "/painting",
		component: Layout,
		meta: {
			navKey: "painting",
		},
		children: [
			{
				//
				path: "square",
				name: "square",
				component: () => import("@/views/painting/square.vue"),
			},
			// 图生文
			{
				path: "text2img",
				name: "Text2Img",
				component: () => import("@/views/painting/text2img.vue"),
			},
			// 图生图
			{
				path: "img2img",
				name: "Img2Img",
				component: () => import("@/views/painting/img2img.vue"),
			},
			// 换脸
			{
				path: "faceSwapper",
				name: "FaceSwapper",
				component: () => import("@/views/painting/faceSwapper.vue"),
			},
			// 图生文
			{
				path: "img2text",
				name: "Img2Text",
				component: () => import("@/views/painting/img2text.vue"),
			},
			// 咒语解析
			{
				path: "shorten",
				name: "shorten",
				component: () => import("@/views/painting/shorten.vue"),
			},
			{
				path: "collect",
				name: "collect",
				component: () => import("@/views/painting/collect.vue"),
			},
		],
	},
];

export default PaintingRoutes;
