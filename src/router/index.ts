import type { App } from "vue";
import type { RouteRecordRaw } from "vue-router";
import { createRouter, createWebHistory } from "vue-router";
import { setupPageGuard } from "./permission";
import { ChatLayout } from "@/views/chat/layout";
import { MjLayout } from "@/views/midjourney/layout";
import { AppLayout } from "@/views/application/layout";
import { HomeLayout } from "@/views/home/<USER>";
// import { PaperLayout } from "@/views/paper-origin/layout";
import Layout from "@/layouts/Layout.vue";

import PaintingRoutes from "./painting";
import { useUserStore } from "@/store";

const routes: RouteRecordRaw[] = [
	{
		path: "/",
		component: HomeLayout,
		meta: {
			navKey: "home",
			content: {
				title: "AIWork365——国内领先的AI写作工具，让创作更简单，办公更轻松！",
				description:
					"AIWork365 是一款强大的 AI写作工具，覆盖全行业，适配全场景，提供高效、准确的写作服务，帮助用户快速生成优质内容。AI论文写作、制作PPT、绘画等功能帮助提升工作效率。",
				keywords:
					"AIWork365,AI写作,AI写作工具,AI写作助手,AI论文,AI聊天,AIPPT,AI智能写作,AI商务办公,AI智能办公,AI营销助手,AI方案计划书创作,AI研究报告创作,小说AI写作,AI新媒体创作,AI短视频创作,AI简历,AI图片生成,AI视频生成",
			},
		},
		children: [
			{
				path: "",
				name: "Home",
				component: () => import("@/views/home/<USER>"),
			},
			// {
			// 	path: "/paper",
			// 	name: "Paper",
			// 	component: () => import("@/views/paper/index.vue"),
			// },
		],
	},
	{
		path: "/paper",
		component: Layout,
		meta: {
			navKey: "paper",
			content: {
				title:
					"AIWork365——AI论文写作神器，万字论文一键生成，低查重率，高专业度。",
				description:
					"AIWork365特别擅长生成高效专业的论文输出，内容逻辑自洽，查重率低于15%，AICG检测低于10%。此外还可以帮助您智能生成毕业告论文、开题报告、课程论文、期末论文、职称论文、实训报告、教学设计、职业生涯规划等内容。",
				keywords:
					"AI论文写作,AI专业论文生成工具,AI毕业论文生成工具,AI开题报AI聊天,期末论文生成工具,AI职称论文生成工具,AI实训报告工具,AI教学设计工具,AI职业生涯规划工具",
			},
		},
		redirect: "/paper",
		children: [
			{
				path: "/paper",
				name: "Paper",
				component: () => import("@/views/paper/index.vue"),
			},
		],
	},
	{
		path: "/paper2",
		component: Layout,
		meta: {
			navKey: "paper",
			content: {
				title:
					"AIWork365——AI论文写作神器，万字论文一键生成，低查重率，高专业度。",
				description:
					"AIWork365特别擅长生成高效专业的论文输出，内容逻辑自洽，查重率低于15%，AICG检测低于10%。此外还可以帮助您智能生成毕业告论文、开题报告、课程论文、期末论文、职称论文、实训报告、教学设计、职业生涯规划等内容。",
				keywords:
					"AI论文写作,AI专业论文生成工具,AI毕业论文生成工具,AI开题报AI聊天,期末论文生成工具,AI职称论文生成工具,AI实训报告工具,AI教学设计工具,AI职业生涯规划工具",
			},
		},
		redirect: "/paper2",
		children: [
			{
				path: "/paper2",
				name: "Paper2",
				component: () => import("@/views/paper/index2.vue"),
			},
			{
				path: "/paper2/confirm",
				name: "Paper2Confirm",
				component: () => import("@/views/paper/paperConfirm.vue"),
			},
			{
				path: "/paper2/history",
				name: "Paper2History",
				component: () => import("@/views/paper/history.vue"),
			},
		],
	},
	{
		path: "/chat",
		name: "Chat",
		component: ChatLayout,
		meta: {
			navKey: "chat",
			content: {
				title:
					"AI Work365——强大的AI实时问答功能，满足用户所需的各场景问答内容。",
				description:
					"AI Work365聊天工具可以基于用户的对话逻辑，通过人类沟通聊天的方式了解用户需求，并根据用户场景智能生成所需问题内容。可以为用户生成短视频脚本、小红书文案、实习工作周报、开学典礼致辞等内容。",
				keywords:
					"AI短视频脚本,AI小红书文案,AI实习工作周报,AI开学典礼致辞,AI聊天工具,AI问答,AI抖音文案,AI场景对话,AI问答生成式内容",
			},
		},
		redirect: "/chat",
		children: [
			{
				path: "/chat/:uuid?",
				name: "ChatDetail",
				component: () => import("@/views/chat/index.vue"),
				props: (route) => ({
					uuid: route.params.uuid,
					deepseek: route.query.deepseek === "true",
				}),
			},
		],
	},
	{
		path: "/mj",
		name: "Midjourney",
		component: MjLayout,
		meta: {
			navKey: "midjourney",
		},
		redirect: "/mj",
		children: [
			{
				path: "/mj/:uuid?",
				name: "Mj",
				component: () => import("@/views/midjourney/index.vue"),
			},
		],
	},
	{
		path: "/apps",
		component: HomeLayout,
		meta: {
			navKey: "create",
			content: {
				title: "AIWork365——国内领先的AI写作工具，让创作更简单，办公更轻松！",
				description:
					"AIWork365 是一款强大的 AI写作工具，覆盖全行业，适配全场景，提供高效、准确的写作服务，帮助用户快速生成优质内容。AI论文写作、制作PPT、绘画等功能帮助提升工作效率。",
				keywords:
					"AIWork365,AI写作,AI写作工具,AI写作助手,AI论文,AI聊天,AIPPT,AI智能写作,AI商务办公,AI智能办公,AI营销助手,AI方案计划书创作,AI研究报告创作,小说AI写作,AI新媒体创作,AI短视频创作,AI简历,AI图片生成,AI视频生成",
			},
		},
		children: [
			{
				path: "",
				name: "Apps",
				component: () => import("@/views/home/<USER>"),
			},
		],
	},
	{
		path: "/apps",
		component: AppLayout,
		meta: {
			content: {
				title: "AIWork365——国内领先的AI写作工具，让创作更简单，办公更轻松！",
				description:
					"AIWork365 是一款强大的 AI写作工具，覆盖全行业，适配全场景，提供高效、准确的写作服务，帮助用户快速生成优质内容。AI论文写作、制作PPT、绘画等功能帮助提升工作效率。",
				keywords:
					"AIWork365,AI写作,AI写作工具,AI写作助手,AI论文,AI聊天,AIPPT,AI智能写作,AI商务办公,AI智能办公,AI营销助手,AI方案计划书创作,AI研究报告创作,小说AI写作,AI新媒体创作,AI短视频创作,AI简历,AI图片生成,AI视频生成",
			},
		},
		children: [
			{
				path: "/apps/:id",
				name: "AppDetail",
				component: () => import("@/views/application/application.vue"),
				meta: {
					hideFooter: true,
				},
			},
		],
	},
	{
		path: "/profile",
		name: "Pro",
		component: Layout,
		redirect: "/profile",
		children: [
			{
				path: "/profile",
				name: "Profile",
				component: () => import("@/views/profile/index.vue"),
				meta: {
					// hideFooter: true
				},
			},
		],
	},
	{
		path: "/collection",
		component: HomeLayout,
		children: [
			{
				path: "/collection",
				name: "Collection",
				component: () => import("@/views/collection/index.vue"),
				meta: {
					hideFooter: true,
				},
			},
		],
	},
	{
		path: "/history",
		component: HomeLayout,
		children: [
			{
				path: "/history",
				name: "History",
				component: () => import("@/views/history/index.vue"),
				meta: {
					hideFooter: true,
				},
			},
		],
	},
	{
		path: "/ppt",
		component: Layout,
		meta: {
			navKey: "ppt",
			content: {
				title: "AI Work365—AI PPT让PPT制作变得如此简单、高效、专业",
				description:
					"AI Work365支持不懂PPT制作的您，通过AI技术便捷快速生成精美、专业的PPT。无论是总结汇报、工作述职还是项目汇报等，统统不再为PPT制作发愁啦!",
				keywords:
					"AI PPT生成工具，任选模板布局灵活切换，在线编辑PPT内容，0基础也能快速用AI制作PPT，月总结PPT，工作述职PPT，简历PPT，工作总结PPT，项目汇报PPT",
			},
		},
		children: [
			{
				path: "",
				name: "PPT",
				component: () => import("@/views/ppt/index.vue"),
			},
			{
				path: "/ppt/index2",
				name: "PPT2",
				component: () => import("@/views/ppt/index2.vue"),
				meta: {
					hideHeader: true,
				},
			},
			{
				path: "/ppt/index2/workspace/home",
				name: "PPTIndex2WorkspaceHome",
				component: () => import("@/views/ppt/WorkSpace/Home/index.vue"),
				meta: {
					hideHeader: true,
				},
			},
			{
				path: "/ppt/workspace/home",
				name: "PPTWorkspaceHome",
				component: () => import("@/views/ppt/WorkSpace/Home/index.vue"),
			},
			{
				path: "/ppt/generate",
				name: "PPTGenerate",
				component: () => import("@/views/ppt/generate.vue"),
				meta: {
					hideFooter: true,
				},
			},
		],
	},
	{
		path: "/search",
		component: Layout,
		children: [
			{
				path: "/search",
				name: "Search",
				component: () => import("@/views/search/index.vue"),
			},
		],
	},
	{
		path: "/distribution",
		component: Layout,
		children: [
			{
				path: "/distribution",
				name: "Distribution",
				component: () => import("@/views/distribution/index.vue"),
			},
			{
				path: "/distribution/center",
				name: "DistributionCenter",
				component: () => import("@/views/distribution/center.vue"),
			},
			{
				path: "/distribution/assets",
				name: "DistributionAssets",
				component: () => import("@/views/distribution/assets.vue"),
			},
			{
				path: "/distribution/agreement",
				name: "DistributionAgreement",
				component: () => import("@/views/distribution/Agreement.vue"),
			}
		],
	},
	{
		path: "/plagiarism-check",
		component: Layout,
		children: [
			{
				path: "",
				name: "PlagiarismCheck",
				component: () => import("@/views/plagiarismCheck/index.vue"),
				meta: {
					navKey: "paper",
				},
			},
		],
	},
	{
		path: "/lower-paper-similarity-rate",
		component: Layout,
		children: [
			{
				path: "",
				name: "LowerPaperSimilarityRate",
				component: () => import("@/views/LowerPaperSimilarityRate/index.vue"),
				meta: {
					navKey: "paper",
				},
			},
		],
		meta: {
			navKey: 'paper'
		}
	},
	{
		path: "/lower-aigc-rate",
		component: Layout,
		children: [
			{
				path: "",
				name: "LowerAigcRate",
				component: () => import("@/views/LowerAIGCRate/index.vue"),
				props: (route) => ({
					type: route.query.type
				}),
			},
		],
		meta: {
			navKey: 'paper'
		}
	},
	{
		path: "/privacypolicy",
		component: Layout,
		children: [
			{
				path: "",
				name: "PrivacyPolicy",
				component: () => import("@/views/PrivacyPolicy/index.vue"),
			},
		],
	},
	{
		path: "/userpaymentagreement",
		component: Layout,
		children: [
			{
				path: "",
				name: "UserPaymentAgreement",
				component: () => import("@/views/UserPaymentAgreement/index.vue"),
			},
		],
	},
	{
		path: "/useragreement",
		component: Layout,
		children: [
			{
				path: "",
				name: "UserAgreement",
				component: () => import("@/views/UserAgreement/index.vue"),
			},
		],
	},
	...PaintingRoutes,
	{
		path: "/aiteam-landing",
		meta: {
			navKey: "team",
		},
		component: () => import("@/views/team/AiTeamLanding.vue"),
	},
	{
		path: "/team",
		component: () => import("@/views/team/layouts/TeamLayout.vue"),
		meta: {
			hideFooter: true,
			navKey: "team",
		},
		beforeEnter: (to, from, next) => {
			const userStore = useUserStore();
			if (!userStore.curTeam) return next("/aiteam-landing");
			if (userStore.curTeam && userStore.curTeam.TeamsUsers?.role === "member")
				return next("/aiteam-landing");
			next();
		},
		children: [
			// {
			// 	path: '',
			// 	name: 'Team',
			// 	component: () => import('@/views/team/agent/index.vue')
			// },
			{
				path: "agent",
				name: "Agent",
				component: () => import("@/views/team/agent/index.vue"),
			},
			{
				path: "agent/detail",
				name: "AgentDetail",
				component: () => import("@/views/team/agent/detail.vue"),
			},
			{
				path: "agent/chat/history",
				name: "AgentChatHistory",
				component: () => import("@/views/team/agent/chatHistory.vue"),
			},
			{
				path: "knowledge",
				component: () => import("@/views/team/knowledge/index.vue"),
			},
			{
				path: "setting",
				component: () => import("@/views/team/setting/index.vue"),
			},
		],
	},
	{
		path: "/office",
		component: Layout,
		children: [
			{
				path: "",
				name: "Office",
				component: () => import("@/views/office/index.vue"),
			},
			{
				path: "/office/history",
				name: "OfficeHistory",
				component: () => import("@/views/office/history.vue"),
			},
			{
				path: "/office/pdf2word",
				name: "Pdf2Word",
				component: () => import("@/views/office/content.vue"),
			},
			{
				path: "/office/pdf2excel",
				name: "Pdf2Excel",
				component: () => import("@/views/office/content.vue"),
			},
			{
				path: "/office/pdf2ppt",
				name: "Pdf2Ppt",
				component: () => import("@/views/office/content.vue"),
			},
			{
				path: "/office/pdfmerge",
				name: "PdfMerge",
				component: () => import("@/views/office/content.vue"),
			},
			{
				path: "/office/pdfsplit",
				name: "PdfSplit",
				component: () => import("@/views/office/content.vue"),
			},
			{
				path: "/office/word2pdf",
				name: "Word2Pdf",
				component: () => import("@/views/office/content.vue"),
			},
			{
				path: "/office/excel2pdf",
				name: "Excel2Pdf",
				component: () => import("@/views/office/content.vue"),
			},
			{
				path: "/office/ppt2pdf",
				name: "Ppt2Pdf",
				component: () => import("@/views/office/content.vue"),
			},
			{
				path: "/office/img2word",
				name: "Img2Word",
				component: () => import("@/views/office/content.vue"),
			},
			{
				path: "/office/img2excel",
				name: "Img2Excel",
				component: () => import("@/views/office/content.vue"),
			},
			{
				path: "/office/img2pdf",
				name: "Img2Pdf",
				component: () => import("@/views/office/content.vue"),
			},
			{
				path: "/office/imgcompress",
				name: "ImgCompress",
				component: () => import("@/views/office/content.vue"),
			},
			{
				path: "/office/videotogif",
				name: "VideoToGif",
				component: () => import("@/views/office/content.vue"),
			},
			{
				path: "/office/doc2translate",
				name: "Doc2Translate",
				component: () => import("@/views/office/content.vue"),
			},
			{
				path: "/office/findpassword",
				name: "FindPassword",
				component: () => import("@/views/office/content.vue"),
			},
			{
				path: "/office/history",
				name: "OfficeHistory",
				component: () => import("@/views/office/history.vue"),
			},
		],
	},
	{
		path: "/wxpay",
		name: "WXPAY",
		component: () => import("@/views/wxh5pay/index.vue"),
	},
	{
		path: "/paymid",
		name: "PayMid",
		component: () => import("@/views/payment/PayMidPage.vue"),
	},
	{
		path: "/demo",
		component: Layout,
		children: [
			{
				path: "editor",
				name: "EditorDemo",
				component: () => import("@/views/demo/editorDemo.vue"),
			},
		]
	},

	{
		path: "/404",
		name: "404",
		component: () => import("@/views/exception/404/index.vue"),
	},

	{
		path: "/500",
		name: "500",
		component: () => import("@/views/exception/500/index.vue"),
	},

	{
		path: "/:pathMatch(.*)*",
		name: "notFound",
		redirect: "/404",
	},
];

export const router = createRouter({
	history: createWebHistory(),
	routes,
	scrollBehavior: () => ({ left: 0, top: 0 }),
});

setupPageGuard(router);

export async function setupRouter(app: App) {
	app.use(router);
	await router.isReady();
}
