import { h, type App, markRaw } from 'vue'
import IconText from '@/assets/images/content.svg'
import IconWord from '@/assets/images/word.svg'
import { DownOne, TransferData, WeixinSearch, Earth, Check, Download, MoreOne, LoadingFour, ArrowLeft, CommentOne, FolderUpload, Back, Undo, History, Star, Plus, Search, Delete, Close, AllApplication, Pencil, CloseOne, CheckOne } from '@icon-park/vue-next'
import StarFill from '@/assets/images/star-fill.svg'
import IconDown from '@/assets/aiwork/svg/down.svg'
import IconSelect from '@/assets/images/select.svg'
import PPT from '@/assets/aiwork/svg/ppt.svg'
import Flower from '@/assets/images/flower.svg'
import Think from '@/assets/aiwork/svg/think.svg'
import Import from '@/assets/aiwork/svg/import.svg'
import Paper from '@/assets/images/paper.svg'
import Paper2 from '@/assets/images/paper2.svg'
import Paper3 from '@/assets/images/paper3.svg'
import Question from '@/assets/images/question.svg'
import Copy from '@/assets/images/copy.svg'
import UpOne from '@/assets/images/upone.svg'
import Question1 from '@/assets/lower/question1.svg'
import Bucket from '@/assets/aiwork/svg/bucket.svg'
import Prev from '@/assets/aiwork/svg/prev.svg'
import Next from '@/assets/aiwork/svg/next.svg'

export interface Icons {
	[key: string]: any;
}
export const icons: Icons = {
	IconFolder: markRaw(
		h("i", { class: "fi fi-rr-folder-open w-[14px] h-[14px]" })
	),
	IconPrev: Prev,
	IconNext: Next,
	IconBucket: Bucket,
	IconQuestion1: Question1,
	IconUp: UpOne,
	IconDown: DownOne,
	IconPaper3: markRaw(h(Paper3)),
	IconCopy: markRaw(h(Copy)),
	IconQuestion: markRaw(h(Question)),
	IconPaper2: markRaw(h(Paper2)),
	IconPaper: markRaw(h(Paper)),
	IconTransferData: markRaw(TransferData),
	IconDelete: markRaw(Delete),
	IconClose: markRaw(Close),
	IconAllApplication: markRaw(AllApplication),
	IconCloseOne: markRaw(CloseOne),
	IconPencil: markRaw(Pencil),
	IconCheck: markRaw(CheckOne),
	IconCheckOne: markRaw(Check),
	IconSearch: markRaw(Search),
	IconPlus: markRaw(Plus),
	IconStar: markRaw(Star),
	IconStarFill: markRaw(h(StarFill)),
	IconHistory: markRaw(History),
	IconUndo: markRaw(Undo),
	IconBack: markRaw(Back),
	IconFolderUpload: markRaw(FolderUpload),
	IconText: markRaw(h(IconText)),
	IconWord: markRaw(h(IconWord)),
	IconComment: markRaw(CommentOne),
	IconArrowLeft: markRaw(ArrowLeft),
	IconLoading: LoadingFour,
	IconMore: markRaw(MoreOne),
	IconSelect: IconSelect,
	IconPPt: markRaw(h(PPT)),
	IconFlower: markRaw(h(Flower)),
	IconThink: markRaw(h(Think)),
	IconEarth: markRaw(Earth),
	IconWeixinSearch: markRaw(WeixinSearch),
	IconImport: markRaw(h(Import)),
	IconDownload: markRaw(Download),
};

export default {
	install(app: App) {
		for (const key of Object.keys(icons)) {
			app.component(key, icons[key])
		}
	}
}
