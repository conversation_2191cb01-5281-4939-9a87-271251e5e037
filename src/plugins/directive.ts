const preventClick = (event) => {
	event.stopPropagation();
	event.preventDefault();
};

// 域名枚举
enum HostnameEnum {
	AIWORK365_NET = "aiwork365.net",
	AIWORK365_COM_CN = "aiwork365.com.cn",
	AIWORK365_CC = "aiwork365.cc",
	AIWORK365_CN = "aiwork365.cn",
	AIWORK365_COM = "aiwork365.com",
	AIWORK365_QA = "mjmobi.com",
}

// 替换文本的辅助函数
export const replaceText = (text) => {
	if (!text) return text;

	// 获取当前域名
	const hostname = window.location.hostname;

	// 只在特定域名下执行替换
	// 这里假设只在aiwork365.cn域名下执行替换，您可以根据需要调整
	if (
		hostname.indexOf(HostnameEnum.AIWORK365_NET) > -1 ||
		hostname.indexOf(HostnameEnum.AIWORK365_QA) > -1 ||
		hostname.indexOf("localhost") > -1
	) {
		return text.replace(/论文|学术/g, "毕业");
	}

	// 其他域名不执行替换
	return text;
};

export const loadingDirective = {
	mounted(el, binding) {
		// 指令被绑定到元素上时调用
		if (binding.value) {
			// 如果指令的值为 true，显示加载状态
			el.classList.add("loading");
			el.addEventListener("click", preventClick, true);
			el.hasLoadingEvent = true;
		}
	},
	updated(el, binding) {
		// 当指令的值发生变化时调用
		if (binding.value) {
			el.classList.add("loading");
			if (!el.hasLoadingEvent) {
				el.addEventListener("click", preventClick, true);
			}
		} else {
			// 如果指令的值为 false，移除加载状态
			el.classList.remove("loading");
			el.removeEventListener("click", preventClick, true);
			el.hasLoadingEvent = false;
		}
	},
	beforeUnmount(el, binding) {
		if (el.hasLoadingEvent) {
			el.removeEventListener("click", preventClick, true);
		}
	},
};

// 替换文本指令
export const replaceDirective = {
	mounted(el, binding) {
		// 保存原始文本内容
		el._originalContent = el.textContent;

		// 替换文本内容
		el.textContent = replaceText(el.textContent);
	},
	updated(el, binding) {
		// 当内容更新时，重新获取原始内容并替换
		const newContent = el.textContent;
		if (newContent !== el._originalContent && newContent !== replaceText(el._originalContent)) {
			el._originalContent = newContent;
			el.textContent = replaceText(newContent);
		}
	}
};

export const useDirective = (app) => {
	app.directive("loading", loadingDirective);
	app.directive("replace", replaceDirective);
};
