import { defineStore } from "pinia";
import {
	getToken,
	removeToken,
	setToken,
	setUser,
	getUser,
	setActivity,
	getActivity,
	getActivityImageShow,
	setActivityImageShow,
} from "./helper";
import { store, useUserStore } from "@/store";
import { fetchSession } from "@/chatgpt";
import { isBoolean } from "@/utils/is";

import { setCookie, getCookie } from '@/utils/cookie';

interface SessionResponse {
	auth: boolean;
	token?: string;
	captchaAppId?: string;
	model: "ChatGPTAPI" | "ChatGPTUnofficialProxyAPI";
	user?: {
		name: string;
		type: string;
	};
	activityInfo?: {
		title: string;
		url: string;
	};
}

export interface AuthState {
	token: string | undefined;
	session: SessionResponse | null;
	shareId?: string | null;
}

export const useAuthStore = defineStore("auth-store", {
	state: (): AuthState => ({
		token: getToken(),
		session: null,
		shareId: null,
	}),

	getters: {
		captchaAppId(state): string {
			return state.session?.captchaAppId || "";
		},
	},

	actions: {
		async getSession() {
			try {
				const data = await fetchSession<SessionResponse>();
				this.session = { ...data };
				const userStore = useUserStore();
				const user: any = data.user;
				if (data?.token) {
					setToken(data.token);
				}
				if (data?.user) {
					// if (
					// 	data.user.type == "temp" &&
					// 	/MicroMessenger/.test(navigator.userAgent)
					// ) {
					// 	window.location.replace(
					// 		"https://data.aiwork.cn/aiwork/sign/getGzhAuthUrl"
					// 	);
					// }
					setUser(data.user);
					userStore.updateUserInfo({
						name: user.nickname || user.username || user.phone,
						description: "用户信息",
						member: user.Members,
						uid: user.uid,
					});
				}
				if (data?.activityInfo) {
					const activityShow = getActivityImageShow()
					setActivity(data.activityInfo);
					// 20241112不需要默认显示
					// if (!isBoolean(activityShow)) {
					// 	setActivityImageShow(true);
					// }
				}

				return Promise.resolve(data);
			} catch (error) {
				return Promise.reject(error);
			}
		},

		setToken(token: string) {
			this.token = token;
			setToken(token);
		},

		getUser() {
			return getUser();
		},
		getActivity() {
			return getActivity();
		},
		removeToken() {
			this.token = undefined;
			removeToken();
		},

		// 更新shareId
		updateShareId(shareId: string) {
			this.shareId = shareId;
			setCookie('ai-share', JSON.stringify({
				shareId,
				timestamp: Date.now()
			}));
		},

		// 获取有效的shareId
		getValidShareId(): string | null {
			const cookieData = getCookie('ai-share');
			if (!cookieData) return null;

			try {
				const { shareId, timestamp } = JSON.parse(cookieData);
				// 检查30天有效期
				if (Date.now() - timestamp < 30 * 24 * 60 * 60 * 1000) {
					return shareId;
				}
			} catch (e) {
				console.error('解析shareId失败', e);
			}
			return null;
		},
	},
});

export function useAuthStoreWithout() {
	return useAuthStore(store);
}
