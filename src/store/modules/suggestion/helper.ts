import { ss } from "@/utils/storage";

const LOCAL_NAME = "suggestionStorage";

export interface SuggestionState {
	id?: number; // ID
	categoryId?: number; // 类别ID
	categoryName?: string; // 类别名称
	uuid?: number | string; // UUID
}

export interface SuggestionArray {
	sugges: SuggestionState[]; // 建议数组
}

export function defaultSetting(): SuggestionArray {
	return {
		sugges: [], // 默认设置为空数组
	};
}

export function getLocalState(): SuggestionArray {
	const localState = ss.get(LOCAL_NAME); // 从本地存储获取状态
	return { ...defaultSetting(), ...localState };
}

export function setLocalState(setting: SuggestionArray): void {
	ss.set(LOCAL_NAME, setting); // 将设置保存到本地存储
}

export function removeLocalState() {
	ss.remove(LOCAL_NAME); // 移除本地存储中的设置
}
