import { defineStore } from "pinia";
import type { SuggestionState, SuggestionArray } from "./helper";
import {
	defaultSetting,
	getLocalState,
	removeLocalState,
	setLocalState,
} from "./helper";

export const useSuggestionStore = defineStore("suggestion-setting-store", {
	state: (): SuggestionArray => getLocalState(),

	actions: {
		// 根据 uuid 获取设置项
		getSetting(uuid: number) {
			return this.sugges.find((item) => item.uuid === uuid);
		},

		// 更新设置项
		updateSetting(settings: Partial<SuggestionState>, uuid: number) {
			const index = this.sugges.findIndex((item) => item.uuid == uuid);
			if (index !== -1) {
				this.sugges[index] = settings;
			} else {
				this.sugges.push(settings);
			}
			this.recordState();
		},

		// 移除设置项
		removeSetting(id: number, uuid: number) {
			const index = this.sugges.findIndex(
				(item) => item.id === id && item.uuid == uuid
			);
			this.sugges.splice(index, 1);
			this.recordState();
		},

		// 重置设置项
		resetSetting() {
			this.$state = defaultSetting();
			removeLocalState();
		},

		// 记录状态
		recordState() {
			setLocalState(this.$state);
		},
	},
});
