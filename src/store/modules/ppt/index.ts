import { defineStore } from 'pinia'
// import type { ChatState,IChatProps } from '@/types/chat'

export interface IChatProps {
  text: string
  inversion: boolean
  loading: boolean
  uuid: number
  isCompute: boolean
  error?: boolean
}
export interface ChatState {
  chat: IChatProps[]
}


export const usePptChatStore = defineStore('ppt-chat-store', {
  state: (): ChatState => ({
    chat: []
  }),

  getters: {

  },
  actions: {
    updateChat(chat: IChatProps, uuid: number) {
      const chatIndex = this.chat.findIndex((item) => item.uuid === uuid)
      if (chatIndex === -1) {
        this.chat.push(chat)
      } else {
        this.chat[chatIndex] = chat
      }
    },
    recordChat() {
        this.chat = []
    }
  }
})
