import { defineStore } from 'pinia'
import type { SettingDrawsState } from './helper'
import { defaultSetting, getLocalState, removeLocalState, setLocalState } from './helper'

export const useDrawSettingStore = defineStore('settingDraw-store', {
  state: (): SettingDrawsState => getLocalState(),
  actions: {
		getSetting() {
			return this.$state
		},

    updateSetting(settings: Partial<SettingDrawsState>) {
      this.$state = { ...this.$state, ...settings }
      this.recordState()
    },

    resetSetting() {
      this.$state = defaultSetting()
      removeLocalState()
    },

    recordState() {
      setLocalState(this.$state)
    },
  },
})
