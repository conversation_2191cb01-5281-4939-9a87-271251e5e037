import { ss } from '@/utils/storage'

const LOCAL_NAME = 'drawSettingsStorage'

export interface SettingDrawsState {
  ar?: string,
	c?: number,
	s?: number,
	v?: string,
	iw: number,
	url?: string
}

export function defaultSetting(): any {
  return {
		ar: "1:1",
		c: 0,
		s: 750,
		v: "5",
		iw: 1,
		url: ''
  }
}

export function getLocalState(): SettingDrawsState {
  const localSetting: SettingDrawsState | undefined = ss.get(LOCAL_NAME)
  return { ...defaultSetting(), ...localSetting }
}

export function setLocalState(setting: SettingDrawsState): void {
  ss.set(LOCAL_NAME, setting)
}

export function removeLocalState() {
  ss.remove(LOCAL_NAME)
}
