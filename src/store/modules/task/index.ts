import { defineStore } from 'pinia'
import { getLocalTask, setLocalTask } from './helper'

export const useTaskStore = defineStore('task-store', {
  state: (): Chat.ChatTasks => getLocalTask(),

  actions: {
    addTaskList(state: Chat.ChatTask) {
			this.task.push(state)
			setLocalTask(this.$state)
    },
    getLocalTask(uuid: number) {
			if (uuid) {
				const data = this.task.find(item => item.uuid === uuid)
				return data
			}
    },
		delLocalTask(uuid: number) {
			const chatIndex = this.task.findIndex(item => item.uuid === uuid)
      if (chatIndex !== -1) {
        this.task.splice(chatIndex, 1)
        setLocalTask(this.$state)
      }
		}
  },
})
