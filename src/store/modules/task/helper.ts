import { ss } from '@/utils/storage'

const LOCAL_TASK  = 'chatTaskStorage'

export function defaultTaskState(): Chat.ChatTasks {
  return {
		task: [{
			messageId: '',
			uuid: 0,
			type: '',
			time: 5000,
			prompt: "",
			size: 0,
			url: ''
		}]
	}
}

export function getLocalTask(): Chat.ChatTasks {
  const localState = ss.get(LOCAL_TASK)
  return {...defaultTaskState() ,...localState }
}

export function setLocalTask(state: Chat.ChatTasks) {
  ss.set(LOCAL_TASK, state)
}

export function delLocalTask(uuid: number) {
  ss.remove(LOCAL_TASK)
}
