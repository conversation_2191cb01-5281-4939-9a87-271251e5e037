import { defineStore } from 'pinia'
import type { UserInfo, UserState } from './helper'
import { defaultSetting, getLocalState, setLocalState } from './helper'
import request from '@/utils/request'

export const useUserStore = defineStore('user-store', {
  state: (): UserState => getLocalState(),
  actions: {
    updateUserInfo(userInfo: Partial<UserInfo>) {
      this.userInfo = { ...this.userInfo, ...userInfo }

      this.recordState()
    },

    resetUserInfo() {
      this.userInfo = { ...defaultSetting().userInfo }
			this.curTeam = undefined
			this.teamList = []
			this.canCreateTeam = true
      this.recordState()
    },

    recordState() {
      setLocalState(this.$state)
    },

		async getTeamList(autoChange = false) {
			this.teamListLoading = true
			return request({
				url: '/api3/aiwork/team/listAll',
			}).then(data => {
				const {isCreated, teams} = data
				this.teamList = teams
				this.canCreateTeam = isCreated
				if(autoChange && data.length > 0) this.curTeam = data[0]
				this.recordState()
			}).finally(() => {
				this.teamListLoading = false
				this.recordState()
				return
			})
		},

		changeTeam(teamId?: string | number | null) {
			if(!teamId) {
				this.curTeam = null
				return this.recordState()
			}
			this.curTeam = this.teamList?.find(item => item.id === teamId)
			this.recordState()
		}
  },
})
