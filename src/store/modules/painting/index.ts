import { create<PERSON><PERSON>, defineStore } from "pinia";
import { getUser } from "@/store/modules/auth/helper";

export const usePaintingStore = defineStore("painting-store", {
	state: () => ({
		onGoingtaskCount: 0, // 正在执行的任务条数
		// maxTaskCount: 1, // 最大并发任务
	}),
	getters: {
		maxTaskCount(state) {
			const members = getUser()?.Member || [];

			const memberChild =
				(members.find((item) => item.code == 300001) || {}).child || [];

			let count = 1;
			memberChild.forEach((c) => {
				if (c.count > count) count = c.count;
			});

			return count;
		},
	},
	actions: {
		setOnGoingTaskCount(count: number) {
			this.onGoingtaskCount = count;
		},
	},
});
