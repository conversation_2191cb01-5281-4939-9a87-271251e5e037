import { defineStore } from "pinia";
import type { PromptStore } from "./helper";
import { getLocalPromptList, setLocalPromptList } from "./helper";

export const usePromptStore = defineStore("prompt-store", {
	state: (): PromptStore => getLocalPromptList(),

	actions: {
		// 更新提示列表
		updatePromptList(promptList: []) {
			this.$patch({ promptList });
			setLocalPromptList({ promptList });
		},
		// 获取提示列表
		getPromptList() {
			return this.$state;
		},
	},
});
