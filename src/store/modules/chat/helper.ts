import { ss } from '@/utils/storage'

const LOCAL_NAME = 'chatStorage'

export function defaultState(): Chat.ChatState {
  let uuid = 1002
	let uid = 1001
	let title = '新建会话'
	let opts = {}
	let url = window.location.href
	const isMj = url.indexOf('mj=1')> -1 || url.indexOf('/mj')>-1
	if(isMj) {
		title = '新建绘图'
		opts = {
			type: 'mj'
		}
	}
  return {
    active: uuid,
		mjActive: uid,
    usingContext: true,
    history: [{ uuid, title: title, isEdit: false, ...opts }],
    chat: [{ uuid, data: [] }]
  }
}

export function getLocalState(): Chat.ChatState {
  const localState = ss.get(LOCAL_NAME)
  return { ...defaultState(), ...localState }
}

export function setLocalState(state: Chat.ChatState) {
  ss.set(LOCAL_NAME, state)
}
