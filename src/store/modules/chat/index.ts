import { defineStore } from 'pinia'
import { getLocalState, setLocalState } from './helper'
import { router } from '@/router'
import { debug } from 'console'

export const useChatStore = defineStore('chat-store', {
  state: (): Chat.ChatState => getLocalState(),
  getters: {
    getChatHistoryByCurrentActive(state: Chat.ChatState) {
      // const active = state.curType == 'mj' ? state.mjActive : state.active
      // const index = state.history.findIndex((item) => item.uuid === active)
      // if (index !== -1) return state.history[index]
      // return null

    },

    getChatByUuid(state: Chat.ChatState) {
      return (uuid?: number) => {
        if (uuid) {
          return state.chat.find((item) => item.uuid === uuid)?.data || []
        }
        const data = state.history.find(item => item.uuid === uuid)
        const active = data?.type == 'mj' ? state.mjActive : state.active
        return state.chat.find((item) => item.uuid === active)?.data || []
      }
    },
  },

  actions: {
    setUsingContext(context: boolean) {
      this.usingContext = context
      this.recordState()
    },

    addHistory(history: Chat.History, chatData: Chat.Chat[] = []) {
      this.history.unshift({ ...history, updateTime: new Date().getTime(), createTime: new Date().getTime() })
      this.chat.unshift({ uuid: history.uuid, data: chatData })
      if (history?.type === 'mj') {
        this.mjActive = history.uuid
      } else {
        this.active = history.uuid
      }
      this.reloadRoute(history.uuid)
    },
    clearHistory(type?: string) {
      if (type) {
        this.history = this.history.filter(item => item.type !== type)
        this.reloadRoute()
        return
      }
      this.history = []
      this.reloadRoute()
    },

    updateHistory(uuid: number, edit: Partial<Chat.History>) {
      const index = this.history.findIndex((item) => item.uuid === uuid)
      if (index !== -1) {
        this.history[index] = { ...this.history[index], ...edit, updateTime: new Date().getTime() }
        this.recordState()
      }
    },

    getHistory(uuid: number) {
      if (!uuid) return
      return this.history.find((item) => item.uuid === uuid)
    },

    getAllHistory() {
      return this.history
    },
    async deleteHistory(uuid: number, type: string) {
      const newHistory = type == 'chat' ? this.history.filter(item => !item.type || item.type == 'chat') : this.history.filter(item => item.type == 'mj')
      const index = newHistory.findIndex((item) => item.uuid === uuid)
      newHistory.splice(index, 1)
      const chatIndex = this.chat.findIndex((item) => item.uuid === uuid)
      this.chat.splice(chatIndex, 1)
      if (type === 'chat') {
        const history = this.history.filter(item => item.type == 'mj')
        this.history = [...history, ...newHistory]
      } else {
        const history = this.history.filter(item => !item.type || item.type == 'chat')
        this.history = [...history, ...newHistory]
      }



      if (newHistory.length === 0) {
        if (type === 'chat') {
          this.active = null
        } else {
          this.mjActive = null
        }

        this.reloadRoute()
        return
      }

      if (index > 0 && index <= newHistory.length) {
        const uuid = newHistory[index - 1].uuid
        if (type === 'chat') {
          this.active = uuid
        } else {
          this.mjActive = uuid
        }
        this.reloadRoute(uuid)
        return
      }

      if (index === 0) {
        if (newHistory.length > 0) {
          const uuid = newHistory[0].uuid
          if (type === 'chat') {
            this.active = uuid
          } else {
            this.mjActive = uuid
          }
          this.reloadRoute(uuid)
        }
      }

      if (index > newHistory.length) {
        const uuid = newHistory[newHistory.length - 1].uuid
        if (type === 'chat') {
          this.active = uuid
        } else {
          this.mjActive = uuid
        }
        this.reloadRoute(uuid)
      }
    },

    async setActive(uuid: number, type: string) {
      if (type === 'chat') {
        this.active = uuid
      } else {
        this.mjActive = uuid
      }
      return await this.reloadRoute(uuid)
    },

    async clearActive(type: string) {
      if (type === 'chat') this.active = null
    },

    getChatByUuidAndIndex(uuid: number, index: number) {
      if (!uuid || uuid === 0) {
        if (this.chat.length) return this.chat[0].data[index]
        return null
      }
      const chatIndex = this.chat.findIndex((item) => item.uuid === uuid)
      if (chatIndex !== -1) return this.chat[chatIndex].data[index]
      return null
    },

    addChatByUuid(uuid: number, chat: Chat.Chat) {
      if (!uuid || uuid === 0) {
        if (this.history.length === 0) {
          const uuid = Date.now()
          this.history.push({ uuid, title: chat.text, isEdit: false })
          this.chat.push({ uuid, data: [chat] })
          this.active = uuid
          this.recordState()
        } else {
          this.chat[0].data.push(chat)
          if (this.history[0].title === '新建会话')
            this.history[0].title = chat.text
          this.recordState()
        }
      }

      const index = this.chat.findIndex((item) => item.uuid === uuid)
      if (index !== -1) {
        this.chat[index].data.push(chat)
        if (this.history[index].title === '新建会话')
          this.history[index].title = chat.text
        this.recordState()
      }
    },

    updateChatByUuid(uuid: number, index: number, chat: Chat.Chat) {
      if (!uuid || uuid === 0) {
        if (this.chat.length) {
          this.chat[0].data[index] = chat
          this.recordState()
        }
        return
      }

      const chatIndex = this.chat.findIndex((item) => item.uuid === uuid)
      if (chatIndex !== -1) {
        this.chat[chatIndex].data[index] = chat
        this.recordState()
      }
    },

    updateChatSomeByUuid(
      uuid: number,
      index: number,
      chat: Partial<Chat.Chat>,
    ) {
      if (!uuid || uuid === 0) {
        if (this.chat.length) {
          this.chat[0].data[index] = { ...this.chat[0].data[index], ...chat }
          this.recordState()
        }
        return
      }

      const chatIndex = this.chat.findIndex((item) => item.uuid === uuid)
      if (chatIndex !== -1) {
        this.chat[chatIndex].data[index] = {
          ...this.chat[chatIndex].data[index],
          ...chat,
        }
        this.recordState()
      }
    },

    deleteChatByUuid(uuid: number, index: number) {
      if (!uuid || uuid === 0) {
        if (this.chat.length) {
          this.chat[0].data.splice(index, 1)
          this.recordState()
        }
        return
      }

      const chatIndex = this.chat.findIndex((item) => item.uuid === uuid)
      if (chatIndex !== -1) {
        this.chat[chatIndex].data.splice(index, 1)
        this.recordState()
      }
    },

    clearChatByUuid(uuid?: number) {
      if (!uuid || uuid === 0) {
        if (this.chat.length) {
          this.chat[0].data = []
          this.recordState()
        }
        return
      }

      const index = this.chat.findIndex((item) => item.uuid === uuid)
      if (index !== -1) {
        this.chat[index].data = []
        this.recordState()
      }
    },

    async reloadRoute(uuid?: number) {
      this.recordState()
      const curItem = this.$state.history.find((item) => item.uuid === uuid)
      const urlObj = new URL(window.location.href)
      let url
      switch (curItem?.type) {
        case 'chat':
          url = `/chat/${uuid}`
          break
        case 'mj':
          url = `/mj/${uuid}`
          break
        default:
          url = `/chat`
      }
      await router.push(url + urlObj.search)
    },

    recordState() {
      setLocalState(this.$state)
    },
  },
})
