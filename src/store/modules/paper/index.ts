import { defineStore } from "pinia";
import type { PaperState } from "./helper";
import { getLocalSetting, setLocalSetting } from "./helper";
import { store } from "@/store";
import {
	fetchPaperTypeList,
	fetchPaperTemplateDetail,
	fetchLatestMessage,
	fetHistoryPaperList,
	fetchAddPaper,
	fetchPaperDetail,
	fetchGeneratePaperCore2,
	fetchPaperStatistics,
	fetchPaperList2,
} from "@/views/paper/apis";
import { router } from "@/router";
import {
	PaperHistory,
	PaperStatistics,
	PaperType,
	TemplateDetailData,
} from "@/views/paper/types";
import { PaperDetail } from "@/views/plagiarismCheck/types";
export const usePaperStore = defineStore("paper-store", {
	state: (): PaperState => getLocalSetting(),
	actions: {
		async setCreateType(createType: any) {
			this.createType = createType;
			this.recordState();
			const urlObj = new URL(window.location.href);
			var params = new URLSearchParams(urlObj.search);
			params.set("type", createType);
			// 替换查询参数的值
			// router.push(`/paper?`+params.toString())
			router.push(`/paper-origin?` + params.toString());
			//router.go(0);
			//window.location.reload()
			//await router.push("/paper")
		},
		recordState() {
			setLocalSetting(this.$state);
		},
		getPaper() {
			return getLocalSetting();
		},
		async getPaperStatistics() {
			try {
				const res = await fetchPaperStatistics<PaperStatistics>();
				this.paperStatistics = res;
			} catch (error) {
				console.log(error);
			}
		},
		async getPaperTypeList() {
			try {
				const res = await fetchPaperTypeList<PaperType[]>();
				this.paperForm.id = res[0].id;
				this.featureList = res;
			} catch (error) {
				console.log(error);
			}
		},
		async getTemplateDetail() {
			try {
				const type = this.featureList.find(
					(item) => item.id === this.paperForm.id
				)?.type;
				const res = await fetchPaperTemplateDetail<TemplateDetailData>({
					type,
				});
				this.preForm = res;
				this.paperForm = {
					id: this.paperForm.id,
					formData:
						res?.formData?.map((item) => ({
							field: item.field,
							value: item.defaultValue || "",
						})) || [],
				};
				console.log("preForm", this.preForm);
				console.log("paperForm", this.paperForm);
			} catch (error) {
				console.log(error);
			}
		},
		async getLatestMessage() {
			try {
				const res = await fetchLatestMessage<string[]>();
				this.latestMessage = res || [];
			} catch (error) {
				console.log(error);
			}
		},
		async getPaperHistory() {
			try {
				this.paperHistoryLoading = true;
				const res = await fetchPaperList2<{
					rows: PaperHistory[];
					count: number;
				}>({
					page: this.page,
					pageSize: this.pageSize,
					status: this.pageHistoryStatus === -1 ? null : this.pageHistoryStatus,
				});
				this.paperHistory = res.rows || [];
				this.paperHistoryCount = res.count || 0;
			} catch (error) {
				console.log(error);
			} finally {
				this.paperHistoryLoading = false;
			}
		},
		async createPaper() {
			try {
				this.createPaperLoading = true;
				const res = await fetchAddPaper<{ id: number }>(this.paperForm);
				const { id } = res;
				router.push({
					name: "Paper2Confirm",
					query: {
						id,
					},
				});
			} catch (error) {
			} finally {
				this.createPaperLoading = false;
			}
		},
		async setPaperType(id: number) {
			this.paperForm.id = id;
			this.getTemplateDetail();
			this.recordState();
		},
		checkPaperForm() {
			// 检查论文类型是否选择
			if (!this.paperForm.id) {
				return {
					valid: false,
					message: "请选择论文类型",
				};
			}

			// 检查表单数据是否存在
			if (!this.paperForm.formData || this.paperForm.formData.length === 0) {
				return {
					valid: false,
					message: "表单数据不完整，请刷新页面重试",
				};
			}

			// 检查每个必填字段是否已填写
			for (const field of this.paperForm.formData) {
				// 跳过profession字段的检查
				if (field.field === "profession" || field.field === "description") {
					continue;
				}
				// 检查必填字段是否有值
				if (!field.value || field.value.trim() === "") {
					// 根据字段名称返回相应的错误信息
					let fieldName = "";
					switch (field.field) {
						case "title":
							fieldName = "论文标题";
							break;
						case "education":
							fieldName = "学历";
							break;
						case "config":
							fieldName = "篇幅长度";
							break;
						default:
							fieldName = field.field;
					}
					return {
						valid: false,
						message: `请填写${fieldName}`,
					};
				}

				// 检查标题长度
				if (field.field === "title" && field.value.trim().length < 4) {
					return {
						valid: false,
						message: "论文标题不能少于4个字",
					};
				}
			}

			// 所有检查通过
			return {
				valid: true,
				message: "验证通过",
			};
		},
		async getPaperDetail(id: number) {
			try {
				const res = await fetchPaperDetail<PaperDetail>({ id });
				this.paperDetail = res;
			} catch (error) {
				console.log(error);
			}
		},
		async generatePaper({ id }: { id: number }) {
			try {
				if (!this.paperDetail) {
					throw new Error("论文详情不存在");
				}
				await fetchGeneratePaperCore2<{ id: number }>({
					id,
					serviceIds: this.paperDetail
						.subjoins!.map((item) => (item.isSelect ? item.id : null))
						.filter(Boolean) as number[],
				});
				router.push({
					name: "Paper2History",
				});
			} catch (error) {
				console.log(error);
			}
		},
		async generateHistoryPaper({ id }: { id: number }) {
			try {
				await fetchGeneratePaperCore2<{ id: number }>({
					id,
				});
				await this.getPaperHistory();
			} catch (error) {
			}
		},
	},
});

export function usePaperStoreWithOut() {
	return usePaperStore(store);
}
