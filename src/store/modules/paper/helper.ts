import { ss } from "@/utils/storage";
import {
	PaperConfig,
	PaperForm,
	PaperHistory,
	PaperStatistics,
	TemplateDetailData,
} from "@/views/paper/types";
import { PaperDetail } from "@/views/plagiarismCheck/types";

const LOCAL_NAME = "paperSetting";
export interface PaperState {
	// 分页
	page: number;
	// 分页大小
	pageSize?: number;
	// 历史记录状态
	pageHistoryStatus: number;
	// 历史记录总数
	paperHistoryCount: number;
	// 历史记录
	paperHistory: PaperHistory[];
	paperHistoryLoading: boolean;
	// 特色列表
	featureList: PaperConfig[];
	// 创建类型
	createType: string;
	// 提交表单信息
	paperForm: PaperForm;
	// 预设表单信息
	preForm: TemplateDetailData;
	// 禁止生成
	disableGenerate: boolean;
	latestMessage: string[];
	createPaperLoading: boolean;
	paperDetail: PaperDetail;
	paperStatistics: PaperStatistics;
}
export function defaultSetting(): PaperState {
	return {
		latestMessage: [],
		disableGenerate: false,
		featureList: [],
		createType: "paper",
		paperForm: { id: 0, formData: [] } as PaperForm,
		preForm: {} as TemplateDetailData,
		paperHistory: [],
		paperHistoryCount: 0,
		paperHistoryLoading: false,
		pageSize: 5,
		page: 1,
		pageHistoryStatus: -1,
		createPaperLoading: false,
		paperDetail: {},
		paperStatistics: { total: 0, list: [] },
	};
}

export function getLocalSetting(): PaperState {
	const localSetting: PaperState | undefined = ss.get(LOCAL_NAME);
	return { ...defaultSetting(), ...localSetting };
}

export function setLocalSetting(setting: PaperState): void {
	ss.set(LOCAL_NAME, setting);
}
