<template>
	<Layout extend>
		<div class="h-screen"></div>
	</Layout>
</template>

<script setup lang="ts">
import Layout from '@/layouts/Layout.vue';
import { onMounted } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute()

onMounted(() => {
	window.$aiwork.openRecharge?.(
		{
			type: route.params.type as ('ppt') || 'ppt',
		}
	).then(() => {
		window.postMessage({
			type: 'aiwork-pay-succeess'
		})
		window.close()
	})
})

</script>
