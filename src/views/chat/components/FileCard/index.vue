<script setup lang="ts">
import getFileType from '@/utils/fileType';
import { NEllipsis, NTooltip, NImage } from 'naive-ui'
import { ref, watch } from 'vue';
// docx, .md, .pdf, .excel
import ExcelActiveImg from '@/assets/images/E_1.png';
import ExcelImg from '@/assets/images/E.png';
import PdfActiveImg from '@/assets/images/pdf_1.png';
import PdfImg from '@/assets/images/pdf.png';
import WordActiveImg from '@/assets/images/word_1.png';
import WordImg from '@/assets/images/word.png';
import MarkdownActiveImg from '@/assets/images/md_1.png';
import MarkdownImg from '@/assets/images/md.png';
import PPTImg from '@/assets/images/PPT.png';
import PPTActiveImg from '@/assets/images/PPT_1.png';

import ForwardImg from '@/assets/images/forward.png';
import { onMounted } from 'vue';

interface Props {
    index: number;
    url: string;
    file: File;
    type: string;
    fileType: string;
    progress: number;
    name: string
    chatting: boolean;
    wordCount: number;
}
interface Emit {
    (ev: 'close', data: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emit>()
// 判断是docx, .md, .pdf, .excel
const fileImg = ref('')
const fileSize = ref('')

// 状态有 uploading uploaded chat
const status = ref('uploading')

watch(() => props.progress, (newValue) => {
    if (newValue === 100) {
        status.value = 'uploaded'
    }
})
watch(() => props.chatting, () => {
    if (props.chatting) {
        status.value = 'chat'
    }
})
const handleClose = () => {
    emit('close', props)
}

const handleFileType = async () => {
    if (props.file) {
        const { ext: type } = await getFileType(props.file)
        if (type === 'docx') {
            if (props.progress === 100) {
                fileImg.value = WordActiveImg;
            } else {
                fileImg.value = WordImg;
            }
        } else if (type === 'pdf') {
            if (props.progress === 100) {
                fileImg.value = PdfActiveImg;
            } else {
                fileImg.value = PdfImg;
            }
        } else if (type === 'excel') {
            if (props.progress === 100) {
                fileImg.value = ExcelActiveImg;
            } else {
                fileImg.value = ExcelImg;
            }
        } else if (type === 'pptx') { 
            if (props.progress === 100) {
                fileImg.value = PPTActiveImg;
            } else {
                fileImg.value = PPTImg;
            }
        } else {
            if (props.progress === 100) {
                fileImg.value = MarkdownActiveImg;
            } else {
                fileImg.value = MarkdownImg;
            }
        }
    } else {
        fileImg.value = PdfImg
        status.value = 'chat'
    }
}
const handleFileSize = () => {
    if (props.file) {
        const size = props.file?.size;
        if (size < 1024) {
            fileSize.value = `${size}B`
        } else if (size < 1024 * 1024) {
            fileSize.value = `${(size / 1024).toFixed(2)}KB`
        } else {
            fileSize.value = `${(size / 1024 / 1024).toFixed(2)}MB`
        }
    } else {
        fileSize.value = '5MB'
    }
}
onMounted(() => {
    handleFileType()
    handleFileSize()
})

</script>

<template>
    <div class="relative"
        :class="status === 'chat' ? 'char-card-container' : status === 'uploading' ? 'uploading-card-container' : 'uploaded-card-container'">
        <div>
            <IconCloseOne v-if="status !== 'chat'"
                class="del w-[12px] h-[12px] text-[12px] before:align-super absolute right-[2px] top-[2px] cursor-pointer"
                @click="handleClose"/>
            <div class="flex flex-row gap-x-[7px] width-[full] items-center">
                <NImage v-if="status === 'chat'" :src="ForwardImg" width="11px" height="8px"
                    class="min-w-[11px] h-[8px] block z-[2] ml-[12px]" preview-disabled />
                <NImage class="min-w-[26px] h-[26px] block z-[2]" :src="fileImg" width="26px" height="26px"
                    object-fit="contain" preview-disabled />
                <div class="flex flex-col min-w-[50%] flex-1 z-[2]" v-if="status !== 'chat'">
                    <NEllipsis class="text-[12px] leading-[16px] text-[#3D3D3D]">{{ name }}</NEllipsis>
                    <div v-if="status === 'uploading'" class="text-[8px] leading-[11px] text-[#A5A5AF] mt-[2px]">
                        <span>{{ progress }}%</span>
                        <span class=" ml-[6px]">{{ fileSize }}</span>
                    </div>
                    <div v-else class="text-[8px] leading-[11px] text-[#A5A5AF] mt-[2px]">
                        <span class="text-[12px] text-[#A5A5AF]">{{ fileSize }}</span>
                        <!-- <span class="text-[12px] text-[#A5A5AF] ml-[6px]">{{ wordCount }}字</span> -->
                    </div>
                </div>
                <div v-else class="flex flex-row mr-[15px] flex-1">
                    <NEllipsis class="text-[12px] leading-[16px] text-[#3D3D3D] flex-1">{{ name }}</NEllipsis>
                    <NTooltip trigger="hover">
                        <template #trigger>
                            <IconCloseOne @click="handleClose"/>
                        </template>
                        不再围绕这个文档问答
                    </NTooltip>
                </div>
            </div>
        </div>
        <div v-if="status === 'uploading'" class=" file-progress-container h-full absolute top-0 left-0 bg-[#E8E9ED]"
            :style="{ '--n-width': progress + '%' }"></div>
    </div>
</template>

<style lang="less">
.upload-card-container {
    border-radius: 8px;
    width: 173px;
    height: 50px;
    background-color: #F5F5F5;

}

.file-progress-container {
    width: var(--n-width);
}

.uploading-card-container {
    &:extend(.upload-card-container);

    >div {
        height: inherit;
        border-radius: 8px;
        padding: 8px 9px;
        width: 100%;

        .del {
            display: none;
        }

        &:hover {
            .del {
                display: block;
            }
        }
    }
}

.uploaded-card-container {
    &:extend(.upload-card-container);
    background-color: #EEF4FF;

    >div {
        padding: 8px 9px;
        width: 100%;

        .del {
            display: none;
        }

        &:hover {
            .del {
                display: block;
            }
        }
    }
}

.char-card-container {
    background-color: #EEF4FF;
    width: 100%;
    height: 37px;
    display: flex;
    align-items: center;
    border-radius: 8px;

    >div {
        width: 100%;
    }
}
</style>