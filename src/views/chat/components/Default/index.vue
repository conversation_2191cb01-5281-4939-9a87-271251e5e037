<script lang="ts" setup>
import {  NImage } from 'naive-ui'
import banner from '@/assets/<EMAIL>'
import jiqiren from '@/assets/jiqiren.png'

</script>
<template>
	<div class="w-full">
		<div class="relative w-full h-[160px]" id="banner">
			<NImage :src="banner" class="w-full h-[160px] mx-auto absolute top-0 right-0 left-0 bottom-0" style="width: 100%" preview-disabled />
			<div class=" absolute top-[30px] left-3 text-[22px]">
				<div class="w-[72px] h-[22px] bg-[#2BAB83] text-[14px] text-[#fff] text-center" style="border-radius: 0px 20px 20px 20px;">Hi~您好</div>
				<div class="pt-[10px]">
					我是您的 <br> AI智能办公助手
				</div>
			</div>
		</div>
		<div class="mt-[30px]">
			<div class="flex items-center">
				<NImage :src="jiqiren" class="w-[45px] h-[30px]" />
				<span class="text-[18px] text-[#333333] pl-[5px]">我可以帮你做</span>
			</div>

			<ul class="grid grid-cols-1 gap-y-2 mt-[10px]">
				<li class="w-full h-[65px] bg-white rounded-[8px] px-[18px] py-[24px]">
						<span class="text-[16px] text-[#333]">文章写作：</span>
						<span class="text-[13px] text-[#808080]">输入标题生成高质量原创文章</span>
				</li>
				<li class="w-full h-[65px] bg-white rounded-[8px] px-[18px] py-[24px]">
						<span class="text-[16px] text-[#333]">论文工具：</span>
						<span class="text-[13px] text-[#808080]">AI自动生成论文，3分钟1万字</span>
				</li>
				<li class="w-full h-[65px] bg-white rounded-[8px] px-[18px] py-[24px]">
						<span class="text-[16px] text-[#333]">爆款文案：</span>
						<span class="text-[13px] text-[#808080]">轻松撰写爆款文案标题，种草笔记</span>
				</li>
				<li class="w-full h-[65px] bg-white rounded-[8px] px-[18px] py-[24px]">
						<span class="text-[16px] text-[#333]">短视频脚本：</span>
						<span class="text-[13px] text-[#808080]">批量产出高质量脚本，引爆流量</span>
				</li>
			</ul>
		</div>
	</div>
</template>
<style>
#banner .n-image img {
	width: 100%
}
</style>
