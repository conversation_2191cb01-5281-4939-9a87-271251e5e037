<script lang="ts" setup>
import { ShortCuts } from '../../types';
import { NImage } from 'naive-ui'
import { replaceText } from '@/plugins/directive';
interface Props {
    shortCut: ShortCuts
}
interface Emit {
    (ev: 'select', shortCut: ShortCuts): void
}
defineProps<Props>()
const emit = defineEmits<Emit>()
</script>

<template>
    <div @click="() => emit('select', shortCut)"
        class=" w-[calc(25%-12px)] h-[104px] rounded-[8px] bg-[#FFFFFF] px-[20px] flex flex-col gap-y-[16px] justify-center card-container sm:w-[calc(50%-12px)]">
        <div class=" flex flex-row gap-x-[8px] items-center">
            <NImage :src="shortCut.icon" class=" w-[20px] h-[20px]" preview-disabled />
            <span class=" text-[14px] text-[#3D3D3D] font-bold">{{ replaceText(shortCut.label) }}</span>
        </div>
        <div class="divider"></div>
        <div class=" text-[#676767] text-[14px] text-center">{{ replaceText(shortCut.desc) }}</div>
    </div>

</template>

<style lang="less" scoped>
.card-container {
    position: relative;
    &:hover {
        cursor: pointer;
        border-radius: 8px;
        &::after {
            content: "";
                position: absolute;
                left: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                border-radius: 10px;
                z-index: 2;
                padding: 2px;
                background: linear-gradient(270deg, #34B8FF 0%, #327DFF 100%);
                -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
                -webkit-mask-composite: xor;
                mask-composite: exclude;
                pointer-events: none;
        }
        box-shadow: 0px 0px 8px 0px #E7EEFF;
    }
}

.divider {
    width: 100%;
    height: 1px;
    background-color: #DEE8FF;
}
</style>
