<script lang="ts" setup>
import { computed, nextTick,ref } from 'vue'
import { NImage } from 'naive-ui'
import { useRoute } from 'vue-router'
import { HoverButton, SvgIcon,Member } from '@/components/common'
import BindAccount from '@/views/components/BindAccount.vue'
import Permission from '@/views/components/Permission.vue'
import { useChatStore,useAppStore } from '@/store'
import payImg from '@/assets/vp1.png'
interface Props {
  usingContext: boolean
}

interface Emit {
  (ev: 'export'): void
  (ev: 'handleMembers'): void
  (ev: 'toggleUsingContext'): void
}
const appStore = useAppStore()
defineProps<Props>()

const emit = defineEmits<Emit>()
const showMember = ref(false)
const needBind = ref(false)
const needPermission = ref(false)
const chatStore = useChatStore()
const collapsed = computed(() => appStore.siderCollapsed)
const route = useRoute()
const { uuid } = route.params as { uuid: string }
const currentChatHistory = computed(() => chatStore.getHistory(+uuid))

const toggleMember = () => {
	showMember.value = true
}
const changeFn = async () => {
	needBind.value = true
}
const handleLogin = () => {
	needPermission.value = true
}

function onScrollToTop() {
  const scrollRef = document.querySelector('#scrollRef')
  if (scrollRef)
    nextTick(() => scrollRef.scrollTop = 0)
}

// function handleExport() {
//   emit('export')
// }

function toggleUsingContext() {
  emit('toggleUsingContext')
}
function handleUpdateCollapsed() {
  appStore.setSiderCollapsed(!collapsed.value)
}
</script>

<template>
  <header
    class="fixed top-0 sm:relative ipad:relative z-9 w-full flex-none text-sm leading-6 backdrop-blur duration-500 bg-[#fff] transition-colors shadow-shadow1 sm:-mx-4 sm:w-[100vw] ipad:-mx-4 ipad:w-[100vw]"
  >
    <div class="relative flex items-center justify-between min-w-0 overflow-hidden h-14">
			<button type="button" class="-my-1 -mr-1 ml-2 flex h-8 w-8 items-center justify-center lg:hidden"  @click="handleUpdateCollapsed">
					<span class="sr-only">Open navigation</span>
					<svg viewBox="0 0 24 24" class="h-6 w-6 stroke-slate-900">
						<path d="M3.75 12h16.5M3.75 6.75h16.5M3.75 17.25h16.5" fill="none" strokeWidth="1.5" strokeLinecap="round">
						</path>
					</svg>
				</button>
      <h1
        class="flex-1 px-4 pr-6 overflow-hidden cursor-pointer select-none text-ellipsis whitespace-nowrap"
        @dblclick="onScrollToTop"
      >
        {{ currentChatHistory?.title || '' }}
      </h1>
      <div class="flex items-center space-x-2">
				<HoverButton v-if="false" @click="toggleMember" class="sm:block ipad:block mr-2">
					<NImage :src="payImg" width="30" height="30" preview-disabled />
				</HoverButton>
        <HoverButton @click="toggleUsingContext" style="display:none">
          <span class="text-xl" :class="{ 'text-[#4b9e5f]': usingContext, 'text-[#a8071a]': !usingContext }">
            <SvgIcon icon="ri:chat-history-line" />
          </span>
        </HoverButton>
        <!-- <HoverButton @click="handleExport" v-if="!currentChatHistory?.type">
          <span class="text-xl text-[#4f555e] dark:text-white">
            <SvgIcon icon="ri:download-2-line" />
          </span>
        </HoverButton> -->

      </div>
    </div>
  </header>
	<Member v-if="showMember" v-model:visible="showMember" @change-fn="changeFn" />
	<BindAccount v-if="needBind" v-model:visible="needBind" @login="handleLogin" />
	<Permission v-if="needPermission" v-model:visible="needPermission" />
</template>
