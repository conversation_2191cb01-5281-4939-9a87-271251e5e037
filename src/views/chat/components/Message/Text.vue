<script lang="ts" setup>
import { computed, ref, watch, nextTick, inject } from 'vue'
import MarkdownIt from 'markdown-it'
import mdKatex from '@traptitech/markdown-it-katex'
import mila from 'markdown-it-link-attributes'
import hljs from 'highlight.js'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { t } from '@/locales'
import { ScrollText } from '@/components/scrollText'
import ChatLoadingSvg from "@/assets/aiwork/svg/chat-loading.svg"
import DownPanel from '../downPanel.vue'
import { NButton, NEllipsis } from 'naive-ui'
import ArrowSvg from "@/assets/aiwork/svg/arrow.svg"
import { useTimeout } from 'vue-hooks-plus'
import { ThinkType } from '../../types'

interface Props {
	inversion?: boolean
	error?: boolean
	text?: string
	fileObj?: any
	loading?: boolean
	asRawText?: boolean
	searchInfo?: any[]
	// 是否展示深度思考
	showThink?: boolean
	// 思考内容
	thinkInfo?: string;
	thinkType?: ThinkType
	messageList?: any[]
	tips?: string
}

const props = defineProps<Props>()

const chatStatus = inject<{
	uuid: string;
	chatIndex: number;
	stepText: string[]
}>('chatStatus')

const getIco = (url: string) => {
	return `${(new URL(url)).origin}/favicon.ico`
}

const { isMobile } = useBasicLayout()

const textRef = ref<HTMLElement>()
const cursorPosition = ref({ x: 0, y: 0 })


const mdi = new MarkdownIt({
	linkify: true,
	highlight(code, language) {
		const validLang = !!(language && hljs.getLanguage(language))
		if (validLang) {
			const lang = language || ''
			return highlightBlock(hljs.highlight(code, { language: lang }).value, lang)
		}
		return highlightBlock(hljs.highlightAuto(code).value, '')
	},
})

mdi.use(mila, { attrs: { target: '_blank', rel: 'noopener' } })
mdi.use(mdKatex, { blockClass: 'katexmath-block rounded-md p-[10px]', errorColor: ' #cc0000' })
const countdown = ref(0)
// 当props.thinkType === ThinkType.Thinking时 启动一个定时器 每秒执行一次 直到props.thinkType === ThinkType.Answer


const wrapClass = computed(() => {
	return [
		'text-wrap',
		props.inversion ? 'min-w-[20px]' : '',
		'max-w-[726px]',
		'rounded-md',
		// 'bg-[#fff]',
		// isMobile.value ? 'p-2' : 'px-3 py-2',
		props.inversion ? 'bg-[#0E69FF]' : 'bg-[#fff]',
		props.inversion ? 'text-[#fff]' : 'text-[#3d3d3d]',
		props.inversion ? 'dark:bg-[#a1dc95]' : 'dark:bg-[#1e1e20]',
		props.inversion ? 'message-request' : 'message-reply',
		{ 'text-red-500': props.error },
	]
})

const text = computed(() => {
	const value = props.text || ''
	if (!props.asRawText)
		return mdi.render(value)
	return value
})
const thinkText = computed(() => {
	const value = props.thinkInfo || ''
	return mdi.render(value)
})

function highlightBlock(str: string, lang?: string) {
	return `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy">${t('chat.copyCode')}</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`
}

const open = (url: string, target: string) => {
	window.open(url, target)
}

/**
 * 遍历最后一个子元素，找到最后的文本节点
 * @param {HTMLElement} container - 容器元素
 * @returns {Text} - 最后的文本节点
 */
function getLastTextNode(container) {
	let lastChild = container.lastChild;

	// 遍历直到找到文本节点
	while (lastChild) {
		if (lastChild.nodeType === Node.TEXT_NODE && lastChild.textContent.trim() !== '') {
			return lastChild;
		} else if (lastChild.nodeType === Node.ELEMENT_NODE) {
			let textNode = getLastTextNode(lastChild);
			if (textNode) {
				return textNode;
			}
		}
		lastChild = lastChild.previousSibling;
	}
	return lastChild;
}

/**
 * 获取文本节点最后一个字符的位置
 * @param {Text} textNode - 文本节点
 * @returns {Object} - x 和 y 坐标
 */
function getLastCharacterPosition(textNode, box) {
	if (!textNode || textNode.nodeType !== Node.TEXT_NODE) {
		throw new Error('Invalid text node');
	}

	const range = document.createRange();
	range.setStart(textNode, textNode.length - 1);
	range.setEnd(textNode, textNode.length);

	const rect = range.getBoundingClientRect();
	const textContainer = box.getBoundingClientRect(); // 获取文本容器的边界位置信息
	return { x: rect.right - textContainer.left, y: rect.bottom - textContainer.bottom };
}

watch(() => props.text, (v) => {
	nextTick(() => {
		if (!textRef.value) return
		const $wrapper = textRef.value.querySelector('.content-wrapper')
		const $ele = textRef.value.querySelector('.content-body')
		if (!$ele) return
		const lastTextNode = getLastTextNode($ele)
		if (!lastTextNode) return
		cursorPosition.value = getLastCharacterPosition(lastTextNode, $wrapper)
	})
})

defineExpose({ textRef })

const handleBecomeMember = () => {
	window.$aiwork.openRecharge({ type: 'ai' })
}
console.log('thinkText:', thinkText)
</script>

<template>
	<div class="p-[20px] sm:p-3" :class="wrapClass" style="box-shadow: 0px 4px 15px 0px rgba(222, 226, 238, 0.6);">
		<!-- 顶部信息 -->
		<div v-if="!inversion">
			<!-- <div v-if="loading && !chatStatus?.stepText?.length" class="py-[10px] mb-[10px]">
				<span class="loader" style="--loader-size: 6px; --loader-gap: 12px;"></span>
			</div>
			<div v-else-if="loading && chatStatus?.stepText?.length" class="mb-[10px] flex items-center ">
				<ChatLoadingSvg class="w-[20px] h-[20px] mr-[6px] text-[#666]" />
				<ScrollText :current="chatStatus.stepText.length - 1" :autoplay="false" :interval="0"
					class="h-[20px] flex-1 text-nowrap" :delayUpdate="100">
					<div v-for="(item, index) in chatStatus.stepText" :data-key="index" :key="item">{{ item }}</div>
				</ScrollText>
			</div> -->
			<!-- <div class="mb-[10px] flex items-center ">
				<ChatLoadingSvg class="w-[20px] h-[20px] mr-[6px] text-[#666]" />
				<ScrollText :current="chatStatus.stepText.length - 1" :autoplay="false" :interval="0"
					class="h-[20px] flex-1 text-nowrap" :delayUpdate="100">
					<div v-for="(item, index) in chatStatus.stepText" :data-key="index" :key="item">{{ item }}</div>
				</ScrollText>
			</div> -->
			<div v-if="searchInfo?.length" class="">
				<DownPanel :show="false">
					<template #trigger="slotProps">
						<NButton icon-placement="right" secondary strong class="rounded-[6px] bg-[#2e33380d]"
							style="--n-border-radius: 6px;">
							<template #icon>
								<ArrowSvg class="w-[14px] h-[14px] transition-all duration-300"
									:class="{ 'rotate-180': slotProps.show }" />
							</template>
							<div class="flex items-center gap-[4px]">
								<IconSearch class="w-[14px] h-[14px] relative top-[1px]" />
								共找到{{ searchInfo.length }}篇资料作为参考
							</div>
						</NButton>
					</template>
					<div class="p-[12px] grid grid-cols-4 gap-[12px] bg-[#2e33380d] rounded-[10px]">
						<div v-for="(item, index) in searchInfo" :key="index"
							class="p-[8px] bg-white rounded-[10px] min-w-0 transition-all duration-300 cursor-pointer hover:shadow-dropdown"
							@click="open(item.url, '_blank')">
							<NEllipsis :line-clamp="2" style="word-break: break-all;">{{ item.title }}</NEllipsis>
							<div class="mt-[12px] text-[12px] text-gray-400 flex gap-[4px] items-center">
								<img :src="getIco(item.url)" class="w-[12px] h-[12px]" />
								<NEllipsis class="flex-1" :tooltip="false">{{ item.url }}</NEllipsis>
							</div>
						</div>
					</div>
				</DownPanel>
			</div>
			<!-- 展示思考过程 -->
			<div v-if="showThink">
				<DownPanel :show="true">
					<template #trigger="slotProps">
						<NButton icon-placement="right" secondary strong class="rounded-[6px] bg-[#2e33380d]"
							style="--n-border-radius: 6px;">
							<template #icon>
								<ArrowSvg class="w-[14px] h-[14px] transition-all duration-300"
									:class="{ 'rotate-180': slotProps.show }" />
							</template>
							<div class="flex items-center gap-[4px]">
								<IconThink class="w-[14px] h-[14px] relative top-[1px]" />
								<span
									v-if="thinkType === ThinkType.Thinking || thinkType === ThinkType.None">思考中...</span>
								<span v-else-if="thinkType === ThinkType.Answer">已深度思考</span>
							</div>
						</NButton>
					</template>
					<div class="px-[12px] border-l-4 border-[#e5e5e5] w-full markdown-body content-body !text-[#8b8b8b]"
						v-html="thinkText"></div>
				</DownPanel>
			</div>
			<div v-if="loading && !chatStatus?.stepText?.length" class="py-[10px] mb-[10px]">
				<span class="loader" style="--loader-size: 6px; --loader-gap: 12px;"></span>
			</div>
			<div v-else-if="loading && chatStatus?.stepText?.length" class="mb-[10px] flex items-center ">
				<ChatLoadingSvg class="w-[20px] h-[20px] mr-[6px] text-[#666]" />
				<ScrollText :current="chatStatus.stepText.length - 1" :autoplay="false" :interval="0"
					class="h-[20px] flex-1 text-nowrap" :delayUpdate="100">
					<div v-for="(item, index) in chatStatus.stepText" :data-key="index" :key="item">{{ item }}</div>
				</ScrollText>
			</div>
		</div>
		<div ref="textRef" class="leading-relaxed break-words" style="width: fit-content; max-width: 100%;">
			<div v-if="!inversion" class="flex items-end relative content-wrapper min-h-[1em]">
				<div v-if="!asRawText" class="w-full markdown-body content-body" v-html="text" />
				<div v-else class="w-full whitespace-pre-wrap content-body" v-text="text" />
				<div v-if="tips" class="tips-container" @click="handleBecomeMember">成为会员，无限问答 →</div>
				<span v-if="loading" class="dark:text-white w-[4px] h-[16px] block animate-blink absolute"
					:style="{ left: cursorPosition.x + 4 + 'px', bottom: '3px' }" />
				<!-- <span class="dark:text-white w-[4px] h-[16px] block animate-blink absolute"
					:style="{ left: cursorPosition.x + 4 + 'px', bottom: '3px' }" /> -->
			</div>
			<div v-else class="whitespace-pre-wrap" v-text="text" />
		</div>
	</div>
</template>

<style lang="less">
@import url(./style.less);
</style>
