<script setup lang='ts'>
import { computed, inject, ref, watch } from 'vue'
import { NImage, NButton, NEllipsis } from 'naive-ui'
import AvatarComponent from './Avatar.vue'
import TextComponent from './Text.vue'
import { SvgIcon } from '@/components/common'
import { copyText } from '@/utils/format'
import { useAuthStore } from '@/store'
import { useIconRender } from '@/hooks/useIconRender'
import { t } from '@/locales'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import Permission from '../../../components/Permission.vue'
import { Icon } from '@iconify/vue'
import { fetchQuestions } from '../../api'
import ArrowSvg from '@/assets/aiwork/svg/arrow.svg'
import ExcelActiveImg from '@/assets/images/E_1.png';
import ExcelImg from '@/assets/images/E.png';
import PdfActiveImg from '@/assets/images/pdf_1.png';
import PdfImg from '@/assets/images/pdf.png';
import WordActiveImg from '@/assets/images/word_1.png';
import WordImg from '@/assets/images/word.png';
import MarkdownActiveImg from '@/assets/images/md_1.png';
import MarkdownImg from '@/assets/images/md.png';
import PPTImg from '@/assets/images/PPT.png';
import { ThinkType } from '../../types'

interface Props {
  dateTime?: string
  text?: string
  fileObj?: any
  inversion?: boolean
  error?: boolean
  loading?: boolean,
  isLimit?: boolean,
  index: number,
  isCompute?: boolean
  searchInfo?: any[]
  showThink?: boolean
  thinkInfo?: string
  thinkType?: ThinkType
  messageList?: string[]
  questionLoading?: boolean
  questionList?: string[]
  showQuestion?: boolean
  tips?: Chat.Tips
}

interface Emit {
  (ev: 'regenerate'): void
  (ev: 'delete'): void
  (ev: 'updateVip'): void
}
const userStore = useAuthStore()
const user = ref(userStore.getUser())
const needPermission = ref(false)
const props = defineProps<Props>()

const emit = defineEmits<Emit>()

const { isMobile } = useBasicLayout()

const { iconRender } = useIconRender()

const textRef = ref<HTMLElement>()

const asRawText = ref(props.inversion)

const messageRef = ref<HTMLElement>()

const addPrompt: any = inject('addPrompt')


const fileImg = computed(() => {
  if (props.fileObj) {
    const { type } = props.fileObj
    switch (type) {
      case 'docx':
        return WordImg
      case 'pdf':
        return PdfImg
      case 'excel':
        return ExcelImg
      case 'pptx':
        return PPTImg
      case 'md':
        return MarkdownImg
    }
  }
  return ''
})
const fileSize = computed(() => {
  if (props.fileObj) {
    const size = props.fileObj?.size;
    if (size < 1024) {
      return `${size}B`
    } else if (size < 1024 * 1024) {
      return `${(size / 1024).toFixed(2)}KB`
    } else {
      return `${(size / 1024 / 1024).toFixed(2)}MB`
    }
  }
  return ''
})

const islimit = computed(() => {
  return props.isLimit
})

const options = computed(() => {
  const common = [
    {
      label: t('chat.copy'),
      key: 'copyText',
      icon: iconRender({ icon: 'ri:file-copy-2-line' }),
    },
    {
      label: t('common.delete'),
      key: 'delete',
      icon: iconRender({ icon: 'ri:delete-bin-line' }),
    },
  ]

  if (!props.inversion) {
    common.unshift({
      label: asRawText.value ? t('chat.preview') : t('chat.showRawText'),
      key: 'toggleRenderType',
      icon: iconRender({ icon: asRawText.value ? 'ic:outline-code-off' : 'ic:outline-code' }),
    })
  }

  return common
})

function handleSelect(key: 'copyText' | 'delete' | 'toggleRenderType') {
  switch (key) {
    case 'copyText':
      copyText({ text: props.text || '' })
      return
    case 'toggleRenderType':
      asRawText.value = !asRawText.value
      return
    case 'delete':
      emit('delete')
  }
}

function handleRegenerate() {
  messageRef.value?.scrollIntoView()
  emit('regenerate')
}

function handleUpgradation() {
  emit('updateVip')
}
function handleLogin() {
  needPermission.value = true
}

// watch(() => props.loading, (v, prev) => {
// 	if(!v && prev && !props.error && props.text?.length) handleQuestion()
// }, { deep: true })

// const handleQuestion = () => {
// 	questionsLoading.value = true
// 	fetchQuestions<string[]>({prompt: props.text}).then(data => {
// 		questionsLoading.value = false
// 		questions.value = data
// 	})
// }
</script>

<template>
  <div ref="messageRef" class="flex w-full mb-6" :class="[{ 'flex-row-reverse': inversion }]">
    <div class="flex items-center justify-center flex-shrink-0 h-8 rounded-full basis-8"
      :class="[inversion ? 'ml-2' : 'mr-2']" style="box-shadow: 0px 4px 10px 0px #CED5E5;">
      <AvatarComponent :image="inversion" />
    </div>
    <div class="text-sm " :class="[inversion ? 'items-end' : 'items-start']">
      <p class="text-xs text-[#b4bbc4]" :class="[inversion ? 'text-right' : 'text-left']">
        {{ dateTime }}
      </p>
      <!-- <div
        class="flex items-end gap-1 mt-2"
        :class="[inversion ? 'flex-row-reverse' : 'flex-row']"
      > -->
      <div class="flex flex-col gap-1 mt-2" :class="[inversion ? 'items-end' : 'items-start']">
        <div v-if="fileObj">
          <div class="file-container">
            <NImage class="min-w-[26px] h-[26px] block z-[2]" :src="fileImg" width="26px" height="26px"
              object-fit="cover" preview-disabled />
            <div class="right">
              <NEllipsis class="title">{{ fileObj?.name }}</NEllipsis>
              <div class="desc">{{ fileSize }}</div>
            </div>
          </div>
          <TextComponent ref="textRef" :inversion="inversion" :error="error" :text="text" :loading="loading"
            :show-think="showThink" :think-type="thinkType" :think-info="thinkInfo" :as-raw-text="asRawText"
            :searchInfo="searchInfo" :messageList="messageList" :tips="tips" />
        </div>
        <TextComponent v-else ref="textRef" :inversion="inversion" :error="error" :text="text" :loading="loading"
          :show-think="showThink" :think-type="thinkType" :think-info="thinkInfo" :as-raw-text="asRawText"
          :searchInfo="searchInfo" :messageList="messageList" :tips="tips" />

        <div class="w-full flex items-center gap-[20px]" :class="[inversion ? 'justify-start' : 'justify-end']">
          <NButton v-if="!fileObj" text quaternary type="info" size="tiny" color="#818181"
            @click="copyText({ text: props.text || '' })">
            <template #icon>
              <Icon icon="ri:file-copy-2-line"></Icon>
            </template>
            复制
          </NButton>
          <!-- <NButton v-if="!inversion" text quaternary type="info" size="tiny" color="#818181" @click="handleRegenerate">
            <template #icon>
              <SvgIcon icon="ri:restart-line" />
            </template>
            再试一次
          </NButton> -->
          <NButton v-if="!inversion" text quaternary type="info" size="tiny" color="#818181" @click="emit('delete')">
            <template #icon>
              <Icon icon="ri:delete-bin-line"></Icon>
            </template>
            删除
          </NButton>
        </div>

        <div v-if="showQuestion" class="w-full flex justify-between items-start">
          <NButton v-if="questionLoading" size="small" :bordered="false" tertiary round icon-placement="right"
            style="--n-color: #EDEEF3;">
            <span class="loader" style="--loader-size: 6px; --loader-gap: 12px;"></span>
          </NButton>
          <div v-if="!questionLoading && questionList?.length" class="mt-[0px] flex flex-col gap-[10px] items-start">
            <NButton class="!cursor-pointer !px-[20px]" size="small" :bordered="false" tertiary round
              v-for="(item, index) in questionList" :key="item" @click="addPrompt(item)" icon-placement="right"
              style="/**--n-color: #EDEEF3;*/">
              {{ item }}
              <template #icon>
                <ArrowSvg class="w-[12px] h-[12px] transform rotate-[-90deg]" />
              </template>
            </NButton>
          </div>
        </div>



        <!-- 原工具栏 -->
        <!-- <div class="flex flex-col">
          <button
            v-if="!inversion"
            class="mb-2 transition text-neutral-300 hover:text-neutral-800 dark:hover:text-neutral-300"
            @click="handleRegenerate"
          >
            <SvgIcon icon="ri:restart-line" />
          </button>
          <NDropdown
            :trigger="isMobile ? 'click' : 'hover'"
            :placement="!inversion ? 'right' : 'left'"
            :options="options"
            @select="handleSelect"
          >
            <button class="transition text-neutral-300 hover:text-neutral-800 dark:hover:text-neutral-200">
              <SvgIcon icon="ri:more-2-fill" />
            </button>
          </NDropdown>
        </div> -->
      </div>
      <!-- <div class="pt-[5px]" v-if="user && user.type === 'temp' && index>1 && !inversion && !islimit && isCompute">
				为了您的账户安全,请登录使用!
				<NButton type="primary" size="small" @click="handleLogin">立即登录</NButton>
			</div> -->
    </div>
    <!-- <div v-if="islimit" class="flex items-start ml-[20px] mt-7">
			<NButton type="primary" size="small" @click="handleUpgradation">升级会员</NButton>
		</div> -->
  </div>
  <Permission v-if="needPermission" v-model:visible="needPermission" />
</template>

<style lang="less">
.file-container {
  width: 221px;
  height: 64px;
  border-radius: 8px;
  box-shadow: 0px 4px 10px 0px #E3EBF3;
  padding: 0 13px;
  background-color: #ffffff;
  display: flex;
  flex-direction: row;
  align-items: center;
  column-gap: 5px;
  margin-bottom: 13px;

  .right {
    flex: 1;
    overflow: hidden;
    display: flex;
    row-gap: 6px;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;

    .title {
      font-size: 14px;
      line-height: 18px;
      color: #3D3D3D;
    }

    .desc {
      font-size: 12px;
      line-height: 16px;
      color: #A5A5AF;
    }
  }
}
</style>