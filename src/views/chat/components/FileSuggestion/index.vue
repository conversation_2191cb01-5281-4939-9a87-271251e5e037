<script setup lang="ts">
import ArrowImg from '@/assets/images/arrow.png';
import { NImage } from 'naive-ui'
interface Props {
    suggestion: string;
    index?: number;
    disabled?: boolean;
}
interface Emit {
    (ev: 'select', data: { suggestion: string }): void
}

defineProps<Props>()
const emit = defineEmits<Emit>()
const handleSelectSuggestion = (suggestion: string) => {
    emit('select', { suggestion })
}
</script>

<template>

    <div class="file-suggestion-container" :class="disabled ? 'disabled' : ''" @click="handleSelectSuggestion(suggestion)">
        <span>{{ suggestion }}</span>
        <NImage :src="ArrowImg" width="10px" height="6px" preview-disabled />
    </div>

</template>

<style lang="less">
.file-suggestion-container {
    display: flex;
    flex-direction: row;
    column-gap: 5px;
    align-items: center;
    padding: 6px 12px;
    border-radius: 8px;
    color: #666666;
    background-color: #F3F5F7;

    &.disabled {
        background-color: #F5F5F5 !important;
        color: #C4C4C4;
        cursor: not-allowed;
    }

    &:hover {
        background-color: #DEE2EE;
        cursor: pointer;
    }
}
</style>