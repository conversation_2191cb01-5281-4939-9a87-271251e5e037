<template>
	<div v-if="agentDetail" class="pt-[84px]">
		<div class="flex gap-[20px]">
			<NAvatar :size="120" :src="agentDetail.profile || defaultAvatar" />
			<div class="flex flex-col gap-2">
				<div class="text-title font-bold text-[24px]">{{ agentDetail.name }}</div>
				<div class=" text-secondary">{{ agentDetail.desc || '暂无描述' }}</div>
			</div>
		</div>
		<div class="mt-[26px]">{{ agentDetail.welcome_message || '暂未设置欢迎语' }}</div>

		<div class="mt-[20px] flex flex-col gap-[10px] items-start">
			<NButton class="!cursor-pointer !px-[20px]" size="small" :bordered="false" tertiary round
				v-for="(item, index) in agentDetail.examples" :key="item" icon-placement="right" @click="() => handleExampleClick(item)"
				style="/**--n-color: #EDEEF3;*/">
				{{ item }}
				<template #icon>
					<ArrowSvg class="w-[12px] h-[12px] transform rotate-[-90deg]" />
				</template>
			</NButton>
		</div>
	</div>
</template>

<script setup lang="ts">
import { NAvatar, NButton } from 'naive-ui';
import request from '@/utils/request';
import { inject, onMounted, ref, watch } from 'vue';
import { useUserStore } from '@/store';
import ArrowSvg from '@/assets/aiwork/svg/arrow.svg'
import defaultAvatar from '@/assets/avatar.jpg'

const props = defineProps<{
	agentId: string,
	teamId: string
}>()
const emit = defineEmits<{
	exampleClick: [str: string]
}>()
const addPrompt: any = inject('addPrompt')

const agentDetail = ref<any>({})
const userStore = useUserStore()

onMounted(() => {
	getAgentDetail()
})
watch(() => props.agentId, () => getAgentDetail())

const getAgentDetail = () => {
	if(!props.agentId || !props.teamId) return
	if(userStore.curTeam?.id !== props.teamId) return
	return request({
		url: '/api3/aiwork/teamAgent/publishList',
		method: 'POST'
	}).then(data => {
		const find = data.find(item => item.id === props.agentId)
		agentDetail.value = find
	})
}

const handleExampleClick = (str: string) => {
	// emit('exampleClick', str)
	addPrompt(str)
}

</script>
