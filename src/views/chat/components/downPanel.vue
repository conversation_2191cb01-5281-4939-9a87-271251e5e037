<script setup lang="ts">
import { isBoolean } from "lodash";
import { ref, watch } from "vue";

const props = defineProps<{
	show?: boolean
}>()

const show = ref(isBoolean(props.show) ? props.show : false);
const $content = ref<HTMLElement | null>(null)
const height = ref(0)

const updateHeight = () => {
	if (!$content.value) {
		height.value = 0
		return
	}
	const rect = $content.value.getBoundingClientRect()
	height.value = (rect.bottom - rect.top + 12) || 0
}

// Use MutationObserver to watch for DOM changes
const observer = new MutationObserver(updateHeight)

watch($content, (el) => {
	if (el) {
		updateHeight()
		observer.observe(el, {
			childList: true,
			subtree: true,
			characterData: true
		})
	} else {
		height.value = 0
		observer.disconnect()
	}
}, { immediate: true })

const handleClick = () => {
	show.value = !show.value
}
</script>

<template>
	<div class="mb-[12px]">
		<div class="down-panel-trigger" @click="handleClick">
			<slot name="trigger" :show="show" />
		</div>

		<div class="down-panel overflow-hidden transition-all duration-300"
			:style="`max-height: ${show ? height : 0}px;`">
			<div class="down-panel-content mt-[12px]" ref="$content">
				<slot />
			</div>
		</div>
	</div>
</template>
