export interface ShortCuts {
	label: string;
    icon?: string;
	desc: string;
	action: string;
	params: ShortCutParams;
}

export interface ShortCutParams {
	file?: ShortCutFileItem;
	query?: string;
	chatBot?: number;
	chatBotName?: string;
	jumpUrl?: string;
}
export interface ShortCutFileItem {
	id: string;
	name: string;
	size: number;
}
export enum ThinkType {
	None = 0,
	Thinking = 1,
	Answer = 2,
}
