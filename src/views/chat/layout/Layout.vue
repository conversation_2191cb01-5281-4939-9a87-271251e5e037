<script setup lang='ts'>
import { computed } from 'vue'
import { NLayout, NLayoutContent, NLayoutFooter, NLayoutHeader } from 'naive-ui'
import { useRoute, useRouter } from 'vue-router'
import { useChatStore, useUserStore } from '@/store'
import Sider from './sider/index.vue'
import { Header, TabBar, CopyRight } from '@/components/common'
import Header1 from '@/components/common/Header1/index.vue'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { useAppStore } from '@/store'
const appStore = useAppStore()
const chatStore = useChatStore()
const router = useRouter()
const userStore = useUserStore()
const route = useRoute()

const historyList = computed(() => chatStore.history.filter(item => !item.type || item.type == 'chat'))

let dataItem = historyList.value.find((item => item.uuid == chatStore.active))
if(dataItem && dataItem.teamId != userStore.curTeam?.id) {
	dataItem = historyList.value.find((item => (userStore.curTeam?.id == item.teamId && item.type == 'chat') || (!item.teamId && (item.type == 'chat' || !item.type))))
	dataItem && chatStore.setActive(dataItem.uuid, "chat")
}
const active = dataItem ? dataItem.uuid : 1002
const urlObj = new URL(window.location.href);
router.replace(`/chat/${active}${urlObj.search}`)


const { isMobile, isPC } = useBasicLayout()

const collapsed = computed(() => appStore.siderCollapsed)

const getMobileClass = computed(() => {
	if (!isPC.value)
		return ['rounded-none', 'shadow-none']
	return ['border', 'rounded-md', 'shadow-md', 'dark:border-neutral-800']
})

const getContainerClass = computed(() => {
	return [
		'h-full',
		{ 'pl-[260px]': isPC.value && !collapsed.value },
	]
})
</script>

<template>
	<div class="h-full dark:bg-[#24272e] transition-all" :class="[!isPC ? 'p-0 overflow-hidden' : 'p-0']">
		<div class="h-full sm:pb-[50px]" :class="getMobileClass">
			<NLayoutHeader>
				<Header1 />
			</NLayoutHeader>
			<NLayout class="z-40 transition" :class="getContainerClass" has-sider>
				<Sider />
				<NLayoutContent style="background-color: #F9FAFF;">
					<RouterView v-slot="{ Component, route }">
						<component :is="Component" :key="route.fullPath" />
					</RouterView>
				</NLayoutContent>
				<!-- <TabBar /> -->
			</NLayout>
			<!-- <NLayoutFooter>
				<CopyRight />
			</NLayoutFooter> -->
		</div>
	</div>
</template>
