import {
	NDropdown,
	NIcon,
	NPopconfirm,
	NInput,
	DropdownOption,
	NEllipsis,
} from "naive-ui";
import { MoreHorizontal16Regular, Save16Regular } from "@vicons/fluent";
import { ref, h, defineComponent, onMounted, computed } from "vue";
import { useChatStore } from "@/store";
import EditSvg from "@/assets/aiwork/svg/edit.svg";
import DeleteSvg from "@/assets/aiwork/svg/delete.svg";
import { watch } from "vue";

const renderDelete = ({ data, onComfirm, showDrop }) => {
	const inputVal = ref(data.title)
	const handleConfirm = () => {
		showDrop = false;
		data.title = inputVal
		onComfirm?.(data);
	}
	return (
		<NPopconfirm
			showIcon={false}
			onPositiveClick={handleConfirm}
			keepAliveOnHover={true}
			v-slots={{
				trigger: () => (
					<span onClick={(event) => event.stopPropagation()}>编辑</span>
				),
			}}
		>
			<div class="w-[370px] px-[16px] py-[22px]">
				<p class="text-[16px] text-[#333] mb-[30px]">编辑聊天名称</p>
				<NInput
					defaultValue={inputVal}
					onUpdateValue={(value) => (inputVal.value = value)}
					onClick={(event) => event.stopPropagation()}
				/>
			</div>
		</NPopconfirm>
	);
};

export const renderSideItem = defineComponent(
	(props) => {
		const { data, bindClick, bindChange } = props;
		const showDrop = ref(false);
		const chatStore = useChatStore();

		const dataSources = computed(() =>
			chatStore.history.filter((item) => item.type === "chat" || !item.type)
		);

		onMounted(() => {
			document.body.addEventListener("click", () => {
				showDrop.value = false;
			});
		});

		watch(
			() => data,
			(v) => {
				console.log(4444, v);
			},
			{ deep: true }
		);

		const options: DropdownOption[] = [
			{
				label: () => renderDelete({ data, onComfirm: bindChange, showDrop }),
				key: "edit",
				icon: () => <EditSvg class="w-[12px] h-[12px]" />,
			},
			{
				label: "删除",
				key: "delete",
				icon: () => <DeleteSvg class="w-[12px] h-[12px] !text-[#f00000]" />,
				props: {
					class: "!text-[#f00000]",
					onClick: (event) => {
						event.stopPropagation();
						// if (dataSources.value.length === 1) {
						// 	window.$notification?.warning({
						// 		content: "至少保留一个聊天记录",
						// 		duration: 2000,
						// 	});
						// 	return;
						// }
						chatStore.deleteHistory(data.uuid, "chat");
					},
				},
			},
		];
		return () => (
			<div
				class="flex flex-1 justify-between items-center gap-[5px] min-w-0"
				onClick={() => bindClick?.(data)}
			>
				<div class="flex-1 min-w-0 overflow-hidden">
					<NEllipsis tooltip={false}>{data.title}</NEllipsis>
				</div>
				<NDropdown options={options} trigger="click" show={showDrop.value}>
					<NIcon
						size={25}
						class="sub-action flex-shrink-0 flex-grow-0"
						onClick={(event) => {
							event.stopPropagation();
							showDrop.value = true;
						}}
					>
						<MoreHorizontal16Regular />
					</NIcon>
				</NDropdown>
			</div>
		);
	},
	{
		props: ["data", "bindClick", "bindChange"],
	}
);

// export const RenderChatItemAction = defineComponent((props) => {
// 	const showDrop = ref(false);
// 	const chatStore = useChatStore();

// 	const Delete = () => (
// 		<NPopconfirm
// 			showIcon={false}
// 			onPositiveClick={() => onComfirm?.(data)}
// 			onUpdate:show={(show, ...rest) =>
// 				!show ? (showDrop.value = false) : null
// 			}
// 			v-slots={{
// 				trigger: <span onClick={(event) => event.stopPropagation()}>编辑</span>,
// 			}}
// 		>
// 			<div class="w-[370px] px-[16px] py-[22px]">
// 				<p class="text-[16px] text-[#333] mb-[30px]">编辑聊天名称</p>
// 				<NInput
// 					value={data.title}
// 					onUpdateValue={(value) => (data.title = value)}
// 				/>
// 			</div>
// 		</NPopconfirm>
// 	)

// 	const options:DropdownOption[] = [
// 		{
// 			label: () => renderDelete({ data, onComfirm: bindChange, showDrop }),
// 			key: "edit",
// 			icon: () => <EditSvg class="w-[12px] h-[12px]" />
// 		},
// 		{
// 			label: "删除",
// 			key: "delete",
// 			icon: () => <DeleteSvg class="w-[12px] h-[12px] !text-[#f00000]" />,
// 			props: {
// 				class: "!text-\[#f00000\]",
// 				onClick: (event) => {
// 					event.stopPropagation();
// 					chatStore.deleteHistory(props.uuid, "chat");
// 				},
// 			},
// 		},
// 	];
// 	return () => {
// 		<NDropdown options={options} trigger="click" show={showDrop.value}>
// 				<NIcon
// 					size={25}
// 					class="sub-action"
// 					onClick={(event) => {
// 						event.stopPropagation();
// 						showDrop.value = true
// 					}}
// 				>
// 					<MoreHorizontal16Regular />
// 				</NIcon>
// 			</NDropdown>
// 	}
// }, {
// 	props: ['uuid']
// })
