<script setup lang="ts">
import type { CSSProperties } from "vue";
import { computed, h, onMounted, ref, watch } from "vue";
import {
	NLayoutSider,
	NButton,
	NTooltip,
	NMenu,
	MenuOption,
	useDialog,
	NIcon,
	NDivider,
	NEllipsis,
	NInput,
	NAvatar
} from "naive-ui";
import List from "./List.vue";
import { useAppStore, useChatStore, useUserStore } from "@/store";
import { useBasicLayout } from "@/hooks/useBasicLayout";
import Footer from "../../../components/Footer.vue";
import { getUser } from "@/store/modules/auth/helper";
import AddSvg from "@/assets/aiwork/svg/add.svg";
import Message1Svg from "@/assets/aiwork/svg/message.1.svg";
import Message2Svg from "@/assets/aiwork/svg/message.2.svg";
import DeleteSvg from "@/assets/aiwork/svg/delete.svg";
import DotSvg from "@/assets/aiwork/svg/dot.svg";
import { renderSideItem } from "./render";
import dayjs from "dayjs";
import SearchSvg from "@/assets/search.svg"
import { useRoute } from "vue-router";
import request from "@/utils/request";
import defaultAvatar from "@/assets/avatar.jpg";

const appStore = useAppStore();
const chatStore = useChatStore();
const { isMobile, isPC } = useBasicLayout();
const isLogin = ref(false);
const memberType = ref(0);
const user = getUser();
const userStore = useUserStore();
const dialog = useDialog();
const searchValue = ref("");
const route = useRoute()

if (user) {
	isLogin.value = user?.id;
}

let keyword = ref("");

if (userStore && userStore.userInfo && userStore.userInfo.member) {
	const member: any = userStore.userInfo.member;
	//code 100001
	memberType.value = member;
} else if (userStore && userStore.userInfo) {
	//code 200001 MJ会员
	memberType.value = 4;
}

const dataSources = computed({
	get: () => {
		const res: any = [
			{
				key: "today",
				label: "今天",
				icon: "",
				children: [],
			},
			{
				key: "lastWeek",
				label: "最近一周",
				icon: "",
				children: [],
			},
			{
				key: "lasterMonth",
				label: "最近30天",
				icon: "",
				children: [],
			},
			{
				key: "earlier",
				label: "更早",
				icon: "",
				children: [],
			},
		];
		// 获取当前日期
		const today = dayjs();
		const oneWeekAgo = today.subtract(1, "week");
		const oneMonthAgo = today.subtract(1, "month");

		const history = chatStore.history.filter(
			(item) => (item.type === "chat" || !item.type) && !item.agentId
		).filter((item) => !searchValue.value || item.title.includes(searchValue.value));
		history.forEach((item) => {
			const _item = {
				key: item.uuid,
				label: item.title,
				icon: h(Message2Svg, { class: "w-[20px] h-[20px] flex-shrink-0 flex-grow-0" }),
				uuid: item.uuid,
				data: item
			}
			const itemDate = dayjs(item.createTime);

			if (itemDate.isSame(today, "day")) {
				res[0].children.push(_item);
			} else if (
				itemDate.isAfter(oneWeekAgo, "day") &&
				itemDate.isBefore(today, "day")
			) {
				res[1].children.push(_item);
			} else if (
				itemDate.isAfter(oneMonthAgo, "day") &&
				itemDate.isBefore(oneWeekAgo, "day")
			) {
				res[2].children.push(_item);
			} else {
				res[3].children.push(_item);
			}
		});
		return res
	},
	set: () => { },
});

async function handleSelect({ uuid }: Chat.History) {
	if (isActive(uuid)) return;
	if (chatStore.active)
		chatStore.updateHistory(chatStore.active, { isEdit: false });
	await chatStore.setActive(uuid, "chat");

	if (!isPC.value) appStore.setSiderCollapsed(true);
}

async function handleAgentSelect(item, index) {
	if(!item.uuid) {
		const uuid = Date.now()
		agentList.value[index].uuid = uuid
		return chatStore.addHistory({
			agentId: item.id,
			teamId: item.teamId,
			title: '新建智能体',
			uuid,
			isEdit: false,
			type: 'chat',
			createTime: new Date().getTime()
		})
	}
	handleSelect(item)
}

const handleClickMenu = (key: string, item?: MenuOption) => {
	if (key === "addChat") return handleAdd();
	if (key === "delete-all") {
		dialog.info({
			content: "确定删除所有聊天记录吗？",
			positiveText: "确定",
			negativeText: "取消",
			onPositiveClick: () => {
				chatStore.clearHistory("chat");
				handleAdd();
			},
		});
		return;
	}
};

const collapsed = computed(() => appStore.siderCollapsed);

function handleAdd() {
	chatStore.addHistory({
		title: "新建会话",
		uuid: Date.now(),
		type: "chat",
		isEdit: false,
		createTime: new Date().getTime(),
	});
	if (!isPC.value) appStore.setSiderCollapsed(true);
}

function handleUpdateCollapsed() {
	appStore.setSiderCollapsed(!collapsed.value);
}

const getMobileClass = computed<CSSProperties>(() => {
	if (!isPC.value) {
		return {
			position: "fixed",
			zIndex: 500,
		};
	}
	return {};
});

const mobileSafeArea = computed(() => {
	if (isMobile.value) {
		return {
			paddingBottom: "env(safe-area-inset-bottom)",
			height: "calc(100% - 66px)",
		};
	}
	return {
		height: "calc(100% - 66px)",
	};
});

watch(
	isPC,
	(val) => {
		appStore.setSiderCollapsed(!val);
	},
	{
		immediate: true,
		flush: "post",
	}
);

const handleInput = (e: any) => {
	const value = e.target.value;
	keyword.value = value;
};

function isActive(uuid: number) {
	return chatStore.active === uuid;
}

onMounted(() => getAgentList())
const agentList = ref<any[]>([]);
const getAgentList = () => {
	if (userStore.curTeam) {
		request({
			url: '/api3/aiwork/teamAgent/publishList',
			method: "POST",
		})
			.then(data => {
				const history = chatStore.getAllHistory()
				agentList.value = data.map(item => {
					const target = history.find((_itm) => _itm.agentId == item.id && _itm.teamId == item.teamId);
					return {...item, uuid: target?.uuid, teamId: item.teamId}
				});
			})
	}
}
</script>

<template>
	<NLayoutSider :collapsed="collapsed" :collapsed-width="0" :width="260" :show-trigger="!isPC ? false : false"
		collapse-mode="transform" position="absolute" :style="getMobileClass" class="shadow-aside overflow-hidden"
		@update-collapsed="handleUpdateCollapsed">
		<div class="w-[260px] overflow-hidden h-[100vh] px-[12px] pb-[12px] pt-[78px] flex flex-col bg-[#f3f4f6]">
			<div v-if="agentList.length" class="flex-0 overflow-x-hidden overflow-y-auto max-h-[200px]">
				<template v-for="(item, index) in agentList" :key="item.id">
					<div class="chat-sub-item" :class="{ active: isActive(item.uuid) }" @click="handleAgentSelect(item, index)" style="--bg-color: #fff; --bg-active-color: rgba(0, 0, 0, .04);--text-active-color:#333;">
						<div class="flex flex-1 justify-start items-center gap-[5px] min-w-0">
							<NAvatar :size="16" :src="item.profile || defaultAvatar" />
							<NEllipsis :tooltip="false">{{item.name}}</NEllipsis>
						</div>
					</div>
				</template>
				<NDivider />
			</div>
			<div class="chat-item_add" @click="handleClickMenu('addChat')" >
				<AddSvg class="chat-item__icon" />
				<span>新建会话</span>
			</div>
			<NDivider />
			<NInput placeholder="搜索"
				style="border:none;background: #F7F8FA; --n-border: none;--n-border-hover:none;--n-border-focus:none;--n-box-shadow-focus: none;--n-box-shadow-hover: none;"
				clearable @change="(v) => searchValue = v" @input="v => searchValue = v">
				<template #prefix>
					<SearchSvg class="w-[14px] h-[14px] text-[#8B8B8B]" />
				</template>
			</NInput>
			<div class="flex-1 overflow-x-hidden overflow-y-auto">
				<template v-for="item in dataSources" :key="item.key">
					<div class="chat-item chat-item-group" v-if="item.children.length">
						<component :is="item.icon" class="chat-item__icon" v-if="item.icon" />
						<span class="text-[12px]">{{ item.label }}</span>
					</div>
					<div class="chat-sub-list">
						<template v-for="subItem in item.children" :key="`${subItem.key}_${subItem.label}`">
							<div class="chat-sub-item" :class="{ active: isActive(subItem.key) }" @click="handleSelect(subItem)">
								<!-- <component :is="subItem.icon" class="chat-item__icon" v-if="subItem.icon" /> -->
								<!-- <span class="flex-1 min-w-0"><NEllipsis class="w-full" :lines="1">{{ subItem.label }}</NEllipsis></span> -->
								<renderSideItem :key="subItem.title" :data="subItem.data" :bindChange="chatStore.updateHistory"
									:bindClick="handleSelect" />
							</div>
						</template>
					</div>
				</template>

			</div>
			<NDivider />
			<div class="chat-item" @click="handleClickMenu('delete-all')">
				<DeleteSvg class="chat-item__icon !w-[16px] !h-[16px]" />
				<span>删除所有会话</span>
			</div>
		</div>

		<!-- <div class="flex flex-col mt-[66px] sm:mt-1 bg-[#fff] backdrop-blur transition-colors" :style="mobileSafeArea">
			<main class="flex flex-col flex-1 min-h-0">
				<div class="py-2 mt-[10px] text-center">
					<div class="w-[220px] mx-auto flex items-center">
						<div class="flex items-center bg-[#F9F9F9] rounded-[6px] focus:bg-[#f1f1f2] mr-2">
							<i class="fi fi-bs-search ml-2 mt-1 text-[18px] text-[#99a2b7]"></i>
							<input type="search"
								class="bg-[#F9F9F9] border border-[#F9F9F9] text-[#4B5675] w-full h-full py-2 px-3 rounded-[6px] appearance-none transition-colors duration-1000 focus:outline-0 shadow-inputShow"
								placeholder="Search..." @input="handleInput" />
						</div>
						<NTooltip trigger="hover">
							<template #trigger>
								<NButton type="success" color="#3dbaa1" @click="handleAdd"><i class="fi fi-rr-plus"></i>
								</NButton>
							</template>
							{{ $t("chat.newChatButton") }}
						</NTooltip>
					</div>
				</div>
				<div class="flex-1 min-h-0 pb-4 overflow-hidden">
					<List :keyword="keyword" />
				</div>
			</main>
			<Footer />
		</div> -->
	</NLayoutSider>
	<template v-if="!isPC">
		<div v-show="!collapsed" class="fixed inset-0 z-40 bg-black/40" @click="handleUpdateCollapsed" />
	</template>
</template>

<style lang="less" scoped>
:deep(.n-menu--vertical) {
	height: calc(100vh - 64px);
	display: flex;
	flex-direction: column;
}

:deep([role="menuitem"]) {
	flex-shrink: 0;
	flex-grow: 0;
}

:deep(.n-submenu) {
	flex: 1;
	overflow-y: scroll;
	overflow-x: hidden;
}

:deep(.n-menu-item-content-header) {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

:deep(.n-menu-item-content-header__extra) {
	height: 25px;
}

// :deep(.sub-action) {
// 	// display: none;
// 	opacity: 0;
// }

// :deep(.n-submenu-children .n-menu-item:hover .sub-action) {
// 	// display: block;
// 	opacity: 1;
// }

:deep(.n-submenu-children .n-menu-item-content) {
	padding-left: 32px !important;
}

:deep(.n-base-icon.n-menu-item-content__arrow) {
	display: none;
}

:deep(.n-layout-sider-scroll-container) {
	overflow: hidden !important;
}

.chat-item,
.chat-sub-item,
.chat-item_add {
	display: flex;
	height: 43px;
	align-items: center;
	padding: 12px;
	gap: 6px;
	// background-color: var(--bg-color, #fff);
	color: rgba(0, 0, 0, 0.5);
	border-radius: 6px;
	transition: all 0.3s ease;
	cursor: pointer;

	&+& {
		margin-top: 6px;
	}

	&:not(.chat-item-group):hover {
		background-color: rgba(0, 0, 0, .04);
	}
	&:not(.chat-item-group).active {
		// background-color: #e6f1ff;
		// background-color: var(--bg-active-color, #E3EDFF);
		background-color: #fff;
		// color: var(--text-active-color, #0e69ff);
		color: rgba(0, 0, 0, 0.85);
	}

	&.chat-item-group {
		color: #676767;
	}

	:deep(.sub-action) {
		opacity: 0;
	}

	&:hover :deep(.sub-action),
	&.active :deep(.sub-action) {
		opacity: 1;
	}
}
.chat-item_add {
	background-color: rgba(0, 87, 255, 0.06);
	color:rgb(0, 87, 255);
	border: .5px solid rgba(0, 102, 255, .15);
	&&:hover {
		color: rgb(0, 87, 255);
		background-color: rgba(0, 87, 255, 0.1);
		border-color: rgba(0, 102, 255, 0.15);
	}
}

.chat-item__icon {
	flex-shrink: 0;
	flex-grow: 0;
	width: 16px;
	height: 16px;
	display: flex;
	place-items: center;
}
</style>
