/* 隐藏折叠面板的内容 */
.accordion-content {
  display: none;
  background-color: #eee;
  color: #6b7280;
  padding-bottom: 15px;
  border-radius: 0 0 10px 10px;
  /* 圆角效果 */
  overflow: hidden;
  /* 防止边框外溢 */
}

.accordion-content p {
  margin: 0;
  padding: 0;
  line-height: 200%;
  padding-left: 15px;
}

.accordion-content p:before {
  content: '\00B7';
  font-size: 30px;
  margin-right: 5px;
  position: relative;
  /* 开启相对定位 */
  top: 4px;
  /* 将点号向上移动一半高度 */
  /* transform: translateY(-50%); 使用负数的百分比值将点号向下移动一半高度 */
  display: inline-block;
  /* 将伪元素转换为行内块元素 */
  font-weight: bold;
}

/* 显示打开的折叠面板的内容 */
.show {
  display: block;
}

/* 样式化折叠面板的标题 */
.accordion-title {
  background-color: #eee;
  padding: 20px;
  border-radius: 10px;
  /* 圆角效果 */
  cursor: pointer;
  position: relative;
}

.title-text {
  padding-left: 15px;
}

/* 将标题背景色改为深灰色 */
.active {
  border-radius: 10px 10px 0 0;
  /* 圆角效果 */
}

/* 定义标题左侧图标样式 */
.icon-left {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
  line-height: 1;
}

/* 定义标题右侧展开/收缩图标样式 */
.icon-right {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  line-height: 1;
}

.hide-scrollbar {
	-ms-overflow-style: none;  /* IE and Edge */
	scrollbar-width: none;  /* Firefox */
}
.hide-scrollbar::-webkit-scrollbar {
	display: none;
}
.text-bg-line {
	position: relative;
}
.text-bg-line::before {
	content: "";
	position: absolute;
	top: 50%;
	left: 50%;
	width: 60%;
	height: 1px;
	background-color: #eee;
	transform: translate(-50%, -50%);
	z-index: 0;
}

.n-alert__close{
	z-index: 9;
}
