import { post, get } from '@/utils/request'
import { AxiosProgressEvent } from 'axios'


export function fetchUploadFile<T>(params?:any) {
	const { onUploadProgress, ...rest } = params || {}
  return post<T>({
    url: '/api3/aiwork/file/uploadFile',
		headers: { 'Content-Type':'multipart/form-data' },
		data: rest,
		onUploadProgress
	})
}

/**
 *
 * @param params 返回的对话内容
 * @returns
 */
export function fetchChatIcon<T>(params: { id?: number }) {
	return post<T>({
		url: "/api3/aiwork/core/getEmote",
		data: params,
	});
}


/**
 * 根据内容获取问题
 * @param params 对话返回的内容
 * @returns
 */
export function fetchQuestions<T>(params:{id?: number}) {
  return post<T>({
    url: '/api3/aiwork/core/getQuestions',
		data: params,
	})
}
