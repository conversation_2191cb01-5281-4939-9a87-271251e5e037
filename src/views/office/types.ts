import { doc2Translate, excelToPdf, imageToPdf, img2Word, imgCompress, mergePdf, pdfToCsv, pdfToPpt, pdfToWord, pptToPdf, searchPassword, splitPdf, wordToPdf } from "./apis";
export interface OfficeCard {
	title: string;
	subtitle: string;
	category: string;
	icon: string;
	path: string;
	background: string;
	backgroundImage?: string;
	grid: string;
	badgeBg: string;
}

export interface SliderCard {
	title: string;
	subtitle: string;
	src: string;
}

export interface TransCard {
	title: string;
	highlight: string;
	highlightColor: string;
	icon: string;
	type: string;
}

export interface ChooseTool {
	title: string;
	icon: string;
	path: string;
}

export interface ChooseTools {
	type: string;
	list: ChooseTool[];
}
export enum OfficeType {
	// PDF转Word
	PDF2WORD = "Pdf2Word",
	// Word转PDF
	WORD2PDF = "Word2Pdf",
	// PDF转PPT
	PDF2PPT = "Pdf2Ppt",
	// PPT转PDF
	PPT2PDF = "Ppt2Pdf",
	// PDF转Excel
	PDF2EXCEL = "Pdf2Excel",
	// Excel转PDF
	EXCEL2PDF = "Excel2Pdf",
	// PDF合并
	PDFMERGE = "PdfMerge",
	// PDF拆分
	PDFSPLIT = "PdfSplit",
	// 图片转文字
	IMG2WORD = "Img2Word",
	// 图片转Excel
	IMG2EXCEL = "Img2Excel",
	// 图片转PDF
	IMG2PDF = "Img2Pdf",
	// 图片压缩
	IMGCOMPRESS = "ImgCompress",
	// 视频转GIF
	VIDEOTOGIF = "videotogif",
	// 文档翻译
	DOC2TRANSLATE = "Doc2Translate",
	// 密码查找
	FINDPASSWORD = "FindPassword",
}

export enum TRANSFORM_TYPE {
	WORD = "WORD",
	EXCEL = "EXCEL",
	PDF = "PDF",
	PPT = "PPT",
}
export enum SplitType {
	// 1按页码范围拆分，2按间距拆分，3一键拆分所有页面
	RANGE = 1,
	INTERVAL = 2,
	ALL = 3,
}

export enum ImageFitOption {
    // 填充页面
    FILL_PAGE = "fillPage",
    // 适应图片大小
    FIT_DOCUMENT_TO_IMAGE = "fitDocumentToImage",
    // 保持横纵比
    MAINTAIN_ASPECT_RATIO = "maintainAspectRatio",
}

export const imageFitOptionList = [
  { label: "填充页面", value: ImageFitOption.FILL_PAGE },
  { label: "适应图片大小", value: ImageFitOption.FIT_DOCUMENT_TO_IMAGE },
  { label: "保持横纵比", value: ImageFitOption.MAINTAIN_ASPECT_RATIO }
];
export const fileTransformOption = [
    { label: "Excel", value: "excel" },
    { label: "Word", value: "word" },
    { label: "PDF", value: "pdf" },
    { label: "PPT", value: "ppt" },
];
export interface InputFileInfo {
	/** 必须，文件URL */
	inputUrl: string;
	/** 必须，文件名称 */
	inputName: string;
	/** 非必须，文件格式 */
	inputFormat?: string;
}

export interface InputArray {
	/** 必须，输入文件列表 */
	inputFiles: InputFileInfo[];
}

export enum CONTENT_MODE {
	// 无
	NONE = "none",
	// 文件列表
	FILE_LIST = "fileList",
	// 文件列表拆分
	FILE_LIST_SPLIT = "fileListSplit",
	// 文件转成slide
	FILE_SLIDE = "fileSlide",
}

export type Config = {
	[key: string]: any;
};

export interface HeaderConfig {
	type: "header";
	label: string;
	options: ButtonConfig[];
}

export interface ButtonConfig {
	type: "buttons";
	name: string;
	label: TRANSFORM_TYPE[];
}

export interface Page {
	mediaId?: number;
	url?: string;
	fileSize?: number;
	fileMd5?: string;
	mediaType?: number;
	name?: string;
	checked?: boolean;
}
export interface CheckedPage extends Page {
	checked: boolean;
}

export interface HistoryResult {
	bizCode: string;
	userId: number;
	teamId: number | null;
	taskId: number;
	taskType: string;
	convertType: string;
	status: string;
	process: string;
	inputName: string;
	inputUrl: string;
	inputFormat: string;
	inputSize: number | null;
	outputId: string;
	outputName: string;
	outputUrl: string;
	outputFormat: string;
	outputSize: number;
	finishTime: string;
}
export const ToolType = {
	IMG_WORD: "图片转WORD",
	IMG_PPT: "图片转PPT",
	IMG_PDF: "图片转PDF",
	IMG_COMPRESS: "图片压缩",
	IMG_FORMAT: "图片格式转换",
	IMG_WATERMARK: "图片加水印",
	WORD_IMG: "WORD转图片",
	PPT_IMG: "PPT转图片",
	PDF_WORD: "PDF转WORD",
	WORD_PDF: "WORD转PDF",
	PDF_PPT: "PDF转PPT",
	PPT_PDF: "PPT转PDF",
	EXCEL_PDF: "EXCEL转PDF",
	PDF_TEXT: "PDF转文本",
	PDF_IMG: "PDF转图片",
	PDF_CSV: "PDF转CSV",
	PDF_HTML: "PDF转HTML",
	URL_PDF: "URL转PDF",
	FILE_PDF: "文件转PDF",
	HTML_PDF: "HTML转PDF",
	MERGE_PDF: "PDF合并",
	SPLIT_PDF: "PDF拆分",
	SPLIT_PDF_BY_COUNT: "按页码拆分PDF",
	PDF_WATERMARK: "PDF加水印",
	VIEW_PDF: "预览PDF",
	DOUBAO_TRANS: "文件翻译",
	DOC_DECODE_TASK: "文档解密",
} as const;

export const officeTypeMap = {
	[OfficeType.PDF2WORD]: pdfToWord,
	[OfficeType.PDF2EXCEL]: pdfToCsv,
	[OfficeType.PDF2PPT]: pdfToPpt,
	[OfficeType.PDFMERGE]: mergePdf,
	[OfficeType.PDFSPLIT]: splitPdf,
	[OfficeType.WORD2PDF]: wordToPdf,
	[OfficeType.EXCEL2PDF]: excelToPdf,
	[OfficeType.PPT2PDF]: pptToPdf,
	[OfficeType.FINDPASSWORD]: searchPassword,
	[OfficeType.DOC2TRANSLATE]: doc2Translate,
	[OfficeType.IMG2PDF]: imageToPdf,
	[OfficeType.IMG2WORD]: img2Word,
	[OfficeType.IMGCOMPRESS]: imgCompress,
	// [OfficeType.IMG2EXCEL]: img2Excel,
	// [OfficeType.IMGCOMPRESS]: imgCompress,
	// [OfficeType.VIDEOTOGIF]: videoToGif,
	// [OfficeType.DOC2TRANSLATE]: doc2Translate,
	// [OfficeType.SEARCH_PASSWORD]: searchPassword,
};
