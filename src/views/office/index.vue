<template>
	<div class="office-container">
		<main class="office-col-container">
			<section>
				<div class=" text-[34px] leading-[45px] text-[#333333]">简单好用,您的AI智能办公助手</div>
				<div class=" text-[18px] leading-[24px] text-[#676767] mt-[8px] mb-[34px]">
					精准转换，工作必备的效率神器
				</div>
				<div class="banner-container">
					<div class="left">
						<Card v-for="item in panelList" :key="item.title" :title="item.title" :subtitle="item.subtitle"
							:category="item.category" :icon="item.icon" :path="item.path" :background="item.background"
							:grid="item.grid" :badgeBg="item.badgeBg" :backgroundImage="item.backgroundImage"
							@click="navigateTo(item.path)" />
					</div>
					<div class="right">
						<n-spin :spinning="loading">
							<Upload @update:value="handleFileUpload" upload-title="拖拽文件到这里，或者选择导入文件" upload-subtitle="导入本地文件" accept="*"
								:size="10" :limit="1" />
						</n-spin>
					</div>
				</div>
			</section>
			<section class="mt-[130px]">
				<div class="text-[34px] leading-[34px] text-[#333333] text-center">
					一键转换格式
				</div>
				<div class="text-[18px] leading-[18px] text-[#676767] mt-[8px] mb-[34px] text-center">
					提升内容输出效率，智能处理所有格式文件
				</div>
				<div class="flex gap-x-[143px]">
					<n-carousel direction="vertical" dot-placement="right" :current-index="currentIndex" show-arrow
						class="!w-[60%] aspect-[16/9]">
						<n-image v-for="item in sliderList" :src="item.src" width="830px" preview-disabled />
					</n-carousel>
					<ul class="custom-dots">
						<li v-for="index of sliderList.length" :key="index" :class="{ ['is-active']: currentIndex === index - 1 }"
							@click="currentIndex = index - 1">
							<div class="title">
								{{ sliderList[index - 1]?.title }}
							</div>
							<div class="subtitle">
								{{ sliderList[index - 1]?.subtitle }}
							</div>
						</li>
						<li>
							<div
								class=" w-[187px] h-[60px] rounded-[4px] bg-[#0E69FF] text-[#fff] text-[16px] text-center leading-[60px] cursor-pointer"
								@click="handleStartConvert">
								开始转换
							</div>
						</li>
					</ul>
				</div>
				<!-- slider是上下滚动 但是要自定义dots区域 dots在最右边 每个dots旁边还有每个slide的介绍 标题和描述 -->
			</section>
			<section class="mt-[133px]">
				<div class="text-[34px] leading-[34px] text-[#333333] text-center">
					更多转换工具
				</div>
				<div class="flex gap-x-[143px] mt-[67px]">
					<TransCardComp v-for="item in transList" :key="item.title" :title="item.title" :highlight="item.highlight"
						:highlightColor="item.highlightColor" :icon="item.icon" :type="item.type"
						@click="handleTransCardClick(item)" />
				</div>
			</section>
		</main>
		<FloatHistory />
	</div>
	<n-modal v-model:show="chooseModalVisible" title="选择工具" preset="card" style="width: 632px;">
		<div class="choose-tools-container">
			<div v-for="(toolGroup, index) in filteredToolsList" :key="index" class="tool-group">
				<div class="tools-grid">
					<div v-for="tool in toolGroup.list" :key="tool.title" class="tool-item" @click="navigateTo(tool.path)">
						<div class="tool-icon flex items-center justify-center rounded-[5px] mb-[11px]">
							<n-image :src="tool.icon" :alt="tool.title" object-fit="contain" width="60" height="60"
								preview-disabled />
						</div>
						<div class="tool-title">{{ tool.title }}</div>
					</div>
				</div>
			</div>
		</div>
	</n-modal>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue';
import { NCarousel, NImage, NModal, UploadFileInfo, useMessage, NSpin } from 'naive-ui';
import { useRouter } from 'vue-router';
import { ls } from '@/utils/storage/local';
import PDF2Word from '@/assets/office/pdf2word.png';
import PDFMerge from '@/assets/office/pdfmerge.png';
import VideoToGif from '@/assets/office/video2gif.png';
import PDF2PPT from '@/assets/office/pdf2ppt.png';
import RetrievePassword from '@/assets/office/retrievepassword.png';
import RetrievePasswordBg from '@/assets/office/retrievepassword-bg.png';
import { ChooseTools, OfficeCard, SliderCard, TransCard } from './types';
import Card from './components/card.vue';
import Upload from './components/upload.vue';
import TransCardComp from './components/transCard.vue';
import FloatHistory from './components/FloatHistory.vue';
import Slide1 from '@/assets/office/slide1.png';
import Slide2 from '@/assets/office/slide2.png';
import Slide3 from '@/assets/office/slide3.png';
import PDFIcon from '@/assets/office/pdf.png';
import OfficeIcon from '@/assets/office/office.png';
import ImageIcon from '@/assets/office/image.png';

import PDF2WordIcon from '@/assets/office/icon/pdf2wordicon.png';
import PDFMergeIcon from '@/assets/office/icon/pdfmergeicon.png';
import PDFSplitIcon from '@/assets/office/icon/pdfspliticon.png';
import PPT2PDFIcon from '@/assets/office/icon/ppt2pdficon.png';
import Word2PDFIcon from '@/assets/office/icon/word2pdficon.png';
import Excel2PDFIcon from '@/assets/office/icon/excel2pdficon.png';
import Image2ExcelIcon from '@/assets/office/icon/image2excelicon.png';
import Image2PDFIcon from '@/assets/office/icon/image2pdficon.png';
import Image2WordIcon from '@/assets/office/icon/image2wordicon.png';
import ImageCompressIcon from '@/assets/office/icon/imagecompressicon.png';
import TranslateIcon from '@/assets/office/icon/translate-icon.png';
import FindPasswordIcon from '@/assets/office/icon/findpasswordicon.png';
import PDF2ExcelIcon from '@/assets/office/icon/pdf2excelicon.png';
import PDF2PPTIcon from '@/assets/office/icon/pdf2ppticon.png';
import { useUploadFile } from './hooks';
const currentIndex = ref(0);
const router = useRouter();

const message = useMessage();
function navigateTo(path: string) {
	if (path === '') {
		message.info('即将上线!')
		return;
	}
	router.push(path);
	chooseModalVisible.value = false;
}

const chooseModalVisible = ref(false)

const { run,loading } = useUploadFile(1, {
	onSuccess(data) {
		ls.set('office_temp_file', data);
	},
	onError(e) {
		console.log('update error', e);
	}
})

const handleFileUpload = async (files: UploadFileInfo[]) => {
	if (files && files.length > 0) {
		const file = files[0];
		const fileName = file.name || '';
		const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
		// 判断是否是可以被处理的文件类型
		const isProcessableFile = isProcessableFileType(fileExtension);
		// 如果是可处理的文件类型，存入localStorage
		if (isProcessableFile) {
			await run([file]);
		} else {
			message.error('不支持的文件类型');
			return
		}

		// 根据文件扩展名过滤工具列表
		filterToolsByFileType(fileExtension);
	}
}

const handleTransCardClick = (item: TransCard) => {
	const { list } = chooseToolsList.value.filter(i => i.type === item.type)?.[0]
	filteredToolsList.value = [
		{
			type: item.type,
			list
		}
	];
	chooseModalVisible.value = true;
}

const handleStartConvert = () => {
	// 根据当前轮播图索引显示对应的转换工具
	const currentType = getCurrentSlideType();
	filteredToolsList.value = chooseToolsList.value.filter(item => item.type === currentType);
	chooseModalVisible.value = true;
}

// 根据当前轮播图索引获取对应的工具类型
const getCurrentSlideType = (): string => {
	const index = currentIndex.value;
	// 根据轮播图索引返回对应的工具类型
	switch (index) {
		case 0: // PDF格式转换
			return 'PDF';
		case 1: // Office格式工具
			return 'Office';
		case 2: // 图片格式工具
			return 'image';
		default:
			return 'PDF';
	}
}

// 判断文件是否可以被处理
function isProcessableFileType(fileType: string): boolean {
	if (!fileType) return false;

	// 可处理的文件类型列表
	const processableTypes = [
		'pdf', 'doc', 'docx', 'xls', 'xlsx',
		'ppt', 'pptx', 'jpg', 'jpeg', 'png', 'gif'
	];

	return processableTypes.includes(fileType);
}

const panelList = ref<OfficeCard[]>([
	{
		title: 'PDF转Word',
		subtitle: '扫描件轻松转换',
		category: 'PDF工具',
		icon: PDF2Word,
		path: '/office/pdf2word',
		background: 'bg-gradient-to-r from-[#D3F6FF] via-[#A7EAFF] to-[#A7EAFF]',
		grid: 'grid-cols-2',
		badgeBg: 'bg-gradient-to-r from-[#76EDFC] to-[#4CD4FF]'
	},
	{
		title: 'PDF合并',
		subtitle: '导出格式支持PDF/Word',
		category: 'PDF工具',
		icon: PDFMerge,
		path: '/office/pdfmerge',
		background: 'bg-gradient-to-r from-[#D6E6FF] via-[#E1F3FF] to-[#E1F3FF]',
		grid: 'grid-cols-2',
		badgeBg: 'bg-gradient-to-l from-[#A4E8FF] to-[#84BEFF]'
	},
	{
		title: 'PDF转PPT',
		subtitle: '转换后的PPT支持编辑',
		category: 'PDF工具',
		icon: PDF2PPT,
		path: '/office/pdf2ppt',
		background: 'bg-gradient-to-r from-[#C2FFF0] to-[#E5FFFE]',
		grid: 'grid-cols-2',
		badgeBg: 'bg-gradient-to-r from-[#09F0C0] to-[#61F69E]'
	},
	{
		title: '找回密码',
		subtitle: '支持找回文件类型：Excel、Word、PDF、PPT',
		category: '办公工具',
		icon: RetrievePassword,
		path: '/office/findpassword',
		background: '',
		backgroundImage: RetrievePasswordBg,
		grid: 'grid-cols-2',
		badgeBg: 'bg-gradient-to-r from-[#A4E8FF] to-[#84BEFF]'
	}
]);

const sliderList = ref<SliderCard[]>([
	{
		title: 'PDF格式转换',
		subtitle: 'AI驱动，可将公式、表格和图片转换为可编辑的文字',
		src: Slide1
	},
	{
		title: 'Office格式工具',
		subtitle: '只需几秒文档转换为易于阅读、防篡改的PDF文件',
		src: Slide2
	},
	{
		title: '图片格式工具',
		subtitle: '一键解析，图片直接转换为可编辑的Office文档',
		src: Slide3
	}
])

const transList = ref<TransCard[]>([
	{
		title: "工具",
		highlight: "PDF",
		highlightColor: "#FF436A",
		icon: PDFIcon,
		type: 'PDF'
	},
	{
		title: "转PDF",
		highlight: "Office",
		highlightColor: "#008EFF",
		icon: OfficeIcon,
		type: 'Office'
	},
	{
		title: "转换&压缩",
		highlight: "图片",
		highlightColor: "#FF9E0C",
		icon: ImageIcon,
		type: 'image'
	}
])

const chooseToolsList = ref<ChooseTools[]>([
	{
		type: 'PDF',
		list: [
			{
				title: 'PDF转Word',
				icon: PDF2WordIcon,
				path: '/office/pdf2word'
			},
			{
				title: 'PDF转PPT',
				icon: PDF2PPTIcon,
				path: '/office/pdf2ppt'
			},
			{
				title: 'PDF合并',
				icon: PDFMergeIcon,
				path: '/office/pdfmerge'
			},
			{
				title: 'PDF拆分',
				icon: PDFSplitIcon,
				path: '/office/pdfsplit'
			}
		]
	},
	{
		type: 'Office',
		list: [
			{
				title: 'Word转PDF',
				icon: Word2PDFIcon,
				path: '/office/word2pdf'
			},
			{
				title: 'Excel转PDF',
				icon: Excel2PDFIcon,
				path: '/office/excel2pdf'
			},
			{
				title: 'PPT转PDF',
				icon: PPT2PDFIcon,
				path: '/office/ppt2pdf'
			}
		]
	},
	{
		type: 'image',
		list: [
			{
				title: '图片转Word',
				icon: Image2WordIcon,
				path: '/office/img2text'
			},
			{
				title: '图片转PDF',
				icon: Image2PDFIcon,
				path: '/office/img2pdf'
			},
			{
				title: '图片压缩',
				icon: ImageCompressIcon,
				path: '/office/imgcompress'
			}
		]
	},
	{
		type: 'extra',
		list: [
			{
				title: '文档翻译',
				icon: TranslateIcon,
				path: '/office/doc2translate'
			},
			{
				title: '找回密码',
				icon: FindPasswordIcon,
				path: '/office/findpassword'
			}
		]
	}
])

const filteredToolsList = ref<ChooseTools[]>([])

function filterToolsByFileType(fileType: string) {
	if (!fileType) return;

	// 根据文件类型过滤工具列表
	if (fileType.includes('pdf')) {
		// PDF文件
		filteredToolsList.value = [
			{
				type: 'PDF',
				list: chooseToolsList.value[0].list
			}
		];
	} else if (fileType.includes('doc') || fileType.includes('docx')) {
		// Word文件
		filteredToolsList.value = [
			{
				type: 'Office',
				list: chooseToolsList.value[1].list.filter(item => item.title.includes('Word'))
			}
		];
	} else if (fileType.includes('xls') || fileType.includes('xlsx')) {
		// Excel文件
		filteredToolsList.value = [
			{
				type: 'Office',
				list: chooseToolsList.value[1].list.filter(item => item.title.includes('Excel'))
			}
		];
	} else if (fileType.includes('ppt') || fileType.includes('pptx')) {
		// PPT文件
		filteredToolsList.value = [
			{
				type: 'Office',
				list: chooseToolsList.value[1].list.filter(item => item.title.includes('PPT'))
			}
		];
	} else if (fileType.includes('jpg') || fileType.includes('jpeg') || fileType.includes('png') || fileType.includes('gif')) {
		// 图片文件
		filteredToolsList.value = [
			{
				type: '图片',
				list: chooseToolsList.value[2].list
			}
		];
	} else {
		// 其他文件类型，显示所有工具
		filteredToolsList.value = chooseToolsList.value;
	}

	chooseModalVisible.value = true;
}

</script>
<style scoped lang="less">
.office-container {
	width: 100%;
	background: linear-gradient(to bottom, #f5f7ff, rgba(236, 241, 255, 0.8858), #e5f1ff);
	margin: 0 auto;
	padding: 70px 0;
}

.office-col-container {
	max-width: 1360px;
	margin: 0 auto;
}

.banner-container {
	display: flex;
	justify-content: space-between;
	align-items: center;
	column-gap: 27px;

	.left {
		width: 40%;
		height: 100%;
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 20px;
	}

	.right {
		flex: 1;
		height: 267px;
		border: 1px dashed transparent;
		background: linear-gradient(#fff, #fff) padding-box,
			repeating-linear-gradient(-45deg, #000 0, #000 0.3em, #fff 0, #fff 0.6em);
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 10px;
	}
}

.custom-dots {
	display: flex;
	flex-direction: column;
	margin: 0;
	padding: 0;
	list-style: none;
	gap: 20px;

	li {
		cursor: pointer;
		padding: 10px;
		border-radius: 4px;
		transition: all 0.3s ease;
		position: relative;

		&.is-active {
			background-color: #F5F7FF;

			&::before {
				content: '';
				position: absolute;
				left: 0;
				top: 0;
				width: 4px;
				height: 100%;
				background-color: #0E69FF;
				border-radius: 2px;
			}

			.title {
				color: #333;
				font-weight: 600;
			}
		}

		.title {
			font-size: 18px;
			color: #676767;
			margin-bottom: 8px;
		}

		.subtitle {
			font-size: 14px;
			color: #999;
		}
	}
}

.tool-group {
	margin-bottom: 30px;
}

.tool-group-title {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	margin-bottom: 15px;
	position: relative;
	padding-left: 12px;

	&::before {
		content: '';
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 4px;
		height: 18px;
		background-color: #0E69FF;
		border-radius: 2px;
	}
}

.tools-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 20px;
	align-items: center;
	justify-content: center;
}

.tool-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	border-radius: 8px;
	cursor: pointer;

	&:hover {
		.tool-title {
			color: #0060FF;
		}
	}

	.tool-icon {
		width: 100px;
		height: 114px;
		background-color: #F0F6FF;
	}

	.tool-title {
		font-size: 14px;
		color: #606060;
		text-align: center;
	}
}
</style>
