<template>
	<div class="flex flex-col items-center justify-between">
		<div class="border border-dashed border-[#0E69FF] rounded-[10px] p-[10px] cursor-pointer">
			<div class="flex gap-[10px]">
				<div v-for="(page, index) in pages" :key="index" class="w-[131px] h-[151px] overflow-hidden">
					<div class="flex flex-col items-center justify-center">
						<img :src="page.url" :style="{ width: '131px', height: '131px' }"
							class="aspect-[1/1] w-[131px] h-[131px] border border-[#DFDFDF] rounded-[10px] flex items-center justify-center object-contain" />
						<div v-if="index === 0">{{ pages?.startPageNumber }}</div>
						<div v-if="index === 1">{{ pages?.endPageNumber }}</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { NImage } from 'naive-ui';
import { CheckedPage } from '../types';

interface Props {
	pages: CheckedPage[] & { startPageNumber: number; endPageNumber: number }
}
const props = defineProps<Props>();
console.log('props', props);
</script>