<template>
	<div class="file-translate-container" v-if="!fileList?.length">
		<NImage :src="PDFBGIcon" class="w-[46px] h-[54px]" object-fit="cover" preview-disabled />
		<NImage :src="WordBGIcon" class="w-[46px] h-[54px]" object-fit="cover" preview-disabled />
		<NImage :src="ExcelBGIcon" class="w-[46px] h-[54px]" object-fit="cover" preview-disabled />
		<NImage :src="PPTBGIcon" class="w-[46px] h-[54px]" object-fit="cover" preview-disabled />
		<NImage :src="TxtBGIcon" class="w-[46px] h-[54px]" object-fit="cover" preview-disabled />
		<NImage :src="CSVBGIcon" class="w-[46px] h-[54px]" object-fit="cover" preview-disabled />
		<NImage :src="MDBGIcon" class="w-[46px] h-[54px]" object-fit="cover" preview-disabled />
	</div>
	<div class="file-translate-list-container" v-else>
		<div class="text-[14px] leading-[18px] text-[#666666] mb-[20px]">全部文件({{ fileList?.length }})</div>
		<div v-for="(file, index) in fileList" :key="index" class="flex items-center justify-start w-full mx-[auto]">
			<UploadItem :file="file" :is-border-dashed="props.isBorderDashed" @delete="handleDelete" />
		</div>
	</div>
</template>

<script lang="ts" setup>
import { NImage, type UploadFileInfo } from 'naive-ui';
// docx/.xlsx/.pptx/.pdf/.txt/.csv/.md
import PDFBGIcon from '@/assets/office/icon/pdf-bg-icon.png';
import WordBGIcon from '@/assets/office/icon/word-bg-icon.png';
import ExcelBGIcon from '@/assets/office/icon/excel-bg-icon.png';
import PPTBGIcon from '@/assets/office/icon/ppt-bg-icon.png';
import TxtBGIcon from '@/assets/office/icon/txt-bg-icon.png';
import CSVBGIcon from '@/assets/office/icon/csv-bg-icon.png';
import MDBGIcon from '@/assets/office/icon/md-bg-icon.png';
import UploadItem from './uploadItem.vue';
interface Props {
	fileList: UploadFileInfo[]
	isBorderDashed: boolean
}

interface Emits {
	(e: 'delete', file: UploadFileInfo): void;
}

const props = defineProps<Props>();

const emit = defineEmits<Emits>();

const handleDelete = (file: UploadFileInfo) => {
	emit('delete', file);
}
</script>

<style lang="less" scoped>
.file-translate-container {
	display: flex;
	width: 100%;
	column-gap: 33px;
	justify-content: center;
	align-items: center;
}

.file-translate-list-container {
	width: 100%;
	height: 100%;
	padding: 18px;
}
</style>
