<template>
	<n-upload class="flex flex-col items-center justify-center text-center w-full h-full" :file-list="fileList"
		:accept="accept" @update:file-list="handleFileList" :show-file-list="false" :max="limit"
		@before-upload="handleBeforeUpload" :multiple="limit > 1" directory-dnd
		style="--n-dragger-border: unset !important;
		--n-dragger-border: unset !important;--n-dragger-color: unset !important;--n-dragger-border-hover: unset !important">
		<n-upload-dragger>
			<div class="flex flex-col items-center justify-center h-full" v-if="show">
				<n-image :preview-disabled="true" :src="Import" alt="import" class="w-[74px] h-[74px]" />
				<span class=" text-[#3D3D3D] text-[14px]">{{ uploadTitle }}</span>
				<div class="import-icon-btn text-[16px] leading-[16px] text-[#676767] flex justify-center items-center gap-2">
					<n-image :preview-disabled="true" :src="ImportIcon" alt="import" class="w-[18px] h-[18px]" />
					{{ uploadSubtitle }}
				</div>
			</div>
		</n-upload-dragger>
	</n-upload>
</template>

<script setup lang="ts">
import { NUpload, NImage, UploadFileInfo, useMessage, NUploadDragger } from 'naive-ui';
import Import from '@/assets/office/import.png';
import ImportIcon from '@/assets/office/import-icon.png';
import { computed, ref } from 'vue';
import { CONTENT_MODE } from '../types';
import { isBoolean } from 'lodash';
const message = useMessage();
interface Props {
	uploadTitle?: string
	uploadSubtitle?: string
	isBorderDashed?: boolean;
	showOptions?: boolean;
	contentMode?: CONTENT_MODE;
	showAfterUpload?: boolean;
	value?: UploadFileInfo[];
	accept?: string;
	limit: number; // 个数
	size: number; // MB
}

interface Emits {
	(e: 'update:value', value: UploadFileInfo[]): void
}

const emit = defineEmits<Emits>();
const props = defineProps<Props>();

// 临时存储上传的文件
const pendingFiles = ref<UploadFileInfo[]>([]);

const show = computed(() => {
	if (isBoolean(props.showAfterUpload) && props.showAfterUpload) {
		return props.showAfterUpload
	}
	return fileList.value.length === 0
});

const fileList = computed({
	get: () => props.value || [],
	set: (files) => emit('update:value', files)
});

const maxSize = computed(() => props.size * 1024 * 1024);

const handleBeforeUpload = async (data: { file: UploadFileInfo }): Promise<boolean> => {
	if (!data.file.file) return false

	return new Promise<boolean>((resolve) => {
		if (data.file.file!.size > maxSize.value) {
			message.error(`${data.file.name} 超过大小限制`)
			resolve(false)
		} else {
			// 验证文件类型
			const fileName = data.file.name;
			const fileExtension = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();
			const acceptValue = props.accept || '';

			// 如果指定了接受的文件类型，则验证文件扩展名
			if (acceptValue && !isFileTypeAccepted(data.file.file!, fileExtension, acceptValue)) {
				message.error('不支持的文件类型');
				resolve(false);
				return;
			}

			// 将文件添加到待处理列表
			pendingFiles.value.push(data.file);
			resolve(true)
		}
	})
}

// 检查文件类型是否被接受
const isFileTypeAccepted = (file: File, fileExtension: string, acceptValue: string): boolean => {
	if (acceptValue === '*') return true;
	const acceptTypes = acceptValue.split(',').map(type => type.trim().toLowerCase());

	// 检查文件扩展名是否在接受列表中
	for (const acceptType of acceptTypes) {
		// 处理 .xxx 格式
		if (acceptType.startsWith('.') && fileExtension === acceptType) {
			return true;
		}
		// 处理 MIME 类型格式，如 image/*
		else if (acceptType.includes('/*')) {
			const mimePrefix = acceptType.split('/')[0];
			if (file.type && file.type.startsWith(mimePrefix + '/')) {
				return true;
			}
		}
		// 处理完整 MIME 类型，如 application/pdf
		else if (file.type === acceptType) {
			return true;
		}
	}

	return false;
};

const handleFileList = () => {
	// 当所有文件都上传完成后，一次性通知父组件
	if (pendingFiles.value.length > 0) {
		const newFileList = [...fileList.value, ...pendingFiles.value];
		emit('update:value', newFileList);
		pendingFiles.value = []; // 清空待处理列表
	}
}

</script>

<style lang="less" scoped>
:deep(.n-upload) {
	height: 100%;
}
:deep(.n-upload-dragger) {
	height: 100%;
}

.import-icon-btn {
	cursor: pointer;
	margin-top: 26px;
	width: 256px;
	height: 56px;
	border-radius: 10px;
	box-shadow: 0px 4px 45px 0px #E7EBF4;
}

.file-list-container {
	max-height: 200px;
	overflow-y: auto;
}

:deep(.n-upload-trigger) {
	height: inherit;
	width: inherit;
}

:deep(.n-upload-file-info__name) {
	display: none;
}
</style>
