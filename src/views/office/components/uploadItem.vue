<template>
    <!-- 上传的PDF文件 w-100 h-100 边框是dashed 右上角有删除icon  -->
    <div class="flex flex-col items-center justify-center ">
        <div
            class="bg-[#F1F7FF] flex flex-col items-center justify-center w-[100px] h-[100px] rounded-[10px] relative"
            :class="{ 'border-dashed border-2 border-gray-300 ': isBorderDashed }">
            <img :src="fileIcon" alt="PDF Icon" class="w-10 h-10">
            <div
                class=" absolute top-[3px] right-[3px] w-[18px] h-[18px] rounded-[50%] bg-[#fff] flex items-center justify-center">
                <IconDelete class=" w-[10px] h-[10px] text-[10px] cursor-pointer" @click.stop="handleDelete" />
            </div>
        </div>
        <NPopover trigger="hover">
            <template #trigger>
                <span class="text-gray-500 w-[100px] mt-[8px] cursor-default whitespace-nowrap overflow-hidden">{{ computedFileName }}</span>
            </template>
            <span>{{ file.name }}</span>
        </NPopover>
    </div>

</template>

<script setup lang="ts">
import { UploadFileInfo, NPopover } from 'naive-ui';
import PDFIcon from '@/assets/office/pdf-icon.png';
import ExcelIcon from '@/assets/office/excel-icon.png';
import WordIcon from '@/assets/office/word-icon.png';
import PPTIcon from '@/assets/office/ppt-icon.png';
import ImageIcon from '@/assets/office/image-icon.png';
import { computed } from 'vue';

const props = withDefaults(defineProps<{
    isBorderDashed?: boolean;
    file: UploadFileInfo;
}>(), {
    isBorderDashed: true
});
const fileIcon = computed(() => {
	// 根据文件名判断文件类型
    const fileName = props.file.name;
    const fileExtension = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();

    // 根据文件扩展名确定图标
    if (fileExtension === '.pdf') {
        return PDFIcon;
    } else if (fileExtension === '.doc' || fileExtension === '.docx') {
        return WordIcon;
    } else if (fileExtension === '.xls' || fileExtension === '.xlsx') {
        return ExcelIcon;
    } else if (fileExtension === '.ppt' || fileExtension === '.pptx') {
        return PPTIcon;
    } else if (['.jpg', '.jpeg', '.png', '.gif', '.bmp'].includes(fileExtension)) {
        return ImageIcon;
    }

    // 默认返回图片图标
    return ImageIcon;
});
const emit = defineEmits<{
    (e: 'delete', file: UploadFileInfo): void
}>();

// 中间部分变成... 展示开始文字和结尾的文件类型
const computedFileName = computed(() => {
    const name = props.file.name;
    const maxLength = 7; // Increased from 7 to account for Chinese characters being wider
    const fileType = name.slice(name.lastIndexOf('.')) || '';

    // Calculate actual width based on character types
    let charCount = 0;
    let i = 0;
    while (i < name.length && charCount < maxLength) {
        // Chinese characters count as 2 units
        charCount += /[\u4e00-\u9fa5]/.test(name[i]) ? 2 : 1;
        i++;
    }

    console.log('i', i);
    if (charCount < maxLength) {
        return name;
    }

    return name.slice(0, i) + '...' + fileType;
})

const handleDelete = (e: Event) => {
    e.stopPropagation();
    emit('delete', props.file);
}
</script>
<style lang="less"></style>
