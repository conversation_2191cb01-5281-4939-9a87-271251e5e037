<template>
    <div class="flex flex-row items-center justify-center relative w-full h-full gap-x-[10px]">
        <div class=" border border-[#DFDFDF] rounded-[10px] w-[237px] h-[187px] overflow-hidden cursor-pointer">
            <NImage :src="startPage.url" class="aspect-[1/1] flex items-center justify-center w-full "
                :object-fit="'contain'" preview-disabled width="237px" height="187px" />
        </div>
        <div style="display: flex; gap: 4px;">
            <div style="width: 6px; height: 6px; background: #9DA2B5; border-radius: 50%;"></div>
            <div style="width: 6px; height: 6px; background: #9DA2B5; border-radius: 50%;"></div>
            <div style="width: 6px; height: 6px; background: #9DA2B5; border-radius: 50%;"></div>
        </div>
        <div class=" border border-[#DFDFDF] rounded-[10px] w-[237px] h-[187px] overflow-hidden cursor-pointer">
            <NImage :src="endPage.url" class="aspect-[1/1] flex items-center justify-center w-full "
                :object-fit="'contain'" preview-disabled />
        </div>
    </div>
</template>
<script setup lang="ts">
import { Page } from '../types';
import { NImage } from 'naive-ui';

interface Props {
    startPage: Page;
    endPage: Page;
}
const props = defineProps<Props>();
</script>