<template>
	<div class="flex flex-col items-center justify-center pt-[20px] px-[20px] bg-white relative top-[1px] h-[99%]">
		<div class="flex flex-col items-center justify-center gap-[10px]">
			<div v-for="(file, index) in fileList" :key="index" class="flex items-center justify-center w-full mx-[auto]">
				<UploadItem :file="file" :is-border-dashed="props.isBorderDashed" @delete="handleDelete" />
			</div>
			<div class="file-crack-container">
				<div class="file-item" v-if="fileInfo">
					<div class="file-name">{{ fileInfo.fileName }}</div>
				</div>
				<div class="loading-status" v-if="status === 'pending'">
					<div class="progress-container mt-[10px] flex items-center justify-center gap-x-[10px]">
						<n-progress type="line" :percentage="progressPercentage" :show-indicator="false" color="#1B71FF"
							style="width: 190px;" />
						<div class="progress-text text-[12px] text-[#B9C0D3] text-right mt-[4px]">{{ progressPercentage }}%</div>
					</div>
					<div class="status-text">破解中</div>
					<div class="countdown-text">{{ formatTime }}</div>
				</div>
			</div>
		</div>
		<n-modal :show="showModal" style="width: 564px; height: 376px">
			<n-card>
				<template #header>
					<div>找回密码</div>
				</template>
				<div class="w-full h-[45px] bg-[rgba(182,220,255,0.2784)] text-[#0E69FF] text-[14px] leading-[45px] px-4">
					AI办公助手保护您的隐私，我们不记录您的密码，阅后即焚请放心使用~</div>
				<div class=" text-[#3D3D3D] text-[16px] leading-[21px]  mt-[18px]">该文档为加密文档，请查看并保存好密码信息</div>
				<div class="text-[#3D3D3D] text-[16px] leading-[21px] flex gap-x-[20px] mt-[16px] mb-[21px]">
					<div class="w-[48px]">
						文件名
					</div>
					<span class="text-[#0E69FF]">{{ fileList?.[0]?.name }}</span>
				</div>
				<div class="text-[#3D3D3D] text-[16px] leading-[21px] flex gap-x-[20px]">
					<div class="w-[48px]" style="text-align-last:justify;">
						密码
					</div>
					<span class="text-[#0E69FF] flex-1">******</span>
					<NButton color="#40B650" style="--n-text-color-hover:#fff; --n-text-color:#fff !important;"
						class="ml-[auto] !text-[#fff]" @click="handleClickCopy">复制密码</NButton>
				</div>
				<template #footer>
					<div class="flex flex-col items-center justify-center gap-[10px]">
						<NButton type="primary" @click="handleClose">关闭</NButton>
						<span class="text-[#3D3D3D] text-[12px] leading-[16px]">复制密码后点击关闭</span>
					</div>
				</template>
			</n-card>
		</n-modal>

		<n-modal :show="showPhoneModal" style="width: 402px; height: 305px">
			<n-card>
				<template #header>
					<div>验证您的手机号</div>
				</template>
				<div class="w-full h-[45px] bg-[rgba(182,220,255,0.2784)] text-[#0E69FF] text-[14px] leading-[45px] px-4">
					系统会自动推送短信通知您的密码找回进度及状态
				</div>
				<div class="phone-form mt-[20px]">
					<div class="form-item mb-[20px]">
						<n-input-number v-model:value="phoneNumber" :show-button="false" placeholder="请输入手机号" class="w-full"
							:disabled="isSubmitting" @update:value="handlePhoneNumberChange">
							<template #prefix>
								<n-image :src="PhoneImg" preview-disabled class=" w-[10px] h-[13px]" />
							</template>
						</n-input-number>
					</div>
					<div class="form-item mb-[20px]">
						<div class="flex justify-between items-center w-full">
							<n-input-number v-model:value="verifyCode" :show-button="false" type="text" maxlength="6"
								placeholder="请输入验证码" class="w-[70%]" :disabled="isSubmitting">
								<template #prefix>
									<n-image :src="PasswordImg" preview-disabled class=" w-[10px] h-[13px]" />
								</template>
							</n-input-number>
							<n-button text type="primary" class="ml-[10px]" :disabled="codeDisabled" @click="handleGetVerifyCode">
								{{ countdownLabel }}
							</n-button>
						</div>
					</div>
				</div>
				<template #footer>
					<div class="flex flex-col items-center justify-center gap-[10px]">
						<n-button type="primary" :disabled="submitDisabled" :loading="isSubmitting" @click="handleSubmit">
							提交
						</n-button>
					</div>
				</template>
			</n-card>
		</n-modal>
	</div>
</template>

<script lang="ts" setup>
import { NModal, NCard, NButton, NInputNumber, NProgress, NImage } from 'naive-ui'
import { UploadFileInfo, useMessage, useDialog } from 'naive-ui';
import { computed, ref, watch } from 'vue';
import PhoneImg from '@/assets/images/phone.png'
import PasswordImg from '@/assets/images/password.png'
import useTimeout from '@/hooks/useTimeout';
import UploadItem from './uploadItem.vue';
import { useRequest } from 'vue-hooks-plus';
import { sendSmsCode, verifySmsCode } from '../apis';

interface FileInfo {
	fileName: string
}

interface Props {
	taskId: number | null;
	status: null | 'pending' | 'fail' | 'success';
	showModal: boolean;
	showPhoneModal: boolean;
	fileList: UploadFileInfo[];
	isBorderDashed: boolean;
	showOptions: boolean;
	value: UploadFileInfo[]
	fileCracked?: boolean
	fileInfo: any; // 添加 fileInfo 属性
}

interface Emits {
	(e: 'delete', file: UploadFileInfo): void;
	(e: 'update:value', value: UploadFileInfo[]): void
	(e: 'update:showModal', value: boolean): void
	(e: 'update:clickCopy', value: boolean): void
	(e: 'phone-submit', phoneData: { phone: string, code: string }): void
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const message = useMessage();
const dialog = useDialog();
const fileList = computed({
	get: () => props.value || [],
	set: (files) => emit('update:value', files)
});

const fileInfo = ref<FileInfo | null>(null);

const remainingSeconds = ref(120) // 2分钟倒计时
const totalSeconds = ref(120) // 总时间

// 计算进度百分比
const progressPercentage = computed(() => {
	const elapsed = totalSeconds.value - remainingSeconds.value
	const percentage = Math.floor((elapsed / totalSeconds.value) * 100)
	return Math.min(Math.max(percentage, 0), 99) // 保持在0-99之间，直到完成才显示100%
})

const formatTime = computed(() => {
	const minutes = Math.floor(remainingSeconds.value / 60)
	const seconds = remainingSeconds.value % 60
	return `${minutes}分钟${seconds}秒`
})

const handleClickCopy = () => {
	emit('update:clickCopy', true);
}

const handleClose = () => {
	if (props.fileCracked) {
		dialog.warning({
			title: '确认退出',
			content: '如果退出，将不保存密码。确定要退出吗？',
			positiveText: '确定',
			negativeText: '取消',
			onPositiveClick: () => {
				emit('update:showModal', false);
				// 重置定时器
				remainingSeconds.value = 120;
			}
		});
	} else {
		emit('update:showModal', false);
		// 重置定时器
		remainingSeconds.value = 120;
	}
}

const { start, stop } = useTimeout(() => {
	if (remainingSeconds.value > 0) {
		remainingSeconds.value--;
	}
}, 1000, {
	immediate: false
});

// 监听状态变化，当状态为pending时开始倒计时
watch(() => props.status, (newStatus) => {
	if (newStatus === 'pending') {
		start();
	} else if (newStatus === 'success' || newStatus === 'fail') {
		stop();
		// 如果成功，将进度设为100%
		if (newStatus === 'success') {
			remainingSeconds.value = 0;
		}
	}
}, { immediate: true });

watch(() => props.fileInfo, () => {
	// 重置倒计时
	remainingSeconds.value = 120;
	// 如果状态是 pending，重新开始计时
	if (props.status === 'pending') {
		stop();
		start();
	}
}, { deep: true });

const handleDelete = (file: UploadFileInfo) => {
	// pending中不可以删除
	if (props.status === 'pending') {
		message.warning('文件正在处理中，请稍后再试');
		return;
	}

	emit('delete', file);
}

const addFiles = () => {
	// Logic to add files
	console.log("Add files clicked");
}

const convertFiles = () => {
	// Logic to convert files
	console.log("Convert files clicked");
}

// 手机验证码模态框相关逻辑
const phoneNumber = ref<number | null>(null);
const verifyCode = ref<number | null>(null);
const countdownSeconds = ref(60);
const countdownActive = ref(false);
const isSubmitting = ref(false);
const intervalTimer = ref<any>(null);

// 验证手机号是否为11位数字
const isValidPhoneNumber = computed(() => {
	if (!phoneNumber.value) return false;
	const phoneStr = phoneNumber.value.toString();
	return phoneStr.length === 11 && /^\d{11}$/.test(phoneStr);
});

// 验证码按钮是否禁用
const codeDisabled = computed(() => {
	return (countdownActive.value && countdownSeconds.value > 0) || !isValidPhoneNumber.value || sendCodeLoading.value;
});

// 提交按钮是否禁用
const submitDisabled = computed(() => {
	return !isValidPhoneNumber.value || !verifyCode.value || verifyCodeLoading.value;
});

// 倒计时标签
const countdownLabel = computed(() => {
	if (sendCodeLoading.value) {
		return '发送中...';
	} else if (countdownActive.value && countdownSeconds.value > 0) {
		return `重新获取(${countdownSeconds.value})`;
	} else {
		return '获取验证码';
	}
});

// 处理手机号变化
const handlePhoneNumberChange = () => {
	// 可以在这里添加手机号格式验证逻辑
	if (phoneNumber.value) {
		const phoneStr = phoneNumber.value.toString();
		if (phoneStr.length > 11) {
			phoneNumber.value = parseInt(phoneStr.slice(0, 11));
		}
	}
};

// 获取验证码API
const { loading: sendCodeLoading, run: runSendSmsCode } = useRequest(sendSmsCode, {
	manual: true,
	onSuccess: () => {
		// 开始倒计时
		countdownActive.value = true;

		// 设置倒计时定时器
		intervalTimer.value = setInterval(() => {
			countdownSeconds.value--;
			if (countdownSeconds.value <= 0) {
				clearInterval(intervalTimer.value);
				countdownSeconds.value = 60;
				countdownActive.value = false;
			}
		}, 1000);

		message.success('验证码已发送');
	},
	onError: (error) => {
		message.error(error?.errmsg || '验证码发送失败，请稍后重试');
		// 发送失败时重置倒计时
		clearInterval(intervalTimer.value);
		countdownSeconds.value = 60;
		countdownActive.value = false;
	}
});

// 获取验证码
const handleGetVerifyCode = () => {
	if (!isValidPhoneNumber.value) {
		message.error('请输入正确的手机号码');
		return;
	}

	// 调用发送验证码API
	runSendSmsCode({ phone: phoneNumber.value!.toString(), taskId: props.taskId! });
};

// 验证验证码API
const { loading: verifyCodeLoading, run: runVerifySmsCode } = useRequest(verifySmsCode, {
	manual: true,
	onSuccess: () => {
		// 触发提交事件，将手机号和验证码传递给父组件
		emit('phone-submit', {
			phone: phoneNumber.value?.toString() || '',
			code: verifyCode.value?.toString() || ''
		});

		// 清空表单
		phoneNumber.value = null;
		verifyCode.value = null;

		// 重置倒计时
		clearInterval(intervalTimer.value);
		countdownSeconds.value = 60;
		countdownActive.value = false;

		message.success('提交成功');
		isSubmitting.value = false;
	},
	onError: (error) => {
		message.error(error?.errmsg || '验证码验证失败，请检查验证码是否正确');
		isSubmitting.value = false;
	}
});

// 提交表单
const handleSubmit = () => {
	if (!isValidPhoneNumber.value) {
		message.error('请输入正确的手机号码');
		return;
	}

	if (!verifyCode.value) {
		message.error('请输入验证码');
		return;
	}

	isSubmitting.value = true;

	// 调用验证验证码API
	runVerifySmsCode({
		phone: phoneNumber.value!.toString(),
		verifyCode: verifyCode.value!.toString(),
		taskId: props.taskId!
	});
};

// 组件卸载时清除定时器
watch(() => props.showPhoneModal, (newVal) => {
	if (!newVal && intervalTimer.value) {
		clearInterval(intervalTimer.value);
	}
});
</script>

<style scoped lang="less">
.file-crack-container {
	display: flex;
	flex-direction: column;
	align-items: center;

	.file-item {
		text-align: center;
		margin-bottom: 16px;

		.file-name {
			font-size: 14px;
			color: #333;
			margin-bottom: 4px;
		}

		.file-size {
			font-size: 12px;
			color: #666;
		}
	}

	.loading-status {
		text-align: center;

		.status-text {
			font-size: 14px;
			color: #333333;
			margin-bottom: 4px;
		}

		.countdown-text {
			font-size: 12px;
			color: #737373;
		}
	}
}

.phone-form {

	:deep(.n-input__border),
	:deep(.n-input__state-border) {
		display: none;
	}

	:deep(.n-input-number-base) {
		border-bottom: 1px solid #d8d8d8;
	}
}
</style>
