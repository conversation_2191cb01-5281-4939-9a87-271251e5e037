<template>
	<div class="flex flex-col items-start pt-[20px] px-[20px] bg-white relative top-[1px] h-[450px]">
		<div class="text-[14px] leading-[18px] text-[#666666] mb-[20px]">全部文件({{ fileList?.length }})</div>
		<div
			class="flex flex-row flex-wrap overflow-y-auto HideScrollbar items-start justify-start gap-[19px] w-full flex-1">
			<template v-if="props.splitType === 1">
				<PagePreview :start-page="startAndEndPages[0].startPage" :end-page="startAndEndPages[0].endPage" />
			</template>
			<!-- 分组模式 -->
			<template v-else-if="props.splitType === 2">
				<GroupPagePreview v-for="(group, groupIndex) in groupPages" :key="groupIndex" :pages="group"/>
			</template>
			<!-- 单页模式 -->
			<template v-else-if="props.splitType === 3">
				<SinglePagePreview v-for="(page, index) in pages" :key="index" :checkable="true" :checked="page.checked"
					width="116" :label="index + 1 + ''" :img-url="page.url" @update:checked="handleUpdateChecked(page)" />
			</template>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { UploadFileInfo } from 'naive-ui';
import { CheckedPage } from '../types';
import PagePreview from './page-preview.vue';
import SinglePagePreview from './single-page-preview.vue';
import GroupPagePreview from './group-page-preview.vue';
import { computed, watch } from 'vue';

interface Props {
	fileList: UploadFileInfo[];
	pages: CheckedPage[];
	splitType: number;
	pageNumber?: number;
	startPage?: number;
	endPage?: number;
	groupPages?: CheckedPage[][]
}
interface Emits {
	(e: 'update:checked', checked: CheckedPage): void;
}

const props = withDefaults(defineProps<Props>(), {
	startPage: 1,
	endPage: 1,
	pageNumber: 2
})
const emit = defineEmits<Emits>();

const handleUpdateChecked = (page: CheckedPage) => {
	emit('update:checked', page);
}

const groupPages = computed(() => {
    const groups: (CheckedPage[] & { startPageNumber: number; endPageNumber: number })[] = [];
    if (props.splitType === 2) {
        // Create groups where each group contains first and last page of each pageNumber interval
        const pageSize = props.pageNumber!;
        const totalGroups = Math.ceil(props.pages.length / pageSize);

        for (let groupIndex = 0; groupIndex < totalGroups; groupIndex++) {
            const startIdx = groupIndex * pageSize;
            const endIdx = Math.min(startIdx + pageSize - 1, props.pages.length - 1);

            // Get first and last page of the current interval
            const firstPage = props.pages[startIdx];
            const lastPage = props.pages[endIdx];

            // Create a group with available pages and page numbers
            const group = [] as any as (CheckedPage[] & { startPageNumber: number; endPageNumber: number });

            // 添加起始页号和结束页号信息
            group.startPageNumber = startIdx + 1; // 页号从1开始
            group.endPageNumber = endIdx + 1;     // 页号从1开始

            if (firstPage) {
                group.push(firstPage);
            }

            // Add second page if it exists and is different from first page
            if (lastPage && startIdx !== endIdx) {
                group.push(lastPage);
            } else if (startIdx !== endIdx) {
                // Add empty page if needed to maintain the pair structure
                group.push({
                    url: '',
                    checked: false
                } as CheckedPage);
            }

            // Only add non-empty groups
            if (group.length > 0) {
                groups.push(group);
            }
        }
    }
    return groups;
});
const startAndEndPages = computed(() => {
	console.log(props.startPage, props.endPage);
	const startAndEndPages: { startPage: CheckedPage, endPage: CheckedPage }[] = [];
	const start = props.startPage ? props?.startPage - 1 : 0;
	const end = props.endPage ? props?.endPage - 1 : props.pages.length - 1;
	if (props.splitType === 1) {
		startAndEndPages.push({ startPage: props.pages[start], endPage: props.pages[end] });
	}
	return startAndEndPages;
});
</script>
