<template>
	<div class="translate-options">
		<n-cascader v-model:value="selectedOptions" show-path placeholder="请选择翻译选项" check-strategy="child" :cascade="true"
			:options="options" @update:value="handleOptionsChange" />
	</div>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps, watch } from 'vue'
import { NCascader } from 'naive-ui'
import type { CascaderOption } from 'naive-ui'

const props = defineProps({
	defaultTransType: {
		type: String,
		default: 'trans_text'
	},
	defaultTransOutType: {
		type: String,
		default: 'only'
	},
	defaultTransLayout: {
		type: String,
		default: 'new'
	}
})

const emit = defineEmits(['update:transType', 'update:transOutType', 'update:transLayout'])

// 嵌套的级联选择器选项
const options = ref<CascaderOption[]>([
	{
		label: '仅文字部分',
		value: 'trans_text',
		children: [
			{
				label: '仅译文',
				value: 'only2',
				children: [
					{
						label: '重排版面',
						value: 'new2'
					},
					{
						label: '继承原版面',
						value: 'inherit2'
					}
				]
			},
			{
				label: '原文+译文',
				value: 'both1',
				children: [
					{
						label: '重排版面',
						value: 'new1'
					},
					{
						label: '继承原版面',
						value: 'inherit1'
					}
				]
			}
		]
	},
	{
		label: '全部内容',
		value: 'trans_all',
		children: [
			{
				label: '仅译文',
				value: 'only3',
				children: [
					{
						label: '重排版面',
						value: 'new3'
					},
					{
						label: '继承原版面',
						value: 'inherit3'
					}
				]
			},
			{
				label: '原文+译文',
				value: 'both4',
				children: [
					{
						label: '重排版面',
						value: 'new4'
					},
					{
						label: '继承原版面',
						value: 'inherit4'
					}
				]
			}
		]
	}
])

// 选中的值
const selectedOptions = ref<string[]>([
	props.defaultTransType,
	props.defaultTransOutType,
	props.defaultTransLayout
])

// 根据子节点值查找完整路径
function findPathByLeaf(options: CascaderOption[], leafValue: string): string[] | null {
	// 遍历所有一级选项
	for (const level1 of options) {
		if (!level1.children) continue

		// 遍历所有二级选项
		for (const level2 of level1.children) {
			if (!level2.children) continue

			// 遍历所有三级选项
			for (const level3 of level2.children) {
				// 找到匹配的叶子节点
				if (level3.value === leafValue) {
					return [String(level1.value), String(level2.value), String(level3.value)]
				}
			}
		}
	}

	return null
}

// 处理选项变化
const handleOptionsChange = (value: string) => {
	const fullPath = findPathByLeaf(options.value, value) as string[]
	const [transType, transOutTypeWithNum, transLayoutWithNum] = fullPath
	const transOutType = transOutTypeWithNum.replace(/\d+$/, '')
	const transLayout = transLayoutWithNum.replace(/\d+$/, '')

	emit('update:transType', transType)
	emit('update:transOutType', transOutType)
	emit('update:transLayout', transLayout)
}

</script>

<style lang="less" scoped>
.translate-options {
	width: 223px;

	:deep(.n-base-selection) {
		--n-height: 45px !important;
		--n-menu-height: 69px !important;
		width: 223px !important;
	}
}

/* 使用全局样式，提高优先级 */
:global(.v-binder-follower-content) {
	height: 100px !important;
}

:global(.n-cascader-menu) {
	min-width: 80px !important;
	--n-menu-height: 69px !important;
}

:global(.n-cascader-submenu) {
	--n-option-color-hover: #F1F6FF !important;
}

:global(.n-cascader-option--pending) {
	--n-option-text-color: #0060FF !important;
	color: #0060FF !important;
}

/* 添加内联样式 */
:deep(.n-cascader) {
	--n-menu-box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
	--n-menu-color: #fff !important;
	--n-menu-height: 69px !important;
	--n-option-font-size: 14px !important;
	--n-option-height: 34px !important;
	--n-option-text-color: #333 !important;
	--n-option-text-color-active: #0060FF !important;
	--n-option-text-color-disabled: #ccc !important;
	--n-option-text-color-hover: #0060FF !important;
	--n-option-color-hover: #F1F6FF !important;
}
</style>
