<template>
	<div class="flex flex-col items-center justify-between relative" @click="handleSelect">
		<NCheckbox v-if="checkable" :checked="checked" class="absolute top-[6px] left-[6px]"
			@update:checked="emit('update:checked', $event)" />
		<div class=" border border-[#DFDFDF] rounded-[10px] w-[116px] h-[116px] overflow-hidden cursor-pointer">
			<NImage :src="imgUrl" :style="{ width: width, height: width }"
				class="aspect-[1/1] w-full flex items-center justify-center" :object-fit="'cover'" preview-disabled />
		</div>
		<div class="flex items-center justify-center">
			<span class="text-[14px] leading-[16px] text-[#333333]">{{ label }}</span>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { NImage, NCheckbox } from 'naive-ui';
interface Props {
    checkable: boolean;
    checked?: boolean;
    width?: string;
    label?: string;
    imgUrl?: string;

}
interface Emits {
    (e: 'update:checked', checked: boolean): void;
}
const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const handleSelect = () => {
	emit('update:checked', !props?.checked)
}
</script>
