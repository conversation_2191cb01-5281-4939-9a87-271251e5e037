<template>
	<div class="top-container">
		<div class=" text-[18px] text-[#3D3D3D] leading-[24px]">{{ title }}</div>
		<div class=" text-[14px] text-[#FF6D6D] leading-[18px]">{{ subTitle }}</div>
	</div>
</template>

<script lang="ts" setup>
interface Props {
	title: string
	subTitle: string
}
defineProps<Props>()
</script>

<style lang="less" scoped>
.top-container {
	width: 100%;
	max-width: 1380px;
	height: 108px;
	aspect-ratio: 1380 / 108;
	border-radius: 8px;
	padding: 18px;
	display: flex;
	flex-direction: column;
	background: #fff;
	box-shadow: 12px 0px 20px 0px #DCE9FF;
	row-gap: 10px;
}
</style>
