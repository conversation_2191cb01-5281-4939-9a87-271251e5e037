<template>
	<div class="bottom-container">
		<div class="header" v-if="!hideTitle">
			<n-image :src="icon" width="48" height="48" preview-disabled />
			<div class="title-container">
				<div class="title">{{ title }}</div>
				<div class="subtitle">{{ subtitle }}</div>
			</div>
		</div>
		<div class="alternate-header" v-else>
			<slot name="alternate-header">
				<div class="alternate-content" v-if="alternateContent">{{ alternateContent }}</div>
			</slot>
		</div>
		<slot></slot>
		<div class="footer">
			<div class="footer-item" v-for="item in footer" v-show="!item.showAfterImport" :key="item.label"
				:class="[item.className, { 'disabled': item.disabled }]" @click="item.onClick"
				:style="{ width: item.width, height: item.height }">
				<!-- <n-icon :size="20" :component="item.icon" /> -->
				<n-icon :component="item.icon" width="16" height="16" preview-disabled />
				<div class="footer-item-label">{{ item.label }}</div>
			</div>
		</div>
	</div>
</template>
<script setup lang="ts">
import { defineProps } from 'vue';
import { NImage, NIcon } from 'naive-ui';
interface Props {
	title: string;
	subtitle: string;
	icon: string;
	footer: any[];
	hideTitle?: boolean;
	alternateContent?: string;
}

defineProps<Props>();

</script>
<style lang="less" scoped>
.bottom-container {
	width: 100%;
	max-width: 1380px;
	height: 100%;
	aspect-ratio: 1380 /554;
	border-radius: 10px;
	padding: 30px 30px 40px 30px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	background: #fff;
	box-shadow: 12px 0px 20px 0px #DCE9FF;
	row-gap: 18px;

	.header {
		display: flex;
		flex-direction: row;
		align-items: center;
		column-gap: 11px;

		.title-container {
			display: flex;
			flex-direction: column;

			.title {
				font-size: 20px;
				line-height: 26px;
				font-weight: 600;
				color: #333333;
			}

			.subtitle {
				font-size: 14px;
				line-height: 18px;
				color: #999999;
			}
		}
	}

	.alternate-header {
		display: flex;
		flex-direction: row;
		align-items: center;
		margin-bottom: 10px;

		.alternate-content {
			font-size: 16px;
			line-height: 22px;
			color: #333333;
		}
	}

	.footer {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-end;
		column-gap: 12px;

		.footer-item {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			column-gap: 4px;
			cursor: pointer;
		}

		.disabled {
			cursor: not-allowed;
			opacity: 0.5;
		}
	}
}
</style>
