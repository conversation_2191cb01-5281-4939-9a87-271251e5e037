<template>
	<div class="left-container">
		<div class="header" v-if="!hideTitle">
			<n-image :src="icon" width="48" height="48" preview-disabled />
			<div class="title-container">
				<div class="title">{{ title }}</div>
				<div class="subtitle">{{ subtitle }}</div>
			</div>
		</div>
		<slot></slot>
		<div class="footer">
			<div class="footer-item" v-for="item in footer" v-show="!item.showAfterImport" :key="item.label"
				:class="[item.className, { 'disabled': item.disabled }]" @click="handleFooterClick(item)"
				:style="{ width: item.width, height: item.height }">
				<!-- <n-icon :size="20" :component="item.icon" /> -->
				<n-icon :component="item.icon" width="16" height="16" preview-disabled />
				<div class="footer-item-label">{{ item.label }}</div>
			</div>
		</div>
	</div>
</template>
<script setup lang="ts">
import { defineProps } from 'vue';
import { NImage, NIcon } from 'naive-ui';
interface Props {
	title: string;
	subtitle: string;
	icon: string;
	footer: any[]
	hideTitle?: boolean
}

defineProps<Props>();

const handleFooterClick = (item: any) => {
	if (item.disabled) return;
	item.onClick();
};
</script>
<style lang="less" scoped>
.left-container {
	width: 1030px;
	height: 100%;
	height: 640px;
	aspect-ratio: 1030 / 640;
	border-radius: 10px;
	padding: 30px 30px 40px 30px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	background: #fff;
	box-shadow: 12px 0px 20px 0px #DCE9FF;
	row-gap: 26px;

	.header {
		display: flex;
		flex-direction: row;
		align-items: center;
		column-gap: 11px;

		.title-container {
			display: flex;
			flex-direction: column;

			.title {
				font-size: 20px;
				line-height: 26px;
				font-weight: 600;
				color: #333333;
			}

			.subtitle {
				font-size: 14px;
				line-height: 18px;
				color: #999999;
			}
		}
	}

	.footer {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		column-gap: 12px;

		.footer-item {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			column-gap: 4px;
			cursor: pointer;
		}

		.disabled {
			cursor: not-allowed;
			opacity: 0.5;
		}
	}
}
</style>
