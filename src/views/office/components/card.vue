<template>
    <div class="card hover-shadow" :class="[`${grid} ${background}`]" :style="backgroundImage ? { backgroundImage: `url(${backgroundImage})`, backgroundSize: 'cover', backgroundPosition: 'center' } : {}">
        <div class="flex flex-col items-start h-full">
            <div class="badge" :class="badgeBg">
                {{ category }}
            </div>
            <div class="flex flex-row">
                <div class="card-content flex-1">
                    <span class=" text-[20px] leading-[27px] font-semibold">{{ title }}</span>
                    <span class=" text-[12px] leading-[16px] text-[#676767]">{{ subtitle }}</span>
                </div>
            </div>
        </div>
        <n-image :src="icon" alt="icon" class="flex-1 !w-[104px] flex justify-end" width="100" preview-disabled/>
    </div>
</template>
<script setup lang="ts">
import { NImage } from 'naive-ui';
import { defineProps } from 'vue';
import { OfficeCard } from '../types';
defineProps<OfficeCard>();
</script>

<style lang="less" scoped>
.card {
    cursor: pointer;
    width: 100%;
    height: 126px;
    border-radius: 10px;
    padding: 0 16px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.card-content {
    margin-top: 8px;
    display: flex;
    flex-direction: column;
    gap: 2px;
    flex: 1;
}

.badge {
    margin-top: 14px;
    width: 58px;
    height: 22px;
    font-size: 12px;
    line-height: 22px;
    padding: 0 5px;
    text-align: center;
    border-radius: 4px;
    font-weight: 600px;
}

.hover-shadow {
    transition: all 0.3s ease-in-out;

    &:hover {
        box-shadow: rgb(38, 57, 77) 0px 20px 30px -10px;
        transform: translateY(-5px);
    }
}
</style>
