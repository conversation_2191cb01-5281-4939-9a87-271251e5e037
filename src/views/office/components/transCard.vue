<template>
    <div class="card-container">
        <NImage :src="icon" class="w-[80px] h-[80px] mx-auto mt-[20px]" preview-disabled/>
        <span class="text-[#333333] text-[22px] leading-[29px] text-center mt-[20px]">
            <span :class="`text-[${highlightColor}]`">{{ highlight }}</span>
            {{ title }}</span>
    </div>
</template>

<script setup lang="ts">
import { NImage } from 'naive-ui';
import { TransCard } from '../types';
interface Props extends TransCard {}


defineProps<Props>();

</script>

<style scoped lang="less">
.card-container {
    width: 33%;
    aspect-ratio: 330/246;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0px 0px 10px 0px #E8EFF6;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
        transition: all 0.3s ease-in-out;
    &:hover {
        box-shadow: rgb(38, 57, 77) 0px 20px 30px -10px;
            transform: translateY(-5px);
    }
}


</style>
