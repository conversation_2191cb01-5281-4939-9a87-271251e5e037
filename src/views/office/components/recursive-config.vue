<template>
	<div class="recursive-config">
		<div v-for="(item, index) in config" :key="index" class="config-item">
			<div class="config-header">
				<span v-if="item.type === 'header'" class=" text-[20px] text-[#333333] inline-block">
					{{ item.label }}
				</span>
				<div v-for="(i, index) in item.options" :key="index">
					<div v-if="i.type === 'buttons'" class=" mt-[20px] w-full flex flex-wrap rounded-[6px] gap-[20px]">
						<span
							class="text-[16px] w-[calc(50%-20px)] h-[56px] text-[#333333] flex items-center justify-center leading-[56px] cursor-pointer rounded-[6px] relative border-[#D8DDEE] border-[1px]"
							v-for="(j, idx) in getButtonOptions(i.label)" :key="idx"
							:class="{ 'checked': form[i.path!] === (typeof j === 'object' ? j.value : j) }"
							@click="handleButtonClick(i.path!, typeof j === 'object' ? j.value : j)">
							{{ typeof j === 'object' ? j.label : j }}
							<IconCheckOne v-if="form[i.path!] === (typeof j === 'object' ? j.value : j)"
								class="absolute top-0 left-0 bg-[#0060FF] text-white text-[14px] p-[4px] rounded-br-[6px] rounded-tl-[6px]" />
						</span>
					</div>
					<div v-else-if="i.type === 'text'">
						<span class="text-[14px] text-[#333333]" v-if="i.show">{{ i.label }}</span>
					</div>
					<div v-else-if="i.type === 'checkboxGroup'">
						<NFormItem :path="i.path">
							<NCheckboxGroup v-model:value="form[i.path!]" :max="1" @update:value="handleFormUpdate">
								<div v-for="(option, idx) in i.options" :key="idx" class="mb-4">
									<NCheckbox :value="option.value">
										{{ option.label }}
									</NCheckbox>
									<div v-if="option.children" class="mt-2">
										<template v-for="(child, childIdx) in option.children" :key="childIdx">
											<div v-if="child.type === 'inputGroup'" class="flex items-center gap-2">
												<template v-for="(item, itemIdx) in child.items" :key="itemIdx">
													<span v-if="item.type === 'text'" class="text-[14px] text-[#333333]">{{ item.content }}</span>
													<NFormItem :path="item.path!" :rule="getInputGroupRules(item.path!)"
														v-else-if="item.type === 'inputNumber'">
														<NInputNumber :disabled="!form[i.path!]?.includes(option.value)" :show-button="false"
															v-model:value="form[item.path!]" :placeholder="item.placeholder" class="w-[60px]"
															:max="item.pageMax" :min="item.pageMin" @update:value="handleFormUpdate"
															:default-value="item.defaultValue" />
													</NFormItem>
												</template>
											</div>
											<span class="text-[14px] text-[#333333]" v-if="child.show" v-html="child.content"></span>
										</template>
									</div>
								</div>
							</NCheckboxGroup>
						</NFormItem>
					</div>
					<div v-else-if="i.type === 'inputGroup'">
						<NFormItem :path="i.path">
							<span class="text-[14px] text-[#333333]" v-if="i.show">{{ i.label }}</span>
							<div v-for="(child, index) in i.items">
								<template v-if="child.type === 'inputNumber'">
									<NInputNumber v-model:value="form[child.path!]" :placeholder="child.placeholder" class="w-[60px]"
										:max="child.pageMax" :min="child.pageMin" @update:value="handleFormUpdate"
										:default-value="child.defaultValue" :show-button="child.showButton" :style="{ width: child.width }">
										<template #suffix v-if="child.suffix">
											{{ child.suffix }}
										</template>
									</NInputNumber>
								</template>
								<template v-else-if="child.type === 'text'">
									<span class="text-[14px] text-[#333333]">{{ i.content }}</span>
								</template>
							</div>
						</NFormItem>
					</div>
					<div v-else-if="i.type === 'slide'">
						<NFormItem :path="i.path">
							<NSlider v-model:value="form[i.path!]" :min="i.start" :max="i.end" :step="i.step" :marks="i.marks"
								:default-value="i.defaultValue" @update:value="handleFormUpdate" />
						</NFormItem>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { NFormItem, NCheckbox, NCheckboxGroup, NSlider, NInputNumber } from 'naive-ui';
import { onMounted, watch } from 'vue';
// define the component name so it can use self-recursion
defineOptions({
  name: 'RecursiveConfig'
});

interface ConfigItem {
	name: boolean;
  type: string;
  label?: string;
  options?: ConfigItem[];
  path?: string;
  value?: string;
  placeholder?: string;
  pageMin?: number;
  pageMax?: number;
  content?: string;
  items?: ConfigItem[];
  children?: ConfigItem[];
  defaultValue?: number;
  show?: boolean;
  start?: number;
  end?: number;
  step?: number;
  marks?: Record<number, string>;
  showButton?: boolean;
  suffix?: string;
  width?: string;
}
interface Props {
  config: ConfigItem[];
  form: Record<string, any>;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:form']);
const getInputGroupRules = (path: string) => {
  return {
    trigger: ['input', 'blur'],
    validator: (rule: any, value: number) => {
      if (path === 'startPage' || path === 'endPage') {
        const currentInputGroup = props.config
          .flatMap(item => item.options || [])
          .flatMap(option => option.options || [])
          .flatMap(opt => opt.children || [])
          .find(child =>
            child.type === 'inputGroup' &&
            child.items?.some(item => item.path === path)
          );

        if (currentInputGroup) {
          const numberInputs = currentInputGroup.items?.filter(item => item.type === 'inputNumber') || [];
          const currentIndex = numberInputs.findIndex(item => item.path === path);

          // For endPage, ensure it's not less than startPage
          if (path === 'endPage' && currentIndex > 0) {
            const startPageValue = props.form['startPage'];
            if (value < startPageValue) {
              return new Error(`结束页要大于开始页`);
            }
          } else if (path === 'startPage') {
            const prevValue = props.form[numberInputs[1].path!];
            if (value > prevValue) {
              return new Error(`开始页要小于结束页`);
            }
          }
        }
      }
      return true;
    }
  };
};
const handleFormUpdate = () => {
  emit('update:form', props.form);
};

// 处理按钮点击事件，更新表单值并触发表单更新
const handleButtonClick = (path: string, value: string) => {
  props.form[path] = value;
  handleFormUpdate();
};

// 获取按钮选项，支持string[]和{label:string,value:string}[]两种格式
const getButtonOptions = (label: any) => {
  if (Array.isArray(label)) {
    return label;
  } else if (typeof label === 'string') {
    return label.split(',');
  }
  return [];
};

// 初始化默认选中：只有当按钮选项只有1个时才自动选择它
const initDefaultSelection = () => {
  props.config.forEach(item => {
    if (item.options) {
      item.options.forEach(option => {
        if (option.type === 'buttons' && option.path) {
          const buttonOptions = getButtonOptions(option.label);
          // 只有当选项数量为1时才自动选择，无论是否已经有选中值
          if (buttonOptions.length === 1) {
            const firstOption = buttonOptions[0];
            props.form[option.path] = typeof firstOption === 'object' ? firstOption.value : firstOption;
            handleFormUpdate();
          }
        }
      });
    }
  });
};

// 组件挂载后初始化默认选中
onMounted(() => {
  initDefaultSelection();
});

// 监听配置变化，当选项动态变化时自动选中唯一选项
watch(() => props.config, (newConfig) => {
  initDefaultSelection();
}, { deep: true });
</script>

<style lang="less" scoped>
.config-item {
  margin-left: 10px;
  margin-bottom: 10px;
  padding: 5px;
}

.config-header {
  margin-bottom: 20px;
}

.child-config {
  margin-left: 20px;
}

.checked {
  border: 1px solid #0060FF;

  &::before {}
}
</style>
