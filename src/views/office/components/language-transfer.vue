<template>
	<div class="language-transfer">
		<n-select class=" !w-[142px]" v-model:value="sourceLang" :options="languageOptions" placeholder="选择源语言"
			style="--n-height: 45px !important;" @update:value="handleSourceLangChange" />
		<div class="cursor-pointer bg-[#A5A5A5] rounded-[50%] text-[#fff] p-[3px]" @click="handleTransfer">
			<IconTransferData />
		</div>
		<n-select class=" !w-[142px]" v-model:value="targetLang" :options="languageOptions" placeholder="选择目标语言"
			@update:value="handleTargetLangChange" />
	</div>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps, watch } from 'vue'
import { NSelect, NIcon } from 'naive-ui'
import type { SelectOption } from 'naive-ui'

const props = defineProps({
	defaultSourceLang: {
		type: String,
		default: '中文'
	},
	defaultTargetLang: {
		type: String,
		default: '英语'
	}
})

const emit = defineEmits(['update:sourceLang', 'update:targetLang'])

// 语言选项
const languageOptions = ref<SelectOption[]>([
	{
		label: '中文',
		value: '中文'
	},
	{
		label: '英语',
		value: '英语'
	},
	{
		label: '日语',
		value: '日语'
	},
	{
		label: '西班牙语',
		value: '西班牙语'
	},
	{
		label: '俄语',
		value: '俄语'
	},
	{
		label: '阿拉伯语',
		value: '阿拉伯语'
	}
])

// 选中的值
const sourceLang = ref(props.defaultSourceLang)
const targetLang = ref(props.defaultTargetLang)

// 监听值变化
watch(sourceLang, (newValue) => {
	emit('update:sourceLang', newValue)
})

watch(targetLang, (newValue) => {
	emit('update:targetLang', newValue)
})

// 处理值变化
const handleSourceLangChange = (value: string) => {
	emit('update:sourceLang', value)
}

const handleTargetLangChange = (value: string) => {
	emit('update:targetLang', value)
}

// 处理语言交换
const handleTransfer = () => {
	const temp = sourceLang.value
	sourceLang.value = targetLang.value
	targetLang.value = temp

	emit('update:sourceLang', sourceLang.value)
	emit('update:targetLang', targetLang.value)
}
</script>

<style lang="less" scoped>
.language-transfer {
	display: flex;
	align-items: center;
	gap: 16px;
	width: 100%;
}

:global(.n-base-selection) {
	--n-height: 45px !important;
}
</style>
