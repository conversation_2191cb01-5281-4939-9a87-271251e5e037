<template>
	<div class="right-container">
		<NForm :model="form">
			<RecursiveConfig :config="config?.config" :form="form" @update:form="handleUpdateForm" />
		</NForm>
	</div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import RecursiveConfig from './recursive-config.vue';
import { NForm } from 'naive-ui';
import { OfficeType } from '../types';
interface ConfigItem {
	type: string;
	label: string;
	options?: ConfigItem[];
	path?: string;
	value?: string;
}

interface Props {
	config: any;
	endPage?: number;
}
interface Emits {
	(e: 'update:form', form: any): void;
}
const props = defineProps<Props>();
const isInitialized = ref(false);



const emit = defineEmits<Emits>();
const form = ref({});
const value = ref(0);
const handleUpdateForm = (form: any) => {
	emit('update:form', form);
}

watch([() => props.config, () => props.endPage], ([newConfig, newEndPage]) => {
	if (newConfig?.officeType === OfficeType.PDFSPLIT) {
		if (!isInitialized.value && newEndPage !== 1) {
			// 首次初始化
			form.value = {
				splitType: [3],
				startPage: 1,
				endPage: newEndPage
			};
			isInitialized.value = true;
		}
		// 每次更新endPage
		form.value.endPage = newEndPage;
	}
}, { immediate: true, deep: true });

watch(() => form.value, (newForm) => {
	console.log('form value changed:', newForm);
}, { deep: true });


</script>
<style lang="less" scoped>
.right-container {
	width: 338px;
	height: 640px;
	aspect-ratio: 338 / 640;
	background: #fff;
	border-radius: 10px;
	padding: 30px 18px 0 18px;
	box-shadow: 12px 0px 20px 0px #DCE9FF;
}
</style>
