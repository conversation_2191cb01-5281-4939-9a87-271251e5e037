<template>
    <div class="flex flex-col items-start pt-[20px] px-[20px] bg-white relative top-[1px] h-[99%]">
        <div class="text-[14px] leading-[18px] text-[#666666] mb-[20px]">全部文件({{ fileList?.length }})</div>
        <div class="flex flex-row items-start justify-start gap-[10px]">
            <div v-for="(file, index) in fileList" :key="index"
                class="flex items-center justify-between w-full mt-[20px]">
                <UploadItem :file="file" :is-border-dashed="props.isBorderDashed" @delete="handleDelete" />
            </div>
            <div class="file-operate-area flex justify-between" v-if="showOptions">
                <div class=" cursor-pointer text-blue-500 w-[180px] aspect-[230/62] flex items-center justify-center gap-x-[5px] border-[1px] border-[#0060FF] rounded-[10px]"
                    @click="addFiles">
                    <IconSelect class="w-[16px] h-[16px]" />
                    <span class="text-[16px] leading-[16px] text-[#0c1017]">添加本地PDF文件</span>
                </div>
                <div class="cursor-pointer text-[#fff] w-[180px] aspect-[230/62] flex items-center justify-center bg-gradient-to-r from-[#009DFF] to-[#7E4AFF]  rounded-[10px]"
                    @click="convertFiles">立即转换</div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { UploadFileInfo } from 'naive-ui';
import { computed } from 'vue';
import UploadItem from './uploadItem.vue';
interface Props {
    fileList: UploadFileInfo[];
    isBorderDashed: boolean;
    showOptions: boolean;
    value: UploadFileInfo[]
}
interface Emits {
    (e: 'update:value', value: UploadFileInfo[]): void
}
const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const fileList = computed({
    get: () => props.value || [],
    set: (files) => emit('update:value', files)
});

const handleDelete = (file: UploadFileInfo) => {
    emit('update:value', fileList.value.filter(f => f.name !== file.name));
}

const addFiles = () => {
    // Logic to add files
    console.log("Add files clicked");
}

const convertFiles = () => {
    // Logic to convert files
    console.log("Convert files clicked");
}
</script>

<style scoped lang="less"></style>
