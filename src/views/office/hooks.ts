import { UploadFileInfo, useMessage } from "naive-ui";
import { Ref, ref } from "vue";
import { useRequest } from "vue-hooks-plus";
import { uploadOfficeFile } from "./apis";
import { OfficeType, officeTypeMap } from "./types";
type UploadFileResult = {
	url: string;
	name: string;
};
// 上传文件
export const useUploadFile = (
	limit: number,
	{
		onSuccess,
		onError,
	}: {
		onSuccess?: (files: UploadFileResult[]) => void;
		onError?: (error: string) => void;
	}
): {
	run: (fileList: UploadFileInfo[]) => Promise<void>;
	loading: Ref<boolean>;
	progress: Ref<number>;
	data: Ref<UploadFileResult[]>;
} => {
	const uploadedFileList = ref<UploadFileResult[]>([]);
	const fileUploading = ref<boolean>(false);
	const progressRef = ref<number>(0);
	const totalFiles = ref<number>(0);
	const singleFileUpload = async (file: UploadFileInfo) => {
		return uploadOfficeFile<any>({
			// 添加 return
			file: file.file!,
			fileName: file.name,
			fileType: 3,
			onUploadProgress: ({ progress: fileProgress }) => {
				progressRef.value =
					(progressRef.value * (totalFiles.value - 1) + fileProgress) /
					totalFiles.value;
			},
		})
			.then((data) => {
				const result = {
					url: data.url,
					name: file.name,
				};
				uploadedFileList.value.push(result);
				return result; // 返回上传结果
			})
			.catch((e) => {
				const errorMsg = e?.errmsg || "上传失败";
				fileUploading.value = false;
				onError?.(errorMsg);
				throw e; // 重新抛出错误，确保 Promise.all 能捕获到
			});
	};

	// 批量上传 Promise.all
	const run = async (fileList: UploadFileInfo[]) => {
		if (fileList.length > limit) {
			onError?.(`文件数量不能超过${limit}个`);
			return;
		}
		fileUploading.value = true;
		totalFiles.value = fileList.length;
		progressRef.value = 0; // 重置进度
		try {
			const result = await Promise.all(
				fileList.map((file) => singleFileUpload(file))
			);
			fileUploading.value = false;
			onSuccess?.(result);
		} catch (error) {
			fileUploading.value = false;
			progressRef.value = 0;
		}
	};

	return {
		run,
		loading: fileUploading,
		progress: progressRef,
		data: uploadedFileList,
	};
};


// AI工具请求
// 不同的工具对应不同的接口
export const useAITools = (
	params: { type: OfficeType } & Record<string, any> & {
		onSuccess?: (data: any) => void;
		onError?: (error: any) => void;
		[key: string]: any;
	}
) => {
	const { onSuccess, onError } = params;
	const refresh = ref<(() => void) | null>(null);
	const run = (query: Record<string, any>) => {
		const { type } = query;
		const api = officeTypeMap[type];
		const { run: runAI, refresh: re } = useRequest(api, {
			manual: true,
			onSuccess,
			onError,
			...params,
		});
		runAI(query);
		refresh.value = re;
	};

	const refreshFunction = () => {
		if (refresh.value) {
			refresh.value();
		}
	};

	return { run, refresh: refreshFunction };
};


