<template>
	<div class="flex flex-col items-center py-[33px] gap-y-[14px] history-container">
		<div
			class=" leading-[45px] font-bold text-[34px] font-['Microsoft_YaHei'] leading-normal tracking-normal bg-gradient-to-r from-[#009DFF] to-[#8800FF] bg-clip-text text-transparent">
			创作记录</div>
		<div
			class=" leading-[24px] opacity-100 font-['Microsoft_YaHei'] text-[18px] font-normal leading-normal tracking-[0em] text-[#676767] mb-[24px]">
			实时留存方便回顾，提升工作和学习效率的得力助手
		</div>
		<div
			class=" shadow-[12px_0px_20px_0px_#DCE9FF] bg-[#fff] 3xl:w-[1470px] 2xl:w-[1346px] xl:w-[980px] lg:w-[724px] md:w-[568px] flex flex-col rounded-[10px]">
			<NDataTable :row-key="(row) => row.taskId" :columns="columns" :data="pageList" :loading="loading"
				:bottom-bordered="false" v-model:checked-row-keys="selectedRowKeys"
				:check-strategy="checkStrategy"
				style="
				--n-merged-th-color: #fff;
				--n-merged-border-color:#E3EDFF;
				--n-th-text-color:#676767;
				--n-border-color:#fff;
				--n-line-height:62px;
				" class="w-full !bg-white rounded-[10px]" />
			<div class="flex justify-end mt-[28px] mb-[34px] mr-[34px]">
				<NPagination :page="queryParams.pageNum" :page-size="queryParams.pageSize" :item-count="total"
					@update:page="queryParams.pageNum = $event" />
			</div>
			<div class=" flex justify-end mb-[34px] mr-[34px]">
				<div
					:class="['flex justify-center w-[230px] h-[62px] rounded-[10px] opacity-100', selectedRowKeys.length ? 'bg-gradient-to-r from-[#009DFF] to-[#7E4AFF] cursor-pointer' : 'bg-[#ACB2C4] cursor-not-allowed']"
					@click="handleBatchDownload">
					<span class="text-[#fff] leading-[62px]">批量下载</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { NDataTable, NEllipsis, useMessage, NPopconfirm, NPagination } from 'naive-ui'
import { useRequest } from 'vue-hooks-plus'
import { batchDeleteFileTask, fetchOfficeFile } from './apis'
import { h, ref, watch, onMounted, onBeforeUnmount, computed } from 'vue'

import PDFIcon from '@/assets/office/pdf-icon.png'
import PPTIcon from '@/assets/office/ppt-icon.png'
import WordIcon from '@/assets/office/word-icon.png'
import ExcelIcon from '@/assets/office/excel-icon.png'
import ImageIcon from '@/assets/office/image-icon.png'
import SuccessImg from '@/assets/images/success.png'
import FailureImg from '@/assets/images/failure.png'

import { icons } from '@/plugins/icons'
const { IconDownload, IconDelete, IconCopy } = icons

import dayjs from 'dayjs'
import { HistoryResult, ToolType } from './types'
import { isNumber } from 'lodash'

const message = useMessage()
const queryParams = ref({
	pageNum: 1,
	pageSize: 5
})
const pageList = ref<HistoryResult[]>([])
const selectedRowKeys = ref<string[]>([])

const total = ref(0)
const pollingTimer = ref<number | null>(null)
const needPolling = ref(false)

type OfficeFile = {
	pageNum: number,
	pageSize: number,
	totalCount: number,
	pageList: HistoryResult[]
}

const toolStatus = (row: any) => {
	console.log('row.convertType', row.convertType)
  // 翻译工具
	if (row.convertType === 'DOUBAO_TRANS') {
    return {
      SUCCESS: h('div', { class: 'flex items-center' }, [
        h('img', { src: SuccessImg, class: 'mr-1', width: 16, height: 16 }),
        '翻译成功'
      ]),
      IN_PROGRESS: h('div', { class: 'flex items-center text-[#ACB2C4]' }, '正在翻译...'),
      SUBMITTED: h('div', { class: 'flex items-center text-[#ACB2C4]' }, '正在翻译...'),
      FAILURE: h('div', { class: 'flex items-center' }, [
        h('img', { src: FailureImg, class: 'mr-1', width: 16, height: 16 }),
        '翻译失败'
      ]),
    }
  }
  // 密码破解工具
	else if (row.convertType === 'DOC_DECODE_TASK') {
    return {
      SUCCESS: h('div', { class: 'flex items-center' }, [
        h('img', { src: SuccessImg, class: 'mr-1', width: 16, height: 16 }),
        '找回成功'
      ]),
      IN_PROGRESS: h('div', { class: 'flex items-center text-[#ACB2C4]' }, '正在找回...'),
      SUBMITTED: h('div', { class: 'flex items-center text-[#ACB2C4]' }, '正在找回...'),
      FAILURE: h('div', { class: 'flex items-center' }, [
        h('img', { src: FailureImg, class: 'mr-1', width: 16, height: 16 }),
        '找回失败'
      ]),
    }
  }
  // 其他工具保持原样
  else {
    return {
      SUCCESS: h('div', { class: 'flex items-center' }, [
        h('img', { src: SuccessImg, class: 'mr-1', width: 16, height: 16 }),
        '转换完成'
      ]),
      IN_PROGRESS: h('div', { class: 'flex items-center text-[#ACB2C4]' }, '正在转换...'),
      SUBMITTED: h('div', { class: 'flex items-center text-[#ACB2C4]' }, '正在翻译...'),
      FAILURE: h('div', { class: 'flex items-center' }, [
        h('img', { src: FailureImg, class: 'mr-1', width: 16, height: 16 }),
        '转换失败'
      ]),
    }
  }
}

const { run, loading } = useRequest(() => fetchOfficeFile<OfficeFile>(queryParams.value), {
	refreshDeps: [queryParams],
	onSuccess(res) {
		pageList.value = res.pageList
		total.value = res.totalCount

		// 检查是否需要轮询
		checkNeedPolling()
	}
})

// 检查是否需要轮询
const checkNeedPolling = () => {
	// 检查当前页面是否有处理中的任务
	const hasProcessingTasks = pageList.value.some(item =>
		item.status === 'IN_PROGRESS' || item.status === 'SUBMITTED' || item.status === 'IN_PROGRESS_2'
	)

	needPolling.value = hasProcessingTasks

	// 根据需要启动或停止轮询
	if (hasProcessingTasks) {
		startPolling()
	} else {
		stopPolling()
	}
}

// 开始轮询
const startPolling = () => {
	if (pollingTimer.value === null) {
		pollingTimer.value = window.setInterval(() => {
			run()
		}, 5000) // 每5秒轮询一次
	}
}

// 停止轮询
const stopPolling = () => {
	if (pollingTimer.value !== null) {
		window.clearInterval(pollingTimer.value)
		pollingTimer.value = null
	}
}

// 组件挂载时检查是否需要轮询
onMounted(() => {
	checkNeedPolling()
})

// 组件卸载前清除轮询定时器
onBeforeUnmount(() => {
	stopPolling()
})

const { loading: batchLoading, run: batchRun } = useRequest(batchDeleteFileTask, {
	manual: true,
	onSuccess(data) {
		message.success('删除成功')
		// 如果当页没有内容 请求上一页的
		if (pageList?.value?.length === 1 && queryParams?.value?.pageNum > 1) {
			queryParams.value.pageNum = queryParams?.value?.pageNum - 1
		}
		run()
	}
})

watch(() => pageList.value, (newVal) => {
	console.log(newVal);
	// 当列表数据变化时，重新检查是否需要轮询
	checkNeedPolling()
})

const handleDownload = (row: any) => {
	const a = document.createElement('a')
	a.href = row.outputUrl
	a.download = row.outputName
	document.body.appendChild(a)
	a.click()
	document.body.removeChild(a)
}

const handleDelete = (row: any) => {
	batchRun({
		taskIdList: [row.taskId]
	})
	// 从选中的行中移除被删除的行
	selectedRowKeys.value = selectedRowKeys.value.filter(key => key !== row.taskId)
}

const fileSize = (size: number | string) => {
	if (isNumber(size)) {
		if (size < 1024) {
			return `${size}B`
		} else if (size < 1024 * 1024) {
			return `${(size / 1024).toFixed(2)}KB`
		} else {
			return `${(size / 1024 / 1024).toFixed(2)}MB`
		}
	} else {
		return size
	}
}

const handleBatchDownload = () => {
	if (!selectedRowKeys.value.length) {
		message.warning('请先选择要下载的文件')
		return
	}

	// 过滤出可以下载的文件（状态为SUCCESS且不是密码破解任务）
	const downloadList = pageList.value.filter(item =>
		selectedRowKeys.value.includes(item.taskId) &&
		item.status === 'SUCCESS' &&
		item.convertType !== 'DOC_DECODE_TASK'
	)

	if (downloadList.length === 0) {
		message.warning('所选文件中没有可下载的文件')
		return
	}

	// 批量下载所有选中的文件
	downloadList.forEach(item => {
		if (item.outputUrl) {
			const a = document.createElement('a')
			a.href = item.outputUrl
			a.download = item.outputName
			document.body.appendChild(a)
			a.click()
			document.body.removeChild(a)
		}
	})

	// 下载完成后清空选择
	selectedRowKeys.value = []
	message.success('批量下载已开始')
}

// 计算可选的行
const selectableRows = computed(() => {
  return pageList.value.filter(row =>
    !(row.status === 'IN_PROGRESS' ||
      row.status === 'SUBMITTED' ||
      row.status === 'FAILURE' ||
      row.status === 'FAILURE_2' ||
      row.status === 'IN_PROGRESS_2' ||
      row.convertType === 'DOC_DECODE_TASK')
  ).map(row => row.taskId);
});

// 设置检查策略，允许显示中间状态
const checkStrategy = 'all';

const columns = ref([
	{
		type: 'selection',
		width: '5%',
		disabled: (row: any) => row.status === 'IN_PROGRESS' || row.status === 'SUBMITTED' || row.status === 'FAILURE' || row.status === 'FAILURE_2' || row.status === 'IN_PROGRESS_2' || row.convertType === 'DOC_DECODE_TASK'
	},
	{
		title: '文件名称',
		key: 'inputName',
		width: '30%',
		render: (row: any) => {
			// 判断文件类型
			const fileIcon = {
				'pdf': PDFIcon,
				'ppt': PPTIcon,
				'pptx': PPTIcon,
				'docx': WordIcon,
				'doc': WordIcon,
				'xls': ExcelIcon,
				'xlsx': ExcelIcon,
				'png': ImageIcon,
				'jpg': ImageIcon,
				'jpeg': ImageIcon,
				'gif': ImageIcon
			}[row.inputFormat] || ImageIcon;

			return h('div', { class: 'flex items-center gap-2' }, [
				h('img', { src: fileIcon, class: 'w-5 h-5' }),
				h(NEllipsis, { style: 'max-width: 200px' }, () => row.inputName)
			]);
		}
	},
	{
		title: '工具名称',
		key: 'convertType',
		width: '15%',
		render: (row) => {
			return ToolType[row.convertType]
		}
	},
	{
		title: '文件状态',
		key: 'status',
		width: '10%',
		render: (row) => {
			return toolStatus(row)[row.status]
		}
	},
	{
		title: '文件大小',
		key: 'outputSize',
		width: '10%',
		render: (row: any) => {
			return row.outputSize ? fileSize(row.outputSize) : '-'
		}
	},
	{
		title: '转换时间',
		key: 'finishTime',
		width: '15%',
		render: (row) => {
			return row.finishTime ? h(NEllipsis, {}, dayjs(row.finishTime).format('YYYY-MM-DD HH:mm:ss')) : '-'
		}
	},
	{
		title: '操作',
		width: '15%',
		render: (row) => {
			return h(
				'div',
				{
					class: 'flex items-center gap-x-[30px]'
				},
				[
					// 密码找回工具显示复制按钮
					row.convertType === 'DOC_DECODE_TASK' && row.status === 'SUCCESS' ? h(
						'div',
						{
							class: 'cursor-pointer text-black hover:text-[#178FFF] transition-colors',
							onClick: () => {
								// 创建一个临时的文本区域
								const textArea = document.createElement('textarea');
								textArea.value = row.outputName;
								document.body.appendChild(textArea);
								textArea.select();
								document.execCommand('copy');
								document.body.removeChild(textArea);
								message.success('密码已复制到剪贴板');
							}
						},
						h(IconCopy)
					) :
					// 其他工具正常显示下载按钮
					row.status === 'SUCCESS' ? h(
						'div',
						{
							class: 'cursor-pointer text-black hover:text-[#178FFF] transition-colors',
							onClick: () => handleDownload(row)
						},
						h(IconDownload)
					) : h('span', { style: 'width: 14px;inline-block' }, '-'),
					h(NPopconfirm, {
						onPositiveClick: () => handleDelete(row),
					}, {
						trigger: () => h('div', {
							class: 'cursor-pointer text-[#FF4D4D]',
						}, h(IconDelete)),
						default: () => '确定要删除这个文件吗？'
					})
				]
			)
		}
	}
])
</script>

<style lang="less" scoped>
.history-container {
	width: 100%;
	background: linear-gradient(to bottom, #f5f7ff, rgba(236, 241, 255, 0.8858), #e5f1ff);
	margin: 0 auto;
	padding: 50px 0;
	background-size: cover;
	background-position: center;
	background-repeat: repeat;
}

// :deep(.n-scrollbar) {
// 	height: 63px;
// 	> :deep(.n-scrollbar-container){
// 		height: 63px;
// 	}
// }
:deep(.n-data-table-table) {
	height: 63px !important;
}

:deep(.n-data-table-empty) {
	border-color: unset !important;
}
</style>
