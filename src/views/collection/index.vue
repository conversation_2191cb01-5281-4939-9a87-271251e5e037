<script setup lang="ts">
import { useMessage } from 'naive-ui';
import { getCollectedCreatebotList, unCollectApp } from '@/chatgpt';
import Card from '@/components/common/Card/index.vue'
import { useRequest } from 'vue-hooks-plus';
import { router } from '@/router';
import useSkeletonCardWidth from '@/hooks/useSkeletonCardWidth';
import { onMounted, ref } from 'vue';
const message = useMessage()
interface Createbot {
    id: number;
    name: string;
    profile: string;
    href: null;
    welcome_message: string;
    hot_num: number;
    type: string;
    free: boolean;
    createdAt: string;
    categoryId: number;
}
const { width, setWidth } = useSkeletonCardWidth();
const containerRef = ref<HTMLElement | null>(null)
onMounted(() => {
    setWidth(containerRef.value!.clientWidth);
})

const { data, run: runGetCollectedCreatebotList, loading } = useRequest<Createbot[]>(getCollectedCreatebotList)
const { run: runUncollectApp } = useRequest<{ status: boolean }>(unCollectApp, {
    manual: true,
    onSuccess: (data) => {
        if (data?.status) {
            message.success('取消收藏成功')
            runGetCollectedCreatebotList()
        } else {
            message.error('取消收藏失败')
        }
    }
})
const handleDeleteCard = ({ id }: any) => {
    // console.log(item)
    runUncollectApp({
        id
    })
}
const handleRedirect = () => {
    router.push('/')
}
</script>
<template>
    <div
        class="w-full !h-full overflow-y-scroll bg-gradient-to-b from-[#f5f7ff] via-[rgba(236, 241, 255, 0.8858)] to-[#e5f1ff] pt-[110px] flex flex-col items-center">
        <section
            class="3xl:w-[1470px] 2xl:w-[1270px] xl:w-[980px] lg:w-[724px] md:w-[568px] mx-[25px] flex flex-col gap-y-[47px] h-full">
            <span class=" text-[20px] text-[#3d3d3d] leading-[26px] font-bold">收藏夹</span>
            <div class=" items-center 3xl:w-[1470px] 2xl:w-[1270px] xl:w-[980px] lg:w-[724px] md:w-[568px] mx-[25px]"
                v-if="data?.length">
                <Transition appear name="box">
                    <div class=" flex flex-row flex-wrap gap-x-[24px] gap-y-[24px] w-full" v-if="!loading"
                        ref="containerRef">
                        <Card v-for="item in data" :key="item.id" :count="item.hot_num" :title="item.name"
                            :description="item.welcome_message" :img-src="item.profile" :id="item.id" :type="item.type"
                            :category-id="item.categoryId" :show-un-collect="true" @delete-card="handleDeleteCard"
                            :free="item.free" :width="width" />
                    </div>
                </Transition>
            </div>
            <div v-else class=" flex flex-col w-full h-full justify-center items-center gap-y-[23px]" ref="containerRef">
                <span class=" text-[14px] text-[#787878] ">您还没有收藏应用，快去体验下吧-</span>
                <div class=" inline-block bg-[#0066FE] h-[60px] rounded-[4px] px-[33px] leading-[60px] text-[#FFFFFF] text-[16px] cursor-pointer"
                    @click="handleRedirect">
                    立即前往</div>
            </div>
        </section>
    </div>
</template>

<style lang="less" scoped>
.box-enter-active,
.box-leave-active {
    transition: opacity 0.5s ease;
}

.box-enter-from,
.box-leave-to {
    opacity: 0;
}
</style>
