import { defineStore } from "pinia";
import { PaperAigcPlatform, PaperRepeatResult, UploadStatus } from "../types";
import {
	fetchPaperAigcCheckList,
	createPaperAigcCheckOrder,
	fetchPaperAigcPrice,
	fetchPaperRepeatUrl,
	fetchPaperAigcPlatforms,
} from "../api";
import { useAuthStore } from "@/store";
import { useMessage } from "naive-ui";
import { useRouter } from "vue-router";

export const useLowerAIGCRate = defineStore(
	"lowerAIGCRate",
	{
		state: () => ({
			// 状态
			platforms: [] as PaperAigcPlatform[],
			currentPlatform: {} as PaperAigcPlatform,
			loading: false,
			form: {
				title: "",
				author: "",
				file: null,
				content: null,
				submitType: "paper",
			},
			activeKey: "aigc-check",
			uploadStatus: "idle" as UploadStatus,
			activeName: "upload",
			submitType: "paper",
			fileTextCount: 0,
			filePrice: 0,
			textPrice: 0,
			previewUrl: "",
			tabs: [
				{
					title: "AIGC检测",
					key: "aigc-check",
				},
				{
					title: "降AIGC",
					key: "lower-aigc-check",
				},
				{
					title: "AIGC检测/降AIGC记录",
					key: "aigc-check-record",
				},
			],
			subActiveKey: "all",
			recordShow: false,
			// 记录类型筛选
			recordTypeFilter: "all", // 可选值: all, aigc, aigcAmend
			// 查重记录相关状态
			records: [] as any[],
			recordsTotal: 0,
			recordsPage: 1,
			recordsPageSize: 10,
			recordCounts: {
				all: 0,
				pending: 0,
				paid: 0,
			},
			recordTabs: [
				{
					title: "全部",
					key: "all",
				},
				{
					title: "待支付",
					key: "pending",
				},
				{
					title: "已支付",
					key: "paid",
				},
			],
			// 文本内容相关状态
			contentText: "",
			contentParsable: true,
			// 预览文档相关状态
			showPreviewModal: false,
			previewDocUrl: "",
			// 上传取消控制器
			abortController: null as AbortController | null,
			currentFile: null,
		}),
		actions: {
			setCurrentFile(file: File | null) {
				this.currentFile = file;
			},
			setSubmitType(name: string) {
				this.submitType = name;
				if (name !== "paper") {
					this.setActiveName("upload");
				}
			},
			setActiveName(name: string) {
				this.activeName = name;
			},
			setUploadStatus(status: UploadStatus) {
				this.uploadStatus = status;
			},
			setPreviewUrl(url: string) {
				this.previewUrl = url;
			},
			setActiveKey(key: string) {
				this.activeKey = key;
			},
			setSubActiveKey(key: string) {
				this.subActiveKey = key;
				this.loadRecords();
			},
			// 设置记录类型筛选
			setRecordTypeFilter(type: string) {
				this.recordTypeFilter = type;
				this.loadRecords();
			},
			setPage(page: number) {
				this.recordsPage = page;
				this.loadRecords();
			},
			setPageSize(size: number) {
				this.recordsPageSize = size;
				this.loadRecords();
			},
			async loadRecords() {
				try {
					// 构建请求参数
					const params: any = {
						page: this.recordsPage,
						pageSize: this.recordsPageSize,
						ispay:
							this.subActiveKey === "all"
								? undefined
								: this.subActiveKey === "pending"
									? false
									: true,
					};

					// 根据记录类型进行筛选
					if (this.recordTypeFilter !== "all") {
						params.action = this.recordTypeFilter;
					}

					const res: PaperRepeatResult = await fetchPaperAigcCheckList(params);
					this.records = res.rows;
					// 更新各状态数量
					this.recordsTotal = res.count;
					this.recordCounts.all = res.total;
					this.recordCounts.pending = res.pending;
					this.recordCounts.paid = res.total - res.pending;
				} catch (error) {
					console.error("加载查重记录失败:", error);
				}
			},
			async getDownloadUrl(id: string) {
				try {
					const { downUrl } = await fetchPaperRepeatUrl<{
						downUrl: string;
					}>({
						id,
					});
					return downUrl;
				} catch (e) {
					console.error(e);
				}
			},
			async checkContentPrice() {
				try {
					const res: {
						url?: string;
						words: number;
						price: number;
					} = await fetchPaperAigcPrice<{
						url?: string;
						words: number;
						price: number;
					}>({
						content: this.contentText!,
						type: this.currentPlatform.type,
						action: this.activeKey === 'aigc-check' ? 'aigc' : 'aigcAmend',
					});
					this.textPrice = res.price;
				} catch (error) {
					console.error("获取查重价格失败:", error);
				}
			},
			handlePay(record: any) {
				const message = useMessage();
				window.$aiwork
					.openRecharge?.({
						type: "check",
						categoryId: record.action === 'aigc' ? 8 : 9,
						action: record.action,
						id: record.id,
					})
					.then(() => {
						// this.resetForm();
						this.activeKey = "lower-check";
						this.loadRecords();
					});
			},
			handleViewContent(record: any) {
				// TODO: 实现查看文件内容逻辑
			},
			handleRecheck(record: any) {
				const router = useRouter();
				if (record.action === 'aigc') {
					router.replace(`/lower-aigc-rate?type=aigc`)
				} else {
					router.replace(`/lower-aigc-rate?type=aigcAmend`)
				}
			},
			handleDownload(record: any) {
				// TODO: 实现下载报告逻辑
			},
			// 处理文件上传
			async handleUpload({ file, onFinish, onError, onProgress }) {
				try {
					const authStore = useAuthStore();
					const message = useMessage()
					this.setUploadStatus("uploading");
					let uploadProgress = 0;

					// 创建新的 AbortController
					this.abortController = new AbortController();

					const formData = new FormData();
					formData.append("file", file.file);
					formData.append("type", this.currentPlatform.type);
					formData.append('action', this.activeKey === 'aigc-check' ? 'aigc' : 'aigcAmend');

					const xhr = new XMLHttpRequest();
					xhr.upload.onprogress = (event) => {
						if (event.lengthComputable) {
							uploadProgress = Math.round((event.loaded / event.total) * 100);
							// 调用进度回调
							onProgress && onProgress(uploadProgress);
						}
					};

					// 监听 abort 事件
					this.abortController.signal.addEventListener("abort", () => {
						xhr.abort();
					});

					const response = await new Promise((resolve, reject) => {
						xhr.open("POST", "/api3/aiwork/file/previewPaperAigc");
						xhr.setRequestHeader("Authorization", `Bearer ${authStore.token}`);

						xhr.onload = () => {
							if (xhr.status === 200) {
								resolve(JSON.parse(xhr.response));
							} else {
								reject(new Error("上传失败"));
							}
						};
						xhr.onerror = () => reject(new Error("网络错误"));
						xhr.onabort = () => reject(new Error("上传已取消"));
						xhr.send(formData);
					});

					if (response.errcode === 200100100001) {
						this.setUploadStatus("error");
						throw new Error("文件解析失败，请重新上传");
					}

					if (response.errcode === 200100100221) {
						this.setUploadStatus("wordCountError");
						throw new Error("文字少于1000，请重新上传");
					}

					if (response.errcode === 0) {
						this.setUploadStatus("success");
						this.setPreviewUrl(response.data.url);
						this.form.file = file;
						this.filePrice = response.data.price;
						this.fileTextCount = response.data.words;
						if (this.fileTextCount > 25000) {
							this.setUploadStatus("error");
							throw new Error("字数超过25000，请重新上传");
						}
						onFinish && onFinish();
						return response.data;
					} else {
						this.setUploadStatus("error");
						throw new Error(response.errmsg || "上传失败");
					}
				} catch (error) {
					// 如果错误是由取消引起的，则设置状态为 idle
					if (error.message === "上传已取消") {
						this.setUploadStatus("idle");
					}
					onError && onError();
					throw error;
				} finally {
					// 清理 abortController
					this.abortController = null;
				}
			},
			// 取消文件上传
			cancelUpload() {
				if (this.abortController) {
					this.abortController.abort();
					this.setUploadStatus("idle");
				}
			},
			// 重置上传状态
			resetUpload() {
				this.setUploadStatus("idle");
				this.previewUrl = "";
				this.filePrice = 0;
			},
			// actions
			async fetchPlatforms(action: 'aigc' | 'aigcAmend' = 'aigc') {
				this.resetForm();
				this.loading = true;
				try {
					const data = await fetchPaperAigcPlatforms<PaperAigcPlatform[]>(
						{
							action
						}
					);
					this.platforms = data;
					if (data.length > 0) {
						this.currentPlatform = data[0];
					}
				} catch (error) {
					console.error("获取平台失败:", error);
				} finally {
					this.loading = false;
				}
			},
			setCurrentPlatform(platform: PaperAigcPlatform) {
				this.currentPlatform = platform;
				if (this.activeName === "upload" && this.form.file) {
					this.handleUpload({
						file: this.form.file,
						onFinish: () => { },
						onError: () => { },
						onProgress: () => { },
					});
				} else if (this.activeName === "paste" && this.contentText) {
					this.checkContentPrice();
				}
			},
			updateForm(field: keyof typeof this.form, value: string) {
				this.form[field] = value;
			},
			resetForm() {
				this.form = {
					title: "",
					author: "",
					file: null,
					content: null,
					submitType: "paper",
				};
				this.currentFile = null;
				this.filePrice = 0;
				this.textPrice = 0;
				this.fileTextCount = 0;
			},
			// 设置文本内容
			setContentText(text: string) {
				this.contentText = text;
			},

			// 设置内容是否可解析
			setContentParsable(parsable: boolean) {
				this.contentParsable = parsable;
			},

			// 检查文本内容是否有效（字数不少于1000）
			checkContentValid() {
				return this.contentText.length >= 1000;
			},

			// 提交文本内容进行解析检查
			async submitContentForParsing() {
				try {
					const message = useMessage();

					// 首先检查字数是否满足要求
					if (this.contentText.length < 1000) {
						this.contentParsable = false;
						message.error("文字少于1000，请重新输入");
						return false;
					}

					// 调用 API 检查内容是否可以解析
					// 这里我们使用 createPaperAigcCheckOrder API 但不真正提交订单
					// 只是用来检查内容是否可以解析
					const testParams = {
						type: this.currentPlatform.type || "default",
						title: "测试标题",
						author: "测试作者",
						content: this.contentText,
						submitType: "paper",
						action: this.activeKey === 'aigc-check' ? 'aigc' : 'aigcAmend',
						// 添加一个标志，表示这只是一个测试请求
						test: true,
					};

					try {
						const response = await createPaperAigcCheckOrder(testParams);

						// 如果返回错误码表示内容解析失败
						if (response.errcode === 200100100001) {
							this.contentParsable = false;
							message.error("文本内容解析失败，请重新输入内容");
							return false;
						}

						this.contentParsable = true;
						return true;
					} catch (error) {
						console.error("内容解析检查失败:", error);
						this.contentParsable = false;
						message.error("文本内容解析失败，请重新输入内容");
						return false;
					}
				} catch (error) {
					this.contentParsable = false;
					return false;
				}
			},

			// 重置文本内容
			resetContent() {
				this.contentText = "";
				this.contentParsable = true;
			},

			// 提交订单
			async submitOrder() {
				if (!this.canSubmitOrder()) {
					return false;
				}
				const message = useMessage();
				const submitParams =
					this.activeName === "upload"
						? { file: this.form.file?.file }
						: { content: this.contentText };
				window.$aiwork
					.openRecharge?.({
						type: this.currentPlatform.type as string,
						categoryId: this.activeKey === 'aigc-check' ? 8 : 9,
						title: this.form.title,
						author: this.form.author,
						submitType: this.submitType,
						action: this.activeKey === 'aigc-check' ? 'aigc' : 'aigcAmend',
						...submitParams,
					})
					.then(() => {
						this.resetForm();
						setTimeout(() => {
							this.activeKey = "lower-check";
							this.loadRecords();
						}, 100);
					});
			},

			// 判断是否可以提交订单
			canSubmitOrder() {
				// 必须有标题和作者
				if (!this.form.title || !this.form.author) {
					return false;
				}

				// 根据activeName判断不同的提交方式
				if (this.activeName === "upload") {
					// 上传文件方式：必须有文件且上传成功
					if (!this.form.file || this.uploadStatus !== "success") {
						return false;
					}
				} else {
					// 粘贴文本方式：必须有内容且长度足够
					if (
						!this.contentText ||
						this.contentText.length < 1000 ||
						!this.contentParsable
					) {
						return false;
					}
				}

				return true;
			},

			// 预览文档
			async previewDocument() {
				if (!this.previewUrl) {
					return false;
				}

				this.previewDocUrl = this.previewUrl;
				this.showPreviewModal = true;
				return true;
			},
			async previewDocumentWithUrl(url: string) {
				this.previewDocUrl = url;
				this.showPreviewModal = true;
				return true;
			},
		},
	}
);
