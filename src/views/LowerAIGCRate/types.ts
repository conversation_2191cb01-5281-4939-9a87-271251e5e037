export interface PaperAigcPlatform {
	title: string;
	type: string;
	desc: string;
	subDesc: string[];
	icon: string;
	priceDesc: string;
	price: number;
	selected: boolean;
	tags: string[];
	detail: Detail;
}

interface Detail {
  title: string;
  list: List[];
}

interface List {
  content: string;
  title?: string;
}

export type UploadStatus =
	| "idle"
	| "uploading"
	| "success"
	| "error"
	| "wordCountError";


interface PaperRepeat {
  id: string;
  userId: number;
  orgUserId: number;
  tempId: null;
  title: string;
  submitType: string;
  type: string;
  status: number;
  key: string;
  downUrl: string;
  author: string;
  price: string;
  words: string;
  identify: string;
  ispay: boolean;
  task_id: null;
  payDate: null;
  createdAt: string;
  updatedAt: string;
  deletedAt: null;
}

/**
 * 剽窃检查结果接口
 */
export interface PaperRepeatResult {
	total: number; // 结果总数
	pending: number; // 未支付数量
	count: number; // 结果总数
	timeout: number; // 超时时间
	rows: Array<PaperRepeat>;
}

export enum Status {
	Pending = 0,
	Processing = 1,
	Completed = 2,
	Failed = 3,
}

export const statusMap = {
	[Status.Pending]: "待支付",
	[Status.Processing]: "检测中",
	[Status.Completed]: "检测完成",
	[Status.Failed]: "失败",
};

export const statusClassMap = {
	[Status.Pending]: "pending",
	[Status.Processing]: "processing",
	[Status.Completed]: "completed",
	[Status.Failed]: "failed",
};

/**
 * 论文详情接口，包含论文基本信息、模板信息和增值服务列表
 */
export interface PaperDetail {
	paper?: Paper;
	template?: Template;
	subjoins?: Subjoin[];
}

/**
 * 增值服务接口，描述论文相关的附加服务
 */
export interface Subjoin {
	id: number;                  // 服务ID
	title: string;               // 服务标题，如"任务书"、"开题报告"
	price: string;               // 当前价格
	originalPrice: string;       // 原价
	power: number;               // 所需算力
	words: number | null;        // 字数，可为空
	tag: string | null;          // 标签，如"导师看到都称赞"
	description: string | null;  // 描述信息
	exampleUrl: string | null;   // 示例图片URL
	type: string;                // 服务类型，如"books"、"proposal"
	templateId: number | null;   // 关联的模板ID
	isGenerate: boolean;         // 是否可生成
	createdAt: string;           // 创建时间
	updatedAt: string;           // 更新时间
	deletedAt: null;             // 删除时间
	isSelect?: boolean;          // 是否被选中
}

/**
 * 论文模板接口，包含模板名称和扩展信息
 */
export interface Template {
	name: string;                // 模板名称，如"论文助手"
	extend: Extend;              // 模板扩展信息
}

export interface Extend {
	icons: string[]; // 左侧图标列表
	chapter: Chapter[]; // 论文功能章节
	content: Content[]; // 文字描述
	examples: string[]; // 案例图片链接
}

export interface Content {
	label: string; // 例如："期刊论文"
	values: string[]; // 例如：["优先近5年", "权威期刊"]
}

export interface Chapter {
	title: string; // 章节标题，例如："封面"、"中英文摘要"等
	icon?: string; // 章节图标，可选
}

export interface Paper {
	id: number; // 论文ID
	title: string; // 论文标题
	list: string[]; // 论文信息列表，例如：["论文助手", "本科", "8000字", "Pro联网版"]
	power: number; // 所需算力
	price: number // 价格
}
