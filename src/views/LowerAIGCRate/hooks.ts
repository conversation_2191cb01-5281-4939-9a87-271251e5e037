import { usePlagiarismStore } from './store/lowerAIGCRate'
import { storeToRefs } from 'pinia'

// usePlatformSelection.ts - 处理平台选择相关逻辑
export const usePlatformSelection = () => {
  const store = usePlagiarismStore()
  const { platforms, currentPlatform, loading } = storeToRefs(store)

  // 初始化时获取平台列表
  store.fetchPlatforms()

  return {
    platforms,
    currentPlatform,
    loading,
    setCurrentPlatform: store.setCurrentPlatform,
  }
}
