import { get, post } from "@/utils/request";

// 获取获取下载链接
export function fetchPaperRepeatUrl<T>(params: { id: string }) {
	return post<T>({
		url: "/api3/aiwork/paperrepeat/getUrl",
		data: params,
	});
}

// 获取查重平台
export function fetchPaperAigcPlatforms<T>(params: any) {
	return post<T>({
		url: "/api3/aiwork/paperaigc/platforms",
		data: params,
	});
}

// 获取查重记录列表
export function fetchPaperAigcCheckList<T>(params: {
	page?: number;
	pageSize?: number;
	title?: string;
	ispay?: boolean;
}) {
	return post<T>({
		url: "/api3/aiwork/paperaigc/list",
		data: params,
	});
}

// 创建降重订单
export function createPaperAigcCheckOrder<T>(params: {
	type: string;
	file?: File;
	content?: string;
	title: string;
	author: string;
	submitType: "paper" | "report";
	action: 'aigc' | 'aigcAmend'
}) {
	const formData = new FormData();
	formData.append("type", params.type);
	formData.append("title", params.title);
	formData.append("author", params.author);
	formData.append("submitType", params.submitType);
	formData.append("action", params.action)
	if (params.content) {
		formData.append("content", params.content);
	} else if (params.file) {
		formData.append("file", params.file);
	}

	return post<T>({
		url: "/api3/aiwork/order/paperAigc",
		data: formData,
		headers: { "Content-Type": "multipart/form-data" },
	});
}

// 粘贴文本计算专用
export function fetchPaperAigcPrice<T>(params: {
	type: string;
	content?: string;
	action: 'aigc' | 'aigcAmend'
}) {
	const formData = new FormData();
	formData.append("type", params.type);
	formData.append("action", params.action)
	if (params.content) {
		formData.append("content", params.content);
	}
	return post<T>({
		url: "/api3/aiwork/file/previewPaperAigc",
		data: formData,
		headers: { "Content-Type": "multipart/form-data" },
	});
}

export function fetchPaperPreview(params: { url: string }) {
	return fetch(params.url, {
		method: "GET",
	}).then((response) => response.blob());
}
