<template>
	<NForm :rules="rules" label-placement="left">
		<!-- 自定义表单组件 -->
		<div class="flex flex-col">
			<div class="flex">
				<div class="text-[14px] text-[#3D3D3D] flex-1">
					<span class="text-[14px] text-[#FF5100]">*</span>
					{{ activeKey === "aigc-check" ? "AIGC检测系统" : "降AIGC系统" }}
				</div>
				<div
					class="w-[50%] h-[28px] bg-[#F1F7FF] rounded-[4px] leading-[28px] font"
				>
					<ScrollText
						:current="2"
						:autoplay="true"
						:interval="2000"
						:delayUpdate="1500"
						:height="28"
					>
						<div
							v-for="(message, index) in latestMessage"
							class="flex items-center justify-start px-[8px] h-[28px]"
						>
							<NIcon :size="16" class="mr-[5px]">
								<NoticeSvg />
							</NIcon>
							<span class="text-gray-500 text-[14px]">{{ message }}</span>
						</div>
					</ScrollText>
				</div>
			</div>
			<div class="flex flex-row flex-wrap relative">
				<SystemCard
					class="mr-[9px] last:mr-0 mt-[18px]"
					v-for="platform in platforms"
					:key="platform.type"
					:platform="platform"
				/>
			</div>
			<div
				class="text-[#3D3D3D] text-[13px] bg-[#F2F5F9] h-[45px] leading-[45px] mt-[28px] px-[10px] relative"
				v-if="currentPlatform?.chargeDesc"
			>
				<!-- 三角形指示器 - 两个选项的情况 -->
				<div
					v-if="platforms.length === 2 && currentPlatform"
					class="absolute top-[-20px] w-0 h-0 border-[10px] border-transparent border-t-[#F2F5F9] transform rotate-180 transition-all duration-300"
					:style="{
						left: currentPlatform.type === platforms[0]?.type ? '15%' : '65%',
					}"
				></div>

				<!-- 三角形指示器 - 四个选项的情况 -->
				<!-- <div v-else-if="platforms.length === 4 && currentPlatform"
					class="absolute top-[-20px] w-0 h-0 border-[10px] border-transparent border-t-[#F2F5F9] transform rotate-180 transition-all duration-300"
					:style="{
						left: getTrianglePosition(currentPlatform.type)
					}">
				</div> -->
				<span class="text-[14px] text-[#FF5100]">*</span>
				收费说明：
				<span class="text-[#848484] text-[13px]">
					{{ currentPlatform?.chargeDesc }}
				</span>
			</div>
		</div>
		<div class="border-b border-gray-200 my-[24px]"></div>
		<div class="flex flex-row gap-x-[16px] justify-between">
			<NFormItem label="论文题目" required class="w-[55%]">
				<NInput
					v-model:value="form.title"
					placeholder="请输入论文题目"
					show-count
					:maxlength="40"
					@update:value="(val) => store.updateForm('title', val)"
				/>
			</NFormItem>
			<NFormItem label="论文作者" required class="w-[40%]">
				<NInput
					v-model:value="form.author"
					placeholder="请输入论文作者"
					show-count
					:maxlength="15"
					@update:value="(val) => store.updateForm('author', val)"
				/>
			</NFormItem>
		</div>
		<NFormItem
			label="提交类型"
			required
			v-if="activeKey === 'lower-aigc-check'"
		>
			<div class="flex gap-x-2">
				<div
					class="cursor-pointer rounded-[4px] py-[7px] px-[16px] text-[14px] transition-colors duration-200"
					:class="
						store.submitType === 'paper'
							? 'bg-[#0E69FF] text-[#FFFFFF]'
							: 'bg-[#F2F5F9] text-[#3D3D3D]'
					"
					@click="handleTypeChange('paper')"
				>
					原文上传/粘贴
				</div>
				<div
					class="cursor-pointer rounded-[4px] py-[7px] px-[16px] text-[14px] transition-colors duration-200"
					:class="
						store.submitType === 'report'
							? 'bg-[#0E69FF] text-[#FFFFFF]'
							: 'bg-[#F2F5F9] text-[#3D3D3D]'
					"
					@click="handleTypeChange('report')"
				>
					AIGC检测报告上传
				</div>
			</div>
		</NFormItem>
		<NFormItem
			v-if="activeKey === 'lower-aigc-check'"
			:label="store.submitType === 'paper' ? '适配平台' : '检测平台'"
			required
		>
			<NRadioGroup v-model:value="checkPlatform">
				<NRadio value="知w">知W</NRadio>
				<NRadio value="w普" disabled>W普</NRadio>
				<NRadio value="万F" disabled>万F</NRadio>
				<NRadio value="其他" disabled>其他</NRadio>
				<span class="text-[#848484]">敬请期待，努力适配中</span>
			</NRadioGroup>
		</NFormItem>
		<NFormItem label="提交方式" path="abstract" required class="w-[100%]">
			<SubmitContainer />
		</NFormItem>
	</NForm>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import {
	NForm,
	NFormItem,
	FormRules,
	NInput,
	NRadioGroup,
	NRadio,
	NIcon,
} from "naive-ui";
import SystemCard from "./system-card.vue";
import SubmitContainer from "./submit-container.vue";
import { useLowerAIGCRate } from "../store/lowerAIGCRate";
import { storeToRefs } from "pinia";
import { ScrollText } from "@/components/scrollText";
import NoticeSvg from "@/assets/aiwork/svg/notice.svg";

const store = useLowerAIGCRate();
const { platforms, form, currentPlatform, activeKey } = storeToRefs(store);
const latestMessage = [
	"微信**3699 刚刚使用了Pro版高阶模型检测AIGC",
	"微信**4560 刚刚使用了旗舰版-精细化降AIGC模型",
	"微信**0416 刚刚使用了专业版-强力降AIGC模型",
	"微信**5583 刚刚使用了基础版高阶模型检测AIGC",
	"微信**7421 刚刚使用了基础版高阶模型检测AIGC",
	"微信**3023 刚刚使用了专业版-强力降AIGC模型",
	"微信**8540 刚刚使用了Pro版高阶模型检测AIGC",
	"微信**9801 刚刚使用了旗舰版-精细化降AIGC模型",
	"微信**2456 刚刚使用了专业版-强力降AIGC模型",
];
// 检测平台选择
const checkPlatform = ref("知w");

const handleTypeChange = (type: string) => {
	store.setSubmitType(type);
	store.updateForm("submitType", type);
};

const rules: FormRules = {
	content: {
		required: true,
		message: "请选择降重系统",
		trigger: ["blur", "input"],
	},
	title: {
		required: true,
		message: "请输入论文题目",
		trigger: ["blur", "input"],
	},
	author: {
		required: true,
		message: "请输入论文作者",
		trigger: ["blur", "input"],
	},
};
</script>

<style lang="less" scoped></style>
