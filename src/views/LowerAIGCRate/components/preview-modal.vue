<template>
	<n-modal :show="visible" @update:show="handleVisibleChange"
		style="width: 765px; height: 80vh; max-height: 756px;--n-padding-top: 0px;--n-padding-left: 0px;--n-padding-right: 0px;">
		<n-card>
			<template #header>
				<div class="bg-[#DCECFF] h-[61px] leading-[61px] text-[22px] text-[#333333] pl-[30px]">{{ title }}</div>
			</template>
			<template #header-extra>
				<div class=" h-[61px] leading-[61px] flex justify-center items-center cursor-pointer bg-[#DCECFF] pr-[20px]"
					@click="handleClose">
					<IconClose class=" w-[25px] h-[25px] text-[22px] " />
				</div>
			</template>
			<slot></slot>
		</n-card>
	</n-modal>
</template>

<script lang="ts" setup>
import { NModal, NCard } from 'naive-ui'

interface Props {
	visible: boolean
	title: string
}
interface Emit {
	(e: 'update:visible', value: boolean): void
}
const props = defineProps<Props>()
const emit = defineEmits<Emit>()

const handleClose = () => {
	emit('update:visible', false)
}

const handleVisibleChange = (value: boolean) => {
	emit('update:visible', value)
}
</script>

<style lang="less" scoped></style>
