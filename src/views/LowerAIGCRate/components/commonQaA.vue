<template>
	<div class="common-qa">
		<div class="title">
			<IconQuestion1 />常见问题
		</div>
		<div class="divider"></div>

		<div class="qa-list">
			<div class="qa-item">
				<div class="question text-[#0E69FF]">1、AIGC检测服务是什么？</div>
				<div class="answer">AIGC检测服务可以区分人工编写的内容和AI模型生成的文本。 系统将快速生成报告，识别文本AIGC率。AIGC检测服务目前作为辅助工具，仅提示文本内容可能由AI生成</div>
			</div>

			<div class="qa-item">
				<div class="question">2、AIGC检测结果说明？</div>
				<div class="answer">检测结果由系统自动生成，为您提供的是相关线索而非判断依据和（或者）判断标准，仅供参考</div>
			</div>

			<div class="qa-item">
				<div class="question">3、降AIGC核心技术</div>
				<div class="answer">1）通过深度语义分析技术，自动调整语句结构，避免与现有文献形成机械重复</div>
				<div class="answer">2）系统采用动态改写策略，在保持原意不变的前提下，有效降低文本的AI痕迹</div>
			</div>
			<!-- <div class="qa-item">
				<div class="question">论文纠错：</div>
				<div class="answer">独创算法加持，避免多次返稿</div>
			</div> -->
		</div>
	</div>
</template>

<script setup lang="ts">
// 不再需要props，使用固定内容
</script>

<style scoped lang="less">
.common-qa {
	padding: 20px;
	background: #fff;
	border-radius: 4px;

	.title {
		font-size: 16px;
		color: #121519;
		font-weight: 500;
		margin-bottom: 15px;
		display: flex;
		align-items: center;

		.icon {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 20px;
			height: 20px;
			border-radius: 50%;
			background-color: #1890ff;
			color: white;
			font-size: 14px;
			margin-right: 8px;
		}
	}

	.divider {
		width: 100%;
		height: 1px;
		background: #E8E8E8;
		margin-bottom: 15px;
	}

	.qa-list {
		.qa-item {
			margin-bottom: 15px;

			&:last-child {
				margin-bottom: 0;
			}

			.question {
				font-size: 16px;
				color: #121519;
				font-weight: 400;
				margin-bottom: 8px;
			}

			.answer {
				font-size: 14px;
				color: #666666;
				line-height: 1.5;
			}
		}
	}
}
</style>
