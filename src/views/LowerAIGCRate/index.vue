<template>
	<main class="h-full w-full mx-auto px-[50px] py-[50px] flex flex-col items-center bg-[#f8f9ff]">
		<div class=" max-w-[1600px]">
			<BannerSlider />
			<!-- tabs -->
			<div class="tabs2">
				<div class="tab2" v-for="tab in tabs" :key="tab.key" :class="{ active: tab.key === store.activeKey }"
					@click="handleTabChange(tab.key)">
					{{ tab.title }}
				</div>
			</div>
			<!-- 子标签 -->
			<div class="flex flex-row gap-x-[16px]" v-if="activeKey === 'aigc-check'">
				<div class="left-container bg-[#fff] rounded-[4px] p-[23px] w-[990px]">
					<CheckForm />
					<div class="flex flex-row justify-between">
						<span class="text-[16px]text-[#333333] leading-[46px]">应付：
							<span class="text-[#FF1A00] text-[24px]">¥{{ computedPrice }}</span>
						</span>
						<div :class="{ 'btn-disabled': !canSubmitOrder, 'opacity-40': !canSubmitOrder }"
							class="text-[#fff] w-[150px] h-[46px] rounded-[4px] bg-gradient-to-r to-[#7E4AFF] from-[#009DFF] flex items-center justify-center cursor-pointer"
							@click="store.submitOrder()">
							提交订单
						</div>
					</div>
				</div>
				<div class="right-container rounded-[4px] flex flex-col gap-y-[16px] max-w-[285px]">
					<PlatformDetail :current-platform="store.currentPlatform" />
					<div class="flex flex-row gap-x-[11px] px-[25px] py-[12px] bg-[#fff] items-center">
						<NImage :src="AcademicQrcode" class="w-[81px] h-[81px] " preview-disabled object-fit="cover" />
						<span class="text-[#3D3D3D] text-[14px] leading-[18px] line-clamp-3 flex-1">
							扫码关注AIWork365学术官方公众号，获取更多资讯
						</span>
					</div>
				</div>
			</div>
			<div class="flex flex-row gap-x-[16px]" v-else-if="activeKey === 'lower-aigc-check'">
				<div class="left-container bg-[#fff] rounded-[4px] p-[23px] w-[990px]">
					<CheckForm />
					<div class="flex flex-row justify-between">
						<span class="text-[16px]text-[#333333] leading-[46px]">应付：
							<span class="text-[#FF1A00] text-[24px]">¥{{ computedPrice }}</span>
						</span>
						<div :class="{ 'btn-disabled': !canSubmitOrder, 'opacity-40': !canSubmitOrder }"
							class="text-[#fff] w-[150px] h-[46px] rounded-[4px] bg-gradient-to-r to-[#7E4AFF] from-[#009DFF] flex items-center justify-center cursor-pointer"
							@click="store.submitOrder()">
							提交订单
						</div>
					</div>
				</div>
				<div class="right-container rounded-[4px] flex flex-col gap-y-[16px] max-w-[285px]">
					<PlatformDetail :current-platform="store.currentPlatform" />
					<div class="flex flex-row gap-x-[11px] px-[25px] py-[12px] bg-[#fff] items-center">
						<NImage :src="AcademicQrcode" class="w-[81px] h-[81px] " preview-disabled object-fit="cover" />
						<span class="text-[#3D3D3D] text-[14px] leading-[18px] line-clamp-3 flex-1">
							扫码关注AIWork365学术官方公众号，获取更多资讯
						</span>
					</div>
				</div>
			</div>
			<div v-else class="flex flex-row gap-x-[16px]">
				<div class="left-container bg-[#fff] rounded-[4px] p-[23px] w-[990px]">
					<div class=" w-full flex">
						<NTabs v-model:value="subActiveKey" type="bar" animated class="sub-tabs w-[70%]"
							@update:value="handleSubTabChange">
							<NTabPane v-for="tab in recordTabs" :key="tab.key" :name="tab.key"
								:tab="tab.title + '(' + recordCounts[tab.key] + ')'">
							</NTabPane>
						</NTabs>
						<!-- 记录类型筛选下拉选项 -->
						<div class="ml-auto">
							<n-select v-model:show="recordShow" v-model:value="recordTypeFilter" :options="recordTypeOptions"
								size="medium" style="width: 160px;" @update:value="handleRecordTypeChange">
								<template #arrow>
									<transition name="slide-left">
										<IconUp v-if="recordTypeFilter" />
										<IconDown v-else />
									</transition>
								</template>
							</n-select>
						</div>
					</div>
					<CheckRecord />
				</div>
				<div class="right-container rounded-[4px] flex flex-col gap-y-[16px] max-w-[285px]">
					<CommonQaA />
					<div class="flex flex-row gap-x-[11px] px-[25px] py-[12px] bg-[#fff] items-center">
						<NImage :src="AcademicQrcode" class="w-[81px] h-[81px] " preview-disabled object-fit="cover" />
						<span class="text-[#3D3D3D] text-[14px] leading-[18px] line-clamp-3 flex-1">
							扫码关注AIWork365学术官方公众号，获取更多资讯
						</span>
					</div>
				</div>
			</div>

		</div>
		<n-image :src="Footer" class="!max-w-[1290px] mt-[21px]" preview-disabled object-fit="contain" />
		<!-- 文档预览模态框 -->
		<preview-modal :visible="store.showPreviewModal" @update:visible="updateModalVisible" title="论文预览">
			<div ref="previewContainer" class="w-full h-[600px] overflow-auto"></div>
		</preview-modal>
	</main>
</template>

<script setup lang="ts">
import CheckForm from './components/check-form.vue';
import CheckRecord from './components/check-record.vue';
import { useLowerAIGCRate } from './store/lowerAIGCRate';
import { NImage, NTabs, NTabPane, NSelect, useMessage } from 'naive-ui';
import PlatformDetail from './components/platform-detail.vue';
import CommonQaA from './components/commonQaA.vue';
import AcademicQrcode from '@/assets/images/academic-qrcode.png'
import { storeToRefs } from 'pinia';
import { computed, ref, watch, nextTick, onMounted } from 'vue';
import PreviewModal from './components/preview-modal.vue';
import BannerSlider from './components/banner-slider.vue';
import { fetchPaperPreview } from './api';
import { renderAsync } from 'docx-preview';
import Footer from '@/assets/lower/footer2.png';
import { useRoute, useRouter } from 'vue-router';

// 定义props接口
interface Props {
	type?: string;
}

// 接收props
const props = defineProps<Props>();

const route = useRoute();
const router = useRouter();
const store = useLowerAIGCRate();


const handleTabChange = (key: 'aigc-check' | 'lower-aigc-check' | 'record') => {
	store.resetForm()
	setTimeout(() => {
		// 只修改URL，不更新组件
		if (key === 'aigc-check') {
			// 静默修改URL，不调用store.fetchPlatforms
			router.replace({ query: { type: 'aigc' } });
		} else if (key === 'lower-aigc-check') {
			// 静默修改URL，不调用store.fetchPlatforms
			router.replace({ query: { type: 'aigcAmend' } });
		} else {
			// 静默修改URL
			router.replace({ query: { type: 'aigc-check-record' } });
		}
	}, 100)
}

const { fileTextCount, previewDocUrl, activeName, textPrice, filePrice, recordTabs, tabs, subActiveKey, recordCounts, activeKey, form, uploadStatus, contentText, contentParsable, currentPlatform, recordShow, recordTypeFilter } = storeToRefs(store)
// 初始化时根据URL参数设置activeKey并获取平台列表
onMounted(() => {
	// 从URL参数或props中获取type
	const type = props.type || route.query.type as string;

	// 根据type参数设置activeKey
	if (type === 'aigc') {
		store.setActiveKey('aigc-check');
		store.fetchPlatforms('aigc');
	} else if (type === 'aigcAmend') {
		store.setActiveKey('lower-aigc-check');
		store.fetchPlatforms('aigcAmend');
	} else {
		store.setActiveKey('aigc-check-record')
	}
})


// 是否正在提交订单
const isSubmitting = ref(false)

const handleSubTabChange = () => {
	nextTick(() => {
		store.loadRecords()
	})
}

// 记录类型选项
const recordTypeOptions = [
	{
		label: '全部',
		value: 'all'
	},
	{
		label: 'AIGC检测记录',
		value: 'aigc'
	},
	{
		label: '降AIGC记录',
		value: 'aigcAmend'
	}
]

// 处理记录类型变更
const handleRecordTypeChange = (value: string) => {
	RecordTypeFilter(value)
}

watch(form.value, (newVal) => {
	console.log('form.value', newVal);
})

const computedPrice = computed(() => {
	const price = activeName?.value === 'upload' ? filePrice?.value : textPrice?.value
	return ((Number(price) || 0).toFixed(2)) // 处理 NaN 情况
})
// 判断是否可以提交订单
const canSubmitOrder = computed(() => {
	// 必须选择平台
	if (!currentPlatform.value || !currentPlatform.value.type) {
		return false
	}

	// 必须填写标题和作者
	if (!form.value.title || !form.value.author) {
		return false
	}

	// 根据activeName判断不同的提交方式
	if (activeName.value === 'upload') {
		// 上传文件方式：必须有文件且上传成功
		if (!form.value.file || uploadStatus.value !== 'success') {
			return false
		}
	} else {
		// 粘贴文本方式：必须有内容且长度足够
		if (!contentText.value || contentText.value.length < 1000 || !contentParsable.value) {
			return false
		}
	}
	if (fileTextCount?.value > 25000) {
		return false
	}
	// 不能正在提交中
	if (isSubmitting.value) {
		return false
	}

	return true
})

// 文档预览相关
const previewContainer = ref(null);
// docx-preview模块
const docxModule = ref(null);

const updateModalVisible = (value: boolean) => {
	store.showPreviewModal = value;
};

// 监听预览模态框显示状态
watch(() => store.showPreviewModal, async (newVal) => {
	if (newVal && previewDocUrl.value) {
		try {

			// 获取文档内容
			const response = await fetchPaperPreview({ url: previewDocUrl.value });

			// 清空容器
			if (previewContainer.value) {
				previewContainer.value.innerHTML = '';

				// 渲染文档
				await renderAsync(response, previewContainer.value, null, {
					className: 'text-docx',
					inWrapper: true,
					ignoreWidth: false,
					ignoreHeight: false,
					ignoreFonts: false,
					breakPages: true,
					ignoreLastRenderedPageBreak: true,
					experimental: true,
					trimXmlDeclaration: true,
					useBase64URL: false,
					useMathMLPolyfill: true,
					debug: false,
					renderHeaders: true,
					renderFooters: true,
					renderFootnotes: true,
					renderEndnotes: true,
					ignoreEmptyParagraphs: false,
					styleMap: {
						'table': 'border-collapse: collapse; width: 100%;',
						'td': 'border: 1px solid #ddd; padding: 8px;',
						'th': 'border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'
					}
				});
			}
		} catch (error) {
			console.error('预览文档失败:', error);
		}
	}
});
</script>

<style lang="less" scoped>
.tabs2 {
	display: flex;
	flex-direction: row;
	column-gap: 10px;

	.tab2 {
		width: 194px;
		height: 44px;
		background: #F0F6FA;
		position: relative;
		line-height: 44px;
		text-align: center;
		box-shadow: inset 14px 0px 8px 0px rgba(240, 246, 250, 0.3);
		border-top-left-radius: 4px;
		border-top-right-radius: 4px;
		cursor: pointer;

		&::after {
			content: '';
			position: absolute;
			top: 0;
			right: -20px;
			width: 0;
			height: 0;
			border-bottom: 44px solid #F0F6FA;
			border-right: 20px solid transparent;
		}

		&.active {
			background: #fff;
			z-index: 2;

			&::after {
				border-bottom: 44px solid #fff;
			}
		}
	}
}

.btn-disabled {
	opacity: 0.4;
	cursor: not-allowed;
}

:deep(.text-docx) {
	table {
		width: 100% !important;
	}
}
</style>
