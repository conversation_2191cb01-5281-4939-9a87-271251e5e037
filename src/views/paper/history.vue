<template>

	<div class="history-container">
		<div class=" flex flex-col gap-y-[20px] w-full max-w-[1180px] mx-auto">
			<!-- header -->
			<div class="header">
				<!-- 返回论文助手 -->
				<div class="flex items-center justify-between w-full">
					<div class="flex items-center cursor-pointer text-[#3D3D3D] text-[14px] leading-[18px]" @click="goBack">
						<IconArrowLeft />
						<span class="ml-1">返回论文助手</span>
					</div>
					<span class="absolute left-1/2 transform -translate-x-1/2 font-bold text-[22px] leading-[29px]">历史记录</span>
				</div>
			</div>
			<n-spin :show="paperHistoryLoading">
				<div class="history-list-container">

					<NTabs v-model:value="pageHistoryStatus" type="bar" animated class="sub-tabs"
						@update:value="handleSubTabChange">
						<NTabPane v-for="tab in recordTabs" :key="tab.key" :name="tab.key" :tab="tab.title">
							<!-- :tab="tab.title + '(' + recordCounts[tab.key] + ')'"> -->
						</NTabPane>
					</NTabs>
					<div class="paper-history-list" v-if="paperHistory.length > 0">
						<div v-for="paper in paperHistory" :key="paper.id" class="paper-item">
							<div class="paper-header">
								<div class="title">{{ paper.type }}：{{ paper.title }}</div>
								<div :class="['status', getStatusClass(paper.status)]">{{ getStatusText(paper.status) }}</div>
							</div>
							<div class="paper-content">
								<div class="info-row">
									<div class="info-item">
										<span class="label">创建时间：</span>
										<span>{{ formatDate(paper.createdAt) }}</span>
									</div>
									<div class="info-item">
										<span class="label">写作类型：</span>
										<span>{{ getPaperTypeName(paper.type) }}</span>
										<span
											class="bg-[#F1F7FF] text-[#0E69FF] text-[12px] leading-[16px] rounded-[2px] px-[8px] py-[3px] ml-[8px]">{{
												getPaperModeName(paper.mode) }}</span>
									</div>
									<div class="info-item-right">
										<span class="label">消费算力：</span>
										<span class="text-[#FF1C1C]">{{ paper.power }}</span>
									</div>
									<div class="info-item basis-[100%]" v-if="paper.subjoins && paper.subjoins.length > 0">
										<span class="label">增值服务：</span>
										<span>
											<template v-for="(title, index) in computedSubjoins(paper.subjoins)" :key="index">
												{{ title }}
												<n-divider v-if="index < computedSubjoins(paper.subjoins).length - 1" vertical />
											</template>
										</span>
									</div>
								</div>
								<div class="action-row">
									<n-button class="h-[32px]"
										v-if="paper.status === PaperStatus.Success || paper.status === PaperStatus.Processing"
										type="primary" style="--n-text-color-hover:#fff;" @click="goBack">再写一篇</n-button>
									<n-button class="h-[32px]" v-if="paper.status === PaperStatus.Wait" style="--n-color:#21AF3D;
									--n-color-hover:#21AF3D;
									--n-color-focus:#21AF3D;
									color:#FFFFFF;" @click="handlePay(paper)">立即支付</n-button>
									<n-button class="h-[32px]" v-if="paper.status === PaperStatus.Success"
										style="--n-border: 1px solid #0E69FF;color:#0E69FF;" @click="downloadPaper(paper)">下载论文</n-button>
									<n-button class="h-[32px]" v-if="hasServiceWithTitle(paper.subjoins, SERVICE_TITLES.PPT) && paper.status === PaperStatus.Success"
										style="--n-border: 1px solid #929292;color:#3D3D3D;" @click="downloadPPT(paper)">答辩PPT</n-button>
									<n-button class="h-[32px]"
										v-if="hasServiceWithTitle(paper.subjoins, SERVICE_TITLES.UNLIMITED_REVISION) && paper.status === PaperStatus.Success"
										style="--n-border: 1px solid #929292;color:#3D3D3D;" @click="downloadPDF">无限改稿</n-button>
								</div>
							</div>
						</div>
						<div class="pagination">
							<n-pagination v-model:page="page" v-model:page-size="pageSize" :item-count="paperHistoryCount"
								@update:page="handlePageChange" @update:page-size="handlePageSizeChange">
								<template #prefix="{ itemCount }">
									共 {{ itemCount }} 条
								</template>
							</n-pagination>
						</div>
					</div>
					<div v-else class="flex justify-center items-center h-full">
						<n-empty description="您的论文记录为空，快去创作论文吧~" style="--n-icon-size: 100px">
							<template #icon>
								<n-image :src="EmptyIcon" preview-disabled object-fit="contain" />
							</template>
						</n-empty>
					</div>
				</div>
			</n-spin>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { NButton, NPagination, NEmpty, NImage, NDivider, NSpin, NTabs, NTabPane } from 'naive-ui';
import { useRouter } from "vue-router";
import { usePaperStore } from "@/store/modules/paper";
import { storeToRefs } from "pinia";
import { onMounted, onUnmounted } from "vue";
import { PaperHistory, PaperHistoryStatus, PaperModeName, PaperStatus, PaperTypeName, Subjoin } from "@/views/paper/types";
import dayjs from "dayjs";
import EmptyIcon from '@/assets/images/empty-icon.png';
import { useRequest } from 'vue-hooks-plus';
import { fetchPaperUrl } from './apis';
import { downloadFile } from '@/utils/downloadFile';
import { useUserStore } from '@/store';
// 常量定义
const SERVICE_TITLES = {
	PPT: '答辩PPT',
	UNLIMITED_REVISION: '无限改稿'
} as const;

const STATUS_TEXT_MAP = {
	[PaperStatus.Wait]: '待支付',
	[PaperStatus.Processing]: '生成中',
	[PaperStatus.Success]: '已完成',
	[PaperStatus.Failed]: '生成失败'
} as const;

const STATUS_CLASS_MAP = {
	[PaperStatus.Wait]: 'pending',
	[PaperStatus.Processing]: 'checking',
	[PaperStatus.Success]: 'completed',
	[PaperStatus.Failed]: 'failed'
} as const;

// 路由和状态管理
const router = useRouter();
const store = usePaperStore();
const userStore = useUserStore();

const { paperHistory, paperHistoryCount, page, pageSize, paperHistoryLoading, pageHistoryStatus } = storeToRefs(store);

// 记录标签定义
const recordTabs = [
	{ key: PaperHistoryStatus.ALL, title: '全部' },
	{ key: PaperHistoryStatus.Wait, title: '待支付' },
	{ key: PaperHistoryStatus.Processing, title: '生成中' },
	{ key: PaperHistoryStatus.Success, title: '已完成' },
];

// API 请求
const { run: runFetchPaperUrl, loading: downloadLoading } = useRequest(fetchPaperUrl, {
	manual: true,
	onSuccess: (data: { url: string, zipUrl: string }) => {
		if (data?.url) {
			downloadFile(data.zipUrl || data.url);
		} else {
			console.error('下载链接为空');
		}
	},
	onError: (error) => {
		console.error('下载论文失败', error);
	}
});

// 生命周期钩子
onMounted(() => {
	store.getPaperHistory();
});
onUnmounted(() => {
	store.pageHistoryStatus = PaperHistoryStatus.ALL
})

// 工具函数
/**
 * 格式化日期
 * @param date 日期字符串
 * @returns 格式化后的日期字符串
 */
const formatDate = (date: string): string => {
	return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

/**
 * 获取论文类型名称
 * @param type 论文类型
 * @returns 论文类型名称
 */
const getPaperTypeName = (type: string): string => {
	return PaperTypeName[type as keyof typeof PaperTypeName] || type;
};

/**
 * 获取论文模式名称
 * @param mode 论文模式
 * @returns 论文模式名称
 */
const getPaperModeName = (mode: string): string => {
	return PaperModeName[mode as keyof typeof PaperModeName] || mode;
};

/**
 * 获取状态文本
 * @param status 状态码
 * @returns 状态文本
 */
const getStatusText = (status: number): string => {
	return STATUS_TEXT_MAP[status] || '未知状态';
};

/**
 * 获取状态类名
 * @param status 状态码
 * @returns 状态类名
 */
const getStatusClass = (status: number): string => {
	return STATUS_CLASS_MAP[status] || '';
};

/**
 * 计算增值服务标题列表
 * @param subJoins 增值服务列表
 * @returns 增值服务标题列表
 */
const computedSubjoins = (subJoins: Subjoin[]): string[] => {
	return subJoins.map(subJoin => subJoin.title);
};

/**
 * 检查是否包含特定标题的服务
 * @param subjoins 增值服务列表
 * @param title 服务标题
 * @returns 是否包含该服务
 */
const hasServiceWithTitle = (subjoins: Subjoin[], title: string): boolean => {
	return subjoins?.some(subjoin => subjoin.title.includes(title)) || false;
};

// 事件处理函数
/**
 * 返回论文助手页面
 */
const goBack = (): void => {
	router.push({
		path: "/paper2"
	});
};

/**
 * 切换标签页
 * @param status 状态码
 */
const handleSubTabChange = (status: number): void => {
	store.pageHistoryStatus = status;
	store.page = 1;
	store.getPaperHistory();
};

/**
 * 切换页码
 * @param page 页码
 */
const handlePageChange = (page: number): void => {
	store.page = page;
	store.getPaperHistory();
};

/**
 * 切换每页显示数量
 * @param pageSize 每页显示数量
 */
const handlePageSizeChange = (pageSize: number): void => {
	store.pageSize = pageSize;
	store.getPaperHistory();
};

/**
 * 下载论文
 * @param paper 论文信息
 */
const downloadPaper = ({ id }: { id: number }): void => {
	if (id) {
		runFetchPaperUrl({ id });
	}
};

/**
 * 下载PPT
 * @param paper 论文信息
 */
const downloadPPT = (paper: PaperHistory): void => {
	if (!paper.id) return;

	if (userStore.userInfo?.uid) {
		const pptSubjoin = paper.subjoins.find(subjoin => subjoin.title === SERVICE_TITLES.PPT);
		if (pptSubjoin) {
			const url = pptSubjoin.href;
			if (url.includes('http')) {
				window.location.href = url;
			} else {
				router.push(url);
			}
		}
	} else {
		window.$aiwork.openLogin().then(() => downloadPPT(paper));
	}
};

/**
 * 下载PDF（无限改稿）
 */
const downloadPDF = (): void => {
	router.push('/apps/91?appid=300');
};

/**
 * 处理支付
 * @param paper 论文信息
 */
const handlePay = async (paper: PaperHistory) => {
	// 支付逻辑，根据实际需求实现
	await store.generateHistoryPaper({
		id: paper.id,
	});
	// 如果当前页没有数据且不是第一页，则请求上一页
	if (paperHistory.value.length === 0 && page.value > 1) {
		page.value -= 1
		await store.getPaperHistory();
	}
};

/**
 * 创建新论文
 * @param paper 论文信息
 */
const createNewPaper = (paper: PaperHistory): void => {
	// 创建新论文逻辑，根据实际需求实现
	router.push({
		path: "/paper2"
	});
};
</script>

<style lang="less" scoped>
.history-container {
	width: 100%;
	background-color: linear-gradient(180deg, #f3f5fd 0%, #f5f8fe 100%);

	.header {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		gap: 20px;
		margin-top: 32px;
		width: 100%;
	}

	.history-list-container {
		width: 100%;
		margin-top: 20px;
	}

	.paper-history-list {
		width: 100%;

		.paper-item {
			height: 174px;
			background: #FFFFFF;
			border-radius: 4px;
			margin-bottom: 14px;
			border: 1px solid #0E69FF;

			.paper-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 10px 16px;
				background: #E2EDFF;
				border-radius: 4px;
				margin-bottom: 16px;

				.title {
					color: #0E69FF;
					font-size: 16px;
					font-weight: 500;
				}

				.status {
					font-size: 14px;

					&.pending {
						color: #FF641C;
					}

					&.checking {
						color: #0E69FF;
					}

					&.completed {
						color: #138C42;
					}

					&.failed {
						color: #FF1A00;
					}
				}
			}

			.paper-content {
				.info-row {
					display: flex;
					flex-wrap: wrap;
					gap: 6px;
					margin-bottom: 16px;
					padding-left: 12px;
					padding-right: 28px;

					.info-item-right {
						flex: 1;
						text-align: right;
					}

					.info-item {
						width: 35%;

						&[class*="basis-"] {
							width: 100%;
						}

						.label {
							color: #666666;
						}
					}
				}

				.action-row {
					display: flex;
					gap: 16px;
					padding-left: 13px;
					justify-content: flex-start;
				}
			}
		}

		.pagination {
			margin-top: 24px;
			display: flex;
			justify-content: flex-end;
		}
	}
}
</style>
