export interface PaperFormData {
	field: string;
	value: string;
}
export interface PaperForm {
	id?: number;
	formData: PaperFormData[]
}
export interface PaperConfig {
	id: number;
	type: string;
	name: string;
	icon: string;
	description: string;
}

export type PaperType = {
	description: string; // 描述
	icon: string; // 图标
	id: number; // ID
	name: string; // 名称
	type: "paper"; // 类型为 "paper"
	url?: string;
};

export type GenerateParams = {
	formData: any; // 表单数据
	id: number; // 可选的 ID
	isTask?: boolean; // 是否为任务书
	style: any //
	detail?: any;
	type?: string; // 论文类型
};

export enum PaperStatus {
	Wait = 0, // 等待生成
	Processing = 1, // 生成中
	Success = 2, // 成功
	Failed = 3, // 失败
}
export enum PaperHistoryStatus {
	ALL = -1,// 全部
	Wait = 0, // 等待生成
	Processing = 1, // 生成中
	Success = 2, // 成功
}

export type ValueAddedService = {
	id: number;
	title: string;
	price: string;
	originalPrice: string;
	power: number;
	tag: string;
	description: string;
	type: string;
	templateId: number;
	checked?: number;
	exampleUrl?: string
};
/**
 * {
		title: "任务书",
		docxUrl: "paper/3/1729148219961/任务书-乡村生活.docx",
		isGenerate: true,
		href: "",
		paperId: 437,
		status: 2
	},
 */
export type Subjoin = {
	title: string; // 标题
	docxUrl: string; // 文档URL
	isGenerate: boolean; // 是否已生成
	href: string; // 链接
	paperId: number | string; // 纸张ID
	status: PaperStatus; // 状态
}


export interface PaperHistory {
  id: number;
  userId: number;
  orgUserId: number;
  tempId: null;
  paperId: null;
  templateId: number;
  title: string;
  description: null;
  language: null;
  space: string;
  status: number;
  docxUrl: null;
  zipUrl: null;
  mode: string;
  power: number;
  type: string;
  price: string;
  words: string;
  totalPower: number;
  totalPrice: string;
  subjoins: Subjoin[];
  model: null;
  createdAt: string;
  updatedAt: string;
  deletedAt: null;
}


export interface TemplateDetailData {
	id: number;
	type: string;
	name: string;
	config: Config;
	formData: FormDatum[];
	style: Style;
	words: Words;
	extend: Extend;
}

interface Extend {
  icons: string[];
  examples: string[];
  content: Content[];
}

interface Content {
  label: string;
  values: string[];
}

interface Words {
  space: Space;
  total: Space;
}

interface Space {
  small: number;
  middle: number;
  max: number;
}

interface Style {
  lable: string;
  mainIcon: string;
  subItems: SubItem[];
}

interface SubItem {
  icon: string;
  label: string;
}

export interface FormDatum {
  label: string;
  formType: string;
  isRequired: boolean;
  field: string;
  defaultValue: string;
  placeholder?: string;
  options?: Option[];
  type?: string;
  description?: string[];
}

interface Option {
	label: string;
	value: string;
	tag?: string;
	isNew?: boolean;
	isHot?: boolean;
}

interface Config {
  'power.base.small': number;
  'power.base.middle': number;
  'power.base.max': number;
  'price.base.small': number;
  'price.base.middle': number;
  'price.base.max': number;
  'power.pro.small': number;
  'power.pro.middle': number;
  'power.pro.max': number;
  'price.pro.small': number;
  'price.pro.middle': number;
  'price.pro.max': number;
  'power.network.small': number;
  'power.network.middle': number;
  'power.network.max': number;
  'price.network.small': number;
  'price.network.middle': number;
  'price.network.max': number;
  'power.deepseek.small': number;
  'power.deepseek.middle': number;
  'power.deepseek.max': number;
  'price.deepseek.small': number;
  'price.deepseek.middle': number;
  'price.deepseek.max': number;
}

export enum PaperTypeEnum {
  Paper = 'paper',
  Proposal = 'proposal',
  Books = 'books',
  Course = 'course',
  Exam = 'exam',
  Adult = 'adult',
  Teach = 'teach',
  Practical = 'practical',
  Technical = 'technical',
  Career = 'career'
}

export const PaperTypeName = {
  [PaperTypeEnum.Paper]: '论文助手',
  [PaperTypeEnum.Proposal]: '开题报告',
  [PaperTypeEnum.Books]: '任务书',
  [PaperTypeEnum.Course]: '课程范文',
  [PaperTypeEnum.Exam]: '期末范文',
  [PaperTypeEnum.Adult]: '成教专升本范文',
  [PaperTypeEnum.Teach]: '教学设计',
  [PaperTypeEnum.Practical]: '实训报告',
  [PaperTypeEnum.Technical]: '职称范文',
  [PaperTypeEnum.Career]: '职业生涯规划书'
} as const;

export enum PaperModeEnum {
  Base = 'base',
  Pro = 'pro',
  Network = 'network',
  Deepseek = 'deepseek'
}

export const PaperModeName = {
  [PaperModeEnum.Base]: '标准版',
  [PaperModeEnum.Pro]: 'Pro版',
  [PaperModeEnum.Network]: 'Pro联网版',
  [PaperModeEnum.Deepseek]: 'Deepseek联网版'
} as const;

export interface PaperStatistics {
  total: number;
  list: List[];
}

interface List {
  title: string;
  value: number;
}
