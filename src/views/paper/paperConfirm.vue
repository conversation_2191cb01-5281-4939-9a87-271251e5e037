<template>
	<div class="page">
		<div class="confirm-container">
			<div class="flex flex-col gap-y-[20px] w-full max-w-[1180px] mx-auto">
				<!-- header -->
				<div class="header">
					<div class="flex items-center justify-between w-full">
						<div class="flex items-center cursor-pointer text-[#3D3D3D] text-[14px] leading-[18px]" @click="goBack">
							<IconArrowLeft />
							<span class="ml-1">返回提交页</span>
						</div>
						<span class="absolute left-1/2 transform -translate-x-1/2 font-bold text-[22px] leading-[29px]">确认订单</span>
					</div>
				</div>

				<!-- 订单内容 -->
				<div class="order-content">
					<div class="paper-info">
						<div class="paper-header">
							<div class="title">
								<IconPaper class="mr-[16px]" />
								<span class="text-[#3D3D3D] text-[16px] leading-[21px]">{{ paperDetail?.template
									?.name }}</span>
								<n-divider :vertical="true" style="--n-color:#3D3D3D;"></n-divider>
								<span class="text-[#3D3D3D] text-[14px] leading-[20px]">
									{{ paperDetail.paper?.title }}
								</span>
							</div>
							<div class="tags flex gap-x-[6px]">
								<div class="bg-[#F1F7FF] text-[#0E69FF] text-[12px] leading-[16px] rounded-[2px] px-[12px] py-[5px]"
									v-for="(item, index) in paperDetail.paper?.list" :key="index">{{
										item }}</div>
							</div>
						</div>
						<div class="paper-content">
							<div class="info-row">
								<div class="info-item">
									<span class="label">生成内容：</span>
									<span>{{ paperDetail.template?.name }}-{{ paperDetail.paper?.title }} x1</span>
								</div>

								<div class="info-item-right">
									<template v-for="(item, index) in paperDetail.template?.extend?.chapter" :key="index">
										<span class="text-[#9E9E9E]" v-if="item.title !== '范文示例'">{{ item.title }}</span>
										<span class="text-[#9E9E9E]  inline-block leading-[18px]" v-else>
											<IconPaper2 class="mr-[8px] inline-block" />
											<span class="text-[#0256FF] text-[14px] relative top-[1px] cursor-pointer"
												@click="handlePaperExampleClick">范文示例</span>
										</span>
										<n-divider v-if="index !== paperDetail.template?.extend?.chapter?.length! - 1" :vertical="true"
											style="--n-color:#9E9E9E;"></n-divider>
									</template>
								</div>
							</div>
							<n-divider style="--n-color:#D8D8D8; width: calc(100% - 36px); margin: 20px auto !important;"></n-divider>
							<div class="info-row flex flex-wrap">
								<div class="info-item flex-1 basis-[calc(50%-10px)] mb-[5px]"
									v-for="(item, index) in paperDetail.template?.extend?.content" :key="index">
									<span class="label">{{ item.label }}：</span>
									<template v-for="(subItem, subIndex) in item.values" :key="subIndex">
										<span class="text-[#9E9E9E]">{{ subItem }}</span>
										<n-divider v-if="subIndex !== item.values?.length! - 1" :vertical="true"
											style="--n-color:#9E9E9E;"></n-divider>
									</template>
								</div>
							</div>
						</div>
					</div>

					<!-- 增值服务部分 -->
					<div class="value-added-services" v-if="paperDetail?.subjoins?.length">
						<div class="service-title">
							<span>增值服务</span>

							<div class="flex items-center text-[#3D3D3D] font-normal">
								<div>
									<span>总金额: </span>
									<span class="price text-[#FF3838]">¥{{ totalSubjoinsPrice }}元</span>
								</div>
								<n-divider style="--n-color:#D8D8D8;" :vertical="true"></n-divider>
								<div class="">
									<n-popover style="background: linear-gradient(0deg, #FFFFFF 86%, #EEF5FF 100%);">
										<template #trigger>
											<div class="flex items-center gap-x-[4px] cursor-pointer">
												<IconPaper3 />
												<span></span>明细
											</div>
										</template>
										<div class=" w-[639px] ">
											<div class="flex items-center justify-between text-[12px] mb-[14px] mt-[8px]">
												<span>含{{paperDetail?.subjoins.filter(item => item.isSelect).length}}件增值服务</span>
												<span>
													合计<span class="price text-[#FF3838] text-[14px]">¥{{ totalSubjoinsPrice }}元</span>
												</span>
											</div>
											<div>
												<div class="flex items-center gap-x-[4px] justify-between">
													<span class="flex items-center gap-x-[4px]">
														<IconPaper3 />
														<span class="text-[#333333]">{{ paperDetail.paper?.title }}</span>
													</span>
													<span class="price flex items-center justify-start gap-x-[8px]">
														<span class="text-[#B3B3B3]">小计:</span>
														<span class="text-[#FF3838]">¥{{ paperDetail.paper?.price }}元</span>
													</span>
												</div>
											</div>
											<div v-for="(service, index) in paperDetail.subjoins.filter(item => item.isSelect)" :key="index"
												class="my-[14px]">
												<div class="flex items-center gap-x-[4px] justify-between">
													<span class="flex items-center gap-x-[4px]">
														<IconPaper3 />
														<span class="text-[#333333]">{{ service.title }}</span>
													</span>
													<span class="price flex items-center justify-start gap-x-[8px]">
														<span class="text-[#B3B3B3]">小计:</span>
														<span class="text-[#FF3838]">{{ service.price }}元</span>
														<span class="text-[#B3B3B3] line-through" v-if="Number(service.originalPrice) > 0">{{
															service.originalPrice }}元</span>
													</span>
												</div>
											</div>
										</div>
									</n-popover>
								</div>
							</div>

						</div>
						<div class="service-list">
							<div class="service-item" v-for="(service, index) in paperDetail.subjoins" :key="index"
								@click="toggleService(service.id)">
								<div class="service-name">
									{{ service.title }}

									<n-popover v-if="service.exampleUrl" trigger="hover" placement="top" :show-arrow="false">
										<template #trigger>
											<div>
												<span class="price" v-if="Number(service.price) > 0">{{ service.price }}元</span>
												<span class="original-price" v-if="Number(service.originalPrice) > 0">{{ service.originalPrice
												}}元</span>
												<span class="price-note" v-if="service.description">{{ service.description }}</span>
												<span
													class="bg-[#E1EBFF] text-[#0256FF] text-[12px] leading-[16px] rounded-tr-[8px] rounded-bl-[8px] px-[8px] py-[3px] ml-[12px] mr-[18px]">
													{{ service.tag }}
												</span>
											</div>
										</template>
										<n-image :src="service.exampleUrl" class="w-[462px]" object-fit="contain" preview-disabled />
									</n-popover>
									<template v-else>
										<span class="price" v-if="Number(service.price) > 0">{{ service.price }}元</span>
										<span class="original-price" v-if="Number(service.originalPrice) > 0">{{ service.originalPrice
										}}元</span>
										<span class="price-note" v-if="service.description">{{ service.description }}</span>
										<span v-if="service.tag"
											class="bg-[#E1EBFF] text-[#0256FF] text-[12px] leading-[16px] rounded-tr-[8px] rounded-bl-[8px] px-[8px] py-[3px] ml-[12px] mr-[18px]">
											{{ service.tag }}
										</span>
									</template>
								</div>
								<div class="checkbox">
									<div class="check-icon" :class="{ checked: service.isSelect }"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="calc-power w-full max-w-[1180px] mx-auto text-center mt-[28px] text-[#B5C0D4]">
					需要消耗算力: {{ calcPower }}
				</div>
				<!-- 支付按钮 -->
				<div class="payment-button">
					<n-button style="--n-text-color-hover:#fff;" type="primary" size="large" :loading="paymentLoading"
						@click="handlePayment">
						立即支付
					</n-button>
				</div>
			</div>
		</div>
	</div>
	<DemoModal v-model:visible="showDemoPaper" :title="'示例'">
		<n-image v-for="(item, index) in paperDetail?.template?.extend?.examples" :key="index" :src="item" class="w-[full]"
			object-fit="contain" preview-disabled />
	</DemoModal>
	<n-modal v-model:show="showModal">
		<n-card style="width: 287px;" :bordered="false" size="huge" role="dialog" aria-modal="true">
			<template #default>
				<div class="text-[14px] text-[#3D3D3D] text-center">
					<p>是否返回提交页？</p>
					<p>可在<span class="text-[#0E69FF]">"历史记录"</span>页面支付该订单</p>
				</div>
			</template>
			<template #footer>
				<div class="w-full flex flex-row gap-x-[10px]">
					<div
						class="bg-[#0E69FF] rounded-[4px] text-[#ffffff] text-[12px] w-[94px] h-[36px] flex justify-center items-center cursor-pointer"
						@click="handleNegClick">继续支付</div>
					<div
						class="bg-[#ffffff] rounded-[4px] border-[1px] border-[#0E69FF] text-[#0E69FF] text-[12px] w-[94px] h-[36px] flex justify-center items-center cursor-pointer"
						@click="handlePosClick">确认离开</div>
				</div>
			</template>
		</n-card>
	</n-modal>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from "vue";
import { NButton, useMessage, NPopover, NImage, NDivider, NModal, NCard } from "naive-ui";
import { useRoute, useRouter } from "vue-router";
import DemoModal from './components/demo-modal.vue';
import { usePaperStore } from "@/store";
import { storeToRefs } from "pinia";

// 路由和消息
const route = useRoute()
const router = useRouter()
const message = useMessage();
const store = usePaperStore()

const { id } = route.query;

const showDemoPaper = ref(false);

const handlePaperExampleClick = () => {
	showDemoPaper.value = true;
};

onMounted(() => {
	store.getPaperDetail(Number(id))
})

const { paperDetail } = storeToRefs(store);

watch(paperDetail, () => {
	if (paperDetail.value && paperDetail.value.paper) {
		console.log(paperDetail.value.paper.title);
	}
})

// 发射事件
const emit = defineEmits(['scroll']);

// 状态

// 状态
const paymentLoading = ref(false);

const showModal = ref(false);

const handleNegClick = () => {
	showModal.value = false;
};

const handlePosClick = () => {
	showModal.value = false;
	router.replace({ path: '/paper2' });
};

const goBack = () => {
	showModal.value = true;
};

// 计算总 power 值
const calcPower = computed(() => {
	// 基础 power
	const basePower = paperDetail.value?.paper?.power || 0;

	// 附加 power (从勾选的 subjoins 中获取)
	let additionalPower = 0;
	if (paperDetail.value?.subjoins) {
		additionalPower = paperDetail.value.subjoins
			.filter(item => item.isSelect)
			.reduce((sum, item) => sum + (item.power || 0), 0);
	}

	// 返回总 power
	return basePower + additionalPower;
});

// 计算勾选的增值服务总价
const totalSubjoinsPrice = computed(() => {
	if (!paperDetail.value?.subjoins?.length) return 0;

	const selectedServicesTotal = paperDetail.value.subjoins
		.filter(item => item.isSelect)
		.reduce((total, service) => total + Number(service.price), 0);

	// 返回勾选的增值服务总价
	return Math.round((selectedServicesTotal + paperDetail?.value?.paper?.price!) * 100) / 100;
});

const toggleService = (id: number) => {
	if (paperDetail.value && paperDetail.value.subjoins) {
		paperDetail.value.subjoins = paperDetail.value.subjoins.map(item => {
			if (item.id === id) {
				item.isSelect = !item.isSelect;
			}
			return item;
		});
	}
};

const handlePayment = () => {
	store.generatePaper({
		id: Number(id),
	});
};
// 生命周期钩子
onMounted(() => {
	// 可以在这里加载数据
});
</script>

<style lang="less" scoped>
.page {
	min-height: 100vh;
	background-color: #F7F9FF;
}

.confirm-container {
	width: 100%;

	.header {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		gap: 20px;
		margin-top: 32px;
		width: 100%;
	}

	.order-content {
		width: 100%;
		margin-top: 20px;
	}

	.paper-info {
		background: #FFFFFF;
		border-radius: 4px;
		border: 1px solid #0E69FF;
		margin-bottom: 14px;

		.paper-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10px 16px;
			background: #E2EDFF;
			border-radius: 4px;
			margin-bottom: 16px;

			.title {
				display: flex;
				align-items: center;
				color: #0E69FF;
				font-size: 16px;
				font-weight: 500;
			}

			.tags {
				font-size: 14px;
			}
		}

		.paper-content {
			padding-bottom: 8px;

			.info-row {
				display: flex;
				flex-wrap: wrap;
				gap: 6px;
				padding-left: 18px;
				padding-right: 28px;

				.info-item-right {
					flex: 1;
					text-align: right;
				}

				.info-item {
					width: 35%;

					&[class*="basis-"] {
						width: 100%;
					}

					.label {
						color: #666666;
					}
				}
			}
		}
	}

	.value-added-services {
		background: #FFFFFF;
		border-radius: 4px;
		padding: 20px;

		margin-top: 20px;

		.service-title {
			font-size: 16px;
			font-weight: 700;
			margin-bottom: 20px;
			color: #333333;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}

		.service-list {
			display: flex;
			flex-wrap: wrap;
			gap: 15px;

			.service-item {
				flex: 0 0 auto;
				min-width: 180px;
				background: #F7F9FF;
				border-radius: 4px;
				padding: 10px 15px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				cursor: pointer;
				transition: all 0.3s ease;
				border: 1px solid #E3ECF7;

				&:hover {
					background: #F0F5FF;
				}

				.service-name {
					font-size: 14px;
					display: flex;
					flex-wrap: nowrap;
					align-items: center;
					gap: 6px;
					white-space: nowrap;
					text-overflow: ellipsis;
					max-width: calc(100% - 30px);

					.price {
						color: #FF1C1C;
						font-weight: 500;
						margin-left: 5px;
					}

					.original-price {
						color: #999999;
						text-decoration: line-through;
						margin-left: 5px;
					}

					.price-note {
						color: #999999;
						font-size: 12px;
					}

					.price-unit {
						color: #FF1C1C;
					}
				}

				.checkbox {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 20px;
					height: 20px;
					cursor: pointer;

					.check-icon {
						width: 16px;
						height: 16px;
						background: #FFFFFF;
						border: 1px solid #DDDDDD;
						border-radius: 2px;
						position: relative;

						&.checked {
							background: #0E69FF;
							border-color: #0E69FF;

							&:after {
								content: '';
								position: absolute;
								top: 2px;
								left: 5px;
								width: 5px;
								height: 10px;
								border: solid white;
								border-width: 0 2px 2px 0;
								transform: rotate(45deg);
							}
						}
					}
				}
			}
		}
	}

	.total-section {
		background: #FFFFFF;
		border-radius: 4px;
		padding: 20px;
		margin-top: 20px;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		gap: 30px;

		.total-power {
			font-size: 14px;

			.power-value {
				color: #FF1C1C;
				font-weight: 500;
			}
		}

		.total-price {
			font-size: 16px;

			.price-value {
				color: #FF1C1C;
				font-weight: 700;
				font-size: 20px;
			}
		}
	}

	.payment-button {
		display: flex;
		justify-content: center;
		margin-top: 14px;

		.n-button {
			min-width: 200px;
		}
	}
}
</style>
