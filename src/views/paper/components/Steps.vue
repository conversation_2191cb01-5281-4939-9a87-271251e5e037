@@ -1,71 +0,0 @@
<script lang="ts" setup>
import { ref } from "vue";
import { NStep, NSteps } from "naive-ui";
import type { StepsProps } from "naive-ui";
import { textTransform } from "@/utils/textTransform";

const current = defineModel("current", { default: 1 });

const currentStatus = ref<StepsProps["status"]>("process");

const handleClick = (index: number) => {
	if (index > current.value) return;
	document
		.querySelector(`#step-${index}`)
		?.scrollIntoView({ behavior: "smooth" });
};
</script>

<template>
	<div
		class="2xl:w-[1000px] w-[750px] hidden md:block ipad:hidden fixed left-1/2 -translate-x-1/2 z-[9] paper-steps-container"
	>
		<div
			class="absolute left-0 top-[160px] -translate-x-full max-w-[200px] p-[20px] pr-[30px]"
		>
			<NSteps vertical :current="current" :status="currentStatus">
				<NStep
					:title="textTransform('输入论文题目')"
					description="生成千字大纲"
					@click="handleClick(1)"
				/>
				<NStep
					title="编辑大纲"
					:description="textTransform('生成论文初稿')"
					@click="handleClick(2)"
				/>
				<NStep
					:title="textTransform('下载论文')"
					:description="textTransform('一键快速下载论文')"
					@click="handleClick(3)"
				/>
			</NSteps>
		</div>
	</div>
</template>

<style lang="less">
.paper-steps-container {
	.n-step.n-step {
		height: 140px;
		flex-shrink: 0;
		flex-basis: 140px;
		flex-grow: 0;
	}
	.n-step-content-header__title {
		font-size: 16px;
		color: #3d3d3d;
		font-weight: normal;
	}
	.n-step-content__description {
		font-size: 14px;
		color: #999999;
	}
	.n-step--wait-status {
		.n-step-content-header__title,
		.n-step-content__description {
			color: #999999;
		}
	}
}
</style>
