<template>
	<div class="banner-slider-container">
		<n-carousel class="banner-slider" :show-arrow="false" autoplay>
			<n-image v-for="(slide, index) in slides" :key="index" :src="slide.image" preview-disabled class="slide-image"></n-image>
			<template #dots="{ total, currentIndex, to }">
				<ul class="custom-dots">
					<li v-for="index of total" :key="index" :class="{ ['is-active']: currentIndex === index - 1 }"
						@click="to(index - 1)"></li>
				</ul>
			</template>
		</n-carousel>
	</div>
</template>

<script setup lang="ts">
import { NCarousel, NImage } from 'naive-ui';
import bannerImage1 from '@/assets/paper/banner.png';
import bannerImage2 from '@/assets/paper/banner2.png';
import bannerImage3 from '@/assets/paper/banner3.png';
import bannerImage4 from '@/assets/paper/banner4.png';

const slides = [
	{
		image: bannerImage1
	},
	{
		image: bannerImage2
	},
	{
		image: bannerImage3
	},
	{
		image: bannerImage4
	}
];
</script>

<style lang="less" scoped>
.banner-slider-container {
	display: flex;
	justify-content: center;
	width: 100%;
}

.banner-slider {
	width: 100%;
	max-width: 1180px;
	max-height: 240px;

	.slide-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 100%;
		padding: 0 40px;

		.slide-text {
			flex: 1;

			.slide-title {
				font-size: 28px;
				font-weight: bold;
				margin-bottom: 16px;
				color: #333;
			}

			.slide-description {
				font-size: 16px;
				color: #666;
				max-width: 80%;
			}
		}

		.slide-image {
			flex: 1;
			display: flex;
			justify-content: flex-end;

			img {
				max-height: 240px;
				object-fit: contain;
			}
		}
	}
}

.custom-dots {
	display: flex;
	position: absolute;
	column-gap: 4px;
	bottom: 45px;
	left: 100px;
}

.custom-dots li {
	display: inline-block;
	width: 40px;
	height: 4px;
	border-radius: 4px;
	background-color: rgba(255, 255, 255, 0.4);
	transition:
		width 0.3s,
		background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	cursor: pointer;
}

.custom-dots li.is-active {
	background: #fff;
}

// 响应式样式
@media screen and (max-width: 1200px) {
	.banner-slider {
		max-width: 100%;
		padding: 0 15px;
	}
}

@media screen and (max-width: 992px) {
	.banner-slider {
		max-height: 220px;

		.slide-content {
			padding: 0 30px;

			.slide-text {
				.slide-title {
					font-size: 24px;
					margin-bottom: 12px;
				}

				.slide-description {
					font-size: 15px;
				}
			}

			.slide-image img {
				max-height: 220px;
			}
		}
	}

	.custom-dots {
		bottom: 35px;
		left: 80px;
	}

	.custom-dots li {
		width: 35px;
	}
}

@media screen and (max-width: 768px) {
	.banner-slider {
		max-height: 180px;

		.slide-content {
			padding: 0 20px;

			.slide-text {
				.slide-title {
					font-size: 20px;
					margin-bottom: 8px;
				}

				.slide-description {
					font-size: 14px;
					max-width: 90%;
				}
			}

			.slide-image {
				img {
					max-height: 180px;
				}
			}
		}
	}

	.custom-dots {
		bottom: 25px;
		left: 60px;
	}

	.custom-dots li {
		width: 30px;
		height: 3px;
	}
}

@media screen and (max-width: 576px) {
	.banner-slider {
		max-height: 150px;

		.slide-content {
			padding: 0 16px;

			.slide-text {
				.slide-title {
					font-size: 18px;
					margin-bottom: 4px;
				}

				.slide-description {
					font-size: 12px;
					max-width: 95%;
				}
			}

			.slide-image {
				img {
					max-height: 150px;
				}
			}
		}
	}

	.custom-dots {
		bottom: 15px;
		left: 40px;
	}

	.custom-dots li {
		width: 25px;
		height: 3px;
	}
}
</style>
