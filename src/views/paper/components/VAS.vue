<script lang="ts" setup>
// 增值服务包
import { NButton, NCheckbox, NEllipsis, NImage } from 'naive-ui'
import SelectedSvg from './svgs/selected.svg'
import { ref } from 'vue';

const $props = defineProps<{
	isTask?: boolean;
	title?: string;
	formData?: any;
	detail?: any;
	style?: any;
}>()

const educationValue = $props.formData.find((item: any) => item.field === 'education')?.value || '';
const eductionOptions = $props.detail.filter(item => item.field === 'education')?.[0]?.options || [];
// small 8000 middle 20000 max 30000
const spaceFieldValue = $props.formData.find((item: any) => item.field === 'space')?.value || '';
const spaceOptions = $props.detail.filter(item => item.field === 'space')?.[0]?.options || [];

const education = eductionOptions?.length ? eductionOptions.find((item: any) => item.value === educationValue)?.label : '';
const workCount = spaceOptions?.length ? spaceOptions.find((item: any) => item.value === spaceFieldValue)?.label : '';
// const style = {
// 	"lable": "您将获得",
// 	"mainIcon": "https://cdn2.weimob.com/static/aiwork365-web-stc/paper/icon/book-close.png",
// 	"subItems": [
// 		{
// 			"icon": "https://cdn2.weimob.com/static/aiwork365-web-stc/paper/icon/book-open.png",
// 			"label": "文献综述\n×1"
// 		},
// 		{
// 			"icon": "https://cdn2.weimob.com/static/aiwork365-web-stc/paper/icon/translate.png",
// 			"label": "中英文摘要\n×1"
// 		},
// 		{
// 			"icon": "https://cdn2.weimob.com/static/aiwork365-web-stc/paper/icon/tag.png",
// 			"label": "中英参考文献\n×1"
// 		}
// 	]
// }

</script>

<template>
	<div class="vas rounded-[14px] py-[40px] px-[20px] relative bg-white w-[750px] mx-auto sm:w-full"
		style="box-shadow: 1px -1px 19px 0px #CBD6E7;">
		<div class="text-sm color-[#3d3d3d] font-bold">{{ $props.style.lable }}</div>
		<div class="mt-[15px]">
			<div class="base-card">
				<div class="base-card_icon flex items-center justify-center">
					<!-- <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none"
						version="1.1" width="30" height="30" viewBox="0 0 30 30">
						<g>
							<g>
								<path
									d="M17.70974453125,2.93115234375L6.72046453125,2.93115234375C5.20256453125,2.93115234375,3.98689083125,4.05605234375,3.98689083125,5.4449123437499996L3.97314453125,25.55495234375C3.97314453125,26.94385234375,5.18885453125,28.06875234375,6.70671453125,28.06875234375L23.20434453125,28.06875234375C24.72224453125,28.06875234375,25.95174453125,26.94385234375,25.95174453125,25.55495234375L25.95174453125,10.47245234375L17.70974453125,2.93115234375ZM20.45704453125,23.04125234375L9.46778453125,23.04125234375L9.46778453125,20.52745234375L20.45704453125,20.52745234375L20.45704453125,23.04125234375ZM20.45704453125,18.01375234375L9.46778453125,18.01375234375L9.46778453125,15.49995234375L20.45704453125,15.49995234375L20.45704453125,18.01375234375ZM17.14824453125,11.00000234375L17.14824453125,4.08716234375L24.70334453125,11.00000234375L17.14824453125,11.00000234375Z"
									fill="#3680F9" fill-opacity="1" style="mix-blend-mode:passthrough" />
							</g>
						</g>
					</svg> -->
					<NImage :src="style.mainIcon" preview-disabled width="24px" height="24px" />
				</div>
				<div class="base-card_content">
					<div class="base-card_content_title text-[14px] font-bold">
						{{ isTask ? "任务书" : "正文" }} ｜ {{ $props.title }}
					</div>
					<div class="base-card_content_desc" v-if="isTask">
						<span class="mr-[10px]">任务书内容包含 ：1.基本内容和要求 2. 参考资料 3. 应完成工作内容 4. 进度安排 5.其他</span>
						<!-- <NButton type="warning" round size="tiny" >含无限改稿</NButton> -->
					</div>
					<div v-else-if="(education || workCount) && !isTask">
						<span class="mr-[10px]">{{ education + ' ' }}{{ workCount }}</span>
					</div>
				</div>
				<div class="base-card_selected-icon">
					<SelectedSvg />
				</div>
			</div>
		</div>

		<div class="mt-[15px] flex justify-between items-stretch gap-[15px] sm:flex-col">
			<div class="base-card" v-for="(item, index) in $props.style.subItems" :key="index">
				<div class="base-card_icon flex items-center justify-center">
					<NImage :src="item.icon" preview-disabled width="24px" height="24px" />
					<!-- <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none"
						version="1.1" width="19" height="19" viewBox="0 0 19 19">
						<g>
							<g>
								<path
									d="M16.712178466796875,13.6926C16.712178466796875,14.0371,16.***************,14.3074,16.057578466796876,14.3074L12.130178466796876,14.3074C11.***************,14.3097,10.733958466796874,14.5254,10.166248466796874,14.9229L10.166248466796874,5.07714C10.164568466796876,4.057040000000001,11.044478466796875,3.22959,12.129578466796875,3.23086L16.057578466796876,3.23086C16.***************,3.23086,16.712178466796875,3.5017199999999997,16.712178466796875,3.8462899999999998L16.712178466796875,13.692L16.712178466796875,13.6926ZM8.856938466796876,14.9229C8.289388466796876,14.5255,7.601218466796875,14.3098,6.893578466796875,14.3074L2.9650384667968748,14.3074C2.6021784667968753,14.311,2.3069584667968748,14.0337,2.310378466796875,13.6926L2.310378466796875,3.84572C2.310378466796875,3.50114,2.5985084667968747,3.23029,2.9650384667968748,3.23029L6.892358466796875,3.23029C7.977488466796875,3.22871,8.857678466796875,4.05589,8.856328466796874,5.0760000000000005L8.856328466796874,14.9223L8.856938466796876,14.9229ZM16.058178466796875,2.00000224795L12.130778466796874,2.00000224795C11.056778466796874,2.00000224795,10.113368466796874,2.492574,9.511588466796875,3.25543C8.909818466796875,2.492574,7.966438466796875,2.00000224795,6.892968466796875,2.00000224795L2.9656484667968748,2.00000224795C1.8805184667968748,1.99842044,1.000333146796875,2.825609,1.001680205876875,3.84572L1.001680205876875,13.6926C1.000334156796875,14.7125,1.880148466796875,15.5396,2.9650384667968748,15.5383L6.892968466796875,15.5383C7.978338466796875,15.5367,8.858618466796875,16.3642,8.856938466796876,17.3846C8.856938466796876,17.729100000000003,9.145058466796876,18,9.511588466796875,18C9.878128466796875,18,10.166248466796874,17.729100000000003,10.166248466796874,17.3846C10.164898466796876,16.3647,11.044678466796874,15.5376,12.129578466796875,15.5389L16.057578466796876,15.5389C17.143178466796876,15.5404,18.023478466796874,14.7126,18.021478466796875,13.692L18.021478466796875,3.84572C18.022878466796875,2.8258330000000003,17.143078466796876,1.99873699,16.058178466796875,2.00000224795Z"
									fill="#3680F9" fill-opacity="1" style="mix-blend-mode:passthrough" />
							</g>
							<g>
								<rect x="3.737017869949341" y="7" width="4.1637797355651855" height="1" rx="0"
									fill="#3680F9" fill-opacity="1" />
							</g>
							<g>
								<rect x="11.023632049560547" y="7" width="4.1637797355651855" height="1" rx="0"
									fill="#3680F9" fill-opacity="1" />
							</g>
							<g>
								<rect x="3.737017869949341" y="9" width="4.1637797355651855" height="1" rx="0"
									fill="#3680F9" fill-opacity="1" />
							</g>
							<g>
								<rect x="11.023632049560547" y="9" width="4.1637797355651855" height="1" rx="0"
									fill="#3680F9" fill-opacity="1" />
							</g>
						</g>
					</svg> -->
				</div>
				<div class="base-card_content">
					<NEllipsis class="base-card_content_title">
						{{ item.label }}
					</NEllipsis>
					<!-- <div class="base-card_content_desc">
						<span class="mr-[10px]">x 1</span>
					</div> -->
				</div>
				<div class="base-card_selected-icon">
					<SelectedSvg />
				</div>
			</div>
			<!-- <div class="base-card">
				<div class="base-card_icon">
					<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none"
						version="1.1" width="19" height="19" viewBox="0 0 19 19">
						<g>
							<g>
								<path
									d="M4.25,13L4.25,13.75C4.249750000000001,14.535,4.85474,15.1874,5.6375,15.2463L5.75,15.25L7.25,15.25C7.66421,15.25,8,15.5858,8,16C8,16.4142,7.66421,16.75,7.25,16.75L5.75,16.75C4.09315,16.75,2.75,15.4069,2.75,13.75L2.75,13C2.75,12.5858,3.0857900000000003,12.25,3.5,12.25C3.9142099999999997,12.25,4.25,12.5858,4.25,13ZM14.0945,8.7355L17.0945,16.235500000000002C17.1933,16.***************,17.011400000000002,16.750500000000002,16.7457,16.75L15.9372,16.75C15.784,16.75,15.6462,16.6567,15.5892,16.514499999999998L14.783,14.5L11.7155,14.5L10.91075,16.514499999999998C10.85365,16.657,10.71549,16.7503,10.562,16.75L9.754999999999999,16.75C9.48963,16.7499,9.30828,16.4818,9.407,16.235500000000002L12.407,8.7355C12.464,8.59324,12.6018,8.5,12.755,8.5L13.7465,8.5C13.9,8.49973,14.0382,8.59302,14.0953,8.7355L14.0945,8.7355ZM13.25,10.66375L12.3148,13L14.1838,13L13.25,10.66375ZM6.5,2.875L6.5,4L9.125,4C9.33211,4,9.5,4.16789,9.5,4.375L9.5,8.875C9.5,9.08211,9.33211,9.25,9.125,9.25L6.5,9.25L6.5,11.125C6.5,11.33211,6.33211,11.5,6.125,11.5L5.375,11.5C5.16789,11.5,5,11.33211,5,11.125L5,9.25L2.375,9.25C2.167893,9.25,2,9.08211,2,8.875L2,4.375C1.99999986589,4.16789,2.167893,4,2.375,4L5,4L5,2.875C5,2.667893,5.16789,2.5,5.375,2.5L6.125,2.5C6.33211,2.499999731779,6.5,2.667893,6.5,2.875ZM13.25,3.25C14.9069,3.25,16.25,4.59315,16.25,6.25L16.25,7C16.25,7.41421,15.9142,7.75,15.5,7.75C15.0858,7.75,14.75,7.41421,14.75,7L14.75,6.25C14.75,5.42157,14.0784,4.75,13.25,4.75L11.75,4.75C11.33579,4.75,11,4.41421,11,4C11,3.5857900000000003,11.33579,3.25,11.75,3.25L13.25,3.25ZM5,5.5L3.5,5.5L3.5,7.75L5,7.75L5,5.5ZM8,5.5L6.5,5.5L6.5,7.75L8,7.75L8,5.5Z"
									fill="#3680F9" fill-opacity="1" style="mix-blend-mode:passthrough" />
							</g>
						</g>
					</svg>
				</div>
				<div class="base-card_content">
					<div class="base-card_content_title ">
						中英文摘要
					</div>
					<div class="base-card_content_desc">
						<span class="mr-[10px]">x 1</span>
					</div>
				</div>
				<div class="base-card_selected-icon">
					<SelectedSvg />
				</div>
			</div>
			<div class="base-card">
				<div class="base-card_icon">
					<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none"
						version="1.1" width="19" height="19" viewBox="0 0 19 19">
						<g>
							<g>
								<path
									d="M3.32475,15.7352L3.32475,14.7143L14.6753,14.7143L14.6753,15.7276L3.32475,15.7352ZM5.03359,2.28762L5.03359,7.24C5.04211,7.58329,5.33092,7.85724,5.68421,7.85714C6.11267,7.85684,6.5238,7.69262,6.82867,7.4L7.48712,6.79048L8.1299,7.41524C8.42509,7.69207,8.8172,7.84996,9.227319999999999,7.85714C9.586649999999999,7.85714,9.877939999999999,7.57402,9.877939999999999,7.22476L9.877939999999999,2.28762L13.9517,2.28762C14.3383,2.28762,14.6517,2.60102,14.6517,2.9876199999999997L14.6517,13.4343L3.32475,13.4343L3.32475,2.9876199999999997C3.32475,2.60102,3.63815,2.28762,4.02475,2.28762L5.03359,2.28762ZM8.57671,2.28762L8.57671,6.03619L8.41993,5.88381L7.80851,5.28952C7.63192,5.12209,7.35017,5.12209,7.17357,5.28952L6.3897,6.05143L6.3897,2.2419000000000002L8.57671,2.28762ZM14.6753,1.0000000000000093L3.32475,1.0000000000000093C2.5931100000000002,0.9999999152264,2.000000122822,1.576487,2,2.28762L2,15.7352C2.0127107,16.4373,2.60208,17,3.32475,17L14.6753,17C15.4069,17,16,16.4235,16,15.7124L16,2.28762C16,1.576486,15.4069,1.0000000000000093,14.6753,1.0000000000000093Z"
									fill="#3680F9" fill-opacity="1" style="mix-blend-mode:passthrough" />
							</g>
						</g>
					</svg>
				</div>
				<div class="base-card_content">
					<div class="base-card_content_title">
						中英文参考文献
					</div>
					<div class="base-card_content_desc">
						<span class="mr-[10px]">x 10</span>
					</div>
				</div>
				<div class="base-card_selected-icon">
					<SelectedSvg />
				</div>
			</div> -->
			<!-- <div class="base-card">
				<div class="base-card_icon">
					<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none"
						version="1.1" width="19" height="19" viewBox="0 0 19 19">
						<g>
							<g>
								<path
									d="M16.172800000000002,4.6346799999999995L16.172800000000002,14.3739L2.46565,14.3739L2.46565,4.6346799999999995L16.172800000000002,4.6346799999999995ZM17.6551,3L1,3L1,16L17.6551,16L17.6551,3Z"
									fill="#3680F9" fill-opacity="1" style="mix-blend-mode:passthrough" />
							</g>
						</g>
					</svg>
				</div>
				<div class="base-card_content">
					<div class="base-card_content_title">
						致谢模板
					</div>
					<div class="base-card_content_desc">
						<span class="mr-[10px]">x 1</span>
					</div>
				</div>
				<div class="base-card_selected-icon">
					<SelectedSvg />
				</div>
			</div> -->
		</div>

		<!-- 增值服务包 -->
		<!-- <div class="text-sm color-[#3d3d3d] font-bold mt-[30px]">增值服务包</div>
		<div class="mt-[15px] flex justify-between items-stretch">
			<div class="vas-card">
				<div class="flex">
					<div class="vas-card_title">开题报告</div>
					<div class="vas-card_tag">主题明确 重点聚焦</div>
				</div>
				<div class="vas-card_desc">
					高效、规范、专业的开题助手
				</div>
			</div>
			<div class="vas-card">
				<div class="flex">
					<div class="vas-card_title">任务书</div>
					<div class="vas-card_tag">专业清晰 规范标准</div>
				</div>
				<div class="vas-card_desc">
					描述性文字描述性文字描述性
				</div>
			</div>
			<div class="vas-card">
				<div class="flex">
					<div class="vas-card_title">答辩PPT</div>
					<div class="vas-card_tag">内容扎实 设计精美</div>
				</div>
				<div class="vas-card_desc">
					描述性文字描述性文字描述性
				</div>
			</div>
		</div>

		<div class="flex justify-center mt-[40px] items-center gap-[15px]">
			<NCheckbox></NCheckbox>
			<NButton type="primary" size="small" style="--n-border-radius: 6px;--n-height: 33px;--n-width: 200px;">立即购买</NButton>
		</div> -->
	</div>
</template>

<style lang="less" scoped>
.vas {
	.base-card {
		flex: 1;
		border: 1px solid #E1ECFA;
		background: #F6FAFF;
		display: flex;
		gap: 12px;
		justify-content: center;
		align-items: center;
		padding: 15px;
		font-size: 12px;
		color: #3d3d3d;
		border-radius: 6px;

		&_icon {
			flex-grow: 0;
			flex-shrink: 0;
		}

		&_content {
			flex: 1;
			display: flex;
			flex-direction: column;

			&_title {}

			&_desc {
				color: #666;
			}
		}

		&_selected-icon {
			flex-grow: 0;
			flex-shrink: 0;
			width: 20px;
			height: 20px;
		}
	}

	.vas-card {
		padding: 18px 20px;
		box-sizing: border-box;
		border: 1px solid #E3ECF7;
		border-radius: 6px;

		&_title {
			font-size: 14px;
			font-weight: 700;
		}

		&_tag {
			background: #FFFBD9;
			border-radius: 2px;
			padding: 2px 6px;
			font-size: 12px;
			color: #D08A22;
			height: 18px;
			box-sizing: border-box;
			margin-left: 6px;
			display: flex;
			align-items: center;
		}

		&_desc {
			font-size: 12px;
			color: #666666;
		}
	}
}
</style>
