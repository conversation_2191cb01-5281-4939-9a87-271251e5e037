<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import { NButton, NProgress } from "naive-ui";
import Note2Svg from "./svgs/note-2.svg";
import { PaperStatus, Subjoin } from "../types";
import { downloadFile } from "@/utils/downloadFile";
import {
	fetchGeneratePaper,
	fetchGeneratePaperProcess,
	fetchPaperUrl,
	fetchValueAddedGeneratePaper,
	fetchValueAddedPaperProgressByMessageId,
	fetHistoryPaperList,
} from "../apis";
import { SelfRequestCode } from "@/utils/request";
import dayjs from 'dayjs'
import { useUserStore } from "@/store";
import SubJoinItem from './SubJoin.vue'
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import { useRequest } from "vue-hooks-plus";
const userStore = useUserStore();

const $props = defineProps<{
	refresh?: any;
}>();

const historyPaperList = ref<any[]>([]);
const paperInfo = ref<any>();
const generating = ref(false)
const { run: getHistoryPaperList } = useRequest<{ rows: any[] }>(() => fetHistoryPaperList({
	pageSize: 20,
	page: 1,
}), {
	manual: true,
	onSuccess: (data) => {
		historyPaperList.value = data.rows;
	}
})
onMounted(() => {
	getHistoryPaperList();
});

watch(
	() => $props.refresh,
	() => {
		console.log('refresh');
		
		// if (loading) return
		getHistoryPaperList();
	}
);

// const getHistoryPaperList = () => {
// 	// if(!userStore.userInfo.uid) return
// 	fetHistoryPaperList<any>({
// 		pageSize: 20,
// 		page: 1,
// 	}).then((data) => {
// 		historyPaperList.value = data.rows;
// 	});
// };

const downloadPaper = (id) => {
	fetchPaperUrl<{ url: string, zipUrl?: string }>({ id }).then((data) => downloadFile(data.zipUrl || data.url));
};

const handleGeneratePaper = async (paperId) => {
	paperInfo.value = null;
	generating.value = true;
	fetchValueAddedGeneratePaper({
		id: paperId,
	}).then((data) => {
		getHistoryPaperList()
		const { messageId } = data || {};
		document.getElementById("history-container")?.scrollIntoView({ behavior: "smooth" });
		const timer = setInterval(() => {
			fetchValueAddedPaperProgressByMessageId({
				messageId,
			}).then((data) => {
				const { message, status, step, url, title } = data || {};
				if (status === "fail") {
					generating.value = false;
					window.$notification?.error({
						title: "生成失败",
						content: message,
						duration: 3000,
					});
					paperInfo.value = null
					generating.value = false;
					clearInterval(timer);
					getHistoryPaperList()
					return;
				}
				if (status === "success") {
					generating.value = false;
					clearInterval(timer);
					paperInfo.value = null;
					downloadFile(url);
					getHistoryPaperList()
					window.$notification?.success({
						title: "生成成功",
						duration: 3000,
					});
					return;
				}
				paperInfo.value = {
					percent: Math.floor((100 / 6) * step),
					status,
					message,
					step,
					title,
					url,
				};
			});
		}, 1000);
	}).then(() => {
	}).catch(err => {
		generating.value = false;
		// if(err?.errcode == SelfRequestCode.NeedPay) {
		// 	window.$aiwork.openRecharge({ type: "paper", paperId }).then(() => {
		// 		handleGeneratePaper(paperId);
		// 	});
		// }
	});
};
watch(() => generating.value, (val) => {
	console.log(1111, val)
})
const options = ref({
	// 唯一key值
	rowKey: 'id',
	// 卡片之间的间隙
	gutter: 17,
	// 是否有周围的gutter
	hasAroundGutter: true,
	// 卡片在PC上的宽度
	width: 368,
	// 自定义行显示个数，主要用于对移动端的适配
	breakpoints: {
		1200: {
			// 当屏幕宽度小于等于1200
			rowPerView: 2,
		},
		800: {
			// 当屏幕宽度小于等于800
			rowPerView: 2,
		},
		500: {
			// 当屏幕宽度小于等于500
			rowPerView: 2,
		},
	},
	// 动画效果
	animationEffect: 'animate__fadeInUp',
	// 动画时间
	animationDuration: 500,
	// 动画延迟
	animationDelay: 300,
	// 背景色
	backgroundColor: '',
	// imgSelector
	imgSelector: 'src.original',
	// 是否懒加载
	lazyload: false,
})
</script>

<template>
	<div class="history-container" id="history-container" v-if="historyPaperList && historyPaperList.length">
		<div class="history-title">一键生成 极速下载</div>
		<div v-if="paperInfo" class="mt-[50px] flex justify-center">
			<NProgress type="circle" :percentage="paperInfo.percent || 0" :gap-offset-degree="180" style="width: 222px">
				<div>
					<div class="text-[18px] text-center block">
						{{ paperInfo.message || "正在分析" }}
					</div>
					<div class="text-[14px] text-center">
						{{ paperInfo.percent || 0 }}%
					</div>
				</div>
			</NProgress>
		</div>
		<Waterfall :list="historyPaperList" :row-key="options.rowKey">
			<template #default="{ item }">
				<div class="history-item">
					<div class="history-item_header">
						<div class="history-item_header_icon">
							<Note2Svg class="w-full h-full" />
						</div>
						<div class="history-item_header_title">{{ item.title }}</div>
						<div v-if="false" class="history-item_header_extra">本科毕业论文</div>
					</div>
					<div class="history-item_time">{{ dayjs(item.updatedAt).format("YYYY-MM-DD HH:mm:ss") }}</div>
					<div class="history-item_body">
						<SubJoinItem v-for="(subjoin, index) in item.subjoins" :sub-join="subjoin"
							:key="index + item.title" :paper-status="item.status" />
						<div class="flex my-[12px] justify-center opacity-0"
							:class="{ '!opacity-100': item.power && item.status === PaperStatus.Wait }">
							<div
								class="inline-block  px-[12px] py-[3px] text-[10px] bg-[#E7EBF7] color-[#9FA6BB] rounded-[4px]">
								需消耗 {{ item.power }} 算力
							</div>
						</div>
						<NButton v-if="item.status === PaperStatus.Wait" type="primary" block
							style="--n-border-radius: 6px" :disabled="!!generating"
							@click="handleGeneratePaper(item.id)">立即支付</NButton>
						<NButton v-else-if="item.status === PaperStatus.Processing" disabled type="primary" block
							style="--n-border-radius: 6px">生成中</NButton>
						<NButton v-else-if="item.status === PaperStatus.Success" type="success" block
							style="--n-border-radius: 6px" @click="downloadPaper(item.id)">立即下载</NButton>
					</div>
				</div>
			</template>
		</Waterfall>

		<!-- <div class="history-wrapper">
			<div class="history-item" v-for="item in historyPaperList" :key="item.id">
				<div class="history-item_header">
					<div class="history-item_header_icon">
						<Note2Svg class="w-full h-full" />
					</div>
					<div class="history-item_header_title">{{ item.title }}</div>
					<div v-if="false" class="history-item_header_extra">本科毕业论文</div>
				</div>
				<div class="history-item_time">{{ dayjs(item.updatedAt).format("YYYY-MM-DD HH:mm:ss") }}</div>
				<div class="history-item_body">
					<SubJoinItem v-for="(subjoin, index) in item.subjoins" :sub-join="subjoin" :key="index + item.title"
						:paper-status="item.status" />
					<div class="flex my-[12px] justify-center opacity-0"
						:class="{ '!opacity-100': item.power && item.status === PaperStatus.Wait }">
						<div
							class="inline-block  px-[12px] py-[3px] text-[10px] bg-[#E7EBF7] color-[#9FA6BB] rounded-[4px]">
							需消耗 {{ item.power }} 算力
						</div>
					</div>
					<NButton v-if="item.status === PaperStatus.Wait" type="primary" block style="--n-border-radius: 6px"
						:disabled="!!generating" @click="handleGeneratePaper(item.id)">立即支付</NButton>
					<NButton v-else-if="item.status === PaperStatus.Processing" disabled type="primary" block
						style="--n-border-radius: 6px">生成中</NButton>
					<NButton v-else-if="item.status === PaperStatus.Success" type="success" block
						style="--n-border-radius: 6px" @click="downloadPaper(item.id)">立即下载</NButton>
				</div>
			</div>
		</div> -->
	</div>
</template>

<style lang="less" scoped>
.history-container {
	.history-title {
		font-size: 14px;
		color: #3d3d3d;
		font-weight: 700;
	}

	.history-wrapper {
		display: grid;
		grid-template-columns: repeat(2, minmax(200px, 1fr));
		gap: 15px;
		margin-top: 25px;
	}

	.history-item {
		background: #f8f8fb;
		padding: 18px;
		border-radius: 10px;
	}

	.history-item_header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		gap: 7px;
	}

	.history-item_header_icon {
		width: 23px;
		height: 23px;
	}

	.history-item_header_title {
		flex: 1;
		font-size: 14px;
		color: #3d3d3d;
	}

	.history-item_header_extra {
		font-size: 12px;
		color: #666;
	}

	.history-item_time {
		margin-top: 8px;
		font-size: 10px;
		color: #888;
	}

	.history-item_body {
		padding: 20px 15px;
		border-radius: 10px;
		font-size: 12px;
		color: #666;
		background-color: #fff;
		margin-top: 10px;
	}
}
</style>
