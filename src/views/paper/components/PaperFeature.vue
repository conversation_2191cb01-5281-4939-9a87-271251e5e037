<template>
	<div class="paper-feature-button" :class="{ 'selected': selected }" @click="handleClick(id!)" v-replace>
		{{ name }}
	</div>
</template>

<script setup lang="ts">
import { PaperConfig } from '../types';

interface Props extends Partial<PaperConfig> {
	selected: boolean;
}
interface Emits {
	(event: 'select', id: number): void;
}

defineProps<Props>();
const $emit = defineEmits<Emits>();

const handleClick = (id: number) => {
	$emit('select', id);
};
</script>

<style lang="less" scoped>
.paper-feature-button {
	width: 160px;
	height: 55px;
	border-radius: 5.49px;
	opacity: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	background: #FFFFFF;
	box-sizing: border-box;
	border: 1px solid #FFFFFF;
	box-shadow: 0px 2px 10px 0px rgba(222, 226, 238, 0.5);
	cursor: pointer;

	&.selected {
		border: 1px solid #0E69FF;
	}
}

/* 响应式样式 */
@media screen and (max-width: 1200px) {
	.paper-feature-button {
		width: 150px;
		height: 50px;
	}
}

@media screen and (max-width: 992px) {
	.paper-feature-button {
		width: 140px;
		height: 48px;
		font-size: 14px;
	}
}

@media screen and (max-width: 768px) {
	.paper-feature-button {
		width: 130px;
		height: 45px;
		font-size: 13px;
	}
}

@media screen and (max-width: 576px) {
	.paper-feature-button {
		width: 110px;
		height: 40px;
		font-size: 12px;
		border-radius: 4px;
	}
}
</style>