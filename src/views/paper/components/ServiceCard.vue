<script lang="ts" setup>
import { NCheckbox, NPopover, NImage, NModal, NCard } from 'naive-ui';
import QuestionImg from '@/assets/images/question.png'
import { ref, watch } from 'vue';
import { ValueAddedService } from '../types';
import WarningImg from '@/assets/images/warning.png';

interface Props extends ValueAddedService {
    checked?: number
}
interface Emit {
    (ev: 'checked', data: { checked: number, id: number, type: string }): void
    (ev: 'scroll'): void
}
const props = defineProps<Props>()
const emit = defineEmits<Emit>()
const cardChecked = ref(false)
const showRechargeTipsModal = ref(false)
const showRechargeCancelTipsModal = ref(false)

watch(() => props.checked, (v) => {
    cardChecked.value = v === 1
})


const handleChecked = (e) => {
    // type是chart的单独处理
    // 当type是chart的时候 并且没有做选择 此时用户是想勾选这个服务 需要弹出提示 您选择了图表功能，请于大纲处勾选左侧的图表进行使用
    // 当type是chart的时候 并且做了选择  此时用户是补想勾选这个服务 需要弹出提示 取消此增值服务后，图表功能将不会生效，此操作不可撤回，请慎重选择
    // if (props.type === 'chart') {
    //     if (cardChecked.value) {
    //         showRechargeCancelTipsModal.value = true
    //         return
    //     } else {
    //         showRechargeTipsModal.value = true
    //         return
    //     }
    // }
    cardChecked.value = !cardChecked.value
    emit('checked', { checked: props.checked ? 0 : 1, id: props.id, type: props.type })
}

const handleShowRechargeTipsOk = () => {
    cardChecked.value = true
    showRechargeTipsModal.value = false
    emit('checked', { checked: 1, id: props.id, type: props.type })
    emit('scroll')
}

const handleShowRechargeCancelTipsOk = () => {
    cardChecked.value = false
    showRechargeCancelTipsModal.value = false
    emit('checked', { checked: 0, id: props.id, type: props.type })
}
const handleShowRechargeCancelTipsCancel = () => {
    showRechargeCancelTipsModal.value = false
}

</script>

<template>
    <div class=" flex flex-col w-[calc(33%-7.5px)] sm:w-auto sm:min-w-[40%] sm:max-w-[50%] sm:flex-1">
        <div class=" extra-service-card  border-[1px] rounded-[4px] p-[20px] relative cursor-pointer select-none"
            :class="checked ? 'bg-[#EEF7FF]' : 'bg-[#fff]'" @click="handleChecked">
            <div class=" flex flex-row  gap-x-[8px]">
                <NCheckbox class="leading-[21px]" :checked="checked === 1" />
                <div class=" extra-service-card-content  flex-1">
                    <div class=" text-[#3d3d3d] font-bold text-[16px] leading-[21px] sm:text-[14px]">
                        {{ props.title }}
                        <NPopover trigger="hover" v-if="props?.exampleUrl">
                            <template #trigger>
                                <NImage :src="QuestionImg" preview-disabled class=" w-[12px] h-[12px]" />
                            </template>
                            <NImage :src="props.exampleUrl" preview-disabled width="664px" height="486px"
                                object-fit="cover" />
                        </NPopover>
                    </div>
                    <div class=" leading-[19px]">
                        <span class=" text-[#FC4700] text-[14px] font-bold leading-[18px] pr-[8px]">{{ props.price
                            }}<span class="text-[#FC4700] text-[10px] pl-[2px] font-normal">元</span></span>
                        <span class=" text-[#AAAAAA] text-[10px] line-through">{{ props.originalPrice }}元</span>
                    </div>

                    <div class=" absolute text-[12px] text-[#fff] leading-[20px] px-[8px] top-[-5px] right-0 rounded-tr-[8px]
                        rounded-bl-[8px] bg-gradient-to-r to-[#FF2D03] from-[#FF8503]">
                        {{ props.tag }}</div>
                </div>
            </div>
        </div>
        <div v-if="props.description"
            class=" bg-[#FFE6E6] text-[#FF380B] text-[10px] leading-[22px] rounded-[4px] text-center tips mt-[9px]"> {{
            props.description }} </div>
    </div>
    <n-modal v-model:show="showRechargeTipsModal">
        <n-card style="width: 355px;--n-padding-left:46px;" :bordered="false" size="huge" role="dialog"
            aria-modal="true">
            <template #header>
                <div class=" flex flex-row items-center justify-center w-full">

                    <span class="  text-[18px]">
                        提示
                    </span>
                </div>
            </template>
            <template #default>
                <div class=" text-[16px] text-[#3D3D3D] text-center">您选择了图表功能，请于大纲处勾选右侧的图表进行使用
                </div>
            </template>
            <template #footer>
                <div class=" w-full flex flex-row gap-x-[10px] items-center justify-center">
                    <div class=" bg-[#317FFF] text-[#ffffff] text-[12px] w-[94px] h-[36px] flex justify-center items-center cursor-pointer rounded-[5px]"
                        @click="handleShowRechargeTipsOk">
                        确认</div>
                </div>
            </template>
        </n-card>
    </n-modal>
    <n-modal v-model:show="showRechargeCancelTipsModal">
        <n-card style="width: 355px;--n-padding-left:22px;" :bordered="false" size="huge" role="dialog"
            aria-modal="true">
            <template #header>
                <div class=" flex flex-row items-center justify-center w-full">
                    <NImage :src="WarningImg" preview-disabled width="18px" height="18px" class=" mr-[8px]" />
                    <span class="  text-[18px]">
                        提示
                    </span>
                </div>
            </template>
            <template #default>
                <div class=" text-[16px] text-[#3D3D3D] text-center">取消此增值服务后，图表功能将不会生效，此操作不可撤回，请慎重选择
                </div>
            </template>
            <template #footer>
                <div class=" w-full flex flex-row gap-x-[10px] items-center justify-center">
                    <div class=" bg-[#317FFF] text-[#ffffff] text-[12px] w-[94px] h-[36px] flex justify-center items-center cursor-pointer rounded-[5px]"
                        @click="handleShowRechargeCancelTipsOk">
                        确认</div>
                    <div class=" bg-[#EAEEF6] text-[#777C8A] text-[12px] w-[94px] h-[36px] flex justify-center items-center cursor-pointer rounded-[5px]"
                        @click="handleShowRechargeCancelTipsCancel">
                        取消</div>
                </div>
            </template>
        </n-card>
    </n-modal>

</template>

<style lang="less" scoped>
.tips {
    // 画一个三角形居中 贴上沿 角向上
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: -5px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 5px solid #FFE6E6;
    }
}
</style>
