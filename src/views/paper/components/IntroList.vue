<script setup lang="ts">
import { NBadge } from "naive-ui";
import { replaceText } from "@/plugins/directive";

const list: any[] = [
	{ label: "从选题到创作，轻松高效一键生成 " },
	{ label: "专业灵活的数据处理与分析" },
	{ label: "论文结构完整，查重率低" },
	{ label: "强大的辅助功能提高论文质量和效率" },
	{ label: "格式规范表述准确，符合学术要求" },
	{ label: "真实文献参考为论点提供有力支持" },
	{ label: "快速扫描大量资料，提供丰富素材" },
	{ label: "观点整合分析，提供更全面客观思考" },
	{ label: "语言优化润色，提高论文表述水平" },
	{ label: "快速获取相关领域最新研究资讯" },
];
</script>

<template>
	<div
		class="intro_wrap 2xl:w-[1000px] w-[750px] min-w-[750px] mx-auto mt-[50px] grid gap-x-[20px] gap-y-[15px] text-[14px] color-[#666] sm:w-full sm:min-w-0"
	>
		<div
			class="bg-[rgba(255,255,255,.6)] flex flex-col gap-[15px] px-[40px] py-[25px] rounded-[10px]"
		>
			<template v-for="(item, index) in list" :key="index">
				<div v-if="index < 5" class="flex items-center relative">
					<NBadge :value="index + 1" color="#3680F9" />
					<span class="ml-[10px] relative"
						>{{ replaceText(item.label) }}
						<div v-if="item.tag" :class="['bradge-tag new']"></div
					></span>
				</div>
			</template>
		</div>
		<div
			class="bg-[rgba(255,255,255,.6)] flex flex-col gap-[15px] px-[40px] py-[25px] rounded-[10px]"
		>
			<template v-for="(item, index) in list" :key="index">
				<div v-if="index >= 5" class="flex items-center relative">
					<NBadge :value="index + 1" color="#3680F9" />
					<span class="ml-[10px] relative"
						>{{ replaceText(item.label) }}
						<div v-if="item.tag" :class="['bradge-tag new']"></div
					></span>
				</div>
			</template>
		</div>
	</div>
</template>

<style lang="less" scoped>
.intro_wrap {
	display: grid;
	grid-template-columns: repeat(2, minmax(40%, 1fr));
	// grid-template-rows: repeat(5, minmax(0, 1fr));
	grid-auto-flow: column dense; /* 自动填充，尽量紧凑排列 */
	// background-color: #fff;
}
@media screen and (max-width: 767px) {
	.intro_wrap {
		display: flex;
		flex-direction: column;
	}
}
</style>
