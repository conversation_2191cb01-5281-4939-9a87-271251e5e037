<template>
	<div class="steps-wrapper">
		<div class="steps-container">
			<div v-for="(item, index) in steps" :key="index" class="step-item">
				<div class="step-content" :class="{
					'step-content--active': item.status === 'active',
					'step-content--loading': item.status === 'loading',
					'step-content--inactive': item.status === 'in-active'
				}">
					<template v-if="item.status === 'loading'">
						<div class="w-[14px] h-[14px] flex items-center justify-center rounded-full bg-[#fff]">
							<IconLoading class="loading-spinner " />
						</div>
					</template>
					<template v-else-if="item.status === 'active'">
						<div class="w-[14px] h-[14px] flex items-center justify-center rounded-full bg-[#0E69FF]">
							<IconSelect class="text-[#fff] text-[10px]" />
						</div>
					</template>
					<template v-else>
						<div class="w-[14px] h-[14px] flex items-center justify-center rounded-full bg-[#A8A8A8]">
							<IconSelect class="text-[#fff] text-[10px]" />
						</div>
					</template>
					<span class="step-label">{{ item.title }}</span>
				</div>
				<div v-if="index !== steps.length - 1" class="step-line" :class="{
					'step-line--active': item.status === 'active',
					'step-line--inactive': item.status === 'in-active' || item.status === 'loading'
				}">
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
interface Step {
	step: number;
	status: 'active' | 'loading' | 'in-active';
	title: string;
}

export default {
	name: 'Steps',
	props: {
		steps: {
			type: Array as () => Step[],
			required: true,
			validator: (value: Step[]) => value.length > 0
		}
	}
};
</script>

<style lang="less" scoped>
.steps-wrapper {
	width: 100%;
	padding: 20px 0;
}

.steps-container {
	display: flex;
	align-items: center;
	width: 100%;
}

.step-item {
	display: flex;
	align-items: center;
	flex: 1;

	&:last-child {
		flex: 0;
	}
}

.step-content {
	display: flex;
	align-items: center;
	position: relative;
	z-index: 2;

	&--active,
	&--loading {
		.step-circle {
			background-color: #0E69FF;
		}

		.step-label {
			color: #0E69FF;
		}
	}

	&--inactive {
		.step-circle {
			background-color: #A8A8A8;
		}

		.step-label {
			color: #A8A8A8;
		}
	}
}

.step-circle {
	width: 24px;
	height: 24px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: background-color 0.3s ease;
}

.step-number {
	color: #fff;
	font-size: 14px;
	font-weight: 500;
}

.step-label {
	margin-left: 8px;
	font-size: 14px;
	white-space: nowrap;
	transition: color 0.3s ease;
}

.step-line {
	flex: 1;
	height: 1px;
	margin: 0 12px;
	background: #A8A8A8;
	position: relative;
	z-index: 1;

	&--active {
		background: #0E69FF;
	}

	&--inactive {
		background: #A8A8A8;
	}
}

.loading-spinner {
	color: #0E69FF;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(360deg);
	}
}
</style>