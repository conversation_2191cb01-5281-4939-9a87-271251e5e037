<template>
	<div class="radio-group-container">
		<n-form-item style="--n-feedback-height: unset !important;" label-placement="left" :label="data.label"
			:path="data.field" :rule="{
			required: data.isRequired,
			message: `请选择${data.label}`,
		}">
			<n-radio-group v-model:value="radioValue" :name="data.field" class="radio-group">
				<template v-for="item in data.options" :key="item.value">
					<div class="flex items-center">
						<n-radio :value="item.value" class="radio-item">
							<n-space align="center">
								{{ item.label }}
								<span v-if="item.isNew" class="tag new-tag">
									NEW
								</span>
								<span v-if="item.isHot" class="tag hot-tag">
									HOT
								</span>
							</n-space>
						</n-radio>
						<n-tooltip v-if="item.tag" trigger="hover" placement="bottom">
							<template #trigger>
								<span class="inline-block cursor-pointer ml-2">
									<IconQuestion />
								</span>
							</template>
							{{ item.tag }}
						</n-tooltip>
					</div>
				</template>
			</n-radio-group>
		</n-form-item>
	</div>
</template>
<script lang="ts" setup>
import { NFormItem, NRadioGroup, NRadio, NTooltip, NSpace } from 'naive-ui';
import { computed, onMounted } from 'vue';
import { usePaperStore } from '@/store';
import { FormDatum } from '@/views/paper/types';

interface Props {
	data: FormDatum;
}

const props = defineProps<Props>();
const paperStore = usePaperStore();

// 从paperForm中获取当前字段的值
const getFieldValue = () => {
	const formItem = paperStore.paperForm.formData.find(item => item.field === props.data.field);
	return formItem ? formItem.value : props.data.defaultValue || '';
};

// 更新paperForm中的值
const updateFieldValue = (value: string) => {
	const formIndex = paperStore.paperForm.formData.findIndex(item => item.field === props.data.field);
	if (formIndex !== -1) {
		paperStore.paperForm.formData[formIndex].value = value;
	} else {
		paperStore.paperForm.formData.push({
			field: props.data.field,
			value: value
		});
	}
	// 记录状态
	paperStore.recordState();
};

// 使用计算属性实现双向绑定
const radioValue = computed({
	get: () => getFieldValue(),
	set: (value) => {
		updateFieldValue(value);
	}
});

// 组件挂载时，确保paperForm中有该字段的值
onMounted(() => {
	if (!getFieldValue() && props.data.defaultValue) {
		updateFieldValue(props.data.defaultValue);
	}
});
</script>

<style lang="less" scoped>
.radio-group-container {
	width: 100%;
	margin-bottom: 15px;
}

.radio-group {
	width: 100%;
	display: flex;
	gap: 50px;
}

.radio-item {
	position: relative;
}

.tag {
	display: inline-block;
	font-size: 8px;
	color: white;
	font-weight: bold;
	padding: 0 3px;
	border-radius: 17px;
	height: 15px;
	line-height: 15px;
	position: relative;
	top: -3px;
}

.new-tag,
.hot-tag {
	background: linear-gradient(to left, #FF9A47, #FF582A);
}

.question-icon {
	margin-left: 4px;
	cursor: pointer;
	vertical-align: middle;
}

/* 适配1366*768屏幕 */
@media screen and (max-width: 1366px) {
	.radio-group {
		gap: 40px;
	}
	
	:deep(.n-form-item-label) {
		font-size: 14px;
	}
}

/* 响应式样式 */
@media screen and (max-width: 1200px) {
	.radio-group {
		gap: 30px;
	}
}

@media screen and (max-width: 992px) {
	.radio-group {
		gap: 20px;
		flex-wrap: wrap;
	}

	.radio-item {
		margin-bottom: 10px;
	}
}

@media screen and (max-width: 768px) {
	.radio-group-container {
		margin-bottom: 10px;
	}

	.radio-group {
		gap: 15px;
	}

	:deep(.n-form-item-label) {
		font-size: 14px;
	}
}

@media screen and (max-width: 576px) {
	.radio-group {
		gap: 10px;
		flex-direction: column;
	}

	.radio-item {
		margin-bottom: 8px;
		font-size: 14px;
	}

	:deep(.n-form-item-label) {
		font-size: 13px;
	}

	.tag {
		font-size: 7px;
		height: 13px;
		line-height: 13px;
	}
}
</style>