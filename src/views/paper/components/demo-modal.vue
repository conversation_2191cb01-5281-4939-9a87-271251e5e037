<template>
	<n-modal :show="visible" @update:show="handleVisibleChange"
		style="width: 685px; height: 80vh; max-height: 770px;--n-padding-top: 0px;--n-padding-left: 0px;--n-padding-right: 0px;">
		<n-card style="max-height: 80vh; display: flex; flex-direction: column;">
			<template #header>
				<div class="bg-[#fff] h-[61px] leading-[61px] text-[22px] text-[#333333] pl-[30px]">{{ title }}</div>
			</template>
			<template #header-extra>
				<div class=" h-[61px] leading-[61px] flex justify-center items-center cursor-pointer bg-[#fff] pr-[20px]"
					@click="handleClose">
					<IconClose class=" w-[25px] h-[25px] text-[22px] " />
				</div>
			</template>
			<div class="modal-content HideScrollbar">
				<slot></slot>
			</div>
		</n-card>
	</n-modal>
</template>

<script lang="ts" setup>
import { NModal, NCard } from 'naive-ui'

interface Props {
	visible: boolean
	title: string
}
interface Emit {
	(e: 'update:visible', value: boolean): void
}
const props = defineProps<Props>()
const emit = defineEmits<Emit>()

const handleClose = () => {
	emit('update:visible', false)
}

const handleVisibleChange = (value: boolean) => {
	emit('update:visible', value)
}
</script>

<style lang="less" scoped>
.modal-content {
	overflow-y: auto;
	max-height: calc(77vh - 61px);
	padding: 16px;

	&::-webkit-scrollbar {
		width: 6px;
	}

	&::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 3px;
	}

	&::-webkit-scrollbar-thumb {
		background: #888;
		border-radius: 3px;
	}

	&::-webkit-scrollbar-thumb:hover {
		background: #555;
	}
}
</style>
