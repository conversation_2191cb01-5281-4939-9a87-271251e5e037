<!-- 论文类型 -->
<template>
	<div class="paper-feature-list">
		<PaperFeature v-for="(item, index) in featureList" :key="index" :selected="paperForm.id === item.id" :id="item.id"
			:icon="item.icon" :name="item.name" :description="item.description" @select="handleSelect" />
	</div>
</template>

<script setup lang="ts">
import { watch } from "vue";
import { usePaperStore } from "@/store/modules/paper";
import { storeToRefs } from "pinia";
import PaperFeature from "./PaperFeature.vue";

const store = usePaperStore();
const { featureList, paperForm } = storeToRefs(store);

// 移除 onMounted 钩子，因为已经在父组件中调用了 getPaperTypeList
// onMounted(() => {
// 	store.getPaperTypeList();
// });

const handleSelect = (id: number) => {
	store.setPaperType(id);
};
</script>

<style lang="less" scoped>
.paper-feature-list {
	display: flex;
	flex-wrap: wrap;
	gap: 9px;
	max-width: 1180px;
	margin: 0 auto;
	justify-content: flex-start;
}

/* 响应式样式 */
@media screen and (max-width: 1200px) {
	.paper-feature-list {
		max-width: 100%;
		padding: 0 15px;
		gap: 8px;
	}
}

/* 适配1366*768屏幕 */
@media screen and (max-width: 1366px) {
	.paper-feature-list {
		max-width: 1080px;
		gap: 8px;
		justify-content: start;
	}
}

@media screen and (max-width: 768px) {
	.paper-feature-list {
		gap: 6px;
	}
}

@media screen and (max-width: 576px) {
	.paper-feature-list {
		gap: 5px;
	}
}
</style>