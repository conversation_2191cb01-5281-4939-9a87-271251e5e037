<template>
	<div class="landing-left-container">
		<div class="title">创作数据</div>
		<div class="create-data-list">
			<div v-for="item in paperStatistics?.list || []" :key="item.title" class="create-data-item">
				<div class="value">{{ item.value }}</div>
				<div class="label">{{ item.title }}</div>
			</div>
		</div>
		<div class="title mt-[18px]">AI驱动学术写作平台</div>
		<div class="content-container">
			<transition-group name="fade">
				<n-image v-for="icon in icons" :key="icon" :src="icon" class="w-full fade-item" object-fit="contain"
					preview-disabled></n-image>
			</transition-group>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

import { NImage } from 'naive-ui';
import { PaperStatistics } from '../types';

interface Props {
	icons: string[]
	paperStatistics: PaperStatistics
}

interface CreateData {
	label: string;
	value: string;
}



const createDataList = ref<CreateData[]>([
	{
		label: '今日写作',
		value: '4513'
	},
	{
		label: '今日查重',
		value: '632'
	},
	{
		label: '无限改稿',
		value: '2612'
	},
	{
		label: '降重/降AIGC',
		value: '268'
	}
]);

const props = defineProps<Props>();
</script>

<style lang="less" scoped>
.landing-left-container {
	display: flex;
	flex-direction: column;
	row-gap: 22px;
	cursor: auto;

	.title {
		font-size: 18px;
		line-height: 24px;
		color: #0654FF;
		text-align: center;
	}

	.create-data-list {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		row-gap: 8px;
		column-gap: 8px;
		width: 100%;

		.create-data-item {
			width: calc(50% - 4px);
			height: auto;
			aspect-ratio: 120/66;
			border: 1px solid #E1E3EE;
			border-radius: 3px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			.value {
				font-size: 20px;
				font-weight: 23px;
				color: #0E69FF;
			}

			.label {
				color: #3D3D3D;
				font-size: 14px;
				font-weight: 18px;
			}
		}
	}

	.content-container {
		display: flex;
		flex-direction: column;
		row-gap: 10px;

		.fade-item {
			transition: opacity 0.3s ease;
		}
	}

	.fade-enter-active,
	.fade-leave-active {
		transition: opacity 0.3s ease;
	}

	.fade-enter-from,
	.fade-leave-to {
		display: none;
	}
}

/* 响应式样式 */
@media screen and (max-width: 1200px) {
	.landing-left-container {
		row-gap: 18px;
	}
}

@media screen and (max-width: 992px) {
	.create-data-list {
		.create-data-item {
			.value {
				font-size: 18px;
			}

			.label {
				font-size: 13px;
			}
		}
	}
}

@media screen and (max-width: 768px) {
	.landing-left-container {
		row-gap: 15px;
	}

	.title {
		font-size: 16px;
	}

	.create-data-list {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		row-gap: 8px;
		column-gap: 8px;
		width: 100%;
		max-width: 500px;
		margin: 0 auto;
	}
}

@media screen and (max-width: 576px) {
	.landing-left-container {
		row-gap: 12px;
	}

	.create-data-list {
		.create-data-item {
			width: calc(50% - 4px);
			aspect-ratio: 100/60;

			.value {
				font-size: 16px;
			}

			.label {
				font-size: 12px;
			}
		}
	}

	.content-container {
		row-gap: 8px;
	}
}
</style>