<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import { NIcon, NModal } from "naive-ui";
import NoticeSvg from "./svgs/notice.svg";
import {
	fetchGenerateOutline,
	fetchPaperTemplateDetail,
	fetchPaperTypeList,
	fetchLatestMessage,
} from "../apis";
import DynamicForm from "./DynamicForm.vue";
import { PaperType, GenerateParams } from "../types";
import CircleSvg from "@/assets/aiwork/svg/circle.svg";
import { ScrollText } from "@/components/scrollText";
import { textTransform } from "@/utils/textTransform";

const $props = defineProps<{
	id?: any;
	generateLoading: boolean;
	disableGenerate: boolean;
	onGenerate: (data: GenerateParams) => void;
	onGenerateTask: (data: GenerateParams) => void;
}>();

interface Emit {
	(e: "select", feature: PaperType): void;
}

const emit = defineEmits<Emit>();

const features = ref<PaperType[]>([]);
const selectedFeatureType = ref<string | null>(null);
const showTaskButton = ref<boolean>(false);
const latestMessage = ref<string[]>([]);

const templateDetail = ref<any>({});
onMounted(() => {
	fetchPaperTypeList<PaperType[]>().then((data) => {
		features.value = data;
		let type = data?.[0].type;
		if ($props.id) {
			type = data?.find((item) => item.id === Number($props.id))?.type as any;
			selectedFeatureType.value = type;
		} else {
			selectedFeatureType.value = data?.[0].type;
		}
		handleSelectFeature(type);
	});
	fetchLatestMessage<string[]>().then((data) => {
		latestMessage.value = data || [];
	});
});

watch(
	() => selectedFeatureType.value,
	(newVal) => {
		showTaskButton.value = newVal === "books";
	}
);

watch(
	() => selectedFeatureType.value,
	(newVal) => {
		handleSelectFeature(newVal as string);
	}
);

const handleSelectFeature = (type?: string) => {
	fetchPaperTemplateDetail({ type }).then((data) => {
		templateDetail.value = data;
	});
};

const handleGenerate = (formData) => {
	const feature = features.value.find(
		(item) => item.type === selectedFeatureType.value
	);
	const style = templateDetail?.value?.style;
	const detail = templateDetail?.value?.formData;
	$props.onGenerate({
		formData,
		id: feature?.id,
		style,
		detail,
		type: selectedFeatureType.value,
	});
	// document.getElementById("outline")?.scrollIntoView()
};
const handleTaskGenerate = (formData) => {
	const detail = templateDetail?.value?.formData;
	$props.onGenerateTask({
		formData,
		id: features.value.find((item) => item.type === selectedFeatureType.value)
			?.id,
		isTask: true,
		detail,
		type: selectedFeatureType.value,
	});
};
const handleTypeSelect = (feature: PaperType) => {
	if ($props.disableGenerate) return;
	if (feature.url) {
		window.open(feature.url);
		return;
	}
	// selectedFeatureType.value = feature.type;
	emit("select", feature);
};
</script>

<template>
	<div class="w-full">
		<!-- 功能列表 -->
		<div
			class="2xl:w-[1000px] w-[750px] mx-auto flex justify-center mt-[40px] sm:w-[100vw] sm:px-4"
		>
			<div class="flex gap-[15px] flex-wrap text-base">
				<div
					class="feature-item"
					:class="{
						'feature-item_active': selectedFeatureType == feature.type,
						'feature-item_inactive': disableGenerate,
					}"
					v-for="(feature, index) in features"
					:key="index"
					:data-id="feature.id"
					@click="handleTypeSelect(feature)"
				>
					<img :src="feature.icon" alt="Feature Image" width="32" height="32" />
					<!-- <span class="flex-1">{{
						textTransform(feature.name, "论文", "类文章")
					}}</span> -->
					<span class="flex-1" v-replace>{{feature.name}}</span>
					<CircleSvg
						class="w-[12px] h-[12px] text-[#3680F9]"
						v-if="selectedFeatureType == feature.type"
					/>
					<!-- <NIcon class="w-[20px] h-[20px]" v-if="selectedFeatureType == feature.type">
						<svg t="1724642903404" class="icon" viewBox="0 0 1024 1024" version="1.1"
							xmlns="http://www.w3.org/2000/svg" p-id="2552" width="200" height="200">
							<path
								d="M515.78513063 522.82861671m-438.99768254 0a438.99769858 438.99769858 0 1 0 877.99538113 0 438.99769858 438.99769858 0 1 0-877.99538113 0Z"
								fill="#3B8FCE" p-id="2553"></path>
							<path
								d="M467.00761388 737.44970978c-0.29267042 0-0.40973876 0.25364255-0.89750183 0h-0.01952185a49.07018735 49.07018735 0 0 1-33.71501846-14.39913321l-155.95148892-155.97099503c-9.52137661-9.52137661-14.28206495-22.00842217-14.28206488-34.49546769s4.76068831-24.9740913 14.28206488-34.49546783a48.73850463 48.73850463 0 0 1 68.97141362 0l122.76326151 122.76327719 258.20869219-240.99998683a48.77751675 48.77751675 0 1 1 66.57156391 71.33225199L500.44948414 724.18221433c-4.31193751 4.0582951-9.17017189 7.18004431-14.34059892 9.36528021-6.08743449 2.61448084-12.60411388 3.90219875-19.10127134 3.90221524z"
								fill="#FFFFFF" p-id="2554"></path>
						</svg>
					</NIcon> -->
				</div>
				<div
					class="feature-item feature-item_empty"
					v-for="(feature, index) in Array(6)"
					:key="index"
				></div>
			</div>
		</div>

		<DynamicForm
			:datasource="templateDetail"
			@submit="handleGenerate"
			@task-submit="handleTaskGenerate"
			:disable-generate="disableGenerate"
			:generate-loading="generateLoading"
			:show-task-button="showTaskButton"
		/>

		<div class="2xl:w-[1000px] w-[750px] mx-auto mt-[30px] sm:w-full">
			<!-- <div class="flex justify-center mt-[30px]">
				<NButton type="primary" size="large" style="--n-width: 200px; --n-height: 50px" class="align-center"
					@click="handleGenerate">生成大纲</NButton>
			</div> -->
			<div class="mt-[20px]">
				<ScrollText :current="1" :autoplay="true" :interval="2000">
					<div
						v-for="(message, index) in latestMessage"
						class="flex justify-center items-center text-sm"
					>
						<NIcon :size="16" class="mr-[5px]">
							<NoticeSvg />
						</NIcon>
						<span class="text-gray-500 text-[14px]">{{
							textTransform(message)
						}}</span>
					</div>
				</ScrollText>
			</div>
		</div>
	</div>
</template>

<style lang="less" scoped>
.feature-item {
	background: rgba(255, 255, 255, 0.5);
	box-sizing: border-box;
	border: 1px solid rgba(255, 255, 255, 0.6);
	box-shadow: 0px 2px 10px 0px rgba(222, 226, 238, 0.5);
	border-radius: 10px;
	min-width: 150px;
	height: 46px;
	display: flex;
	align-items: center;
	gap: 10px;
	padding: 0 10px;
	flex-grow: 0;
	flex-shrink: 0;
	flex-wrap: nowrap;
	cursor: pointer;
}

.feature-item_active {
	border: 1px solid #3680f9;
}

.feature-item_inactive {
	border: 1px solid rgba(194, 194, 194, 1);
	user-select: none;
	cursor: not-allowed;
}

.feature-item_empty {
	height: 0;
	overflow: hidden;
	opacity: 0;
}

.required::before {
	content: "*";
	margin-right: 8px;
	color: red;
	display: inline-block;
	font-size: 20px;
	vertical-align: middle;
	margin-bottom: -0.125em;
}
</style>
