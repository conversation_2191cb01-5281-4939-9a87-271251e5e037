<script lang="ts" setup>
import {
	NForm,
	NFormItem,
	NCascader,
	NInput,
	NIcon,
	NRadio,
	NRadioGroup,
	FormInst,
	NButton,
	NModal,
	NImage,
	useMessage,
	NDivider,
	NPopover
} from "naive-ui";
import subjectOptions from "@/json/professions.json";
import { ref, watch, reactive, nextTick } from "vue";
import TaskDemoImg1 from "@/assets/images/task-demo1.png";
import TaskDemoImg2 from "@/assets/images/task-demo2.png";
import TaskDemoImg3 from "@/assets/images/task-demo3.png";
import { textTransform } from "@/utils/textTransform";
import { useRequest } from "vue-hooks-plus";
import { fetchPaperTitleSuggestion } from "@/views/paper/apis";
import StepProgress from "./Recommend-steps.vue";
interface Props {
	showTaskButton: boolean;
	datasource: any;
	disableGenerate?: boolean;
	generateLoading?: boolean;
	onChange?: (value: any) => void;
}

interface Step {
	step: number;
	status: 'active' | 'loading' | 'in-active';
	title: string;
}
interface Emit {
	(e: "task-submit", array: any[]): void;
}

interface Emit {
	(e: "task-submit", array: any[]): void;
	(e: "submit", array: any[]): void;
}

const emit = defineEmits<Emit>();
const $props = defineProps<Props>();
const form = ref<FormInst | null>(null);

const professionIndex = ref<number>(-1);
const titleIndex = ref<number>(-1);
const formData: any = reactive({});
const rawFormData: any = reactive({});
const data = ref<any[]>([]);
const power = ref();
const space = ref();
const mode = ref();
const list = ref<any[]>([]);
const message = useMessage();
const visible = ref(false);
const showSuggestion = ref(false)
const currentRef = ref<number>(1)
const inputRef = ref()
const steps = ref<Step[]>([
	{ step: 1, status: 'loading', title: '开始预处理信息' },
	{ step: 2, status: 'in-active', title: '分析' },
	{ step: 3, status: 'in-active', title: '完成' }
])

watch(
	() => $props.generateLoading,
	(newValue) => {
		if (newValue) {
			for (const item of rawFormData.value) {
				formData[item.field] = item.defaultValue || null;
				if (item.field == "space") {
					space.value = formData[item.field];
				}
				if (item.field == "mode") {
					mode.value = formData[item.field];
				}
			}
			// 科目
			professionIndex.value = rawFormData.value.findIndex(
				(item: any) => item.field === "profession"
			);
			// 题目
			titleIndex.value = rawFormData.value.findIndex(
				(item: any) => item.field === "title"
			);
		}
	}
);
watch(
	() => $props.datasource,
	(newValue) => {
		list.value = newValue.formData;
		rawFormData.value = newValue.formData;
		for (const item of newValue.formData) {
			formData[item.field] = item.defaultValue || null;
			if (item.field == "space") {
				space.value = formData[item.field];
			}
			if (item.field == "mode") {
				mode.value = formData[item.field];
			}
		}
		// 科目
		professionIndex.value = newValue.formData.findIndex(
			(item: any) => item.field === "profession"
		);
		// 题目
		titleIndex.value = newValue.formData.findIndex(
			(item: any) => item.field === "title"
		);
	},
	{ deep: true }
);

watch(
	() => data.value,
	(newValue) => {
		$props.onChange?.(newValue);
	}
);

const handleSubmit = (e) => {
	e.preventDefault();
	let formValue: any = [];
	form.value?.validate((errors) => {
		if (!errors) {
			for (let item in formData) {
				formValue.push({
					field: item,
					value: formData[item],
				});
			}
			//console.log(formValue)
			emit("submit", formValue);
		} else {
			message.error(errors?.[0]?.[0]?.message || "请填写完整信息");
			//alert(errors)
		}
	});
};
const handleTaskSubmit = (e) => {
	e.preventDefault();
	let formValue: any = [];
	form.value?.validate((errors) => {
		if (!errors) {
			for (let item in formData) {
				formValue.push({
					field: item,
					value: formData[item],
				});
			}
			//console.log(formValue)
			emit("task-submit", formValue);
		} else {
			message.error(errors?.[0]?.[0]?.message || "请填写完整信息");
			//alert(errors)
		}
	});
};
const handleTaskShow = () => {
	visible.value = true;
};
const handleTaskClose = () => {
	visible.value = false;
};
const suggestionData = ref<any[]>([])
const { run: runTitleSuggestion, loading: titleSuggestionLoading } = useRequest(fetchPaperTitleSuggestion, {
	manual: true,
	onSuccess: (res) => {
		if (res.length) {
			steps.value[1].status = 'active'
			steps.value[2].status = 'loading'
			setTimeout(() => {
				suggestionData.value = res
			}, 500)
		} else {
			message.error('AI没有找到合适的标题')
			steps.value[0].status = 'loading'
			steps.value[1].status = 'in-active'
			steps.value[2].status = 'in-active'
			suggestionData.value = []
			showSuggestion.value = false
		}
	},
	onError: () => {
		message.error('AI没有找到合适的标题')
		steps.value[0].status = 'loading'
		steps.value[1].status = 'in-active'
		steps.value[2].status = 'in-active'
		suggestionData.value = []
		showSuggestion.value = false
	}
});

const handleRunSuggestion = () => {
	if (titleSuggestionLoading.value || $props.disableGenerate) return
	nextTick(() => {
		suggestionData.value = []
		steps.value[0].status = 'loading'
		steps.value[1].status = 'in-active'
		steps.value[2].status = 'in-active'
		if (formData.title) {
			showSuggestion.value = true
			setTimeout(() => {
				steps.value[0].status = 'active'
				steps.value[1].status = 'loading'
				runTitleSuggestion({ title: formData.title })
			}, 500)
		} else {
			message.error('请输入题目')
		}
	})
}
const handleSuggestionHide = () => {
	showSuggestion.value = false
	suggestionData.value = []
	steps.value[0].status = 'loading'
	steps.value[1].status = 'in-active'
}
const handleSuggestionSelect = (item) => {
	formData.title = item
	showSuggestion.value = false
	suggestionData.value = []
	steps.value[0].status = 'loading'
	steps.value[1].status = 'in-active'
	steps.value[2].status = 'in-active'
}

let inputEnd = ref(false);

const compositionend = (event) => {
	event.preventDefault();
	inputEnd.value = true;
};
const compositionstart = (event) => {
	event.preventDefault();
	inputEnd.value = false;
};

</script>

<template>
	<NForm ref="form" :model="formData" label-placement="left" label-width="auto" :disabled="disableGenerate">
		<div class="sm:w-[100vw] sm:px-4">
			<div v-if="list?.length && (professionIndex >= 0 || titleIndex >= 0)"
				class="subject 2xl:w-[1000px] w-[750px] mx-auto mt-[30px] flex gap-[20px] sm:flex-col sm:gap-0 sm:mt-0 sm:w-full">
				<div class="w-[186px] flex-0 sm:w-full" v-if="professionIndex >= 0">
					<NFormItem :show-label="false" :path="'profession'">
						<NCascader :placeholder="list[professionIndex].placeholder" expand-trigger="hover"
							check-strategy="child" :options="subjectOptions" filterable
							:default-value="list[professionIndex].defaultValue || null"
							v-model:value="formData.profession">
						</NCascader>
					</NFormItem>
				</div>
				<NFormItem :show-label="false" :path="'title'" class="flex-1" v-if="titleIndex >= 0" :rule="[
					{ required: true, message: '请输入题目' },
					{ min: 4, message: '题目长度不能少于4个字' },
				]">
					<NPopover :show="showSuggestion" trigger="manual" class="w-full" placement="bottom"
						@clickoutside="handleSuggestionHide" :show-arrow="false" @hide="handleSuggestionHide"
						:style="{ width: `var(--v-target-width)` }">
						<template #trigger>
							<NInput :placeholder="list[titleIndex].placeholder" class="flex-1" maxlength="50"
								:default-value="list[titleIndex].defaultValue" v-model:value="formData.title"
								@keypress.enter="handleRunSuggestion" @compositionend="compositionend"
								@compositionstart="compositionstart" :ref="inputRef">
								<template #prefix>
									<div class="flex items-center">
										<NIcon :size="16">
											<svg xmlns="http://www.w3.org/2000/svg"
												xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1"
												width="15" height="15" viewBox="0 0 15 15">
												<g>
													<g>
														<path
															d="M7.00496,1.3333740234375C9.31058,1.3333787918075,11.4123,2.6545340234375,12.4118,4.7322240234374995C13.4113,6.8099140234375,13.1319,9.276574023437501,11.693,11.0780440234375L13.2715,12.7999740234375C13.5396,13.0588740234375,13.5432,13.4871740234375,13.2797,13.7507740234375C13.0161,14.0142740234375,12.5878,14.0105740234375,12.3289,13.7425740234375L10.74963,12.0213740234375C7.51966,14.6013740234375,2.70933,13.2186740234375,1.342432,9.3173040234375C-0.024469999999999992,5.4159240234375,2.87104,1.3333742618565,7.00496,1.3333740234375ZM7.00496,2.6667040234375C4.427630000000001,2.6667040234375,2.3382899999999998,4.7560440234375,2.3382899999999998,7.3333740234375C2.3382899999999998,9.9107040234375,4.427630000000001,12.0000740234375,7.00496,12.0000740234375C9.58166,11.9991740234375,11.67,9.9100740234375,11.67,7.3333740234375C11.67,4.756674023437499,9.58166,2.6675940234375,7.00496,2.6667040234375Z"
															fill="#525252" fill-opacity="1"
															style="mix-blend-mode: passthrough" />
													</g>
												</g>
											</svg>
										</NIcon>
										<NDivider class="w-[1px] h-[16px] bg-[#525252] mx-[10px]" :vertical="true" />
									</div>
								</template>
								<template #suffix>
									<div class="w-[94px] h-[34px] rounded-[4px] flex items-center justify-center gap-x-[5px]"
										@click="handleRunSuggestion"
										:class="{ 'cursor-not-allowed bg-[#D8D8D8] text-[#7C7C7C]': disableGenerate, 'cursor-pointer bg-[#EDF4FF] text-[#0E69FF]': !disableGenerate }">
										<IconFlower />优化标题
									</div>
								</template>
							</NInput>
						</template>
						<div v-if="!suggestionData?.length">
							<div class="text-[#69A2FF]">AI正在智能推荐标题</div>
							<div
								class="bg-gradient-to-r from-[#F0F6FF] to-[rgba(237, 236, 255, 0.38)] h-[40px] rounded-[4px] px-[18px] flex items-center justify-start mt-[14px] mb-[2px]">
								<step-progress :steps="steps" :current-step="currentRef" />
							</div>

						</div>
						<div v-else>
							<div class=" flex items-center gap-x-[5px] text-[#69A2FF] mb-[2px]">
								<IconFlower />AI智能推荐标题(原创)
							</div>
							<div v-for="item in suggestionData" :key="item"
								class=" ml-[17px] leading-[30px] cursor-pointer hover:bg-[#E1ECFA]">
								<div class="flex items-center gap-x-[5px] text-[#666666]"
									@click="handleSuggestionSelect(item)">
									{{ item }}
								</div>
							</div>
							<div class="flex items-center gap-x-[5px] text-[#0E69FF] cursor-pointer ml-[17px] mt-[12px] mb-[5px]"
								@click="handleRunSuggestion">
								<IconLoading /> 换一批
							</div>
						</div>
					</NPopover>
				</NFormItem>
			</div>
			<div class="selector 2xl:w-[1000px] w-[750px] mx-auto mt-[30px] sm:w-full sm:mt-0">
				<template v-for="(field, index) in list || []" :key="index">
					<template v-if="field.field === 'title'"></template>
					<template v-else-if="field.field === 'profession'"></template>
					<template v-else>
						<template v-if="field.formType === 'input'">
							<NFormItem :label="field.label" :path="field.field" :rule="{
								required: field.isRequired,
								message: `请输入${field.label}`,
							}">
								<NInput v-model:value="formData[field.field]" :placeholder="field.placeholder"
									size="large" />
							</NFormItem>
						</template>

						<template v-if="field.formType === 'radio'">
							<NFormItem :label="field.label" :path="field.field" :rule="{
								required: field.isRequired,
								message: `请选择${field.label}`,
							}">
								<div>
									<n-radio-group v-model:value="formData[field.field]" :name="formData[field.field]"
										class="sm:!flex sm:flex-wrap sm:gap-2">
										<template v-for="item in field.options">
											<n-radio :value="item.value" class="relative">
												{{ item.label }}
												<span v-if="item.isNew"
													class="absolute text-[10px] text-[#FF5900] top-[-5px] right-[-15px] font-bold">
													NEW
												</span>
											</n-radio>
										</template>
									</n-radio-group>
									<div class="block" v-if="field.description">
										<p class="mt-[5px] text-[#999] text-[12px]" v-for="item in field.description">
											{{ item }}
										</p>
									</div>
								</div>
							</NFormItem>
						</template>
						<template v-if="field.formType === 'textarea'">
							<NFormItem :label="field.label" :path="field.field" :rule="{
								required: field.isRequired,
								message: `请输入${field.label}`,
							}" :class="`${!field.isRequired ? 'no-required' : ''}`">
								<NInput type="textarea" v-model:value="formData[field.field]"
									:placeholder="textTransform(field.placeholder)" />
							</NFormItem>
						</template>
					</template>
				</template>
			</div>

			<div class="flex justify-center mt-[30px]">
				<div v-if="showTaskButton" class="flex flex-row items-center gap-x-[12px]">
					<NButton type="primary" size="large" style="--n-width: 200px; --n-height: 50px" class="align-center"
						@click="handleTaskSubmit" :disabled="disableGenerate">
						生成任务书</NButton>
					<NButton type="default" size="large" @click="handleTaskShow"
						style="--n-width: 124px; --n-height: 50px" class="align-center" :disabled="disableGenerate">
						查看预览
					</NButton>
				</div>
				<NButton v-else type="primary" size="large" style="--n-width: 200px; --n-height: 50px"
					class="align-center" @click="handleSubmit" :disabled="disableGenerate">
					生成大纲</NButton>
			</div>
		</div>
	</NForm>
	<NModal :show-icon="true" v-model:show="visible" style="width: 740px; background: #fff"
		class="relative w-[749px] bg-[#fff] rounded-[30px]" @close="visible = false">
		<div>
			<div class="absolute top-[40px] right-[34px] cursor-pointer" @click="handleTaskClose">
				<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none"
					version="1.1" width="20" height="20" viewBox="0 0 20 20">
					<g>
						<path
							d="M11.875,10L20,18.125L18.125,20L10,11.875L1.875,20L0,18.125L8.125,10L0,1.875L1.875,0L10,8.125L18.125,0L20,1.875L11.875,10Z"
							fill="#444444" fill-opacity="1" style="mix-blend-mode: passthrough" />
					</g>
				</svg>
			</div>
			<div class="text-center text-[24px] leading-[44px] pt-[28px]">
				任务书示例
			</div>
			<div class="overflow-y-scroll HideScrollbar h-[493px] w-full flex flex-col mb-[55px]">
				<img :src="TaskDemoImg1" width="100%" preview-disabled />
				<img :src="TaskDemoImg2" width="100%" preview-disabled />
				<img :src="TaskDemoImg3" width="100%" preview-disabled />
			</div>
		</div>
	</NModal>
</template>

<style lang="less" scoped>
.subject :deep(.n-base-selection),
.subject :deep(.n-input) {
	border-radius: 10px;
}

.subject :deep(.n-base-selection-label),
.subject :deep(.n-input-wrapper) {
	height: 46px;
	border-radius: 10px;
	box-shadow: 0px 5px 24px 0px #c8dcff;
}

.subject :deep(.n-input .n-input__input-el) {
	height: 100%;
	font-size: 14px;
}

.subject :deep(.n-base-selection-input__content),
.subject :deep(.n-base-selection-label),
.subject :deep(.n-input__placeholder) {
	font-size: 14px;
}

// :deep(.n-radio) {
// 	margin-left: 20px;
// }
:deep(.n-radio + .n-radio) {
	margin-left: 40px;
}

@media (max-width: 767px) {
	:deep(.n-radio + .n-radio) {
		margin-left: 0;
	}
}

:deep(.n-form-item.no-required .n-form-item-label .n-form-item-label__text) {
	padding-right: 11px;
}
</style>
