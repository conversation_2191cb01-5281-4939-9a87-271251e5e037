<template>
	<div class="complex-input-container">
		<NPopover :show="showSuggestion" trigger="manual" class="w-full" placement="bottom"
			@clickoutside="handleSuggestionHide" :show-arrow="false" @hide="handleSuggestionHide"
			:style="{ width: `var(--v-target-width)` }">
			<template #trigger>
				<n-input class="complex-input" style="--n-height: 54px;" :value="formData.title?.value"
					@update:value="updateFormValue('title', $event)" :placeholder="preFormData.title?.placeholder"
					:default-value="preFormData.title?.defaultValue" @compositionend="handleCompositionEnd"
					@compositionstart="handleCompositionStart">
					<template #prefix>
						<template v-if="professionIndex !== -1">
							<div class="cascader-wrapper">
								<NCascader class="profession-cascader" :placeholder="preFormData.profession?.placeholder || '选择专业'"
									expand-trigger="hover" check-strategy="child" :options="subjectOptions" filterable
									@update:value="updateFormValue('profession', $event)" :placeholder-class="'cascader-placeholder'">
								</NCascader>
							</div>
							<NDivider class="divider" :vertical="true" />
						</template>
					</template>
					<template #suffix>
						<div class="optimize-button" @click="handleRunSuggestion" :class="{
							'disabled': disableGenerate,
							'enabled': !disableGenerate
						}">
							<IconFlower />优化标题
						</div>
					</template>
				</n-input>
			</template>
			<div v-if="!suggestionList.length" class="suggestion-loading">
				<div class="suggestion-title">AI正在智能推荐标题</div>
				<div class="suggestion-progress">
					<step-progress :steps="suggestionSteps" :current-step="currentStep" />
				</div>
			</div>
			<div v-else class="suggestion-list">
				<div class="suggestion-title">
					<IconFlower />AI智能推荐标题(原创)
				</div>
				<div v-for="(item, index) in suggestionList" :key="index" class="suggestion-item"
					@click="handleSuggestionSelect(item)">
					<div class="suggestion-text">
						{{ item }}
					</div>
				</div>
				<div class="refresh-button" @click="handleRunSuggestion">
					<IconLoading /> 换一批
				</div>
			</div>
		</NPopover>
	</div>
</template>

<script lang="ts" setup>
import { NInput, NCascader, NDivider, useMessage, NPopover } from 'naive-ui';
import subjectOptions from "@/json/professions.json";
import { usePaperStore } from '@/store';
import { storeToRefs } from 'pinia';
import { computed, nextTick, ref, watch, type ComputedRef } from 'vue';
import StepProgress from './Recommend-steps.vue';
import { useRequest } from 'vue-hooks-plus';
import { fetchPaperTitleSuggestion } from '../apis';
import type { FormDatum } from '../types';

interface Step {
	step: number;
	status: 'active' | 'loading' | 'in-active';
	title: string;
}

interface SuggestionStep extends Step {
	step: 1 | 2 | 3;
}

const INITIAL_STEPS: SuggestionStep[] = [
	{ step: 1, status: 'loading', title: '开始预处理信息' },
	{ step: 2, status: 'in-active', title: '分析' },
	{ step: 3, status: 'in-active', title: '完成' }
];

const SUGGESTION_DELAY = 300;
const SUGGESTION_ANIMATION_DELAY = 500;

// 状态管理
const currentStep = ref(1);
const suggestionList = ref<string[]>([]);
const showSuggestion = ref(false);
const isComposing = ref(false);
const suggestionSteps = ref<SuggestionStep[]>([...INITIAL_STEPS]);
const professionIndex = ref(-1);
const titleIndex = ref(-1);

const message = useMessage();
const store = usePaperStore();
const { paperForm, preForm, disableGenerate } = storeToRefs(store);

// 计算属性
const formData = computed(() => {
	return {
		profession: paperForm.value?.formData?.[professionIndex.value],
		title: paperForm.value?.formData?.[titleIndex.value]
	};
});

const preFormData = computed(() => {
	return {
		profession: preForm.value?.formData?.[professionIndex.value],
		title: preForm.value?.formData?.[titleIndex.value]
	};
});

// 监听 preForm 变化，动态计算索引
watch(() => preForm.value?.formData, (newValue) => {
	if (!Array.isArray(newValue)) return;

	professionIndex.value = newValue.findIndex(item => item.field === "profession");
	titleIndex.value = newValue.findIndex(item => item.field === "title");
}, { immediate: true });

// 使用 useRequest 处理 API 请求
const { run: runTitleSuggestion, loading: titleSuggestionLoading } = useRequest(fetchPaperTitleSuggestion, {
	manual: true,
	onSuccess: handleSuggestionSuccess,
	onError: () => handleSuggestionError('AI没有找到合适的标题')
});

// 方法
function updateFormValue(field: 'title' | 'profession', value: any): void {
	const index = field === 'title' ? titleIndex.value : professionIndex.value;
	if (paperForm.value?.formData?.[index]) {
		paperForm.value.formData[index].value = value;
	}
}

function handleCompositionStart(): void {
	isComposing.value = true;
}

function handleCompositionEnd(): void {
	isComposing.value = false;
}

function handleRunSuggestion(): void {
	if (titleSuggestionLoading.value || disableGenerate.value) return;

	const currentTitle = formData.value.title?.value;
	if (!currentTitle) {
		showErrorMessage('请输入题目');
		return;
	}

	resetSuggestionState();
	showSuggestion.value = true;

	nextTick(() => {
		setTimeout(() => {
			updateSuggestionStep(0, 'active');
			updateSuggestionStep(1, 'loading');
			runTitleSuggestion({ title: currentTitle });
		}, SUGGESTION_DELAY);
	});
}

function handleSuggestionSuccess(res: string[]): void {
	if (!Array.isArray(res) || res.length === 0) {
		handleSuggestionError('AI没有找到合适的标题');
		return;
	}

	updateSuggestionStep(1, 'active');
	updateSuggestionStep(2, 'loading');

	setTimeout(() => {
		suggestionList.value = res;
		updateSuggestionStep(2, 'active');
		updateSuggestionStep(3, 'loading');

		setTimeout(() => {
			updateSuggestionStep(3, 'active');
		}, SUGGESTION_DELAY);
	}, SUGGESTION_ANIMATION_DELAY);
}

function handleSuggestionError(msg: string): void {
	showErrorMessage(msg);
	resetSuggestionState();
}

function handleSuggestionHide(): void {
	showSuggestion.value = false;
	resetSuggestionState();
}

function handleSuggestionSelect(item: string): void {
	if (!item) return;
	updateFormValue('title', item);
	handleSuggestionHide();
}

// 辅助函数
function resetSuggestionState(): void {
	suggestionList.value = [];
	suggestionSteps.value = [...INITIAL_STEPS];
}

function updateSuggestionStep(stepIndex: number, status: SuggestionStep['status']): void {
	if (stepIndex >= 0 && stepIndex < suggestionSteps.value.length) {
		suggestionSteps.value[stepIndex].status = status;
	}
}

function showErrorMessage(msg: string): void {
	message.error(msg);
	handleSuggestionHide();
}
</script>

<style lang="less" scoped>
.complex-input-container {
	width: 100%;
	max-width: 1180px;
	margin: 0 auto;
	height: 54px;
}

.cascader-wrapper {
	min-width: 120px;
}

.divider {
	width: 1px;
	height: 16px;
	background: #525252;
	margin: 0 10px;
}

:deep {
	.n-cascader {
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.n-base-selection {
		&-placeholder {
			line-height: 34px;
			display: inline-block !important;
			color: #909399 !important;
		}

		&-input__content {
			line-height: 1.5;
		}
	}
}

.optimize-button {
	width: 94px;
	height: 34px;
	border-radius: 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 5px;
	transition: all 0.3s ease;

	&.disabled {
		cursor: not-allowed;
		background-color: #D8D8D8;
		color: #7C7C7C;
	}

	&.enabled {
		cursor: pointer;
		background-color: #EDF4FF;
		color: #0E69FF;

		&:hover {
			background-color: darken(#EDF4FF, 5%);
		}
	}
}

.suggestion {
	&-loading,
	&-list {
		.suggestion-title {
			color: #69A2FF;
			display: flex;
			align-items: center;
			gap: 5px;
			margin-bottom: 2px;
		}
	}

	&-loading {
		.suggestion-progress {
			background: linear-gradient(to right, #F0F6FF, rgba(237, 236, 255, 0.38));
			height: 40px;
			border-radius: 4px;
			padding: 0 18px;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			margin-top: 14px;
			margin-bottom: 2px;
		}
	}

	&-list {
		.suggestion-item {
			margin-left: 17px;
			line-height: 30px;
			cursor: pointer;
			transition: background-color 0.2s ease;

			&:hover {
				background-color: #E1ECFA;
			}

			.suggestion-text {
				display: flex;
				align-items: center;
				gap: 5px;
				color: #666666;
			}
		}

		.refresh-button {
			display: flex;
			align-items: center;
			gap: 5px;
			color: #0E69FF;
			cursor: pointer;
			margin: 12px 0 5px 17px;
			transition: opacity 0.2s ease;

			&:hover {
				opacity: 0.8;
			}
		}
	}
}

/* 适配1366*768屏幕 */
@media screen and (max-width: 1366px) {
	.complex-input-container {
		max-width: 1080px;
	}
	
	.cascader-wrapper {
		min-width: 110px;
	}
	
	.optimize-button {
		width: 90px;
		font-size: 13px;
	}
}

/* 响应式样式 */
@media screen and (max-width: 1200px) {
	.complex-input-container {
		max-width: 100%;
		padding: 0 15px;
	}
}

@media screen and (max-width: 992px) {
	.optimize-button {
		width: 85px;
		font-size: 13px;
	}
}

@media screen and (max-width: 768px) {
	.complex-input-container {
		padding: 0 10px;
		height: 50px;
	}

	:deep(.n-input) {
		--n-height: 50px !important;
	}

	.optimize-button {
		width: 75px;
		height: 30px;
		font-size: 12px;
	}

	.suggestion {
		&-loading .suggestion-progress {
			height: 35px;
			padding: 0 15px;
		}

		&-list .suggestion-item {
			line-height: 28px;
			font-size: 14px;
		}
	}
}

@media screen and (max-width: 576px) {
	.complex-input-container {
		padding: 0 5px;
		height: 45px;
	}

	:deep(.n-input) {
		--n-height: 45px !important;
	}

	:deep(.n-cascader) {
		font-size: 12px;
	}

	.divider {
		height: 14px;
		margin: 0 5px;
	}

	.optimize-button {
		width: 65px;
		height: 28px;
		font-size: 11px;
		gap: 3px;
	}

	.suggestion {
		&-loading {
			.suggestion-title {
				font-size: 13px;
			}

			.suggestion-progress {
				height: 30px;
				padding: 0 10px;
			}
		}

		&-list {
			.suggestion-title {
				font-size: 13px;
			}

			.suggestion-item {
				margin-left: 10px;
				line-height: 26px;
				font-size: 13px;
			}

			.refresh-button {
				margin-left: 10px;
				font-size: 13px;
			}
		}
	}
}
</style>