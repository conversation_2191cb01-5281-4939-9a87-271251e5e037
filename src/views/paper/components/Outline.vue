<script lang="ts" setup>
import { onBeforeMount, ref, watch } from "vue";
import {
	NText,
	NIcon,
	NProgress,
	NCollapse,
	NCollapseItem,
	NButton,
	NTooltip,
	NInput,
	NPopconfirm,
	NImage,
} from "naive-ui";
import Notice2Svg from "./svgs/notice-2.svg";
import NaotuSvg from "./svgs/naotu.svg";
import Naotu1Svg from "./svgs/naotu1.svg";
import ProcessSvg from "./svgs/process.svg";
import Process1Svg from "./svgs/process1.svg";
import ChartSvg from "./svgs/chart.svg";
import Chart1Svg from "./svgs/chart1.svg";
import EditSvg from "./svgs/edit.svg";
import DeleteSvg from "./svgs/delete.svg";
import FingerSvg from "./svgs/finger.svg";
import RefreshSvg from "./svgs/refresh.svg";
import NoteImage from "@/assets/aiwork/note.png";
// import { markdown2outline, markdown2TreeOutline, outline2markdown } from "@/utils/markdown/paper";

const outline = defineModel<any>("outline");
const firstTime = defineModel<boolean>("firstTime");

interface Emit {
	(ev: 'chart-checked'): void
}
const $emit = defineEmits<Emit>();
const $props = defineProps<{
	// outline: any;
	progress?: number;
	statusText?: string;
	onRetryGenerate?: () => void;
	loading?: boolean;
	checkable?: boolean;
	paperType?: string;
}>();

const expandedNames = ref<number[]>([]);

watch(
	() => outline.value,
	(newVal) => {
		// expandedNames.value = newVal?.children?.map((_: any, i: number) => i)
		// tree.value = markdown2outline(newVal as string)
		// console.log("outline changed", tree.value)
		expandedNames.value = outline.value?.children?.map(
			(_: any, i: number) => i
		);
		if($props.loading) document.querySelector('#outline-retry')?.scrollIntoView({behavior: 'smooth', block: "end"})
	}
);

const handleExpand = (index: number, event: Event) => {
	if (event.target?.tagName === "svg" || event.target?.tagName === "path") {
		if (event.target?.tagName === "path") {
			if (event.target?.parentNode?.parentNode?.classList?.contains("no-expand")) {
				event.preventDefault();
				event.stopPropagation();
				return;
			}
		} else if (event.target?.parentNode?.classList?.contains("no-expand")) {
			event.preventDefault();
			event.stopPropagation();
			return;
		}
	}
	let set = new Set(expandedNames.value);
	if (set.has(index)) {
		set.delete(index);
	} else {
		set.add(index);
	}
	expandedNames.value = Array.from(set);
};

const handleDelete = (i1, i2) => {
	if (outline.value.children[i1].children.length <= 1)
		return window.$notification?.error({
			title: "删除失败",
			content: "每个章节至少保留一项",
			duration: 2000,
		});
	outline.value.children[i1].children.splice(i2, 1);
};

const handleDeleteTitle = (i1) => {
	outline.value.children.splice(i1, 1);
}

const scrollIntoView = (selector: string) => {
	document.querySelector(selector)?.scrollIntoView({ behavior: "smooth" });
};

const handleShowEdit = (item) => {
	item.editable = true;
	document.body.addEventListener(
		"click",
		(event) => {
			console.log(event.target)
			if ((event.target as Element)?.nodeName === "INPUT") return handleShowEdit(item);
			item.editable = false;
		},
		{
			once: true,
			capture: true,
		}
	);
};
const updateFirstTime = (data: string) => {
	if (firstTime) {
		firstTime.value = false;
	}
	if (!!data) {
		$emit('chart-checked')
	}
};
// const updateOutline = () => outline.value = outline2markdown(tree.value)
</script>

<template>
	<div id="outline" class="flex flex-col items-center sm:w-[100vw] sm:px-4">
		<div class="text-center text-[22px] font-bold">
			<NText type="primary">编辑大纲，生成全文</NText>
		</div>
		<div class="text-[14px] mt-[30px]">
			<NIcon :size="16" class="mr-[5px]">
				<Notice2Svg />
			</NIcon>
			大纲可<NText type="warning">直接点击编辑和删除</NText>，点击<NText type="primary">右侧编辑图标</NText>可以对标题和概要内容进行<NText
				type="warning">编辑</NText>，点击<NText type="primary">删除图标</NText>可对大纲内容进行<NText type="warning">删除</NText>
		</div>
		<div v-if="!outline">
			<div class="flex flex-col justify-center items-center mt-[30px] gap-[18px]">
				<NImage :src="NoteImage" preview-disabled class="w-[32px]" @click="scrollIntoView('#step-1')" />
				<div class="text-[#8D8D8D]">输入文章标题生成大纲</div>
			</div>
		</div>
		<div v-else class="sm:w-full">
			<div v-if="outline?.title" class="mt-[50px] text-center text-[20px] color-[#000]">
				题目：{{ outline.title }}
			</div>
			<div v-if="outline?.topic" class="mt-[20px] text-center color-[#000]">
				科目：{{ outline.topic }}
			</div>
			<div v-if="outline != null && loading" class="mt-[50px] flex justify-center">
				<NProgress type="circle" :percentage="$props.progress || 0" :gap-offset-degree="180"
					style="width: 222px">
					<div>
						<div class="text-[18px] text-center block">
							{{ $props.statusText || "正在生成大纲" }}
						</div>
						<div class="text-[14px] text-center">
							{{ $props.progress || 0 }}%
						</div>
					</div>
				</NProgress>
			</div>
			<div class="mt-[70px] rounded-[14px] py-[24px] px-[20px] relative bg-white w-[750px] sm:w-full"
				style="box-shadow: 1px -1px 19px 0px #cbd6e7">
				<div>
					<NCollapse :expanded-names="expandedNames">
						<NCollapseItem v-for="(l1, i1) in outline?.children || []" :key="i1" :name="i1"
							@click="(e) => handleExpand(i1, e)">
							<template #header>
								<div class=" flex w-full flex-row justify-between items-center mr-[7px]">
									<NInput v-if="l1.editable" v-model:value="l1.title" data-test="123" @click.stop />
									<div v-else class=" flex-1 font-bold leading-[18px] break-all">{{ l1.title }}</div>
									<NTooltip trigger="hover" v-if="l1.title !== '参考文献'">
										<template #trigger>
											<NIcon :size="24" class="ml-[20px] cursor-pointer no-expand"
												@click="handleShowEdit(l1)">
												<EditSvg />
											</NIcon>
										</template>
										<div>可对标题内容进行编辑</div>
									</NTooltip>
									<NPopconfirm @positive-click="(e) => handleDeleteTitle(i1)" placement="top">
										<template #trigger>
											<NIcon :size="24" class="ml-[16px] cursor-pointer no-expand">
												<DeleteSvg />
											</NIcon>
										</template>
										<div class=" mr-[5px]">
											确定删除 <NText type="primary">【{{ l1.title }}】</NText>?
											<br />
											章节不足会导致论文总体字数不足
										</div>
									</NPopconfirm>
								</div>
							</template>
							<div v-for="(l2, i2) in l1?.children || []" :key="`${i1}.${i2}`"
								class="flex flex-row justify-between items-center px-[16px] rounded-[6px] py-[14px] transition-all hover:bg-gray-100 sm:flex-col sm:items-start"
								@click.stop.prevent>
								<div class="flex flex-col flex-1">
									<NInput v-if="l2.editable" v-model:value="l2.title" data-test="123" @click.stop />
									<div v-else class="font-bold mb-[13px] break-all">{{ l2.title }}</div>
									<div v-for="(l3, i3) in l2?.children || []" :key="`${i1}.${i2}.${i3}`"
										style="padding-left: 16px;">
										<!-- <NInput class="w-full" v-if="l2.editable" v-model:value="l3.title"
											@click.stop /> -->
										<div class=" text-[14px] text-[#3D3D3D] leading-[28px]">{{ l3 }}</div>
									</div>
								</div>
								<div class="flex justify-end items-center pl-[20px] sm:mt-2 sm:pl-4" v-if="l2?.content">
									<!-- <div class="flex justify-center items-center gap-[7px] border-[1px] border-solid border-gray-300 rounded-[4px] p-[4px]"
										v-if="$props.paperType === 'paper' || $props.paperType === 'adult' || $props.paperType === 'proposal'">
										<NTooltip trigger="hover">
											<template #trigger>
												<i v-if="checkable" class="operate-icon naotu-icon"
													@click="l2?.chartType === 'mind' ? l2.chartType = null : l2.chartType = 'mind'; updateFirstTime(l2.chartType)"
													:class="l2.chartType === 'mind' ? 'active' : ''" />
												<i v-else class="operate-icon naotu-icon" />
											</template>
											<div>点亮图标，本节插入思维导图</div>
										</NTooltip>
										<NTooltip trigger="hover">
											<template #trigger>
												<i v-if="checkable" class="operate-icon process-icon"
													@click="l2?.chartType === 'flow' ? l2.chartType = null : l2.chartType = 'flow'; updateFirstTime(l2.chartType)"
													:class="l2.chartType === 'flow' ? 'active' : ''" />
												<i v-else class="operate-icon process-icon" />
											</template>
											<div>点亮图标，本节插入流程图</div>
										</NTooltip>
										<NTooltip trigger="hover">
											<template #trigger>
												<i v-if="checkable" class="operate-icon chart-icon"
													@click="l2.table = !l2.table; updateFirstTime(l2.table)"
													:class="l2.table ? 'active' : ''" />
												<i v-else class="operate-icon chart-icon" />
											</template>
											<div>点亮图标，本节插入表格</div>
										</NTooltip>
									</div> -->
									<NTooltip trigger="hover" v-if="l2.title !== '参考文献'">
										<template #trigger>
											<NIcon :size="24" class="ml-[20px] cursor-pointer"
												@click="handleShowEdit(l2)">
												<EditSvg />
											</NIcon>
										</template>
										<div>可对标题内容进行编辑</div>
									</NTooltip>
									<NPopconfirm @positive-click="() => handleDelete(i1, i2)" placement="top">
										<template #trigger>
											<NIcon :size="24" class="ml-[16px] cursor-pointer">
												<DeleteSvg />
											</NIcon>
										</template>
										<div>
											确定删除 <NText type="primary">【{{ l2.title }}】</NText>?
											<br />
											章节不足会导致论文总体字数不足
										</div>
									</NPopconfirm>
								</div>
							</div>
						</NCollapseItem>
					</NCollapse>
				</div>
				<!-- 浮层提示 -->
				<!-- <div class="absolute top-0 w-[173px] left-full translate-x-[20px] rounded-[10px] p-[20px] color-[#666] text-[12px] sm:hidden"
					style="box-shadow: 1px -1px 19px 0px #cbd6e7"
					v-if="$props.paperType === 'paper' || $props.paperType === 'adult'">
					<div class="color-[#3D3D3D] font-[14px]">进阶小TIPS：</div>
					<div class="mt-[13px] flex items-center gap-[3px] text-[12px]">
						<NIcon :size="23">
							<FingerSvg />
						</NIcon>
						<span>点亮右侧按钮</span>
					</div>
					<div class="mt-[13px] flex items-center gap-[3px] text-[12px]">
						<NIcon :size="24">
							<NaotuSvg />
						</NIcon>
						<span>：插入<NText type="primary">思维导图</NText></span>
					</div>
					<div class="mt-[13px] flex items-center gap-[3px] text-[12px]">
						<NIcon :size="24">
							<ProcessSvg />
						</NIcon>
						<span>：插入<NText type="primary">流程图</NText></span>
					</div>
					<div class="mt-[13px] flex items-center gap-[3px] text-[12px]">
						<NIcon :size="24">
							<ChartSvg />
						</NIcon>
						<span>：插入<NText type="primary">表格</NText></span>
					</div>
					<div class="mt-[20px]">支持修改、删除内容</div>
				</div> -->

				<!-- 刷新按钮 -->
				<div id="outline-retry" class="mt-[30px] flex justify-center">
					<NButton type="primary" size="small" ghost round icon-placement="right"
						@click="() => !$props.loading && $props.onRetryGenerate?.()" :loading="$props.loading">
						<span v-if="!$props.loading">大纲不满意？重新生成</span>
						<span v-if="$props.loading">生成大纲中</span>
						<template v-if="!$props.loading" #icon>
							<NIcon :size="22">
								<RefreshSvg />
							</NIcon>
						</template>
					</NButton>
				</div>
			</div>
		</div>
	</div>
</template>

<style scoped lang="less">

:deep(.n-collapse-item__header) {
	// height: 40px;
	// line-height: 40px;
	min-height: 40px;
	line-height: unset;
	background: #fafafa;
	font-size: 14px;
	font-weight: bold;
	color: #3d3d3d;
	padding: 09px 10px !important;
	border-radius: 6px;
	--n-title-font-weight: bold;

	.n-collapse-item-arrow path {
		stroke: #3d3d3d;
		stroke-width: 1px;
	}
}

:deep(.n-collapse-item__content-wrapper) {
	padding: 14px 16px;

	.n-collapse-item__content-inner {
		padding-top: 0 !important;
	}
}

:deep(.n-collapse .n-collapse-item:not(:first-child)) {
	border: none;
}

:deep(.n-collapse-item__content-wrapper) {
	padding-left: 0;
	padding-right: 0;
}

.operate-icon {
	display: inline-block;
	background-repeat: no-repeat;
	background-size: 24px 24px;
	width: 24px;
	height: 24px;
	cursor: pointer;

	&:hover {
		width: 24px;
		height: 24px;
	}
}

.naotu-icon {
	background-image: url(@/assets/images/mind.png);

	&.active {
		background-image: url(@/assets/images/mind_active.png);

	}
}

.process-icon {
	background-image: url(@/assets/images/flow.png);

	&.active {
		background-image: url(@/assets/images/flow_active.png);
	}
}

.chart-icon {
	background-image: url(@/assets/images/chart.png);

	&.active {
		background-image: url(@/assets/images/chart_active.png);
	}
}
</style>
