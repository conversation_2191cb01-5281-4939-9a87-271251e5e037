<script lang="ts" setup>
import { ref, watch } from 'vue';
import ServiceCard from './ServiceCard.vue';
import { ValueAddedService } from '../types';

interface Props {
    services: ValueAddedService[]
}

interface Emit {
    (event: 'checked', data: { id: number, power: number, type: string }[]): void
    (ev: 'scroll'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emit>()

const services = ref<ValueAddedService[]>(props.services || [])
const onChecked = (data: { checked: number, id: number, type: string }) => {
    services.value = services.value.map(item => {
        if (item.id === data.id) {
            item.checked = data.checked
        }
        return item
    })
    emit('checked', services.value.filter(item => item.checked).map(({ id, power, type }) => ({ id, power, type })))
}
watch(() => props.services, (newVal) => {
	console.log('11111', newVal)
	emit('checked', services.value.filter(item => item.checked).map(({ id, power, type }) => ({ id, power, type })))
})
</script>
<template>
    <div class="evas rounded-[14px] py-[40px]  relative bg-white w-[750px] mx-auto mt-[38px] sm:w-[100vw] sm:-mx-4"
        style="box-shadow: 1px -1px 19px 0px #CBD6E7;">
        <div class="text-sm color-[#3d3d3d] font-bold px-[32px]">
            <span class="text-sm color-[#3d3d3d] font-bold flex flex-row relative">增值附加服务
                <div class=" tag">
                    <div class="hot"></div>
                </div>
            </span>
        </div>
        <div class=" flex flex-wrap flex-raw w-full gap-[14px] pt-[18px] px-[27px]">
            <ServiceCard v-for="(item, index) in services" :key="index" :id="item.id" :tag="item.tag"
                :checked="item.checked" :price="item.price" :originalPrice="item.originalPrice" :power="item.power"
                :description="item.description" :title="item.title" :type="item.type" :templateId="item.templateId"
                :exampleUrl="item.exampleUrl" @checked="onChecked" @scroll="() => emit('scroll')">
            </ServiceCard>
        </div>
    </div>
</template>
<style lang="less" scoped>
.evas {
    .tag {
        width: 23px;
        height: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: url("@/assets/aiwork/nav/nav-tag-bg.png") no-repeat center center / contain;
        display: flex;
        justify-content: center;
        align-items: center;

        .text {
            width: 13px;
            height: 7px;
        }

        .hot {
            .text;
            background: url("@/assets/aiwork/nav/hot.png") no-repeat center center / contain;
        }

        .new {
            .text;
            background: url("@/assets/aiwork/nav/new.png") no-repeat center center / contain;
        }
    }

    .base-card {
        flex: 1;
        border: 1px solid #E1ECFA;
        background: #F6FAFF;
        display: flex;
        gap: 12px;
        justify-content: center;
        align-items: center;
        padding: 15px;
        font-size: 12px;
        color: #3d3d3d;
        border-radius: 6px;

        &_icon {
            flex-grow: 0;
            flex-shrink: 0;
        }

        &_content {
            flex: 1;
            display: flex;
            flex-direction: column;

            &_title {}

            &_desc {
                color: #666;
            }
        }

        &_selected-icon {
            flex-grow: 0;
            flex-shrink: 0;
            width: 20px;
            height: 20px;
        }
    }

    .vas-card {
        padding: 18px 20px;
        box-sizing: border-box;
        border: 1px solid #E3ECF7;
        border-radius: 6px;

        &_title {
            font-size: 14px;
            font-weight: 700;
        }

        &_tag {
            background: #FFFBD9;
            border-radius: 2px;
            padding: 2px 6px;
            font-size: 12px;
            color: #D08A22;
            height: 18px;
            box-sizing: border-box;
            margin-left: 6px;
            display: flex;
            align-items: center;
        }

        &_desc {
            font-size: 12px;
            color: #666666;
        }
    }
}
</style>
