<template>
	<n-float-button :right="isPC ? 50 : 10" :bottom="140" :width="60" :height="96" shape="square" type="default"
		class="text-[#333333] sm:z-[2]" style="--n-color-hover: #fff; --n-box-shadow: 0 2px 8px 0 #dce4f6">
		<div class="float-container flex flex-col items-center justify-center w-full px-[10px] py-[10px]">
			<div class="text-[12px] flex flex-col justify-center items-center hover:text-[#0E69FF]"
				@click="handleHistoryClick">
				<NImage :src="PaperIcon" width="22px" height="22px" preview-disabled />
				<span class="leading-[18px] text-[14px] pt-[1px] text-center  mt-[9px]">历史记录</span>
			</div>
		</div>
	</n-float-button>
</template>

<script setup lang="ts">
import { NFloatButton, NImage } from 'naive-ui'
import PaperIcon from '@/assets/images/paper-icon.png'
import { useRouter } from "vue-router";
import { useBasicLayout } from "@/hooks/useBasicLayout";

const { isPC } = useBasicLayout();
const router = useRouter();

const handleHistoryClick = () => {
  router.push("/paper2/history");
};
</script>

<style lang="less" scoped>
.float-container {
  cursor: pointer;
  border-radius: 10px;
  background: #ffffff;
}

:deep(.n-float-button--square-shape) {
  border-radius: 10px !important;
}

:deep(.n-float-button__body) {
  &:hover {
    background: #ffffff;
  }
}
</style>
