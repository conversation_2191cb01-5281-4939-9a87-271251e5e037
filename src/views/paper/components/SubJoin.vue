<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { Subjoin, PaperStatus } from '../types';
import { useRequest } from 'vue-hooks-plus';
import { fetchPaperUrl } from '../apis';
import { downloadFile } from "../../../utils/downloadFile";
import { useUserStore } from '@/store';

const userStrore = useUserStore()

const router = useRouter()

interface Props {
    subJoin: Subjoin
    paperStatus: PaperStatus
}
const { run: runFetchPaperUrl } = useRequest(fetchPaperUrl, {
    manual: true,
    onSuccess: (data: { url: string }) => {
        downloadFile(data.url)
    }
})
const props = defineProps<Props>()
const goToApps = (url: string, title: string) => {
    if (props.paperStatus === PaperStatus.Processing) return
		if(title === '答辩PPT' && !userStrore.userInfo?.uid) {
			window.$aiwork.openLogin().then(() => goToApps(url, title))
			return
		}
    if (url) {
        if (url.includes('http')) {
					title === '答辩PPT' ? window.location.href = url : window.open(url)
        } else {
            router.push(url)
        }
    } else {
        console.error('url is empty')
        router.push('/apps/91?appid=300')
    }
}
const downloadPaper = (id) => {
    runFetchPaperUrl({ id })
};
</script>

<template>
    <div class="flex justify-between mb-[25px] text-[#666666]">
        <div v-replace>{{ subJoin.title }}</div>
        <div v-if="subJoin.status === PaperStatus.Wait">
            <span>
                等待生成
            </span>
        </div>
        <div v-else-if="subJoin.title.includes('AI无限改稿') || subJoin.title === '答辩PPT'">
            <span class=" block " @click="goToApps(subJoin.href, subJoin.title)"
                :class="paperStatus === PaperStatus.Processing ? ' text-[#C5C5C5] cursor-not-allowed' : 'text-[#21AC58] cursor-pointer'">点击使用</span>
        </div>
        <div v-else-if="subJoin.status === PaperStatus.Processing">
            <span>
                生成中...
            </span>
        </div>
        <div v-else-if="(subJoin.status === PaperStatus.Success) && subJoin.docxUrl"
            class=" flex flex-row  text-[12px] leading-[16px] gap-x-[16px]">
            <span class=" block text-[#005AFF] cursor-pointer" style=" cursor: pointer;"
                @click="downloadPaper(subJoin.paperId)">下载</span>
            <!-- <span class="cursor-pointer" style=" cursor: pointer;">删除</span> -->
        </div>
        <div v-else-if="(subJoin.status === PaperStatus.Success) && !subJoin.docxUrl">
            <span>已完成</span>
        </div>
    </div>

</template>

<style lang="less" scoped></style>
