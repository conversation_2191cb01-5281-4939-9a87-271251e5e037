import { post } from "@/utils/request";
import { AxiosProgressEvent } from "axios";
// 获取论文列表
export function fetchPaperList2<T>(params: {
	page?: number;
	pageSize?: number;
	type?: string;
	title?: string;
	status?: number | null;
}) {
	return post<T>({
		url: "/api3/paper/core2/list",
		data: params,
	});
}


// 获取论文统计信息
export function fetchPaperStatistics<T>() {
	return post<T>({
		url: "/api3/paper/core2/statistic",
	});
}

// 生成论文
export function fetchGeneratePaperCore2<T>(params: {
	id: number;
	serviceIds?: number[];
}) {
	return post<T>(
		{
			url: "/api3/paper/core2/generatePaper",
			data: params,
		},
		{ payParams: { type: "paper", paperId: params.id } }
	);
}

// 获取论文详情
export function fetchPaperDetail<T>(params: { id: number }) {
	return post<T>({
		url: "/api3/paper/core2/detail",
		data: params,
	});
}

// 添加论文
export function fetchAddPaper<T>(params?: any) {
	return post<T>({
		url: "/api3/paper/core2/add",
		data: {
			...params,
		},
	});
}

export function fetchPaperTypeList<T>(params?: any) {
	return post<T>({
		url: "/api3/paper/template/listAll",
		data: {
			page: 1,
			title: "",
			pageSize: 100,
			type: "paper",
			...params,
		},
	});
}

export function fetchPaperTemplateDetail<T>(params?: any) {
	return post<T>({
		url: "/api3/paper/template/detail",
		data: {
			type: "paper",
			...params,
		},
	});
}

interface IGenerateOutlineParams {
	onDownloadProgress: (progressEvent: AxiosProgressEvent) => void;
}

export function fetchGenerateOutline<T>(
	params?: IGenerateOutlineParams & Record<string, any>
) {
	const { onDownloadProgress, ...rest } = params || {};
	return post<T>({
		url: "/api3/paper/core/generateOutline",
		data: {
			...rest,
		},
		onDownloadProgress,
	});
}

export interface GeneratePaperProgress {
	message: string; // 提示信息
	status: "pending" | "success" | "fail";
	step: number; // 进度标识 3：正在创作摘要，4：正在创作论文正文，5：正在制作论文，6：处理完成
	id: string; // 论文ID
	url: string; // 论文下载地址
	title: string; // 论文标题
}

export function fetchGeneratePaperProcess(params?: { messageId: string }) {
	return post<GeneratePaperProgress>(
		{
			url: "/api3/paper/core/getByMessageId",
			data: params,
		},
		{ level: 0 }
	);
}

// 生成论文内容
export function fetchGeneratePaper<T>(params?: {
	outline?: string; // markdown格式的大纲
	id: string | number;
}) {
	return post<T>(
		{
			url: "/api3/paper/core/generatePaper",
			data: {
				...params,
			},
		},
		{ payParams: { type: "paper", paperId: params?.id } }
	);
}

// 获取历史论文列表
export function fetHistoryPaperList<T>(params?: {
	title?: string;
	page?: number;
	pageSize?: number;
	type?: string;
	status?: number | null;
}) {
	return post<T>(
		{
			url: "/api3/paper/core/list",
			data: {
				// type: 'paper',
				...params,
			},
		},
		{ level: 0 }
	);
}

// 获取论文链接
export function fetchPaperUrl<T>(params?: { id: number }) {
	return post<T>({
		url: "/api3/paper/core/getUrl",
		data: params,
	});
}

// 最近论文信息
export function fetchLatestMessage<T>() {
	return post<T>({
		url: "/api3/paper/core/notice",
	});
}
// 生成论文-增值
export function fetchValueAddedGeneratePaper(params: {
	outlineData?: any;
	id: number;
	serviceIds?: number[];
}) {
	return post(
		{
			url: "/api3/paper/core/generatePaper2",
			data: params,
		},
		{ payParams: { type: "paper", paperId: params?.id } }
	);
}
// 生成任务书-增值
export function fetchValueAddedGenerateTaskPaper(params: {
	outlineData?: any;
	id: number;
	serviceIds?: number[];
}) {
	return post(
		{
			url: "/api3/paper/core/generateBooks",
			data: params,
		},
		{ payParams: { type: "paper" } }
	);
}
// 获取论文进度-增值
export function fetchValueAddedPaperProgressByMessageId(params: {
	messageId: string;
}) {
	return post({
		url: "/api3/paper/core/getByMessageId2",
		data: params,
	});
}

// 获取增值服务列表
export function fetchValueAddedPaperService(params: { id: number }) {
	return post({
		url: "/api3/paper/core/services",
		data: params,
	});
}
// 智能推荐标题
export function fetchPaperTitleSuggestion(params: { title: string }) {
	return post({
		url: "/api3/paper/core/generateTitle",
		data: params,
	});
}
