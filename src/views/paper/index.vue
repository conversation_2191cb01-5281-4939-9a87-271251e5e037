<script setup lang="ts">
import {inject, ref, onMounted, watch, reactive, onUnmounted} from "vue";
import logoImage from "@/assets/aiwork/logo_612.png";
import { NButton, NCard, NImage, NProgress, NModal, useMessage } from "naive-ui";
import SelectCreateOptions from "./components/SelectCreateOptions.vue";
import IntroList from "./components/IntroList.vue";
import Outline from "./components/Outline.vue";
import VAS from "./components/VAS.vue";
import EVAS from './components/EVas.vue';
import Steps from "./components/Steps.vue";
import {
	fetchGenerateOutline,
	fetchGeneratePaper,
	fetchGeneratePaperProcess,
	fetchValueAddedPaperService,
	fetchValueAddedGeneratePaper,
	fetchValueAddedPaperProgressByMessageId,
	fetchValueAddedGenerateTaskPaper
} from "./apis";
import { GenerateParams, ValueAddedService } from "./types";
import Notice2Svg from "./components/svgs/notice-2.svg";
import History from "./components/History.vue";
import { markdown2outline, newOutline2markdown, outline2markdown } from "@/utils/markdown/paper";
import { downloadFile } from "@/utils/downloadFile";
import { useRoute } from "vue-router";
import { useRequest } from "vue-hooks-plus";
import WarningImg from '@/assets/images/warning.png';
import GlobalFloat from "@/components/common/GlobalFloat/index.vue";
import {useUserStore} from "@/store";
import { textTransform } from "@/utils/textTransform";
import { useRouter } from "vue-router";
const router = useRouter();
// import Payment from "./components/Payment.vue";

type CurrentGeneratePaperInfo = {
	percent: number; // 进入
	status: "success" | "fail" | "pending"; // 状态
	message: string;
	step: number; // 步骤
	title: string;
	url: string; // 下载地址
};

type OutlineInfo = {
	loading?: boolean;
	outline?: any; // 大纲详细内容
	progress?: number; // 进度
	stateText?: string; // 状态文字
	power?: number; // 消耗算力
	paperId?: number; // 论文ID
};

const route = useRoute();
const { id } = route.query;

// 生成论文大纲信息
const outlineInfo = reactive<OutlineInfo>({});
const generateParams = ref<GenerateParams>();
const generatePaperId = ref<number>();
// 通过大纲生成论文进度信息
const currentGeneratePaperInfo = ref<CurrentGeneratePaperInfo | null>();
const updateHistorySign = ref<any>(null);
const step = ref(1);
const generateLoadingRef = ref(false);
const isTask = ref(false);
const showValueAddedService = ref(false);
const valueAddServiceList = ref<ValueAddedService[]>([]);
const serviceIds = ref<number[]>([]);
const serviceTotal = ref<number>(0);
const showChartTipsModal = ref(false);

const outlineValueCheckable = ref(true);
const outlineFirstTime = ref(true);
const userRestCount = ref(0)

const disableForm = ref(false)

const userStore = useUserStore()
const message = useMessage()

onMounted(() => {
	if(userStore.curTeam) {
		userStore.changeTeam(null)
		message.success('团队版暂不支持论文相关功能, 已自动为您切换到个人版本')
	}
})

// 获取增值服务内容
const { run: runFetchValueAddedPaperService } = useRequest(fetchValueAddedPaperService, {
	manual: true,
	onSuccess: (data) => {
		showValueAddedService.value = true;
		valueAddServiceList.value = data.map((item: any) => {
			if (item.isSelect) {
				item.checked = 1
				serviceIds.value.push(item.id)
				serviceTotal.value += item.power
			} else {
				item.checked = 0
			}
			return item
		});
	},
})
// 获取获取论文进度
const { run: runFetchValueAddedPaperProgressByMessageId, cancel: cancelFetchValueAddedPaperProgressByMessageId, loading: fetchValueAddedPaperProgressByMessageIdLoading } = useRequest(fetchValueAddedPaperProgressByMessageId, {
	manual: true,
	pollingInterval: 5000,
	pollingWhenHidden: false,
	onSuccess: (data) => {
		const { message, status, step, url, title, zipUrl, isRefresh = false } = data || {};
		if (status === "fail") {
			window.$notification?.error({
				title: "生成失败",
				content: message,
				duration: 3000,
			});
			generateLoadingRef.value = false;
			disableForm.value = false;
			cancelFetchValueAddedPaperProgressByMessageId()
			updateHistorySign.value = new Date().getTime();
			return;
		}
		if (status === "success") {
			cancelFetchValueAddedPaperProgressByMessageId()
			currentGeneratePaperInfo.value = {
				percent: 100,
				status: "success",
				message,
				step,
				title,
				url: zipUrl,
			};
			generateLoadingRef.value = false;
			disableForm.value = false;
			window.$notification?.success({
				title: "生成成功",
				duration: 3000,
			});
			updateHistorySign.value = new Date().getTime();
			setTimeout(() => {
				downloadFile(zipUrl);
			}, 1000);
			return;
		}
		if (isRefresh) {
			updateHistorySign.value = new Date().getTime();
		}
		currentGeneratePaperInfo.value = {
			percent: Math.floor((100 / 6) * step),
			status,
			message,
			step,
			title,
			url,
		};
		// updateHistorySign.value = new Date().getTime();
	},
	onError: (err) => {
		updateHistorySign.value = new Date().getTime();
		generateLoadingRef.value = false;

	}
})

// 生成论文
const { run: runFetchValueAddedGeneratePaper } = useRequest(fetchValueAddedGeneratePaper, {
	manual: true,
	onSuccess: (data) => {
		runFetchValueAddedPaperProgressByMessageId({ messageId: data?.messageId })
		generateLoadingRef.value = true;
		disableForm.value = false
	},
	onError: (err) => {
		updateHistorySign.value = new Date().getTime();
	}
})

// 生成任务书 TODO
const { run: runFetchValueAddedGenerateTaskPaper } = useRequest(fetchValueAddedGenerateTaskPaper, {
	manual: true,
	onSuccess: (data) => {
		runFetchValueAddedPaperProgressByMessageId({ messageId: data?.messageId })
	},
	onError: (err) => {
		updateHistorySign.value = new Date().getTime();
	}
})

// 生成论文
const handleGeneratePaper = async () => {
	if (!outlineInfo || outlineInfo.loading) return
	const outlineData = newOutline2markdown(outlineInfo.outline);
	runFetchValueAddedGeneratePaper({
		outlineData,
		id: generatePaperId.value as number,
		serviceIds: serviceIds.value
	})
}

// 创作任务书
const handleGenerateTask = (data: GenerateParams) => {
	if (!data.formData) return;
	isTask.value = true;
	disableForm.value = true;
	valueAddServiceList.value = [];
	serviceIds.value = [];
	serviceTotal.value = 0;
	generateParams.value = data;
	Object.assign(outlineInfo, {
		loading: true,
		stateText: "正在分析",
		progress: 0,
		outline: "",
	})
	runFetchValueAddedGenerateTaskPaper(data)
	step.value = 2
}



// 创作大纲(后期会改造)
const handleGenerateOutline = (data: GenerateParams) => {
	if (!data.formData) return;
	isTask.value = false;
	disableForm.value = true;
	valueAddServiceList.value = [];
	serviceIds.value = [];
	serviceTotal.value = 0;
	generateParams.value = data;
	outlineValueCheckable.value = false;
	outlineInfo.outline = null;
	cancelFetchValueAddedPaperProgressByMessageId()
	Object.assign(outlineInfo, {
		loading: true,
		stateText: "正在分析",
		progress: 0,
		outline: "",
	})
	currentGeneratePaperInfo.value = null;
	fetchGenerateOutline({
		formData: data.formData,
		id: data.id,
		onDownloadProgress: ({ event }) => {
			const xhr = event.target;
			const { responseText } = xhr;

			try {
				const arr = responseText.split("\n");
				const content = arr.map((x: string) => JSON.parse(x).text).join("");
				const chunk = JSON.parse(arr.at(-1));
				const {
					// @ts-ignore
					progressInfo: { progress, title },
					paperInfo: _paperInfo,
				} = chunk;
				Object.assign(outlineInfo, {
					...outlineInfo,
					stateText: title,
					progress,
					loading: progress == 100 ? false : true,
					paperId: _paperInfo?.id,
					power: _paperInfo?.power,
				})
				// console.log(content);

				updateOutline(content);
				generatePaperId.value = _paperInfo?.id;
				if (step.value != 2) step.value = 2;
			} catch (e) {
				console.log(e)
			}
		},
	}).then((data) => {
		step.value = 3;
		outlineValueCheckable.value = true;
		updateHistorySign.value = new Date().getTime();
	}).catch(e => {
		cancelFetchValueAddedPaperProgressByMessageId()
	}).finally(() => {
		generateLoadingRef.value = false;
		runFetchValueAddedPaperService({
			id: data.id
		})
	});
};

const updateOutline = (content: string) => outlineInfo.outline = markdown2outline(content);
const handleValueAddedService = (data: { id: number, power: number, type: string }[]) => {
	// const hasChart = data.some(item => item.type === 'chart');
	// if (!hasChart) {
	// 	outlineValueCheckable.value = false;
	// 	outlineInfo.outline?.children.map(item => {
	// 		if (item.children) {
	// 			item.children.map((child: any) => {
	// 				if (child?.chartType) {
	// 					child.chartType = null;
	// 				}
	// 				if (child?.table) {
	// 					child.table = false;
	// 				}
	// 			})
	// 		}
	// 	})
	// } else {
	// }
	outlineValueCheckable.value = true;
	serviceIds.value = data.map(item => item.id);
	serviceTotal.value = data.reduce((prev, next) => prev + next.power, 0);
}

const handleEVasScroll = () => {
	step.value = 2;
	scrollIntoView(`#step-2`);
}
const handleChartChecked = () => {
	valueAddServiceList.value = valueAddServiceList.value.map(item => {
		// if (item.type === 'chart') {
		// 	item.checked = 1
		// }
		return item
	})
}


watch(
	() => step.value,
	(v) => {
		scrollIntoView(`#step-${v}`);
	}
);

const scrollIntoView = (selector: string) => {
	document.querySelector(selector)?.scrollIntoView({ behavior: "smooth" });
};
const handleTypeSelect = (feature: {id:number}) => {
	// 修改query 用vue-router的方式
	router.replace({
		query: {
			...route.query,
			id: feature.id.toString()
		}
	});
};

</script>

<template>
	<div class="page">
		<Steps v-model:current="step" />
		<div class="min-w-[750px] sm:min-w-0 sm:w-[100vw]">
			<div id="step-1">
				<div class="py-[85px] flex flex-col items-center sm:py-8">
					<img :src="logoImage" alt="AIWork365 Logo" class="w-[306px]" />
					<p class="text-[24px] mt-[30px] sm:text-lg sm:px-4 sm:text-center">
						{{ textTransform('背靠海量数据库资源，一键快速生成具有专业性和高质量的万字文稿')}}
					</p>

					<SelectCreateOptions @generate="handleGenerateOutline" @generate-task="handleGenerateTask" :id="id"
						:disable-generate="disableForm" :generate-loading="generateLoadingRef" @select="handleTypeSelect"/>
					<IntroList />
				</div>
			</div>

			<div id="step-2">
				<div class="pt-[100px]" v-if="!isTask">
					<Outline :loading="outlineInfo?.loading" v-model:outline="outlineInfo.outline"
						:paper-type="generateParams?.type" :statusText="outlineInfo?.stateText"
						:progress="outlineInfo?.progress" :checkable="true" v-model:first-time="outlineFirstTime"
						@chart-checked="handleChartChecked"
						@retryGenerate="handleGenerateOutline(generateParams as GenerateParams)" />
				</div>
				<!-- <div class="w-[750px] mt-[37px] mx-auto flex justify-between items-center p-[15px] gap-[10px] rounded-[6px]"
					style="border: 1px solid #e0e6ed">
					<NoteSvg class="w-[34px] h-[34px]" />
					<div class="text-[16px] font-[700] grow-0 color-[#3d3d3d]">
						预览范文效果
					</div>
					<div class="text-[12px] color-[#666] flex-1">
						怕付费后效果不满意?提前看看正文生成效果如何?
					</div>
					<NButton type="primary" size="small" class="w-[78px]"
						style="--n-width: 78px; --n-border-radius: 6px" @click="scrollIntoView('#preview')">查看</NButton>
				</div> -->

				<!-- <div class="w-[750px] mt-[45px] mx-auto flex justify-center items-center gap-[7px]">
					<Note3Svg class="w-[30px] h-[30px]" />
					<span class="text-[14px] color-[#222]">文章AI痕迹太重?
						<NText type="primary" class="cursor-pointer">点击一键降低>></NText>
					</span>
				</div> -->
			</div>

			<div id="step-3" class="pt-[40px] sm:w-full sm:px-4">
				<div class="sm:w-full" v-if="outlineInfo?.outline">
					<VAS :title="outlineInfo.outline?.title" :form-data="generateParams?.formData"
						:detail="generateParams?.detail" :style="generateParams?.style" :is-task="isTask" />
				</div>
				<div v-if="showValueAddedService && valueAddServiceList?.length" class="sm:w-full">
					<EVAS :services="valueAddServiceList" @checked="handleValueAddedService"
						@scroll="handleEVasScroll" />
				</div>
				<div class="flex flex-col justify-center items-center mt-[26px]">
					<span v-if="!outlineInfo?.outline && !isTask"
						class="flex flex-col justify-center items-center gap-[18px]">
						<NButton class="" type="primary" disabled
							style="--n-width: 360px; --n-height: 50px; --n-border-radius: 6px">{{textTransform('点击下载论文')}}</NButton>
						<div class="text-[#8D8D8D] text-center">{{textTransform("还没有可以下载的论文")}}</div>
					</span>

					<!-- <div v-if="outlineInfo?.power && !currentGeneratePaperInfo"
						class=" h-[33px] leading-[33px] px-[32px] text-[14px] bg-[#E7EBF7] color-[#9FA6BB] rounded-[4px]">
						需消耗 {{ outlineInfo.power }} 算力
					</div> -->
					<div v-if="outlineInfo?.power && !currentGeneratePaperInfo"
						class="relative bg-[#EBF0F6] rounded-[3px] text-[#FFFFFF] leading-[34px] px-[25px] mb-[19px] "
						:class="!outlineInfo || outlineInfo.loading ? 'cursor-not-allowed' : ''"
						type="primary" :disabled="!outlineInfo || outlineInfo.loading">
						需消耗 {{ serviceTotal + outlineInfo.power }} 算力
					</div>
					<NButton v-if="outlineInfo?.outline && !currentGeneratePaperInfo" class="relative" type="primary"
						:disabled="!outlineInfo || outlineInfo.loading"
						style="--n-width: 360px; --n-height: 50px; --n-border-radius: 6px" @click="handleGeneratePaper">
						点击{{ isTask ? "生成任务书" : textTransform("生成论文") }}
					</NButton>
					<span class=" text-[20px] leading-[26px] text-[#000000] mb-[33px]" v-if="
						currentGeneratePaperInfo &&
						currentGeneratePaperInfo.status !== 'success'">{{ isTask ? " 任务书" : textTransform("论文") }}生成中...</span>

					<NProgress v-if="
						currentGeneratePaperInfo &&
						currentGeneratePaperInfo.status !== 'success'
					" type="circle" :percentage="currentGeneratePaperInfo.percent || 0" :gap-offset-degree="180" style="width: 222px">
						<div>
							<div class="text-[18px] text-center block">
								{{ currentGeneratePaperInfo.message }}
							</div>
							<div class="text-[14px] text-center">
								{{ currentGeneratePaperInfo.percent || 0 }}%
							</div>
						</div>
					</NProgress>

					<span class=" text-[12px] leading-[16px] text-[#666666] mt-[45px]" v-if="
						currentGeneratePaperInfo &&
						currentGeneratePaperInfo.status !== 'success'">平台所生成的全文为范文初稿，仅用作参考。不能直接用于毕业论文、发表刊物等</span>
					<NButton v-if="currentGeneratePaperInfo?.status === 'success'" class="relative mt-[19px] "
						type="primary" style="--n-width: 360px; --n-height: 50px; --n-border-radius: 6px"
						@click="downloadFile(currentGeneratePaperInfo?.url)">
						点击下载{{ isTask ? "任务书" : "论文" }}
					</NButton>
				</div>

				<div class="w-[750px] mt-[73px] mx-auto sm:w-full">
					<History :refresh="updateHistorySign" />
				</div>
			</div>
			<div id="step-4" class="py-[90px]">
				<div class="w-[750px] 2xl:w-[1000px] mx-auto sm:w-[100vw] sm:px-4">
				</div>

				<div
					class="w-[750px] 2xl:w-[1000px] mx-auto mt-[60px] bg-[#F4F8FF] rounded-[10px] px-[40px] py-[30px] text-[#3D3D3D] text-[14px] leading-[34px] sm:w-full sm:px-4 sm:leading-6">
					<div class="flex items-center gap-[7px] sm:items-start sm:mb-2">
						<Notice2Svg class="w-[14px] h-[14px] shrink-0 sm:mt-2" />
						<span>{{textTransform('论文助手,采用自研大模型技术,确保论文隐私')}}</span>
					</div>
					<div class="flex items-center gap-[7px] sm:items-start sm:mb-2">
						<Notice2Svg class="w-[14px] h-[14px] shrink-0 sm:mt-2" />
						<span>支付之前,请确认能接受机器生成带来的误差和局限性,再支付费用,确认支付后无法退款</span>
					</div>
					<div class="flex items-center gap-[7px] sm:items-start sm:mb-2">
						<Notice2Svg class="w-[14px] h-[14px] shrink-0 sm:mt-2" />
						<span>所有生成的范文模板只可用作格式参考,不允许抄袭、代写、直接挪用等行为</span>
					</div>
				</div>
			</div>
		</div>
		<GlobalFloat />

	</div>

	<n-modal v-model:show="showChartTipsModal">
		<n-card style="width: 244px;" :bordered="false" size="huge" role="dialog" aria-modal="true">
			<template #header>
				<div class=" flex flex-row items-center">
					<NImage :src="WarningImg" preview-disabled width="18px" height="18px" />
					<span class="  text-[18px]">
						提示
					</span>
				</div>
			</template>
			<template #default>
				<div class=" text-[16px] text-[#3D3D3D] text-center">您当前算力不足，此次生成需扣除算力 <span class=" text-[#005AFF]">
						{{ serviceTotal +
							outlineInfo?.power! || 0 }}
					</span>剩余论文算力
					<span class=" text-[#FF5100]">
						{{ userRestCount }}
					</span>
					，请及时充值
				</div>
			</template>
			<template #footer>
				<div class=" w-full flex flex-row gap-x-[10px]">
					<div class=" bg-[#317FFF] text-[#ffffff] text-[12px] w-[94px] h-[36px] flex justify-center items-center cursor-pointer"
						@click="">
						立即充值</div>
				</div>
			</template>
		</n-card>
	</n-modal>

	<!-- <Payment v-model:show="paymentShow" /> -->
</template>

<style lang="less">
@import "./index.less";

.page {
	color: #3d3d3d;
	font-size: 16px;
}

#step-1 {
	background: url("@/assets/images/paper-bg.png");
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
}

/*
#step-4 {
	background: url("/images/paper-bg.1.png");
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
}
*/
</style>
