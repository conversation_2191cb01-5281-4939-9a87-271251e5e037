<template>
	<div class="page flex-1 flex flex-col">
		<!-- 落地页 -->
		<template v-if="currentPage === 'landing'">
			<div class="landing-container">
				<div class="landing-left">
					<LandingLeft :icons="preForm?.extend?.icons" :paper-statistics="paperStatistics" />
				</div>
				<div class="landing-right">
					<!-- 顶部轮播图 -->
					<BannerSlider />
					<PaperFeatureList />
					<ComplexInput />
					<div v-for="item in textAreaList" :key="item.field" class="w-full max-w-[1180px] mx-auto">
						<n-form-item :label="item.label" label-placement="left" class="textarea-form-item"
							style="--n-feedback-height: unset !important;">
							<n-input style="--n-height:54px;" type="textarea" rows="1" :placeholder="replaceText(item.placeholder)"
								:default-value="item.defaultValue" @update:value="(val) => updateTextAreaValue(item.field, val)" />
						</n-form-item>
					</div>
					<div class="flex flex-col w-full max-w-[1180px] mx-auto">
						<RadioGroupList v-for="item in radioList" :key="item.field" :data="item" />
					</div>
					<div class="w-full max-w-[1180px] mx-auto text-center">
						<n-checkbox v-model:checked="agreement" :label="replaceText('我已阅读并同意：生成的论文范文仅用于参考，不作为毕业、发表使用等')" />
					</div>
					<div class="w-full max-w-[1180px] mx-auto text-center flex gap-x-[12px] justify-center">
						<n-button type="primary" size="large" :loading="generateLoading" @click="handleGeneratePaper"
							:disabled="!agreement">
							立即生成
						</n-button>
						<n-button size="large" @click="demoModalVisible = true">
							范文示例
						</n-button>
					</div>
					<div class="w-full max-w-[1180px] mx-auto text-center">
						<ScrollText :current="1" :autoplay="true" :interval="2000">
							<div v-for="(message, index) in latestMessage" :key="index"
								class="flex justify-center items-center text-sm">
								<NIcon :size="16" class="mr-[5px]">
									<NoticeSvg />
								</NIcon>
								<span class="text-gray-500 text-[14px]">{{ textTransform(message) }}</span>
							</div>
						</ScrollText>
					</div>
				</div>
			</div>
		</template>

		<!-- 浮动历史和弹窗 -->
		<FloatHistory />
		<DemoModal v-model:visible="demoModalVisible" :title="demoModalTitle">
			<div class="demo-images-container">
				<n-image v-for="(item, index) in preForm?.extend?.examples" :key="index" :src="item" :preview-disabled="true" />
			</div>
		</DemoModal>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { NButton, NCheckbox, useMessage, NIcon, NImage, NInput, NFormItem } from "naive-ui";
import { useRoute, useRouter } from "vue-router";
import { storeToRefs } from "pinia";
import { textTransform } from "@/utils/textTransform";
import { ValueAddedService } from "./types";
import NoticeSvg from "./components/svgs/notice.svg";
import BannerSlider from "./components/BannerSlider.vue";
import PaperFeatureList from "./components/PaperFeatureList.vue";
import LandingLeft from './components/LandingLeft.vue';
import ComplexInput from './components/ComplexInput.vue';
import RadioGroupList from './components/RadioGroupList.vue';
import { ScrollText } from "@/components/scrollText";
import DemoModal from './components/demo-modal.vue';
import FloatHistory from './components/FloatHistory.vue'
import { usePaperStore } from "@/store";
import { replaceText } from '@/plugins/directive';

const message = useMessage();

// 自己写的

const store = usePaperStore()

onMounted(async () => {
	store.getPaperStatistics();
	await store.getPaperTypeList() // 先获取论文类型列表并默认选中第一项
	await store.getTemplateDetail() // 然后获取模板详情
	await store.getLatestMessage()
})

const { preForm, latestMessage, paperStatistics } = storeToRefs(store)


const radioList = computed(() => {
	const fields = preForm.value?.formData || [];
	return fields.filter((item) => item.formType === 'radio');
});

const textAreaList = computed(() => {
	const fields = preForm.value?.formData || [];
	return fields.filter((item) => item.formType === 'textarea');
})

// 页面状态
const currentPage = ref('landing'); // 'landing' 或 'order'
const generateLoading = ref(false);
const paymentLoading = ref(false);
const demoModalVisible = ref(false);
const agreement = ref(true);

const demoModalTitle = computed(() => {
	return '示例-' + preForm.value?.name;
})

const handleGeneratePaper = () => {
	const { valid, message: msg } = store.checkPaperForm();
	if (!valid) {
		message.error(msg);
		return;
	}
	message.success('验证通过');
	store.createPaper()
}

function updateTextAreaValue(field: string, value: string): void {
	if (preForm.value?.formData) {
		const index = preForm.value.formData.findIndex(item => item.field === field);
		if (index !== -1) {
			preForm.value.formData[index].field = value;
		}
	}
}
</script>

<style lang="less" scoped>
.page {
	min-height: 100vh;
	max-height: 900px;
	background-color: #F7F9FF;
	overflow-y: auto;
}

.landing-container {
	margin: 20px 0 0 28px;
	display: flex;
	flex-wrap: wrap;
	height: calc(100% - 40px);

	.landing-left {
		width: 25%;
		max-width: 248px;
		height: 100%;
		overflow-y: auto;
	}

	.landing-right {
		flex: 1;
		display: flex;
		flex-direction: column;
		row-gap: 16px;
		height: 100%;
		overflow-y: auto;
		padding-right: 28px;
		padding-bottom: 20px;

		.content {
			max-width: 1200px;
			margin: 0 auto;
			padding: 16px;
		}

		.paper-type-buttons {
			display: flex;
			justify-content: center;
			margin-bottom: 16px;

			.paper-type-button {
				margin: 0 8px;
				min-width: 120px;
				border-radius: 20px;
			}
		}

		.form-container {
			background-color: #fff;
			border-radius: 10px;
			padding: 20px;
			box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

			.form-item {
				margin-bottom: 16px;

				.label {
					font-weight: 500;
					margin-bottom: 8px;
				}

				.options {
					:deep(.n-radio) {
						margin-right: 16px;
						margin-bottom: 8px;
					}
				}

				&.agreement {
					margin-top: 20px;
				}

				&.button {
					margin-top: 20px;
					text-align: center;

					.n-button {
						min-width: 180px;
					}
				}
			}
		}
	}
}

.order-container {
	width: 100%;

	.order-header {
		padding: 20px;
		display: flex;
		align-items: center;
		max-width: 1200px;
		margin: 0 auto;

		.back-button {
			cursor: pointer;
			display: flex;
			align-items: center;

			.icon-back {
				margin-right: 5px;
			}
		}

		.order-title {
			margin-left: 20px;
			font-size: 18px;
			font-weight: 500;
		}
	}

	.order-content {
		max-width: 1200px;
		margin: 0 auto;
		padding: 20px;

		.order-info {
			background-color: #fff;
			border-radius: 10px;
			padding: 30px;
			margin-bottom: 20px;
			box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

			.paper-info {
				margin-bottom: 20px;
				padding-bottom: 20px;
				border-bottom: 1px solid #eee;

				.paper-type,
				.paper-school,
				.paper-word-count {
					margin-bottom: 10px;
					color: #666;
				}

				.paper-title {
					font-weight: 500;
					font-size: 16px;
				}
			}

			.service-list {
				.service-item {
					display: flex;
					justify-content: space-between;
					margin-bottom: 15px;

					.service-name {
						color: #333;
					}

					.service-price {
						.original-price {
							text-decoration: line-through;
							color: #999;
							margin-right: 10px;
						}

						.current-price {
							color: #f56c6c;
							font-weight: 500;
						}
					}
				}
			}

			.total-section {
				margin-top: 20px;
				padding-top: 20px;
				border-top: 1px solid #eee;

				.total-power,
				.total-price {
					display: flex;
					justify-content: flex-end;
					margin-bottom: 10px;

					.power-value,
					.price-value {
						margin-left: 10px;
						font-weight: 500;
						color: #f56c6c;
					}
				}
			}
		}

		.payment-button {
			text-align: center;

			.n-button {
				min-width: 200px;
			}
		}
	}
}

.demo-images-container {
	width: 100%;

	.n-image {
		margin-bottom: 16px;
		width: 100%;

		&:last-child {
			margin-bottom: 0;
		}
	}
}
.textarea-form-item {
	.n-form-item-label {
		// Add your desired styles
		position: relative;
		top: 10px;
	}
}

/* 响应式样式 */
@media screen and (max-width: 1200px) {
	.landing-container {
		margin: 15px;
		height: calc(100% - 30px);

		.landing-right {
			row-gap: 12px;
			padding-right: 0;
		}
	}
}

/* 适配1366*768屏幕 */
@media screen and (max-width: 1366px) {
	.landing-container {
		margin: 15px 15px 15px 20px;

		.landing-left {
			max-width: 220px;
			margin-right: 30px;
		}

		.landing-right {
			padding-right: 15px;
			padding-left: 15px;

			.w-full.max-w-\[1180px\] {
				max-width: 1080px;
			}
		}
	}

	.page {
		max-height: 768px;
	}

	.textarea-form-item {
		.n-form-item-label {
			font-size: 14px;
		}
	}
}

@media screen and (max-width: 992px) {
	.landing-container {
		.landing-left {
			width: 30%;
		}
	}
}

@media screen and (max-width: 768px) {
	.landing-container {
		flex-direction: column;
		margin: 12px;
		height: calc(100% - 24px);

		.landing-left {
			width: 100%;
			max-width: 100%;
			margin-bottom: 12px;
			height: auto;
		}

		.landing-right {
			row-gap: 12px;
			max-height: calc(100% - 200px);

			.form-container {
				padding: 16px;
			}
		}
	}
}

@media screen and (max-width: 576px) {
	.landing-container {
		margin: 8px;
		height: calc(100% - 16px);

		.landing-right {
			row-gap: 8px;
			max-height: calc(100% - 180px);

			.form-container {
				padding: 12px;
			}
		}
	}
}
</style>