
export interface SearchData {
  id: number;
  name: string;
  type: string;
  index: number;
  parentId: number;
  status: number;
  icon: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: null;
  Createbots: Createbot[];
}

export interface Createbot {
	id: number | null;
	name: string;
	profile: string;
	href: null;
	welcome_message: string;
	hot_num: number;
	type: string;
	isHot: boolean;
	free: boolean;
	createdAt: string;
	updatedAt: string;
	categoryId?: string | number | null;
	CategoryCreatebot: CategoryCreatebot;
}

interface CategoryCreatebot {
  categoryId: number;
  createbotId: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: null;
}