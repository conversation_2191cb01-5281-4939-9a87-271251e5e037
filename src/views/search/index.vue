<script setup lang="ts">
import Logo from '@/assets/images/logo1.png'
import { NImage } from 'naive-ui';
import { useRoute } from 'vue-router';
import PaperAirplaneInput from '@/components/common/PaperAirplaneInput/index.vue'
import Card from '@/components/common/Card/index.vue'
import { useRequest } from 'vue-hooks-plus';
import { searchAppsWithKeywordsOrCategoryId } from './apis';
import { ref, onMounted } from 'vue';
import { Createbot, SearchData } from './types';
import { formatChild } from '@/utils/utils';
import { router } from '@/router';
import useSkeletonCardWidth from '@/hooks/useSkeletonCardWidth';

const route = useRoute()
const { keywords = "" } = route.query
const app_list = ref<Createbot[]>([])

const { run } = useRequest<Createbot[]>(searchAppsWithKeywordsOrCategoryId, {
    manual: true,
    onSuccess: (data) => {
        app_list.value = data;
    }
})
const { width, setWidth } = useSkeletonCardWidth();

onMounted(() => {
    run({ keywords, type: 'createbots' })
})

const handleChange = (value: string) => {
    run({ keywords: value, type: 'createbots' })
    router.push({ query: { keywords: value } })
}

</script>
<template>
    <div
        class=" w-full h-[100%] overflow-y-scroll bg-gradient-to-b from-[#f5f7ff] via-[rgba(236, 241, 255, 0.8858)] to-[#e5f1ff] pt-[110px] flex flex-col items-center">
        <section class="3xl:w-[1470px] 2xl:w-[1270px] xl:w-[980px] lg:w-[724px] md:w-[568px] mx-[25px]">
            <!-- 顶部logo+搜索 -->
            <section
                class=" 3xl:w-[1470px] 2xl:w-[1270px] xl:w-[980px] lg:w-[724px] md:w-[568px] flex flex-col gap-y-[22px] w-full items-center justify-center">
                <NImage :src="Logo" class=" w-[310px] " />
                <PaperAirplaneInput placeholder="请输入" @submit="handleChange" :value="keywords" />
            </section>
            <!-- 全部应用 -->
            <section class=" flex flex-col items-start w-full mt-[54px]">
                <div class=" text-[24px] text-[#3d3d3d] leading-[32px] font-bold mb-[30px]">全部应用</div>
                <div class=" mt-[24px] mb-[24px] flex flex-row flex-wrap gap-x-[24px] gap-y-[24px] w-full">
                    <Card v-for="item in app_list" :key="(item.id as number)" :title="item.name"
                        :description="item.welcome_message" :img-src="item.profile" :id="item.id" :type="item.type"
                        :category-id="item.categoryId" :free="item.free" :width="width" :href="item.href" />
                </div>
            </section>
        </section>
    </div>
</template>
<style lang="less" scoped></style>