<script lang="ts" setup>
import { NForm, NFormItem, NInput, FormInst, NSpace, NButton } from 'naive-ui'
import { computed, onMounted, ref } from 'vue';
import { fetchBindPay } from '../api';
interface Props {
    model: {
        nickname: string,
        phone: string,
        openId: string
    }
}
interface Emit {
    (e: 'bind-phone'): void;
    (e: 'bind-openId'): void;
}
const props = defineProps<Props>()
const emit = defineEmits<Emit>()
const formRef = ref<FormInst | null>(null)
const model = ref({
    nickname: '',
    phone: '',
    openId: ''
})
const orderNo = ref("")
const wechatBind = computed(() => {
    return !!model.value.openId
})
const phoneBind = computed(() => {
    return !!model.value.phone
})
onMounted(() => {
    model.value = props.model
})
const handleBindPhone = () => {
    emit('bind-phone')
}
const handleBindWechat = () => {
    emit('bind-openId')
}
const handleBindPay = () => {
	if(!orderNo.value) return  window.$notification?.error({content: "请输入支付单号", duration: 1500})
	fetchBindPay({payOrderNo: orderNo.value})
		.then(res => {
			window.$notification?.success({content: "绑定成功", duration: 1500, onClose: () => window.location.reload()})
		})
}
</script>

<template>

    <div class=" flex flex-col py-[30px] px-[30px]">
        <span class=" text-[16px] text-[#3d3d3d] leading-[21px] mb-[20px]">基础信息</span>
        <div class="divider mb-[20px]" :data-nickname="model.nickname"> </div>

        <NForm ref="formRef" label-placement="left" label-width="100" class=" max-w-[50%]">
            <NFormItem label="昵称">
                <span>{{ model.nickname }}</span>
            </NFormItem>
            <NFormItem label="手机号">
                <span v-if="phoneBind">{{ model.phone }}</span>
                <span v-else @click="handleBindPhone"
                    class=" inline-block w-[80px] cursor-pointer text-[#0E69FF] text-[14px]">去绑定
                    ></span>
            </NFormItem>
            <NFormItem label="微信号">
                <span v-if="wechatBind">已绑定</span>
                <span v-else @click="handleBindWechat"
                    class=" inline-block w-[80px] cursor-pointer text-[#0E69FF] text-[14px]">去绑定
                    ></span>
            </NFormItem>
						<NFormItem label="绑定订单号">
							<NSpace>
								<NInput v-model:value="orderNo" placeholder="请输入支付单号" />
								<NButton type="primary" @click="handleBindPay" :disabled="!orderNo">绑定</NButton>
							</NSpace>
            </NFormItem>
        </NForm>
    </div>

</template>

<style lang="less" scoped>
.divider {
    width: 100%;
    height: 1px;
    opacity: 1;
    background: #e5e5e5;
}
</style>
