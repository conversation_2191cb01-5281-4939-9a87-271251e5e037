<script lang="ts" setup>
import { ref } from 'vue';
import BindAccount from '@/views/components/BindAccount.vue'
import BindWechat from '@/views/components/BindWechat.vue'
import BaseInfo from './BaseInfo.vue';
import UseRecord from './UseRecord.vue';
import { Member } from '@/components/common'
interface User {
    nickname: string;
    phone: string;
    openId: string;
}
interface Props {
    user: User
}
interface Emit {
    (e: 'bind-success'): void
}

const props = defineProps<Props>();
const emit = defineEmits<Emit>();
const bindPhone = ref(false);
const bindWechat = ref(false);
const activeKey = ref(1);

const handleSwitchTab = (key: number) => {
    activeKey.value = key;
}
const handleLogin = () => {
    bindPhone.value = false;
}
const handleBindPhone = () => {
    bindPhone.value = true;
}
const handleBindWechat = () => {
    bindWechat.value = true;
}
const changeFn = (res) => {
    bindWechat.value = false;
    console.log('res: ', res);
}
</script>

<template>
    <div class=" w-full">

        <div class="flex flex-row mb-[20px] mt-[40px]">
            <div class="block rounded-tl-[4px] rounded-bl-[4px] cursor-pointer"
                :class="activeKey === 1 ? 'active-block' : ''" @click="handleSwitchTab(1)">
                基础信息
            </div>
            <!-- <div class="block rounded-tr-[4px] rounded-br-[4px] cursor-pointer"
                :class="activeKey === 2 ? 'active-block' : ''" @click="handleSwitchTab(2)">
                使用记录
            </div> -->
        </div>
        <div class="w-full min-h-[272px] bg-[#fff] rounded-[4px]">
            <BaseInfo v-if="activeKey === 1" :model="user" @bind-phone="handleBindPhone"
                @bind-open-id="handleBindWechat" />
            <UseRecord v-else />
        </div>
        <BindAccount v-model:visible="bindPhone" @login="handleLogin" @bind-success="() => emit('bind-success')" />
        <BindWechat v-model:visible="bindWechat" @login="handleLogin" />
        <!-- <Member v-model:visible="bindWechat" @change-fn="changeFn" /> -->
    </div>
</template>

<style lang="less" scoped>
.block {
    width: 120px;
    height: 40px;
    line-height: 40px;
    opacity: 1;
    color: #3D3D3D;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
}

.active-block {
    background: #0E69FF;
    color: #ffffff;
}
</style>
