<script lang="ts" setup>
import { ref } from 'vue';
import { NImage } from 'naive-ui';
import { Member } from '../types';
import dayjs from 'dayjs';
import customParseFormat from "dayjs/plugin/customParseFormat";

import { replaceText } from '@/plugins/directive';
import ActiveVip from '@/assets/images/active-vip.png';

dayjs.extend(customParseFormat);
interface Props {
    members: Partial<Member>[]
		hidePaper?: boolean
}
interface Emit {
    (ev: 'update-select-member', item: Member): void
}
const props = defineProps<Props>();
const emit = defineEmits<Emit>();

const activeKey = ref(0);

const handleSwitchTab = (key: number) => {
    activeKey.value = key;
    emit('update-select-member', props.members[key]);
}

</script>

<template>
    <div class=" flex flex-row justify-between three-tabs gap-1">
        <div class="flex-1 h-[50px] relative rounded-tl-[10px] rounded-tr-[10px] left flex items-center justify-start gap-x-[4px] cursor-pointer"
            :class="activeKey === 0 ? 'active bg-[#0E69FF]' : 'bg-[#E1ECFF]'" @click="handleSwitchTab(0)"
            :data-color="activeKey === 0 ? '#0E69FF' : '#E1ECFF'">
            <span class=" text-[16px] pl-[15%] truncate " :class="activeKey === 0 ? 'tab-text-active' : 'tab-text'">{{
                members[0].tab
                }}</span>
            <NImage :src="ActiveVip" width="14" height="14" preview-disabled review-disabled>
            </NImage>
            <span v-if="members?.[0]?.isMember" class=" text-[10px] pl-[9px] truncate "
                :class="activeKey === 0 ? 'tab-text-active' : 'tab-text'">{{
                members[0].permanent ? '' :
                dayjs(members[0].endDate).format('YYYY.MM.DD') + '到期' }}</span>
        </div>
        <div v-if="!props.hidePaper" class="flex-1 h-[50px] relative rounded-tl-[10px] rounded-tr-[10px] center flex items-center justify-start gap-x-[4px] cursor-pointer pl-[27px]"
            :class="activeKey === 1 ? ' active bg-[#0E69FF]' : 'bg-[#E1ECFF]'" @click="handleSwitchTab(1)">
            <span class=" text-[16px] truncate " :class="activeKey === 1 ? 'tab-text-active' : 'tab-text'">{{replaceText(members[1].tab)}}</span>
            <NImage :src="ActiveVip" width="14" height="14" preview-disabled review-disabled>
            </NImage>
            <span v-if="members?.[1]?.isMember" class=" text-[10px] pl-[9px] truncate "
                :class="activeKey === 1 ? 'tab-text-active' : 'tab-text'">{{
                members[1].permanent ? '' :
                dayjs(members[1].endDate).format('YYYY.MM.DD') + '到期' }}</span>
        </div>
        <div class="flex-1 h-[50px] relative rounded-tl-[10px] rounded-tr-[10px] right flex items-center justify-start gap-x-[4px] cursor-pointer pl-[27px]"
            :class="activeKey === 2 ? 'active bg-[#0E69FF]' : 'bg-[#E1ECFF]'" @click="handleSwitchTab(2)">
            <span class=" text-[16px] truncate " :class="activeKey === 2 ? 'tab-text-active' : 'tab-text'">{{
                members[2].tab
                }}</span>
            <NImage :src="ActiveVip" width="14" height="14" preview-disabled review-disabled>
            </NImage>
            <span v-if="members?.[2]?.isMember" class=" text-[10px] pl-[9px] truncate "
                :class="activeKey === 2 ? 'tab-text-active' : 'tab-text'">{{
                members[2].permanent ? '' :
                dayjs(members[2].endDate).format('YYYY.MM.DD') + '到期' }}</span>
        </div>
				<div class="flex-1 h-[50px] relative rounded-tl-[10px] rounded-tr-[10px] right flex items-center justify-start gap-x-[4px] cursor-pointer pl-[27px]"
            :class="activeKey === 3 ? 'active bg-[#0E69FF]' : 'bg-[#E1ECFF]'" @click="handleSwitchTab(3)">
            <span class=" text-[16px] truncate " :class="activeKey === 3 ? 'tab-text-active' : 'tab-text'">{{
                members[3].tab
                }}</span>
            <NImage :src="ActiveVip" width="14" height="14" preview-disabled review-disabled>
            </NImage>
            <span v-if="members?.[3]?.isMember" class=" text-[10px] pl-[9px] truncate "
                :class="activeKey === 3 ? 'tab-text-active' : 'tab-text'">{{
                members[3].permanent ? '' :
                dayjs(members[3].endDate).format('YYYY.MM.DD') + '到期' }}</span>
        </div>
    </div>

</template>

<style lang="less" scoped>
.left {
    &::after {
        content: " ";
        position: absolute;
        bottom: 0;
        right: -12px;
        border-width: 0 13px 45px 0;
        border-style: solid;
        border-color: transparent transparent #E1ECFF transparent;
    }
}

.left.active {
    &::after {
        border-color: transparent transparent #0E69FF transparent;
    }
}

.center {
    &::before {
        content: " ";
        position: absolute;
        bottom: 0;
        left: -12px;
        border-width: 45px 13px 0 0;
        border-style: solid;
        border-color: transparent #E1ECFF transparent transparent;
    }

    &::after {
        content: " ";
        position: absolute;
        bottom: 0;
        right: -12px;
        border-width: 0 13px 45px 0;
        border-style: solid;
        border-color: transparent transparent #E1ECFF transparent;
    }
}

.center.active {
    &::before {

        border-color: transparent #0E69FF transparent transparent;
    }

    &::after {

        border-color: transparent transparent #0E69FF transparent;
    }
}

.right {
    &::before {
        content: " ";
        position: absolute;
        bottom: 0;
        left: -12px;
        border-width: 45px 13px 0 0;
        border-style: solid;
        border-color: transparent #E1ECFF transparent transparent;
    }
}

.right.active {
    &::before {
        border-color: transparent #0E69FF transparent transparent;
    }
}

.tab-text-active {
    color: #ffffff;
}

.tab-text {
    color: #3D3D3D;
}
</style>
