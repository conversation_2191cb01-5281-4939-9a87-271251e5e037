<script lang="ts" setup>
import { NDataTable } from 'naive-ui';
import { ref } from 'vue';
const columns = ref([
    {
        title: '时间',
        key: 'time'
    },
    {
        title: '变动原因',
        'key': 'reason'
    },
    {
        title: '变动信息',
        key: 'info'
    }
])
</script>

<template>
    <div class=" flex flex-col py-[30px] px-[30px]">
        <span class=" text-[16px] text-[#3d3d3d] leading-[21px] mb-[20px]">使用记录</span>
        <NDataTable :columns="columns" :bordered="true"/>
    </div>
</template>