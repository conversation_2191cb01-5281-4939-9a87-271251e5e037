<script lang="ts" setup>
import { NEllipsis, NImage } from "naive-ui";
import { Member } from "../types";
import Crown2 from "@/assets/images/crown2.png";
import MemberSvg from "@/assets/aiwork/svg/member.svg";
import { useRoute } from "vue-router";
import { replaceText } from '@/plugins/directive';

const route = useRoute();

interface Props {
	member?: Partial<Member> | undefined;
}
const props = defineProps<Props>();

const handleBecomeMember = () => {
	// @ts-ignore
	window.$aiwork
		?.openRecharge?.({ type: "ai", categoryId: props.member?.categoryId })
		.then(() => {
			window.location.reload();
		})
		.catch(() => {});
};
</script>

<template>
	<div class="w-full min-h-[130px] bg-[#F5F9FF] rounded-bl-[10px] rounded-br-[10px] text-[#3D3D3D]">
		<template v-if="member?.isMember">
			<div class="w-full flex flex-row flex-wrap p-[35px] gap-y-[30px] gap-x-[30px]">
				<div v-for="item in member.content"
					class="flex flex-row flex-nowrap w-[29%] items-center justify-start">
					<NImage :src="Crown2" preview-disabled class="w-[18px] h-[18px] block" />
					<NEllipsis class="flex-1 ml-[14px]">{{ replaceText(item) }}</NEllipsis>
					<!-- <span class="flex-1 truncate ml-[14px]">{{ item }}</span> -->
				</div>
			</div>
		</template>
		<template v-else>
			<div class="pt-8">
				<div class="w-full h-full flex flex-col items-center justify-center gap-y-[14px]">
					<div class="flex flex-row items-center justify-center text-[#AFAFAF] gap-x-[9px]">
						<NImage :src="Crown2" preview-disabled class="w-[18px] h-[18px]" />
						<span>暂无会员权益</span>
					</div>
					<div class="member-btn" @click="handleBecomeMember">
						<MemberSvg class="w-[16px] h-[16px]" />
						成为会员
					</div>
				</div>
			</div>
		</template>
	</div>
</template>

<style lang="less" scoped>
.member-btn {
	width: 110px;
	height: 36px;
	box-sizing: border-box;
	background: linear-gradient(
		var(--login-btn-bg-deg),
		#2079ff 0%,
		#af53ff 100%
	);
	border-radius: 4px;
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 7px;
	font-size: 14px;
	color: #ffffff;
	cursor: pointer;

	transition: --login-btn-bg-deg 0.3s ease-in-out;

	&:hover {
		--login-btn-bg-deg: -90deg;
	}
}
</style>
