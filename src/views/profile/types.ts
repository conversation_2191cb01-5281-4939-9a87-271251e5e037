/**
 *  100001 文字会员
    200001 论文算力包
    400001 ppt会员
 */
export enum MemberEnum {
	TEXT = 100001,
	PAPER = 200001,
	PPT = 400001,
	MJ = 300001,
}

export const MemberType = {
	[MemberEnum.TEXT]: "文字会员",
	[MemberEnum.PAPER]: "论文算力包",
	[MemberEnum.PPT]: "PPT会员",
	[MemberEnum.MJ]: "AI绘图",
} as const;
export const CategoryType = {
	[MemberEnum.TEXT]: 1,
	[MemberEnum.PAPER]: 3,
	[MemberEnum.PPT]: 2,
	[MemberEnum.MJ]: 4,
} as const;

export interface Member {
	isMember: boolean;
	tab?: string;
	code: number; // 会员类型编码
	name: string; // 姓名
	endDate: string; // 结束日期
	permanent: boolean; // 是否永久
	limitCount: boolean; // 是否限制数量
	use: number; // 使用数量
	count: number; // 总数量
	description: null | string; // 描述
	child: MemberChild[]; // 子成员列表
	content?: string[];
	categoryId?: number;
}

interface MemberChild {
	id: number; // ID
	userId: number; // 用户ID
	orgUserId: number; // 组织用户ID
	name: string; // 姓名
	code: number; // 编码
	level: number; // 等级
	permanent: boolean; // 是否永久
	count: number; // 数量
	limitCount: boolean; // 是否限制数量
	limitUnit: string; // 限制单位
	use: number; // 使用数量
	endDate: string; // 结束日期
	type: string; // 类型
	description: string; // 描述
	offset: null; // 偏移量
	source: null; // 来源
	isMain: boolean; // 是否主要
	createdAt: string; // 创建时间
	updatedAt: string; // 更新时间
	deletedAt: null; // 删除时间
}
