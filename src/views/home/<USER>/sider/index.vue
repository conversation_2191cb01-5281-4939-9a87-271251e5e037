<script setup lang="ts">
import { NImage, NLayoutSider, NMenu, NIcon } from 'naive-ui';
import { useAppStore } from '@/store'
import type { MenuInst, MenuOption } from 'naive-ui'
import { computed, h, nextTick, onMounted, ref } from 'vue';
import Writing from '@/assets/images/writing.png';
import WritingACtive from '@/assets/images/writing-active.png';
import Chat from '@/assets/images/chat.png';
import ChatActive from '@/assets/images/chat-active.png';
import Ppt from '@/assets/images/AIppt.png';
import PptActive from '@/assets/images/ppt-active.png';
import Draw from '@/assets/images/draw.png';
import DrawActive from '@/assets/images/draw-active.png';
import Comic from '@/assets/images/comic.png';
import ComicActive from '@/assets/images/comic-active.png';
import { renderIcon } from '@/utils/utils';
import { useRouter, RouterLink } from 'vue-router';
import { AiworkType } from '@/typings';
import { getUser } from '@/store/modules/auth/helper';
import HistoryImg from '@/assets/History.png';
import CollectionImg from '@/assets/collection.png';
import { replaceText } from '@/plugins/directive';

const appStore = useAppStore()
const router = useRouter()
const user = getUser();
const collapsed = computed(() => appStore.siderCollapsed)
const selectedKey = ref('writing')
const isWriting = computed(() => selectedKey.value === 'writing' || selectedKey.value === 'academic' || selectedKey.value === 'summary' || selectedKey.value === 'plan' || selectedKey.value === 'novel')
const isChat = computed(() => selectedKey.value === 'chat')
const isPPt = computed(() => selectedKey.value === 'ppt')
const isDraw = computed(() => selectedKey.value === 'draw')
const isComic = computed(() => selectedKey.value === 'comic')
const menuOption: MenuOption[] = [
    {

        title: 'AI 写作',
        key: 'writing',
        icon: isWriting ? renderIcon(WritingACtive) : renderIcon(Writing),
        children: [
            {
                label: () =>
                    h(
                        RouterLink,
                        {
                            to: {
                                name: 'Paper',
                                query: {
                                    id: '1'
                                }
                            },

                        },
                        // { default: () => '学术长文' }
												{default: () => replaceText('论文助手')}
                    ),
                key: 'academic',
            },
            {
                label: () =>
                    h(
                        RouterLink,
                        {
                            to: {
                                name: 'AppDetail',
                                params: {
                                    id: '78'
                                },
                                query: {
                                    appid: '409'
                                }
                            },
                        },
                        { default: () => '总结报告' }
                    ),
                key: 'summary'
            },
            {
                label: () =>
                    h(
                        RouterLink,
                        {
                            // http://localhost:1025/apps/92?appid=530&type=long
                            to: {
                                name: 'AppDetail',
                                params: {
                                    id: '84'
                                },
                                query: {
                                    appid: '530',
                                    type: 'long'
                                }
                            },
                        },
                        { default: () => '方案策划' }
                    ),
                key: 'plan'
            },
            {
                label: () =>
                    h(
                        RouterLink,
                        {
                            // http://localhost:1025/apps/79?appid=2
                            to: {
                                name: 'AppDetail',
                                params: {
                                    id: '79'
                                },
                                query: {
                                    appid: '2'
                                }
                            },
                        },
                        { default: () => '小说创作' }
                    ),
                key: 'novel'
            },
            // {
            //     label: () =>
            //         h(
            //             RouterLink,
            //             {
            //                 to: {
            //                     name: 'Apps',
            //                 }
            //             },
            //             { default: () => '更多应用' }
            //         ),
            //     key: 'more'
            // }
        ]
    },
    {
        key: 'chat',
        icon: isChat ? renderIcon(Chat) : renderIcon(ChatActive),
        label: () =>
            h(
                RouterLink,
                {
                    to: {
                        name: 'Chat',
                        params: {
                            uuid: '1002'
                        },
                        query: {
                            deepseek: 'true'
                        }
                    },
                },
                { default: () => 'AI 聊天' }
            ),
    },
    {
        key: 'ppt',
        icon: isPPt ? renderIcon(Ppt) : renderIcon(PptActive),
        label: () =>
            h(
                RouterLink,
                {
                    to: {
                        name: 'PPT',
                    },
                    params: {
                        uuid: 1002
                    }
                },
                { default: () => 'AI PPT' }
            ),
    },
		{
			key: 'draw',
			icon: isDraw ? renderIcon(Draw) : renderIcon(DrawActive),
			label: () =>
				h(
					RouterLink,
					{
						to: {
							name: 'square',
						},
					},
					{ default: () => 'AI 绘图' }
				),
		}
    // {
    //     title: () => renderDrawingUpdate(),
    //     key: 'draw',
    //     icon: isDraw ? renderIcon(Draw) : renderIcon(DrawActive)
    // },
    // {
    //     title: () => renderComicUpdate(),
    //     key: 'comic',
    //     icon: isComic ? renderIcon(Comic) : renderIcon(ComicActive)
    // }
]
const handleGotoCollection = () => {
    return new Promise((resolve, reject) => {
        const u = getUser();
        if (u.type === 'temp') {
            (window.$aiwork as AiworkType).openLogin?.()
            return reject({ message: "请先登录" });
        }
        router.push({ name: 'Collection' })
    })
}
const handleGotoHistory = () => {
    return new Promise((resolve, reject) => {
        const u = getUser();
        if (u.type === 'temp') {
            (window.$aiwork as AiworkType).openLogin?.()
            return reject({ message: "请先登录" });
        }
        router.push({ name: 'History' })
    })
}
</script>
<template>
    <NLayoutSider bordered :collapsed="collapsed" :width="250" :collapsed-width="0" collapse-mode="width"
        class=" flex flex-col" style=" display: flex; flex-direction: column;">
        <div class=" flex-1 overflow-y-scroll HideScrollbar">
            <NMenu :options="menuOption" class="mt-[80px]" :default-expand-all="true" :value="selectedKey" />
        </div>
        <div class=" h-[100px]">
            <div class=" w-[calc(100%-34px)] ml-[17px] h-[1px] bg-[#DCDCDC]"></div>
            <div class=" w-[calc(100%-34px)] ml-[17px] h-[42px] pl-[16px] flex flex-row text-[16px] text-[#3D3D3D] items-center justify-start cursor-pointer"
                @click="handleGotoCollection">
                <NImage :src="CollectionImg" width="26px" height="26px" preview-disabled />
                <span class=" pl-[10px]">收藏夹</span>
            </div>
            <div class=" w-[calc(100%-34px)] ml-[17px] h-[42px] pl-[16px] flex flex-row text-[16px] text-[#3D3D3D] items-center justify-start cursor-pointer"
                @click="handleGotoHistory">
                <NImage :src="HistoryImg" width="26px" height="26px" preview-disabled />
                <span class=" pl-[10px]">创作历史</span>
            </div>
        </div>
    </NLayoutSider>
</template>
<style lang="less" scoped>
:deep(.n-layout-sider-scroll-container) {
    display: flex;
    flex-direction: column;
}
</style>
