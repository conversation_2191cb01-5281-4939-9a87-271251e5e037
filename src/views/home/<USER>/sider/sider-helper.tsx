export const renderDrawingUpdate = () => {
	return (
		<div class="flex justify-start items-center h-full">
			AI 绘图
			<svg
				xmlns="http://www.w3.org/2000/svg"
				xmlns:xlink="http://www.w3.org/1999/xlink"
				fill="none"
				version="1.1"
				width="32"
				height="26"
				viewBox="0 0 32 26"
			>
				<defs>
					<linearGradient
						x1="1"
						y1="0.5"
						x2="0"
						y2="0.5"
						id="master_svg0_2_0532"
					>
						<stop offset="0%" stop-color="#1076FE" stop-opacity="1" />
						<stop
							offset="97.14285731315613%"
							stop-color="#02AFFF"
							stop-opacity="1"
						/>
					</linearGradient>
				</defs>
				<g>
					<g>
						<path
							d="M0,12L26,12C29.3137,12,32,9.31371,32,6C32,2.68629,29.3137,0,26,0L7.04131,0C3.9209,0,1.3913,2.52959,1.3913,5.65L1.3913,6L1.3913,8L1.3913,10Q1.3913,11,0,12Z"
							fill="url(#master_svg0_2_0532)"
							fill-opacity="1"
						/>
					</g>
					<g>
						<rect
							x="0"
							y="13"
							width="23"
							height="13"
							rx="0"
							fill="#D8D8D8"
							fill-opacity="0"
						/>
					</g>
					<g>
						<path
							d="M7.064,8.120000000000001L6.416,8.384C6.68,8.8,6.992,9.144,7.343999999999999,9.424C6.872,9.656,6.224,9.856,5.344,10.008C5.5120000000000005,10.184,5.72,10.512,5.808,10.68075C6.8,10.472,7.5280000000000005,10.2,8.064,9.864C9.192,10.416,10.664,10.56,12.472000000000001,10.624C12.52,10.376,12.655999999999999,10.048,12.792,9.88C11.08,9.856,9.719999999999999,9.768,8.68,9.368C9.048,8.992,9.248000000000001,8.559999999999999,9.36,8.104L12,8.104L12,4.912L9.456,4.912L9.456,4.327999999999999L12.504,4.327999999999999L12.504,3.648L5.504,3.648L5.504,4.327999999999999L8.664,4.327999999999999L8.664,4.912L6.216,4.912L6.216,8.104L8.544,8.104C8.448,8.432,8.28,8.736,7.976,9.008C7.6240000000000006,8.776,7.32,8.488,7.064,8.120000000000001ZM6.936,6.792L8.664,6.792L8.664,7.088L8.648,7.48L6.936,7.48L6.936,6.792ZM9.448,7.48L9.456,7.096L9.456,6.792L11.248000000000001,6.792L11.248000000000001,7.48L9.448,7.48ZM6.936,5.536L8.664,5.536L8.664,6.208L6.936,6.208L6.936,5.536ZM9.456,5.536L11.248000000000001,5.536L11.248000000000001,6.208L9.456,6.208L9.456,5.536ZM15.616,8.368C15.856,8.76,16.136,9.288,16.264,9.624L16.784,9.312C16.656,8.984,16.375999999999998,8.48,16.119999999999997,8.096L15.616,8.368ZM13.768,8.152000000000001C13.608,8.616,13.352,9.096,13.04,9.432C13.184,9.52,13.432,9.696,13.544,9.8C13.856,9.432,14.176,8.847999999999999,14.36,8.304L13.768,8.152000000000001ZM17.168,4.01587L17.168,6.8C17.168,7.848,17.112000000000002,9.2,16.472,10.136C16.631999999999998,10.216,16.928,10.448,17.048000000000002,10.59275C17.768,9.56,17.872,7.96,17.872,6.8L17.872,6.624L18.904,6.624L18.904,10.63313L19.64,10.63313L19.64,6.624L20.456,6.624L20.456,5.92L17.872,5.92L17.872,4.5120000000000005C18.688000000000002,4.3759999999999994,19.567999999999998,4.176,20.240000000000002,3.9195L19.64,3.3575C19.064,3.6145,18.055999999999997,3.86337,17.168,4.01587ZM14.408,3.37362C14.512,3.58237,14.616,3.83125,14.704,4.064L13.224,4.064L13.224,4.688000000000001L16.784,4.688000000000001L16.784,4.064L15.472,4.064C15.376,3.79912,15.224,3.46987,15.088,3.205L14.408,3.37362ZM15.688,4.696C15.6,5.04,15.432,5.528,15.288,5.872L14.168,5.872L14.624,5.752C14.592,5.464,14.464,5.032,14.304,4.712L13.696,4.856C13.84,5.176,13.944,5.592,13.976,5.872L13.096,5.872L13.096,6.504L14.696,6.504L14.696,7.24L13.136,7.24L13.136,7.888L14.696,7.888L14.696,9.784C14.696,9.864,14.672,9.888,14.584,9.888C14.496,9.896,14.248,9.896,13.984,9.888C14.08,10.064,14.176,10.336,14.2,10.52C14.608,10.52,14.904,10.504,15.112,10.4C15.32,10.296,15.376,10.12,15.376,9.8L15.376,7.888L16.8,7.888L16.8,7.24L15.376,7.24L15.376,6.504L16.912,6.504L16.912,5.872L15.968,5.872C16.104,5.568,16.247999999999998,5.192,16.384,4.84L15.688,4.696ZM24.104,3.24475L24.104,4.656000000000001L21.264,4.656000000000001L21.264,8.57675L22.016,8.57675L22.016,8.096L24.104,8.096L24.104,10.66763L24.896,10.66763L24.896,8.096L26.992,8.096L26.992,8.53662L27.776,8.53662L27.776,4.656000000000001L24.896,4.656000000000001L24.896,3.24475L24.104,3.24475ZM22.016,7.352L22.016,5.4L24.104,5.4L24.104,7.352L22.016,7.352ZM26.992,7.352L24.896,7.352L24.896,5.4L26.992,5.4L26.992,7.352Z"
							fill="#FFFFFF"
							fill-opacity="1"
						/>
					</g>
				</g>
			</svg>
		</div>
	);
};
export const renderComicUpdate = () => {
	return (
		<div class="flex justify-start items-center h-full">
			AI 漫剪
			<svg
				xmlns="http://www.w3.org/2000/svg"
				xmlns:xlink="http://www.w3.org/1999/xlink"
				fill="none"
				version="1.1"
				width="32"
				height="26"
				viewBox="0 0 32 26"
			>
				<defs>
					<linearGradient
						x1="1"
						y1="0.5"
						x2="0"
						y2="0.5"
						id="master_svg0_2_0532"
					>
						<stop offset="0%" stop-color="#1076FE" stop-opacity="1" />
						<stop
							offset="97.14285731315613%"
							stop-color="#02AFFF"
							stop-opacity="1"
						/>
					</linearGradient>
				</defs>
				<g>
					<g>
						<path
							d="M0,12L26,12C29.3137,12,32,9.31371,32,6C32,2.68629,29.3137,0,26,0L7.04131,0C3.9209,0,1.3913,2.52959,1.3913,5.65L1.3913,6L1.3913,8L1.3913,10Q1.3913,11,0,12Z"
							fill="url(#master_svg0_2_0532)"
							fill-opacity="1"
						/>
					</g>
					<g>
						<rect
							x="0"
							y="13"
							width="23"
							height="13"
							rx="0"
							fill="#D8D8D8"
							fill-opacity="0"
						/>
					</g>
					<g>
						<path
							d="M7.064,8.120000000000001L6.416,8.384C6.68,8.8,6.992,9.144,7.343999999999999,9.424C6.872,9.656,6.224,9.856,5.344,10.008C5.5120000000000005,10.184,5.72,10.512,5.808,10.68075C6.8,10.472,7.5280000000000005,10.2,8.064,9.864C9.192,10.416,10.664,10.56,12.472000000000001,10.624C12.52,10.376,12.655999999999999,10.048,12.792,9.88C11.08,9.856,9.719999999999999,9.768,8.68,9.368C9.048,8.992,9.248000000000001,8.559999999999999,9.36,8.104L12,8.104L12,4.912L9.456,4.912L9.456,4.327999999999999L12.504,4.327999999999999L12.504,3.648L5.504,3.648L5.504,4.327999999999999L8.664,4.327999999999999L8.664,4.912L6.216,4.912L6.216,8.104L8.544,8.104C8.448,8.432,8.28,8.736,7.976,9.008C7.6240000000000006,8.776,7.32,8.488,7.064,8.120000000000001ZM6.936,6.792L8.664,6.792L8.664,7.088L8.648,7.48L6.936,7.48L6.936,6.792ZM9.448,7.48L9.456,7.096L9.456,6.792L11.248000000000001,6.792L11.248000000000001,7.48L9.448,7.48ZM6.936,5.536L8.664,5.536L8.664,6.208L6.936,6.208L6.936,5.536ZM9.456,5.536L11.248000000000001,5.536L11.248000000000001,6.208L9.456,6.208L9.456,5.536ZM15.616,8.368C15.856,8.76,16.136,9.288,16.264,9.624L16.784,9.312C16.656,8.984,16.375999999999998,8.48,16.119999999999997,8.096L15.616,8.368ZM13.768,8.152000000000001C13.608,8.616,13.352,9.096,13.04,9.432C13.184,9.52,13.432,9.696,13.544,9.8C13.856,9.432,14.176,8.847999999999999,14.36,8.304L13.768,8.152000000000001ZM17.168,4.01587L17.168,6.8C17.168,7.848,17.112000000000002,9.2,16.472,10.136C16.631999999999998,10.216,16.928,10.448,17.048000000000002,10.59275C17.768,9.56,17.872,7.96,17.872,6.8L17.872,6.624L18.904,6.624L18.904,10.63313L19.64,10.63313L19.64,6.624L20.456,6.624L20.456,5.92L17.872,5.92L17.872,4.5120000000000005C18.688000000000002,4.3759999999999994,19.567999999999998,4.176,20.240000000000002,3.9195L19.64,3.3575C19.064,3.6145,18.055999999999997,3.86337,17.168,4.01587ZM14.408,3.37362C14.512,3.58237,14.616,3.83125,14.704,4.064L13.224,4.064L13.224,4.688000000000001L16.784,4.688000000000001L16.784,4.064L15.472,4.064C15.376,3.79912,15.224,3.46987,15.088,3.205L14.408,3.37362ZM15.688,4.696C15.6,5.04,15.432,5.528,15.288,5.872L14.168,5.872L14.624,5.752C14.592,5.464,14.464,5.032,14.304,4.712L13.696,4.856C13.84,5.176,13.944,5.592,13.976,5.872L13.096,5.872L13.096,6.504L14.696,6.504L14.696,7.24L13.136,7.24L13.136,7.888L14.696,7.888L14.696,9.784C14.696,9.864,14.672,9.888,14.584,9.888C14.496,9.896,14.248,9.896,13.984,9.888C14.08,10.064,14.176,10.336,14.2,10.52C14.608,10.52,14.904,10.504,15.112,10.4C15.32,10.296,15.376,10.12,15.376,9.8L15.376,7.888L16.8,7.888L16.8,7.24L15.376,7.24L15.376,6.504L16.912,6.504L16.912,5.872L15.968,5.872C16.104,5.568,16.247999999999998,5.192,16.384,4.84L15.688,4.696ZM24.104,3.24475L24.104,4.656000000000001L21.264,4.656000000000001L21.264,8.57675L22.016,8.57675L22.016,8.096L24.104,8.096L24.104,10.66763L24.896,10.66763L24.896,8.096L26.992,8.096L26.992,8.53662L27.776,8.53662L27.776,4.656000000000001L24.896,4.656000000000001L24.896,3.24475L24.104,3.24475ZM22.016,7.352L22.016,5.4L24.104,5.4L24.104,7.352L22.016,7.352ZM26.992,7.352L24.896,7.352L24.896,5.4L26.992,5.4L26.992,7.352Z"
							fill="#FFFFFF"
							fill-opacity="1"
						/>
					</g>
				</g>
			</svg>
		</div>
	);
};
