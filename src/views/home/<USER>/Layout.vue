<script setup lang="ts">
import { computed } from "vue";
import {
	NLayout,
	NLayoutHeader,
	NLayoutContent,
	NLayoutFooter,
} from "naive-ui";
import { CopyRight } from "@/components/common";
import Header1 from "@/components/common/Header1/index.vue";

import { useBasicLayout } from "@/hooks/useBasicLayout";
import { useAppStore } from "@/store";
import Sider from "./sider/index.vue";
import { useRoute } from "vue-router";
const appStore = useAppStore();
const { isMobile, isIpad } = useBasicLayout();
const route = useRoute()

const collapsed = computed(() => appStore.siderCollapsed);

const getMobileClass = computed(() => {
	if (isMobile.value || isIpad.value) return ["rounded-none", "shadow-none"];
	return ["dark:border-neutral-800"];
});

const getContainerClass = computed(() => {
	return ["h-full", { "": !isMobile.value && !collapsed.value }];
});
</script>

<template>
	<div class="h-full dark:bg-[#24272e] transition-all" :class="[isMobile ? 'p-0' : 'p-0']">
		<div class="h-full sm:pb-[100px] ipad:pb-[100px]" :class="getMobileClass">
			<NLayoutHeader>
				<Header1 />
			</NLayoutHeader>
			<NLayout class="z-40 transition" :class="getContainerClass" has-sider>
				<Sider class="sm:!hidden ipad:!hidden" />
				<NLayoutContent class="h-full">
					<RouterView v-slot="{ Component, route }" style="height: unset;">
						<component :is="Component" :key="route.fullPath" />
					</RouterView>
					<NLayoutFooter class="h-fit" v-if="!route.meta.hideFooter">
						<CopyRight />
					</NLayoutFooter>
				</NLayoutContent>
			</NLayout>
			<!-- <NLayoutFooter>
				<CopyRight />
			</NLayoutFooter> -->
		</div>
	</div>
</template>

<style lang="less" scoped>
:deep(.n-layout-scroll-container) {
	height: 100vh;
}
</style>
