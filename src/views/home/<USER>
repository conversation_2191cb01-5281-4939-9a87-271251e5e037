.guess-tag {
	height: 30px;
	padding: 0 17px;
	background: rgba(255, 255, 255, 0.3);
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	font-weight: normal;
	font-variation-settings: "opsz" auto;
	color: #666666;
	border-radius: 5px;
}
.box-enter-active,.box-leave-active {
  transition: opacity 0.5s ease;
}
.box-enter-from,.box-leave-to {
  opacity: 0;
}

.title {
	display: -webkit-box;
	overflow: hidden;
	text-overflow: ellipsis;
	word-break: break-all;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}
.scroll-container {
	height: auto !important;
}