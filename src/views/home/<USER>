<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import banner from "@/assets/images/banner-bj-1.png"
import InputSearch from '@/components/common/InputSearch/index.vue'
import IconTitle from '@/components/common/IconTitle/index.vue'
import Card from '@/components/common/Card/index.vue'
import Tabs from '@/components/common/Tabs/index.vue'
import { NImage, NSkeleton } from 'naive-ui'
import { getCategoryAndCreatesByCategoryId, getAppsCategoryList, homeFetch } from '@/chatgpt'
import Fire from '@/assets/images/fire.png';
import Classify from '@/assets/images/category.png';
import { useRequest } from 'vue-hooks-plus'
import { HomeData } from './types'
import { router } from '@/router'
import useSkeletonCardWidth from '@/hooks/useSkeletonCardWidth'
import GlobalFloat from '@/components/common/GlobalFloat/index.vue'
import { replaceText } from '@/plugins/directive';

interface IProps {
	id: number,
	name: string,
	profile: string
	hot_num: number,
	welcome_message: string,
	CategoryCreatebot: any,
	Category: any[],
	href?: string
}
interface ICategory {
	id: number;
	name: string;
	active?: boolean;
}

const application_list = ref<any[]>([])
const app_list = ref<any[]>([
	{
		id: -1,
	}, {
		id: -2,
	},
	{
		id: -3,
	},
	{
		id: -4,
	}, {
		id: -5,
	}, {
		id: -6,
	}, {
		id: -7,
	}, {
		id: -8,
	}
])
const recommend = ref<any[]>([{
	id: -1,
}, {
	id: -2,
},
{
	id: -3,
},
{
	id: -4,
}, {
	id: -5,
}])
const app_hots = ref<any[]>([
	{
		id: -1,
	}, {
		id: -2,
	},
	{
		id: -3,
	},
	{
		id: -4,
	}, {
		id: -5,
	}, {
		id: -6,
	}, {
		id: -7,
	}, {
		id: -8,
	}
])
const current = ref(-1)
const keyword = ref("")
const containerRef = ref<HTMLElement | null>(null)

const { width, setWidth } = useSkeletonCardWidth();

const homeClassificationRef = ref<HTMLElement | null>(null)

const classificationWidth = computed(() => {
	return homeClassificationRef.value?.clientWidth
})


let newList: any[] = []
const formatChild = (list: any) => {
	for (let item of list) {
		for (let mp of item.Createbots) {
			const index = newList.findIndex((item: IProps) => item.id === mp.id)
			if (index == -1) {
				newList.push(mp)
			}
		}
	}
}

useRequest<HomeData>(() => homeFetch({
	count: recommend.value.length,
	hotCount: app_hots.value.length
}), {
	onSuccess: (data) => {
		app_hots.value = data.hots
		recommend.value = data.recommend
	}
})
const { run, loading } = useRequest(getCategoryAndCreatesByCategoryId, {
	manual: true,
	onSuccess: data => {
		newList = []
		formatChild(data)
		app_list.value = newList
	}
})

// const loadData = async ({ id, keywords = "" }: { id?: number, keywords?: string }) => {
//     show.value = false
//     newList = []
//     const response = await getAppsCategoryList({ categoryId: id && id > 0 ? id : '', keywords })
//     formatChild(response)
//     show.value = true
//     app_list.value = newList
// }
// const loadHotData = async (keywords = "") => {
//     const response = await chatbotsList(keywords) as unknown as any[]
//     const list = response.filter(item => item.name === '热门应用')
//     hot_app_list.value = list[0].Chatbots
// }
onMounted(async () => {
	setWidth(homeClassificationRef.value!.clientWidth);
	const response = await getCategoryAndCreatesByCategoryId() as unknown as ICategory[]
	if (response.length > 0) {
		response.map((item, index) => {
			if (index === 0) {
				item.active = true
				current.value = item.id
			} else {
				item.active = false
			}
			return item
		})
	}
	application_list.value = response
	if (current.value > 0) {
		run({ categoryId: current.value })
	}
})
const handleCategoryClick = (id: number) => {
	application_list.value.forEach((item: { active: boolean; id: number }) => {
		item.active = false
		if (item.id === id) {
			item.active = true
			current.value = id
		}
	});
	run({ categoryId: id })
}
const handleInput = (value: any) => {
	keyword.value = value

}
const handleClick = () => {
	if (keyword.value?.length === 0) {
		return
	}
	router.push({ path: '/search', query: { keywords: keyword.value } })
}
const handleRecommend = (keywords: string) => {
	router.push({ path: '/search', query: { keywords } })
}
</script>
<template>
	<div>
		<div id="scroll-container" class=" w-full h-[100%] overflow-y-scroll bg-[#F6F6FA] pt-[110px] flex flex-col items-center sm:pt-4 ipad:pt-4"
				 style="height: auto !important;">
			<section class=" home-container 3xl:w-[1470px] 2xl:w-[1270px] xl:w-[980px] lg:w-[724px] md:w-[568px]
            mx-[25px] sm:mx-4 ipad:mx-4" ref="containerRef">
				<section class="flex flex-col justify-center items-center ">
					<section class=" w-full home-banner-container relative h-[296px] sm:h-auto sm:py-6 rounded-md overflow-hidden">
						<NImage preview-disabled :src="banner" class="w-full h-full mx-auto absolute top-0 right-0 left-0 bottom-0 sm:hidden ipad:hidden" />
						<!-- <img :src="banner" class="hidden sm:block absolute  top-0 right-0 h-full object-cover max-w-7xl" /> -->
						<div class="h-full relative flex flex-col items-center justify-center gap-y-[24px]">
							<span class="text-[30px] text-[#3d3d3d] font-semibold title mx-[100px] sm:mx-4 sm:text-xl sm:text-center">
								从<span class="font-variation-settings-opsz-auto bg-gradient-text">灵感到内容优化</span>
								<br class="hidden sm:block" />一站式完成
								助你跨越创作瓶颈
							</span>
							<InputSearch @change="handleInput" @click="handleClick" />
							<div class="relative flex flex-row justify-start items-center gap-x-[10px] pt-[12px] sm:flex-wrap sm:px-4 sm:gap-3  ipad:flex-wrap ipad:px-4 ipad:gap-3"
									 v-if="recommend.length">
								<span class="inline-block sm:w-full">您可能要找:</span>
								<span class="guess-tag cursor-pointer" v-for="item in recommend" @click="handleRecommend(item.name)" >
									{{ replaceText(item.name) }}
								</span>

							</div>
							<div v-else
									 class="relative flex flex-row justify-start items-center gap-x-[10px] pt-[12px]  cursor-pointer sm:flex-wrap sm:px-4 sm:gap-3 ipad:flex-wrap ipad:px-4 ipad:gap-3">
								<span class="inline-block sm:w-full">您可能要找:</span>
								<n-skeleton height="30px" width="93px" class="guess-tag" v-for="item in Array(3)"  />
							</div>
						</div>
					</section>
					<section class="home-tender-container flex flex-col items-start w-full mt-[44px]">
						<IconTitle :icon-src="Fire" title="热门推荐" />
						<Transition appear name="box">
							<div class=" mt-[24px] mb-[24px] flex flex-row flex-wrap gap-x-[24px] gap-y-[24px] w-full">
								<Card v-for="item in app_hots || Array(8)" :key="item.id" :count="item.hot_num" :title="item.name" :description="item.welcome_message"
											:img-src="item.profile" :id="item.id" :type="item.type || ''" :category-id="item.categoryId" :href="item.href" :width="width"
											:free="item.free" />
							</div>
						</Transition>
					</section>
					<section class="home-classification-container flex flex-col items-start w-full mt-[44px]" ref="homeClassificationRef">
						<IconTitle :icon-src="Classify" title="应用分类" />
						<div class=" mt-[24px] mb-[26px]">
							<Tabs :options="application_list" :on-click="handleCategoryClick" :container-width="classificationWidth" />
						</div>
						<Transition appear name="box">
							<div v-if="!loading" class=" mt-[24px] mb-[24px] flex flex-row flex-wrap gap-x-[24px] gap-y-[24px] w-full min-h-[700px] content-start">
								<Card v-for="item in app_list" :key="item.id" :title="item.name" :description="item.welcome_message" :img-src="item.profile" :id="item.id"
											:category-id="current" :type="item.type || ''" :href="item.href" :width="width" :free="item.free" />
							</div>
						</Transition>
					</section>
				</section>
			</section>
		</div>
		<GlobalFloat />
	</div>
</template>
<style lang="less" scoped>
@import './home.less';
</style>
