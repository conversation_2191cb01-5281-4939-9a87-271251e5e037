<script setup lang="ts">
import { ref,onMounted,computed } from 'vue'
import { NImage } from 'naive-ui'
import { TabBar } from '@/components/common'
import img1 from '@/assets/images/01.png'
import apple from '@/assets/images/apple.svg'
import window from '@/assets/images/windows.svg'
import Linux from '@/assets/images/Linux.svg'
import icon1 from '@/assets/images/icon1.svg'
import toolIcon1 from '@/assets/images/tool_icon1.svg'
import toolIcon2 from '@/assets/images/tool_icon2.svg'
import toolIcon3 from '@/assets/images/tool_icon3.svg'
import toolIcon4 from '@/assets/images/tool_icon4.svg'
import toolIcon5 from '@/assets/images/tool_icon5.svg'
import toolIcon6 from '@/assets/images/tool_icon6.svg'
import windowsdown from '@/assets/images/windowsdown.svg'
import appledown from '@/assets/images/appledown.svg'
import download from '@/assets/images/download.svg'
const system = ref()
const isDownload = ref(true)

onMounted(() => {
	const userAgent = navigator.userAgent;
	if (userAgent.indexOf("Windows") !== -1 || userAgent.indexOf("Win") !== -1) {
		system.value = "windows"
	} else if (userAgent.indexOf("Macintosh") !== -1 || userAgent.indexOf("Mac") !== -1) {
		system.value = "mac"
	} else if (userAgent.indexOf("Linux") !== -1) {
		system.value = "linux"
	}

	if(userAgent.indexOf('chatrobot')>-1) {
		isDownload.value = false
	} else {
		isDownload.value = true
	}
})

const isHidden = computed(()=> {
	return false
})

const renderIcon = () => {
	if (system.value === 'mac') {
		return apple
	} else if (system.value == 'windows') {
		return window
	} else if (system.value == 'linux') {
		return Linux
	} else {
		return apple
	}
}
const renderTitle = () => {
	if (system.value === 'mac') {
		return 'Download for MacOS'
	} else if (system.value == 'windows') {
		return 'Download for Windows'
	} else if (system.value == 'linux') {
		return 'Download for Linux'
	} else {
		return 'Download for Windows'
	}
}

const renderLink = () => {
	if (system.value === 'mac') {
		return 'https://cdn.aiwork365.cn/software/v3.0/ChatRobot.dmg'
	} else if (system.value == 'windows') {
		return 'https://cdn.aiwork365.cn/software/v3.0/ChatRobot_x64.msi'
	} else if (system.value == 'linux') {
		return 'https://cdn.aiwork365.cn/software/v3.0/ChatRobot_x86_64.deb'
	}
}

</script>
<template>
	<section class="bg-three">
		<section class="relative bg-bgColor">
			<div class="relative mx-auto max-w-container px-4 pt-16 sm:px-1 lg:px-8 lg:pt-20 h-[520px] sm:h-[450px]">
				<div class="mx-auto max-w-[40rem] sm:max-w-container lg:max-w-none">
					<div class="relative flex flex-col items-center justify-center px-4">
						<h1
							class="max-w-[810px] text-[#083A5E] mt-20 sm:mt-3 text-[40px] text-center font-semibold sm:text-[20px]">
							无论您有什么问题<br /> 我们的
							<span class="textGradient">
								AI
							</span>
							都会给您满意的答案
						</h1>
						<p class="mb-8 text-lg w-[50%] sm:w-[100%] text-[16px] text-[#497696] mt-[20px] mx-auto text-center">
							人工智能文案正在彻底改变内容的创建方式。AI 可以为博客、文章、网站、社交媒体等创建内容。
						</p>

						<div class="grid sm:grid-cols-1" :class="isDownload && !isHidden ? 'grid-cols-2': 'grid-cols-1'">
							<a v-if="isDownload && !isHidden&&false" :href="renderLink()"
								class="ckButtonNew ckButtonblue cursor-pointer transition duration-300 mr-[20px] sm:!hidden" download>
								<div class="flex items-center">
									<NImage :src="renderIcon()" class="w-[32px] h-[36px] mr-[5px]" />
									{{ renderTitle() }}
								</div>
							</a>
							<a href="/chat" class="ckButtonNew ckButtonblue2 cursor-pointer transition duration-300 mr-[20px] sm:mt-2 sm:mr-0">
								开始免费体验AI <span class="ml-[8px]">→</span>
							</a>
						</div>
					</div>
				</div>
			</div>
			<div class="sm:hidden">
				<div
					class="absolute left-[15%] top-[50%] xl:left-[3%] lg:top-[40%] md:top-[40%] flex items-center gap-[15px] pl-[7px] pr-[20px] py-[7px] rounded-[6px] bg-[#CBF3E7]">
					<NImage :src="img1" class="w-[32px] h-[32px]" />
					<span class="text-[#083A5E] text-[14px]">
						内容写作
					</span>
				</div>
				<div
					class="absolute right-[15%] top-[50%] xl:right-[3%] lg:top-[35%] md:top-[35%] flex items-center gap-[15px] pl-[7px] pr-[20px] py-[7px] rounded-[6px] bg-[#CBF3E7]">
					<NImage :src="img1" class="w-[32px] h-[32px]" />
					<span class="text-[#083A5E] text-[14px]">博客写作</span>
				</div>
				<div
					class="absolute left-[5%] bottom-[-10%] xl:bottom-[12%] flex items-center gap-[15px] pl-[7px] pr-[20px] py-[7px] rounded-[6px] bg-[#CBF3E7]">
					<NImage :src="img1" class="w-[32px] h-[32px]" />
					<span class="text-[#083A5E] text-[14px]">文案写作</span>
				</div>
				<div
					class="absolute right-[3%] bottom-0 xl:bottom-[21%] flex items-center gap-[15px] pl-[7px] pr-[20px] py-[7px] rounded-[6px] bg-[#CBF3E7]">
					<NImage :src="img1" class="w-[32px] h-[32px]" />
					<span class="text-[#083A5E] text-[14px]">
						AI 内容写作
					</span>
				</div>
			</div>
		</section>
		<section class="relative min-h-[281px] mt-[100px] sm:mt-0">
			<h2 class="text-center text-[40px] text-[#083A5E] font-semibold mb-[60px] sm:text-[20px] sm:mb-5">您的专家级私人助理</h2>
			<div class="grid grid-cols-3 gap-x-8 relative mx-auto max-w-container sm:grid-cols-1 sm:ml-3 sm:mr-3">
				<div
					class="flex items-center border border-[#CBF3E7] px-[20px] py-[20px] transition:0.3s rounded-[5px] cursor-pointer hover:bg-[#33B89F] hover:text-[#fff] mb-[20px] sm:mb-[10px]  sm:py-[10px] ">
					<div
						class="w-[60px] h-[60px] border border-[#CBF3E7] rounded-[100%] bg-[#fff] flex justify-center items-center">
						<NImage :src="icon1" class="w-[30px] h-[27px] relative hover:bg-[#fff]" preview-disabled />
					</div>
					<div class="ml-[20px]">
						<div class="font-normal">对于博客作者</div>
						<div class="text-[20px] leading-[26px] font-medium sm:text-[16px]">
							写作速度提升10倍
						</div>
					</div>
				</div>
				<div
					class="flex items-center border border-[#CBF3E7] px-[20px] py-[20px] transition:0.3s rounded-[5px] cursor-pointer hover:bg-[#33B89F] hover:text-[#fff] mb-[20px]  sm:mb-[10px]  sm:py-[10px]">
					<div
						class="w-[60px] h-[60px] border border-[#CBF3E7] rounded-[100%] bg-[#fff] flex justify-center items-center">
						<NImage :src="icon1" class="w-[30px] h-[27px] relative hover:bg-[#fff]" preview-disabled />
					</div>
					<div class="ml-[20px]">
						<div class="font-normal">对于学生</div>
						<div class="text-[20px] leading-[26px] font-medium sm:text-[16px]">
							帮助解决疑难问题，提高学习效率
						</div>
					</div>
				</div>
				<div
					class="flex items-center border border-[#CBF3E7] px-[20px] py-[20px] transition:0.3s rounded-[5px] cursor-pointer hover:bg-[#33B89F] hover:text-[#fff] mb-[20px]  sm:mb-[10px]  sm:py-[10px]">
					<div
						class="w-[60px] h-[60px] border border-[#CBF3E7] rounded-[100%] bg-[#fff] flex justify-center items-center">
						<NImage :src="icon1" class="w-[30px] h-[27px] relative hover:bg-[#fff]" preview-disabled />
					</div>
					<div class="ml-[20px]">
						<div class="font-normal">对于程序员</div>
						<div class="text-[20px] leading-[26px] font-medium sm:text-[16px]">
							可以提供技术支持和代码生成
						</div>
					</div>
				</div>
				<div
					class="flex items-center border border-[#CBF3E7] px-[20px] py-[20px] transition:0.3s rounded-[5px] cursor-pointer hover:bg-[#33B89F] hover:text-[#fff]  sm:mb-[10px]  sm:py-[10px]">
					<div
						class="w-[60px] h-[60px] border border-[#CBF3E7] rounded-[100%] bg-[#fff] flex justify-center items-center">
						<NImage :src="icon1" class="w-[30px] h-[27px] relative hover:bg-[#fff]" preview-disabled />
					</div>
					<div class="ml-[20px]">
						<div class="font-normal">对于教育工作者</div>
						<div class="text-[20px] leading-[26px] font-medium sm:text-[16px]">
							提供教学资源、案例分析、课程设计等
						</div>
					</div>
				</div>
				<div
					class="flex items-center border border-[#CBF3E7] px-[20px] py-[20px] transition:0.3s rounded-[5px] cursor-pointer hover:bg-[#33B89F] hover:text-[#fff]  sm:mb-[10px]  sm:py-[10px]">
					<div
						class="w-[60px] h-[60px] border border-[#CBF3E7] rounded-[100%] bg-[#fff] flex justify-center items-center">
						<NImage :src="icon1" class="w-[30px] h-[27px] relative hover:bg-[#fff]" preview-disabled />
					</div>
					<div class="ml-[20px]">
						<div class="font-normal">对于商务人士</div>
						<div class="text-[20px] leading-[26px] font-medium sm:text-[16px]">
							提供业务咨询、市场分析、客户服务等
						</div>
					</div>
				</div>
				<div
					class="flex items-center border border-[#CBF3E7] px-[20px] py-[20px] transition:0.3s rounded-[5px] cursor-pointer hover:bg-[#33B89F] hover:text-[#fff]">
					<div
						class="w-[60px] h-[60px] border border-[#CBF3E7] rounded-[100%] bg-[#fff] flex justify-center items-center">
						<NImage :src="icon1" class="w-[30px] h-[27px] relative hover:bg-[#fff]" preview-disabled />
					</div>
					<div class="ml-[20px]">
						<div class="font-normal">对于研究人员</div>
						<div class="text-[20px] leading-[26px] font-medium sm:text-[16px]">
							提供文献检索、数据查询、实验设计等
						</div>
					</div>
				</div>
			</div>
		</section>
	</section>
	<section class="bg-four relative sm: pb-[60px]">
		<div class="relative mx-auto max-w-container z-10 pt-[50px]">
			<h2 class="text-center text-[40px] text-[#083A5E] leading-[65px] sm:leading-8 font-semibold mb-[60px]  sm:text-[20px] sm:mb-5">
				生成 AI 写作包含 <br /> 你最喜欢的工具
			</h2>
			<div class="grid grid-cols-3 gap-x-8 gap-y-[30px] sm:gap-y-3 relative mx-auto sm:grid-cols-1 sm:ml-3 sm:mr-3">
				<div
					class="flex items-start border border-[#CBF3E7] rounded-[10px] px-[30px] py-[30px] sm:px-[15px] sm:py-[15px] gap-[25px] hover:bg-[#D4F7EC]">
					<div
						class="flex-none w-[60px] h-[60px] border border-[#CBF3E7] rounded-[100%] bg-[#fff] flex justify-center items-center">
						<NImage :src="toolIcon1" class="w-[30px] h-[30px]" preview-disabled />
					</div>
					<div>
						<div class="text-[24px] leading-[1.24] font-medium mb-[10px] sm:text-[18px]">
							博客内容
						</div>
						<div class="text-[14px] text-[#083A5E] leading-[22px] font-normal">
							通过分析和理解博客内容，为读者提供更好的建议和问题解答，博客作者可以利用 AI 来吸引更多的读者，并提高博客的价值和影响力
						</div>
					</div>
				</div>
				<div
					class="flex items-start border border-[#CBF3E7] rounded-[10px]  px-[30px] py-[30px] sm:px-[15px] sm:py-[15px] gap-[25px] hover:bg-[#D4F7EC]">
					<div
						class="flex-none w-[60px] h-[60px] border border-[#CBF3E7] rounded-[100%] bg-[#fff] flex justify-center items-center">
						<NImage :src="toolIcon2" class="w-[38px] h-[31px]" preview-disabled />
					</div>
					<div>
						<div class="text-[24px] leading-[1.24] font-medium mb-[10px] sm:text-[18px]">
							电商文案
						</div>
						<div class="text-[14px] text-[#083A5E] leading-[22px] font-normal">
							电商企业可以通过输入产品相关的信息、关键词或描述来生成高质量的产品文案。AI可以根据大量的数据学习和理解产品的特点、功能、目标用户等方面，生成符合语言规范、文笔流畅、信息丰富的文案。
						</div>
					</div>
				</div>
				<div
					class="flex items-start border border-[#CBF3E7] rounded-[10px]  px-[30px] py-[30px] sm:px-[15px] sm:py-[15px] gap-[25px] hover:bg-[#D4F7EC]">
					<div
						class="flex-none w-[60px] h-[60px] border border-[#CBF3E7] rounded-[100%] bg-[#fff] flex justify-center items-center">
						<NImage :src="toolIcon3" class="w-[26px] h-[27px]" preview-disabled />
					</div>
					<div>
						<div class="text-[24px] leading-[1.24] font-medium mb-[10px] sm:text-[18px]">
							销售文案
						</div>
						<div class="text-[14px] text-[#083A5E] leading-[22px] font-normal">
							销售人员可以通过输入客户相关的信息和关键词，快速生成符合客户需求的销售文案。AI可以根据大量的数据学习和理解不同行业和领域的产品特点、市场趋势等方面，生成针对性强、语言准确、内容丰富的销售文案。
						</div>
					</div>
				</div>
				<div
					class="flex items-start border border-[#CBF3E7] rounded-[10px]  px-[30px] py-[30px] sm:px-[15px] sm:py-[15px] gap-[25px] hover:bg-[#D4F7EC]">
					<div
						class="flex-none w-[60px] h-[60px] border border-[#CBF3E7] rounded-[100%] bg-[#fff] flex justify-center items-center">
						<NImage :src="toolIcon4" class="w-[38px] h-[31px]" preview-disabled />
					</div>
					<div>
						<div class="text-[24px] leading-[1.24] font-medium mb-[10px] sm:text-[18px]">
							数字广告文案
						</div>
						<div class="text-[14px] text-[#083A5E] leading-[22px] font-normal">
							广告人员和营销人员可以通过输入广告相关的信息、目标受众等参数来生成高质量的数字广告文案。AI可以根据大量的数据学习和理解不同行业和领域的产品特点、市场趋势等方面，生成符合语言规范、文笔流畅、信息丰富的广告文案。
						</div>
					</div>
				</div>
				<div
					class="flex items-start border border-[#CBF3E7] rounded-[10px]  px-[30px] py-[30px] sm:px-[15px] sm:py-[15px] gap-[25px] hover:bg-[#D4F7EC]">
					<div
						class="flex-none w-[60px] h-[60px] border border-[#CBF3E7] rounded-[100%] bg-[#fff] flex justify-center items-center">
						<NImage :src="toolIcon5" class="w-[38px] h-[31px]" preview-disabled />
					</div>
					<div>
						<div class="text-[24px] leading-[1.24] font-medium mb-[10px] sm:text-[18px]">
							社交媒体内容
						</div>
						<div class="text-[14px] text-[#083A5E] leading-[22px] font-normal">
							社交媒体运营人员可以通过输入话题、关键词、目标受众等参数来生成高质量的社交媒体内容。AI可以根据大量的数据学习和理解不同受众的兴趣爱好、话题热点、文化背景等方面，生成符合语言规范、内容适合、有趣易懂的社交媒体内容。
						</div>
					</div>
				</div>
				<div
					class="flex items-start border border-[#CBF3E7] rounded-[10px]  px-[30px] py-[30px]  sm:px-[15px] sm:py-[15px] gap-[25px] hover:bg-[#D4F7EC]">
					<div
						class="flex-none w-[60px] h-[60px] border border-[#CBF3E7] rounded-[100%] bg-[#fff] flex justify-center items-center">
						<NImage :src="toolIcon6" class="w-[30px] h-[27px]" preview-disabled />
					</div>
					<div>
						<div class="text-[24px] leading-[1.24] font-medium mb-[10px]">
							网站复制
						</div>
						<div class="text-[14px] text-[#083A5E] leading-[22px] font-normal">
							网站内容创作者和SEO从业者可以通过输入相关的主题或关键词来生成符合搜索引擎算法要求的原创性强的文章。AI可以根据大量的数据学习和理解不同领域的知识和信息，生成语言规范、结构清晰、内容丰富的文章。
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
	<section class="bg-footerBg pb-[100px] sm:hidden" id="download" v-if="isDownload && !isHidden">
		<div class="relative mx-auto max-w-[960px] z-10 pt-[80px]">
			<h2 class="text-center text-[50px] text-[#083A5E] leading-[65px] font-semibold mb-[20px] sm:text-[40px]">
				下载地址
			</h2>
			<p class="text-lg w-[50%] text-[16px] text-[#497696] mb-[60px] mx-auto text-center">
				适用于 Windows、MacOS 和 Linux等系统
			</p>
			<div class="grid grid-cols-3 sm:grid-cols-1 sm:ml-6 sm:mr-6 sm:gap-y-4">
				<a href="https://cdn.aiwork365.cn/software/v3.0/ChatRobot.dmg" download
					class="ckButtonNew ckButtonblue cursor-pointer transition duration-300 mr-[20px] min-w-[200px] sm:mr-0" style="justify-content: space-between;">
					<NImage :src="windowsdown" class="w-[32px] h-[32px]  mr-[10px] flex-none text-[#fff]"
						preview-disabled />
					<span class="text-[20px] font-medium flex-1">Download for Windows</span>
					<NImage :src="download" class="w-[20px] h-[20px] relative ml-[10px] flex-none" preview-disabled />
				</a>
				<a href="`https://cdn.aiwork365.cn/software/v3.0/ChatRobot_x64.msi" download
					class="ckButtonNew ckButtonblue cursor-pointer transition duration-300 mr-[20px] min-w-[200px] sm:mr-0" style="justify-content: space-between;">
					<NImage :src="appledown" class="w-[32px] h-[32px]  mr-[10px] flex-none text-[#fff]" preview-disabled />
					<span class="text-[20px] font-medium flex-1">Download for MacOS</span>
					<NImage :src="download" class="w-[20px] h-[20px] relative ml-[10px] flex-none" preview-disabled />
				</a>
				<a href="https://cdn.aiwork365.cn/software/v3.0/ChatRobot_x86_64.deb" download
					class="ckButtonNew ckButtonblue cursor-pointer transition duration-300 mr-[20px] min-w-[200px] sm:mr-0 justify-start" style="justify-content: space-between;">
					<NImage :src="Linux" class="w-[32px] h-[38px]  mr-[10px] flex-none text-[#fff]" preview-disabled />
					<span class="text-[20px] font-medium flex-1">Download for Linux</span>
					<NImage :src="download" class="w-[20px] h-[20px] relative ml-[10px] flex-none" preview-disabled />
				</a>
			</div>
		</div>
	</section>
	<TabBar />
</template>
