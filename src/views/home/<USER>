export interface HomeData {
	recommend: Recommend[];
	hots: Hot[];
}

interface Hot {
	id: number;
	name: string;
	systemMsg: string;
	profile: string;
	href: null;
	welcome_message: string;
	hot_num: number;
	type: string;
	isHot: boolean;
	categoryId: number;
	createdAt: string;
	updatedAt: string;
}

interface Recommend {
	id: number;
	name: string;
	systemMsg: string;
	profile: string;
	href: null;
	welcome_message: string;
	hot_num: number;
	type: string;
	isHot: boolean | null;
	createdAt: string;
	updatedAt: string;
}
