<template>
	<div class="pt-[100px] mx-auto text-center text-[24px] text-[#00B600]">
		<p>
			<NIcon size="80px">
				<CheckmarkCircleSharp />
			</NIcon>
		</p>

		<div class="pt-[12px]">
			<span>{{ ["正在支付", "支付成功", '支付失败'][status] }}</span>
			<p v-if="status == 0" class="inline-block w-[30px] text-left">
				<span class="breathe">...</span>
			</p>
		</div>

		<div v-if="status == 1" class="pt-[28px]">
			<NButton type="success" style="width: 200px" size="large" @click="handleBack">{{ timeCount }}s跳转</NButton>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { NIcon, NButton, useMessage } from "naive-ui";
import { CheckmarkCircleSharp } from "@vicons/ionicons5";
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { fetchWechatRecharge } from "./apis";

const $route = useRoute();
const $message = useMessage();

console.log(">>>$route", $route);

const status = ref(0);
const timeCount = ref(3);
const timer = ref();

onMounted(() => {
	const { goodsId } = $route.query;
	window.rprm.rec({
		elementid: 'payment_start',
		payment_type: 'wechat_h5',
		payment_goods_id: goodsId,
		payment_scene: 'h5',
	});
})

const startTimer = () => {
	timer.value = setTimeout(() => {
		timeCount.value -= 1;

		if (timeCount.value <= 0) {
			handleBack();
			return;
		}

		startTimer();
	}, 1000);
};

const handleBack = () => {
	clearTimeout(timer.value);
	// 安全地处理URL
	const url = $route.query?.url ? String($route.query.url) : location.origin;
	window.location.replace(url);
};
const handleSuccess = () => {
	startTimer();
	status.value = 1;
};
const handleFail = () => {
	status.value = 2
	startTimer();
}

// 加载js
const loadWxSDK = (url) => {
	if (typeof window.WeixinJSBridge !== "undefined")
		return Promise.resolve(true);
	const { promise, resolve } = Promise.withResolvers();
	const script = document.createElement("script");
	script.src = url;
	script.onload = () => {
		resolve(true);
	};
	document.body.appendChild(script);

	return promise;
};

// 微信公众号支付
const wechatPublicPay = (params, callback) => {
	if (typeof window.WeixinJSBridge == "undefined") {
		callback &&
			callback({
				err: new Error("WeixinJSBridge is undefined"),
				status: false,
			});

		return;
	}

	console.log(">>>params", params);
	window.WeixinJSBridge.invoke(
		"getBrandWCPayRequest",
		{
			appId: params.appId, //公众号名称，由商户传入
			timeStamp: params.timeStamp, //时间戳，自1970年以来的秒数
			nonceStr: params.nonceStr, //随机串
			package: params.package,
			signType: params.signType, //微信签名方式：
			paySign: params.paySign, //微信签名
		},
		function (res) {
			console.log(">>>getBrandWCPayRequest res", res);
			if (!callback) return;
			if (res.err_msg == "get_brand_wcpay_request:ok") {
				// 使用以上方式判断前端返回,微信团队郑重提示：
				//res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
				return callback({ err: null, status: true });
			}

			return callback({ err: new Error(res.err_msg), status: false });
		}
	);
};

const handlePay = () => {
	const { goodsId } = $route.query;

	fetchWechatRecharge<any>({
		goodsId,
		url: $route.query.url
	}).then((res) => {

		const { isJsH5, h5Data } = res || {};
		if (!isJsH5) return $message.error("当前支付环境不支持");

		wechatPublicPay(h5Data.data, ({ status, err }) => {
			window.rprm.rec({
				elementid: status ? 'payment_success' : 'payment_fail',
				payment_type: 'wechat_h5',
				payment_goods_id: goodsId,
				payment_scene: 'h5'
			});
			if (!status) return handleFail();
			handleSuccess();
		});
	});
};

onMounted(() => {
	loadWxSDK("https://res.wx.qq.com/open/js/jweixin-1.6.0.js").then(handlePay);
});
</script>

<style lang="less" scoped>
.breathe {
	padding-left: 4px;
	display: inline-block;
	overflow: hidden;
	word-break: keep-all;
	white-space: nowrap;
	width: 2px;
	animation: breathe 1s infinite forwards;
	vertical-align: bottom;
	text-align: left;
	letter-spacing: 4px;
}

@keyframes breathe {
	0% {
		opacity: 1;
		width: 0px;
	}

	35% {
		opacity: 1;

		width: 15px;
	}

	70% {
		opacity: 1;
		width: 30px;
	}

	100% {
		opacity: 0;
		width: 30px;
	}
}
</style>
