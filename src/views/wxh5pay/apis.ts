import { useUserStore } from "@/store";
import { post } from "@/utils/request";

/**
 * 获取微信充值QR
 * @returns
 */
export function fetchWechatRecharge<T>(params: {
	goodsId?: string;
}) {
	return post<T>({
		url: "/api3/aiwork/alipay/prepay/" + params.goodsId,
		data: {
			url: window.landingUrl || "",
			teamId: useUserStore().curTeam?.id || "",
			...params,
		},
	});
}
/**
 * 查询是否充值
 * @returns
 */
export function fetchIsPay<T>() {
	return post<T>({
		url: "/api3/aiwork/pay/ispay",
	});
}
