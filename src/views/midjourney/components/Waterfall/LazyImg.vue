<script setup lang="ts">
import { computed, inject } from 'vue'
import { NImage } from 'naive-ui'
import loading from "@/assets/loading.png"
interface Props {
	url: string,
	className?: string
}
const props = defineProps<Props>()
const src = computed(() => props.url)
const DEFAULT_LOADING = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'
const imgLoaded = inject('imgLoaded') as () => void
const handleLoad = () => {
	imgLoaded()
}

</script>

<template>
	<NImage ref="lazyRef" :src="src" lazy :placeholder="DEFAULT_LOADING" :class="className" @load="handleLoad">
		<template #placeholder>
			<div class="h-[300px] w-full bg-gray-900 flex justify-center items-center rounded-[10px]">
				<NImage :src="loading" />
			</div>
		</template>

	</NImage>
</template>
<style>
@import url(./index.less);
</style>
