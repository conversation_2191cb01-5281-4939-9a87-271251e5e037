

<script setup lang="ts">
import type { PropType } from 'vue'
import { ref,computed } from 'vue'
import { NEllipsis,NTooltip } from 'naive-ui'
import useClipboard from 'vue-clipboard3'
import { AppTitle24Regular,DocumentCopy48Regular } from '@vicons/fluent'
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import LazyImg from './LazyImg.vue'

interface Emit {
	(ev: 'setInputvalue', msg: string): void
}
const emit = defineEmits<Emit>()

const props = defineProps({
	domRef: {
		type: String
	},
  list: {
    type: Array,
  },
  options: {
    type: Object as PropType<any>,
  },
  pageSize: {
    type: Number,
    default: 100,
  },
})
const list:any = computed(()=> props.list)

const curIndex = ref<number>(-1)
const { toClipboard } = useClipboard()

function handleMouseOver(index: number) {
	curIndex.value = index
}
function handleMouseLeave() {
	curIndex.value = -1
}
function handleCopy(msg: string) {
	toClipboard(msg)
}
function handleApply(msg: string){
	emit('setInputvalue', msg)
}
</script>

<template>
   <Waterfall
      :list="list"
      :row-key="options.rowKey"
      :gutter="options.gutter"
      :has-around-gutter="options.hasAroundGutter"
      :width="options.width"
      :breakpoints="options.breakpoints"
      :img-selector="options.imgSelector"
      :background-color="options.backgroundColor"
      :animation-effect="options.animationEffect"
      :animation-duration="options.animationDuration"
      :animation-delay="options.animationDelay"
      :lazyload="options.lazyload"
      :load-props="options.loadProps"
      :cross-origin="options.crossOrigin"
    >
      <template #item="{ item, index }">
        <div class="bg-gray-900 rounded-lg shadow-md overflow-hidden transition-all duration-300 ease-linear hover:shadow-lg hover:shadow-gray-600 group"
					@mouseover="handleMouseOver(index)"
					@mouseleave="handleMouseLeave()"
				>
          <div class="overflow-hidden relative"  id="image-scroll-container">
            <LazyImg :url="item.url" class="cursor-pointer transition-all duration-300 ease-linear group-hover:scale-105 h-auto" />
						<div class="px-4 pt-2 pb-4 absolute bottom-2 left-2 right-2 bg-gray-900 opacity-80 rounded-[10px] hidden" :class="curIndex==index? 'show': 'hidden'">
							<h2 class="pb-2 text-[#fff]">
								<NEllipsis :line-clamp="3">{{ item.prompt }}</NEllipsis>
							</h2>
							<div class="flex justify-end gap-2">
								<NTooltip trigger="hover">
									<template #trigger>
										<div class="cursor-pointer" @click="handleCopy(item.prompt)"><DocumentCopy48Regular class="w-[22px] h-[22px] text-[#fff]"  /></div>
									</template>
									复制描述
								</NTooltip>

								<NTooltip trigger="hover">
									<template #trigger>
										<div class="cursor-pointer" @click="handleApply(item.prompt)"><AppTitle24Regular class="w-[22px] h-[22px] text-[#fff]" /></div>
									</template>
									创造同款
								</NTooltip>
							</div>
						</div>
					</div>
        </div>
      </template>
    </Waterfall>
</template>
