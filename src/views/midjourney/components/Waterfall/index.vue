
<script setup lang="ts">
import { reactive, ref } from 'vue'
import { NButton } from 'naive-ui'
import WaterfallList from './WaterfallList.vue'
import jsonCase from '@/json/case.json'
import 'vue-waterfall-plugin-next/dist/style.css'
import loading from '@/assets/loading.png'

interface Emit {
	(ev: 'setInputvalue', msg: string): void
}
const emit = defineEmits<Emit>()

const options = reactive({
  // 唯一key值
  rowKey: 'id',
  // 卡片之间的间隙
  gutter: 10,
  // 是否有周围的gutter
  hasAroundGutter: true,
  // 卡片在PC上的宽度
  width:280,
  // 自定义行显示个数，主要用于对移动端的适配
  breakpoints: {
    1200: {
      // 当屏幕宽度小于等于1200
      rowPerView: 4,
    },
    800: {
      // 当屏幕宽度小于等于800
      rowPerView: 3,
    },
    500: {
      // 当屏幕宽度小于等于500
      rowPerView: 2,
    },
  },
  // 动画效果
  animationEffect: 'animate__fadeInUp',
  // 动画时间
  animationDuration: 500,
  // 动画延迟
  animationDelay: 300,
  // 背景色
  backgroundColor: '',
  // imgSelector
  imgSelector: 'src.original',
  // 加载配置
  loadProps: {
		loading
  },
  // 是否懒加载
  lazyload: false,
})
const list = ref(jsonCase?.data?.list)
const categorys = ref(jsonCase?.data?.categorys)
const curCategory = ref<number>(1)
const curList = ref<any>(list.value.filter(item => item.category == 1))

const dialogVisible = ref(false)

const handSetInput = (msg: string) => {
	emit('setInputvalue', msg)
}
const handleCategory = (item: { id: number, title: string}) => {
	const data = list.value.filter((mp)=> mp.category === item.id)
	curCategory.value = item.id
	curList.value = data
}

</script>

<template>
  <div class="h-screen flex flex-col overflow-hidden mt-[30px]">
		<div class="sm:ml-[10px] w-full md:ml-[10px] lg:ml-[10px] xl:ml-[10px] 2xl:ml-[120px]">
			<ul class="flex gap-2 mb-[10px] sm:flex-wrap">
				<li v-for="(item,index) of categorys" :key="index">
					<NButton strong secondary round :type="curCategory === item.id ? 'warning': 'success'" @click="handleCategory(item)">
						{{ item.title }}
					</NButton>
				</li>
			</ul>
		</div>
    <!-- 左侧列表 -->
    <div class="overflow-y-auto w-full transition duration-300 select-none opacity-100 translate-y-0">
      <WaterfallList :list="curList" :options="options" @cardClick="dialogVisible = true" @set-inputvalue="handSetInput" />
    </div>
  </div>
</template>
