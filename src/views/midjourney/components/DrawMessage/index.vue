<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { NImage, NButton, NIcon, NTooltip } from 'naive-ui'
import AvatarComponent from './Avatar.vue'
import TextComponent from './Text.vue'
import { Member } from '@/components/common'
import { useChat } from '../../../hooks/useChat'
import { fetchUpscale, fetchVariation, fetchMjByMessageId } from '@/chatgpt'
import { useChatStore, useTaskStore, useDrawSettingStore } from '@/store'
import { t } from '@/locales'
import { Loading } from '@/components/common'
import Permission from '../../../components/Permission.vue'

interface Props {
	index: number,
	dateTime?: string
	text?: string
	inversion?: boolean
	error?: boolean
	loading?: boolean
	imgObj?: any
}

interface SelectProps {
	msgId: string,
	tag: string
}

interface Emit {
	(ev: 'regenerate'): void
	(ev: 'delete'): void
}
const MAX_REQUEST_COUNT = 100
let requstCount = 0

const route = useRoute()
const { uuid } = route.params as { uuid: string }

const chatStore = useChatStore()
const taskStore = useTaskStore()
const useDrawStore = useDrawSettingStore()
const { addChat, updateChat, updateChatSome, getChatByUuidAndIndex } = useChat()
const dataSources = computed(() => chatStore.getChatByUuid(+uuid))

const props = defineProps<Props>()

const emit = defineEmits<Emit>()

const messageRef = ref<HTMLElement>()
const asRawText = ref(props.inversion)
const loading = ref<boolean>(false)
const needPermission = ref(false)
const showMember = ref<boolean>(false)
const downLoading = ref<boolean>(false)
const currentSelectTag = ref<SelectProps[]>([])
const img_width = ref<string>()

const vData = [
	{ name: 'V1', desc: '使用第一张重绘' },
	{ name: 'V2', desc: '使用第二张重绘' },
	{ name: 'V3', desc: '使用第三张重绘' },
	{ name: 'V4', desc: '使用第四张重绘' },
]
const uData = [
	{ name: 'U1', desc: '使用第一张生成大图' },
	{ name: 'U2', desc: '使用第二张生成大图' },
	{ name: 'U3', desc: '使用第三张生成大图' },
	{ name: 'U4', desc: '使用第四张生成大图' },
]

function addTags(msgId: string, tag: string) {
	const item = currentSelectTag.value.find((item) => item.msgId === msgId && item.tag === tag)
	if (!item) {
		currentSelectTag.value.push({ msgId, tag })
	}
}

function removeTags(msgId: string, tag: string) {
	if (msgId && tag) {
		const index = currentSelectTag.value.findIndex((item) => item.msgId === msgId && item.tag === tag)
		if (index > -1) {
			currentSelectTag.value.splice(index, 1)
		}
	}
}

const isSelected = (msgId: string, tag: string, tags: any[]) => {
	const item = currentSelectTag.value.find((item) => item.msgId === msgId && item.tag === tag)
	if (item) {
		return true
	}
	if (tags && tags.length > 0) {
		const exists = tags.find((item) => item.tag === tag)
		if (exists) {
			return true
		}
	}
	return false
}

let newMessage: any
const upscaleOrvariation = async (
	pormpt: string,
	msgId: string,
	msgHash: string,
	index: number,
	type: string,
	msg: string,
	tag: string
) => {

	if (loading.value) return
	newMessage = {
		dateTime: new Date().toLocaleString(),
		text: `${msg}`,
		inversion: true,
		error: false,
	}
	addChat(+uuid, {
		...newMessage,
	})
	loading.value = true

	try {
		getMessage(pormpt, msgId, msgHash, index, type, tag)
	} catch (error) {

	}
}

async function getMessage(pormpt: string, msgId: string, msgHash: string, index: number, type: string, tag: string) {
	const fetchUpscaleOrVariation = type == 'upscale' ? fetchUpscale : fetchVariation
	try {
		requstCount = 0
		const options = useDrawStore.getSetting()
		const response: any = await fetchUpscaleOrVariation<any>({
			content: pormpt,
			msgId,
			msgHash,
			index,
			options
		})
		addChat(+uuid, {
			...newMessage,
			loading: true,
			imgObj: {
				hasTag: false,
				progress: 0,
				isQueue: true,
				title: '',
				content: '',
				msgID: '',
				msgHash: '',
				imageUrl: '',
				downImage: false,
				tags: []
			},
			inversion: false,
		})

		if (response && response?.messageId) {
			taskStore.addTaskList({ type: type, messageId: response?.messageId, uuid, time: response?.time })
			getMessageId(response?.messageId, type, response?.time, msgId, tag)
		}
	} catch (error: any) {
		newMessage.text = error?.message || error?.errmsg
		if (error.errcode == 30000001) {
			showMember.value = true
			newMessage.text = '当前用户未登录，请登录后免费体验一次或进行购买后使用！'
		} else if (error.errcode == 30000002) {
			showMember.value = true
			newMessage.text = '当前用户服务期限已到期或体验次数已用尽，请购买后使用！'
		}
		addChat(+uuid, {
			...newMessage,
			inversion: false,
			error: true
		})
		removeTags(msgId, tag)
		loading.value = false
	}
}

let timer: NodeJS.Timeout
async function getMessageId(messageId: string, type: string, time: number, msgId: string, tag: string) {
	try {
		const response: any = await fetchMjByMessageId<any>({
			messageId,
		})

		const data = response
		if (data && data.uri) {
			newMessage.imgObj = {
				hasTag: data.uri.endsWith('.png') ? true : false,
				imageId: data?.id,
				imageUrl: data?.uri,
				msgHash: data?.hash,
				msgID: data?.id,
				progress: data.progress,
				title: data?.title,
				content: data?.content,
				downImage: type == 'upscale' ? true : false,
				tags: []
			}
			updateChat(+uuid, dataSources.value.length - 1, {
				...newMessage,
				loading: true,
				inversion: false
			})
		}
		if (data.progress === 'done') {
			loading.value = false
			clearTimeout(timer)
			taskStore.delLocalTask(+uuid)
			removeTags(msgId, tag)

			const currentChat = getChatByUuidAndIndex(
				+uuid,
				props.index
			)
			if (currentChat && currentChat.imgObj) {
				currentChat.imgObj?.tags?.push({ msgId, tag })
				updateChatSome(+uuid, props.index, {
					...currentChat
				})
			}
		}
		if (requstCount >= MAX_REQUEST_COUNT) {
			clearTimeout(timer)
			updateChat(+uuid, dataSources.value.length - 1, {
				dateTime: new Date().toLocaleString(),
				text: '很抱歉，我们无法生成您所需的图片。请您稍后再试，或检查您的输入是否正确。',
				inversion: false,
				error: true,
				loading: false,
			})
			loading.value = false
			taskStore.delLocalTask(+uuid)
			removeTags(msgId, tag)
			return
		}
		if (response.progress !== 'done') {
			timer = setTimeout(() => {
				getMessageId(messageId, type, time, msgId, tag)
			}, time)
		}
	} catch (error: any) {
		const errorMessage = error?.errmsg || error?.message || t('common.wrong')
		taskStore.delLocalTask(+uuid)
		removeTags(msgId, tag)
		loading.value = false
		const currentChat = getChatByUuidAndIndex(
			+uuid,
			dataSources.value.length - 1,
		)

		if (currentChat?.text && currentChat.text !== '') {
			updateChatSome(+uuid, dataSources.value.length - 1, {
				text: errorMessage,
				error: true,
				loading: false,
			})
			return
		}

		updateChat(+uuid, dataSources.value.length - 1, {
			dateTime: new Date().toLocaleString(),
			text: errorMessage,
			inversion: false,
			error: true,
			loading: false
		})
	}
}

defineExpose({
	getMessageId
})

const tagClick = (
	content: string,
	msgId: string,
	msgHash: string,
	tag: string,
	msg: string
) => {
	addTags(msgId, tag)
	switch (tag) {
		case 'V1':
			upscaleOrvariation(content, msgId, msgHash, 1, 'variation', msg, tag)
			break
		case 'V2':
			upscaleOrvariation(content, msgId, msgHash, 2, 'variation', msg, tag)
			break
		case 'V3':
			upscaleOrvariation(content, msgId, msgHash, 3, 'variation', msg, tag)
			break
		case 'V4':
			upscaleOrvariation(content, msgId, msgHash, 4, 'variation', msg, tag)
			break
		case 'U1':
			upscaleOrvariation(content, msgId, msgHash, 1, 'upscale', msg, tag)
			break
		case 'U2':
			upscaleOrvariation(content, msgId, msgHash, 2, 'upscale', msg, tag)
			break
		case 'U3':
			upscaleOrvariation(content, msgId, msgHash, 3, 'upscale', msg, tag)
			break
		case 'U4':
			upscaleOrvariation(content, msgId, msgHash, 4, 'upscale', msg, tag)
			break
		default:
			break
	}
}

// const handleVariations = (content:string, msgID: string,msgHash: string, msg: string) => {
// 	upscaleOrvariation(content, msgID, msgHash,1,'variation',msg)
// }

function handleRegenerate() {
	emit('regenerate')
}

function downloadIamge(imgSrc: string, fileName?: string) {
	const xhr = new XMLHttpRequest()
	xhr.open('GET', imgSrc)
	xhr.responseType = 'blob'
	xhr.send()
	xhr.onload = function () {
		const fileBlob = xhr.response;
		const fileUrl = URL.createObjectURL(fileBlob)
		console.log(fileUrl)
		const ele = document.createElement('a')
		ele.setAttribute('href', fileUrl)
		ele.setAttribute('download', "")
		ele.click()
		ele.innerHTML = '下载'
		document.body.appendChild(ele)
	}
}

function onImageLoad(event: any) {
	const image = event.target
	const ratio = image.width / image.height;
	console.log(image.width, image.height, ratio);
	if (image.width == image.height) {
		img_width.value = "w-[350px]"
	} else if (image.width < image.height) {
		img_width.value = "w-[196px]"
	}
}

</script>

<template>
	<div ref="messageRef" class="flex w-full mb-6 overflow-hidden" :class="[{ 'flex-row-reverse': inversion }]">
		<div class="flex items-center justify-center flex-shrink-0 h-8 overflow-hidden rounded-full basis-8"
			:class="[inversion ? 'ml-2' : 'mr-2']">
			<AvatarComponent :image="inversion" />
		</div>

		<div class="overflow-hidden text-sm">
			<p class="text-xs text-[#b4bbc4]" :class="[inversion ? 'text-right' : 'text-left']">
				{{ dateTime }}
			<div v-if="imgObj && !imgObj.isQueue" class="pt-[3px]">
				<span class="text-[14px] text-[#595961]">{{ imgObj.title }}</span>
			</div>
			</p>
			<div class="flex items-end gap-1 mt-2" :class="[inversion ? 'flex-row-reverse' : 'flex-row']">
				<div v-if="error || !imgObj">
					<TextComponent ref="textRef" :inversion="inversion" :error="error" :text="text" :loading="loading"
						:as-raw-text="asRawText" />
				</div>

				<div v-if="imgObj && imgObj.isQueue && !error"
					class="loading bg-[#fff] w-[300px] h-[300px] sm:w-[200px] sm:h-[200px] border border-slate-100 rounded-[10px] flex flex-col items-center justify-center bg-grid-slate-100 relative">
					<Loading title="正在生成中..." />
				</div>

				<div v-if="imgObj && imgObj.imageUrl && !imgObj.isQueue && !error">
					<div class="flex flex-col justify-self-start w-full max-w-[550px] rounded-[8px] overflow-hidden border border-[#e5e7eb] sm:min-h-[300px]"
						:class="img_width">
						<div class="max-w-[100%] max-h-[350px] w-fit rounded-[8px] overflow-hidden">
							<div class="flex w-full h-full flex-auto">
								<div class="h-full cursor-pointer" :class="img_width">
									<div class="w-full h-full">
										<NImage class="object-cover h-full w-full" :src="imgObj.imageUrl"
											@load="onImageLoad" alt="" />
									</div>
								</div>
							</div>
						</div>
					</div>
					<div v-if="!imgObj.downImage && imgObj.progress === 'done'">
						<div class="mt-[5px]">
							<n-tooltip placement="bottom" trigger="hover" v-for="item of uData">
								<template #trigger>
									<n-button type="primary" size="small" color="#3dbaa1"
										:disabled="isSelected(imgObj.msgID, item.name, imgObj.tags)"
										style="margin-right: 8px;" class="sm:min-w-[44px] min-w-[64px]"
										@:click="tagClick(imgObj.content, imgObj.msgID, imgObj.msgHash, item.name, item.desc)">
										{{ item.name }}
									</n-button>
								</template>
								<span>{{ item.desc }}</span>
							</n-tooltip>
							<NButton type="primary" size="small" color="#3dbaa1" style="vertical-align: -2px;"
								class="sm:min-w-[44px] min-w-[64px]" @:click="handleRegenerate">
								<NIcon>
									<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
										viewBox="0 0 20 20">
										<path
											d="M12 6v3l4-4l-4-4v3c-4.42 0-8 3.58-8 8c0 1.57.46 3.03 1.24 4.26L6.7 14.8A5.87 5.87 0 0 1 6 12c0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8c0 3.31-2.69 6-6 6v-3l-4 4l4 4v-3c4.42 0 8-3.58 8-8c0-1.57-.46-3.03-1.24-4.26z"
											fill="currentColor"></path>
									</svg>
								</NIcon>
							</NButton>
						</div>
						<div class="mt-[5px]">
							<n-tooltip placement="bottom" trigger="hover" v-for="item of vData">
								<template #trigger>
									<n-button type="primary" size="small" color="#3dbaa1"
										:disabled="isSelected(imgObj.msgID, item.name, imgObj.tags)"
										style="margin-right: 8px; " class="sm:min-w-[44px] min-w-[64px]"
										@:click="tagClick(imgObj.content, imgObj.msgID, imgObj.msgHash, item.name, item.desc)">
										{{ item.name }}
									</n-button>
								</template>
								<span>{{ item.desc }}</span>
							</n-tooltip>

						</div>
					</div>
					<div v-if="imgObj.downImage">
						<div class="mt-[5px]">
							<NButton type="primary" size="small" color="#3dbaa1" :loading="downLoading"
								style="min-width: 64px; vertical-align: -2px;" @:click="downloadIamge(imgObj.imageUrl)">
								下载原图
							</NButton>
							<!-- <NButton
									type="primary"
									size="small"
									style="min-width: 64px; vertical-align: -2px;margin-left: 10px;"
									@:click="handleVariations(imgObj.content,imgObj.msgID, imgObj.msgHash,'使用这张图重绘')"
								>
									Make Variations
								</NButton> -->
						</div>
					</div>
				</div>
			</div>
		</div>
		<Member v-if="showMember" v-model:visible="showMember" />
		<Permission v-if="needPermission" v-model:visible="needPermission" />
	</div>

</template>
