<script setup lang='ts'>
import type { CSSProperties } from 'vue'
import { computed, ref, watch } from 'vue'
import { NLayoutSider, useMessage,NButton,NTooltip } from 'naive-ui'
import List from './List.vue'
import { useAppStore, useChatStore, useUserStore } from '@/store'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import Footer from '../../../components/Footer.vue'
import { getUser } from '@/store/modules/auth/helper'
const appStore = useAppStore()
const chatStore = useChatStore()
const { isMobile } = useBasicLayout()
const isLogin = ref(false)
const memberType = ref(0)
const user = getUser()
const userStore = useUserStore();
if (user) {
	isLogin.value = user?.id
}

const dataSources = computed(() => chatStore.history.filter(item=> item.type == 'mj'))
let keyword = ref('')

if (userStore && userStore.userInfo && userStore.userInfo.member) {
	const member: any = userStore.userInfo.member
	//code 100001
	memberType.value = member
} else if (userStore && userStore.userInfo) {
	//code 200001 MJ会员
	memberType.value = 4
}
const message = useMessage()
const collapsed = computed(() => appStore.siderCollapsed)

function handMjAdd() {
	let data = dataSources.value.filter(item => item?.type === 'mj')
	if (data && data.length >= 2) {
		message.warning('绘图只能创建两个')
		return
	}
	chatStore.addHistory({ title: '新建绘图', uuid: Date.now(), isEdit: false, type: 'mj', createTime: new Date().getTime() })
	if (isMobile.value)
		appStore.setSiderCollapsed(true)
}

function handleUpdateCollapsed() {
	appStore.setSiderCollapsed(!collapsed.value)
}

const getMobileClass = computed<CSSProperties>(() => {
	if (isMobile.value) {
		return {
			position: 'fixed',
			zIndex: 50,
		}
	}
	return {}
})

const mobileSafeArea = computed(() => {
	if (isMobile.value) {
		return {
			paddingBottom: 'env(safe-area-inset-bottom)',
			height: 'calc(100% - 66px)'
		}
	}
	return {
		height: 'calc(100% - 66px)'
	}
})

watch(
	isMobile,
	(val) => {
		appStore.setSiderCollapsed(val)
	},
	{
		immediate: true,
		flush: 'post',
	},
)
const handleInput = (e: any) => {
	const value = e.target.value
	keyword.value = value
}
</script>

<template>
	<NLayoutSider :collapsed="collapsed" :collapsed-width="0" :width="260" :show-trigger="isMobile ? false : 'arrow-circle'"
		collapse-mode="transform" position="absolute" bordered :style="getMobileClass" class="shadow-shadow1"
		@update-collapsed="handleUpdateCollapsed">
		<div class="flex flex-col mt-[66px] sm:mt-2 bg-[#fff] backdrop-blur transition-colors"
			:style="mobileSafeArea">
			<main class="flex flex-col flex-1 min-h-0">
				<div class="py-2 mt-[10px]">
					<div class="w-[220px] mx-auto flex items-center">
						<div class="flex items-center bg-[#F9F9F9] rounded-[6px] focus:bg-[#f1f1f2] mr-2">
							<IconSearch class="ml-2 mt-1 text-[18px] text-[#99a2b7]" />
							<input type="search" class="bg-[#F9F9F9] border border-[#F9F9F9] text-[#4B5675] w-full h-full py-2 px-3 rounded-[6px] appearance-none transition-colors duration-1000 focus:outline-0 shadow-inputShow" placeholder="Search..."  @input="handleInput"/>
						</div>
						<NTooltip trigger="hover">
							<template #trigger>
								<NButton type="success" color="#3dbaa1" @click="handMjAdd"><i class="fi fi-rr-plus"></i></NButton>
							</template>
							{{ $t('chat.newChatMJButton') }}
						</NTooltip>
					</div>
				</div>
				<div class="flex-1 min-h-0 pb-4 overflow-hidden">
					<List :keyword="keyword"/>
				</div>
			</main>
			<Footer />
		</div>
	</NLayoutSider>
	<template v-if="isMobile">
		<div v-show="!collapsed" class="fixed inset-0 z-40 bg-black/40" @click="handleUpdateCollapsed" />
	</template>
</template>
