<script setup lang='ts'>
import { computed } from 'vue'
import { NLayout, NLayoutHeader, NLayoutContent ,NLayoutFooter} from 'naive-ui'
import { useRouter } from 'vue-router'
import { <PERSON><PERSON>,<PERSON>bBar,CopyRight } from '@/components/common'
import { useChatStore } from '@/store'
import Sider from './sider/index.vue'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { useAppStore } from '@/store'
const appStore = useAppStore()
const chatStore = useChatStore()
const router = useRouter()

const historyList = computed(() => chatStore.history.filter(item=> item.type == 'mj'))
const dataItem = historyList.value.find((item=> item.uuid == chatStore.mjActive))
const active = dataItem ? dataItem.uuid : 1001

const urlObj = new URL(window.location.href);
router.replace(`/mj/${active}${urlObj.search}`)

const { isMobile } = useBasicLayout()

const collapsed = computed(() => appStore.siderCollapsed)

const getMobileClass = computed(() => {
	if (isMobile.value)
		return ['rounded-none', 'shadow-none']
	return [ 'dark:border-neutral-800']
})

const getContainerClass = computed(() => {
	return [
		'h-full',
		{ 'pl-[260px]': !isMobile.value && !collapsed.value },
	]
})
</script>

<template>
	<div class="h-full dark:bg-[#24272e] transition-all" :class="[isMobile ? 'p-0 overflow-hidden' : 'p-0']">
		<div class="h-full" :class="getMobileClass">
			<NLayoutHeader class="sm:hidden">
				<Header />
			</NLayoutHeader>
			<NLayout class="z-40 transition" :class="getContainerClass" has-sider>
				<Sider />
				<NLayoutContent class="h-full sm:pb-14" style="background-color: #f8f7fa;">
					<RouterView v-slot="{ Component, route }">
						<component :is="Component" :key="route.fullPath" />
					</RouterView>
				</NLayoutContent>
				<TabBar />
			</NLayout>
			<NLayoutFooter>
				<CopyRight />
			</NLayoutFooter>
		</div>
	</div>
</template>
