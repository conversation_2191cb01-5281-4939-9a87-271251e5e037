<script setup lang="ts">
import type { Ref } from 'vue'
import type { CSSProperties } from 'vue'
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { NAutoComplete, NButton, NInput, useDialog, useMessage } from 'naive-ui'
import html2canvas from 'html2canvas'
import { DrawMessage, Waterfall } from './components'
import { useScroll } from '../hooks/useScroll'
import { useChat } from '../hooks/useChat'
import { useCopyCode } from '../hooks/useCopyCode'
import { useUsingContext } from '../hooks/useUsingContext'
import HeaderComponent from './components/Header/index.vue'
import { HoverButton, SvgIcon, SettingDraw, Upload, MjList } from '@/components/common'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import {
  useChatStore,
  usePromptStore,
  useTaskStore,
  useDrawSettingStore,
  useSuggestionStore,
} from '@/store'
import { fetchImagine, fetchMjByMessageId, chatbotsList } from '@/chatgpt'
import { Member } from '@/components/common'
import { t } from '@/locales'
import Permission from '../components/Permission.vue'
import { ImageMultiple16Filled, Delete16Regular } from '@vicons/fluent'

let controller = new AbortController()
const MAX_REQUEST_COUNT = 100
let requstCount = 0

const route = useRoute()
const dialog = useDialog()
const ms = useMessage()

const chatStore = useChatStore()
const suggestionStore = useSuggestionStore()

useCopyCode()

const { isMobile } = useBasicLayout()
const { addChat, updateChat, updateChatSome, getChatByUuidAndIndex } = useChat()
const { scrollRef, scrollToBottom, scrollToBottomIfAtBottom } = useScroll()
const { usingContext, toggleUsingContext } = useUsingContext()

const { uuid } = route.params as { uuid: string }

const dataSources = computed(() => chatStore.getChatByUuid(+uuid))
const suggestions = suggestionStore.getSetting(+uuid)
const suggestion = ref<any>(suggestions)

const prompt = ref<string>('')
const loading = ref<boolean>(false)
const inputRef = ref<Ref | null>(null)
const showMember = ref<boolean>(false)
const showSetting = ref<boolean>(false)
const mjList = ref<boolean>(false)
const needPermission = ref<boolean>(false)
const isMock = ref<boolean>(false)
const showUpload = ref<boolean>(false)
// 添加PromptStore
const promptStore = usePromptStore()
const taskStore = useTaskStore()
const useDrawStore = useDrawSettingStore()

const currentMessageId = ref()
const suggestionList = ref()
const { promptList: promptTemplate } = storeToRefs<any>(promptStore)

// 未知原因刷新页面，loading 状态不会重置，手动重置
dataSources.value.forEach((item, index) => {
  if (item.loading) updateChatSome(+uuid, index, { loading: false })
})

function handleSubmit(ismock?: boolean) {
  if (ismock) {
    isMock.value = true
  } else {
    isMock.value = false
  }
  const mj = chatStore.getHistory(+uuid)
  if (mj?.type) {
    handleMessageSend()
  }
}

let newMessage: any = {}
async function handleMessageSend(text?: string) {
  let message = prompt.value || text
  if (loading.value) return

  if (!message || message.trim() === '') return

  newMessage = {
    dateTime: new Date().toLocaleString(),
    text: message.trim(),
    inversion: true,
    error: false,
  }
  if (newMessage.text) {
    addChat(+uuid, {
      ...newMessage,
    })
    scrollToBottom()
    loading.value = true
    prompt.value = ''

    try {
      getMessage(newMessage.text)
    } catch (error) {
      loading.value = false
    }
  }
}

let timer: NodeJS.Timeout
async function getMessageId(messageId: string, time: number) {
  try {
    const response: any = await fetchMjByMessageId<any>({
      messageId,
    })
    const data = response

    if (data && data.uri) {
      newMessage.imgObj = {
        hasTag: data.uri.endsWith('.png') ? true : false,
        imageId: data?.id,
        imageUrl: data?.uri,
        msgHash: data?.hash,
        msgID: data?.id,
        progress: data.progress,
        isQueue: data.progress === 0 ? true : false,
        title: data?.title,
        content: data?.content,
        downImage: false,
        tags: [],
      }
      newMessage.messageId = messageId
      updateChat(+uuid, dataSources.value.length - 1, {
        ...newMessage,
        inversion: false,
      })
    }

    if (data.progress === 'done') {
      loading.value = false
      clearTimeout(timer)
      taskStore.delLocalTask(+uuid)
    }

    if (requstCount >= MAX_REQUEST_COUNT) {
      clearTimeout(timer)
      loading.value = false
      updateChat(+uuid, dataSources.value.length - 1, {
        dateTime: new Date().toLocaleString(),
        text:
          '很抱歉，我们无法生成您所需的图片。请您稍后再试，或检查您的输入是否正确。',
        inversion: false,
        error: true,
        loading: false,
        messageId: '',
      })
      taskStore.delLocalTask(+uuid)
      return
    }

    if (response.progress !== 'done') {
      timer = setTimeout(() => {
        getMessageId(messageId, time)
        requstCount++
      }, time)
    }
  } catch (error: any) {
    loading.value = false
    taskStore.delLocalTask(+uuid)
    const errorMessage = error?.errmsg || error?.message || t('common.wrong')
    const currentChat = getChatByUuidAndIndex(
      +uuid,
      dataSources.value.length - 1,
    )
    if (currentChat?.text && currentChat.text !== '') {
      updateChatSome(+uuid, dataSources.value.length - 1, {
        text: errorMessage,
        error: true,
        loading: false,
      })
      return
    }
    updateChat(+uuid, dataSources.value.length - 1, {
      dateTime: new Date().toLocaleString(),
      text: errorMessage,
      inversion: false,
      error: true,
      loading: false,
    })

    scrollToBottomIfAtBottom()
  }
}

async function getMessage(message: string) {
  try {
    const options = useDrawStore.getSetting()
    requstCount = 0
    const response: any = await fetchImagine<any>({
      prompt: message,
      options,
    })
    addChat(+uuid, {
      ...newMessage,
      imgObj: {
        hasTag: false,
        progress: 0,
        isQueue: true,
        title: '',
        content: '',
        msgID: '',
        msgHash: '',
        imageUrl: '',
        downImage: false,
        tags: [],
      },
      inversion: false,
    })
    scrollToBottom()
    if (response && response?.messageId) {
      taskStore.addTaskList({
        type: 'imagine',
        messageId: response?.messageId,
        uuid: +uuid,
        time: response?.time,
      })
      getMessageId(response?.messageId, response.time)
    }
  } catch (error: any) {
    newMessage.text = error?.message || error?.errmsg
    if (error.errcode == 30000001) {
      showMember.value = true
      newMessage.text = '当前用户服务期限已到期或未购买服务，请购买后使用！'
    } else if (error.errcode == 30000002) {
      showMember.value = true
      newMessage.text = '当前用户服务期限已到期或未购买服务，请购买后使用！'
    }
    addChat(+uuid, {
      ...newMessage,
      inversion: false,
      error: true,
    })
    loading.value = false
  }
}

//绘图重试
async function onRegenerateMj(index: number) {
  const requestOptions = dataSources.value[index]
  let message = requestOptions.text
  if (loading.value) return

  if (!message || message.trim() === '') return

  newMessage = {
    dateTime: new Date().toLocaleString(),
    text: message.trim(),
    inversion: true,
    error: false,
  }
  if (newMessage.text) {
    addChat(+uuid, {
      ...newMessage,
    })
    loading.value = true
    try {
      getMessage(newMessage.text)
    } catch (error) {
      loading.value = false
    }
  }
}

function handleExport() {
  if (loading.value) return

  const d = dialog.warning({
    title: t('chat.exportImage'),
    content: t('chat.exportImageConfirm'),
    positiveText: t('common.yes'),
    negativeText: t('common.no'),
    onPositiveClick: async () => {
      try {
        d.loading = true
        const ele = document.getElementById('image-wrapper')
        const canvas = await html2canvas(ele as HTMLDivElement, {
          useCORS: true,
        })
        const imgUrl = canvas.toDataURL('image/png')
        const tempLink = document.createElement('a')
        tempLink.style.display = 'none'
        tempLink.href = imgUrl
        tempLink.setAttribute('download', 'chat-shot.png')
        if (typeof tempLink.download === 'undefined')
          tempLink.setAttribute('target', '_blank')

        document.body.appendChild(tempLink)
        tempLink.click()
        document.body.removeChild(tempLink)
        window.URL.revokeObjectURL(imgUrl)
        d.loading = false
        ms.success(t('chat.exportSuccess'))
        Promise.resolve()
      } catch (error) {
        ms.error(t('chat.exportFailed'))
      } finally {
        d.loading = false
      }
    },
  })
}

function handleDelete(index: number) {
  if (loading.value) return

  dialog.warning({
    title: t('chat.deleteMessage'),
    content: t('chat.deleteMessageConfirm'),
    positiveText: t('common.yes'),
    negativeText: t('common.no'),
    onPositiveClick: () => {
      chatStore.deleteChatByUuid(+uuid, index)
    },
  })
}

function handleUpdateVip() {
  showMember.value = true
}

function handleClear() {
  if (loading.value) return

  dialog.warning({
    title: t('chat.clearChat'),
    content: t('chat.clearChatConfirm'),
    positiveText: t('common.yes'),
    negativeText: t('common.no'),
    onPositiveClick: () => {
      chatStore.clearChatByUuid(+uuid)
    },
  })
}

function handleEnter(event: KeyboardEvent) {

  if (!isMobile.value) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      handleSubmit()
    }
  } else {
    if (event.key === 'Enter' && event.ctrlKey) {
      event.preventDefault()
      handleSubmit()
    }
  }
}

// 可优化部分
// 搜索选项计算，这里使用value作为索引项，所以当出现重复value时渲染异常(多项同时出现选中效果)
// 理想状态下其实应该是key作为索引项,但官方的renderOption会出现问题，所以就需要value反renderLabel实现
const searchOptions = computed(() => {
  if (prompt.value.startsWith('/')) {
    return promptTemplate.value
      .filter((item: { key: string }) =>
        item.key
          .toLowerCase()
          .includes(prompt.value.substring(1).toLowerCase()),
      )
      .map((obj: { value: any }) => {
        return {
          label: obj.value,
          value: obj.value,
        }
      })
  } else {
    return []
  }
})

// value反渲染key
const renderOption = (option: { label: string }) => {
  for (const i of promptTemplate.value) {
    if (i.value === option.label) return [i.key]
  }
  return []
}

const placeholder = computed(() => {
  const mj = chatStore.getHistory(+uuid)
  if (isMobile.value) return t('chat.placeholderMobile')
  return mj?.type ? t('chat.mJplaceholder') : t('chat.placeholder')
})

const buttonDisabled = computed(() => {
  return loading.value || !prompt.value || prompt.value.trim() === ''
})
const isIphonex = () => /iphone/gi.test(navigator.userAgent) && window.screen && (window.screen.height === 812 && window.screen.width === 375);
const footerClass = computed(() => {
  let classes = ['p-4 py-4 bg-[#fff]']
  if (isMobile.value)
    classes = [
      'fixed',
      'left-0',
      'right-0',
      'p-2',
      'pr-3',
      'overflow-hidden',
      isIphonex() ? 'bottom-[84px]' : 'bottom-[56px]'
    ]
  return classes
})
const drawMessageRef = ref()
const handleSetting = () => {
  showSetting.value = true
}
const handleMjList = () => {
  mjList.value = true
}
const handleUploadImg = () => {
  showUpload.value = true
}

function setMembers() {
  showMember.value = true
}
function handSetInput(msg: string) {
  prompt.value = msg
}
const searchParams = new URLSearchParams(window.location.search);
const params: any = {};
for (const [key, value] of searchParams) {
  params[key] = value;
}
const botid = params?.botid

const loadSuggestion = async (keywords: string) => {
  // 提示词
  const response: any = await chatbotsList(keywords)
  suggestionList.value = response
  if (response && response.length > 0) {
    if (!!botid) {
      for (let item of response) {
        for (let mp of item.Chatbots) {
          if (botid == mp.id) {
            const data = {
              id: mp.id,
              name: mp.name,
              categoryId: item.id,
              categoryName: item.name,
              uuid: Number(+uuid)
            }
            suggestionStore.updateSetting(data, +uuid)
            suggestion.value = data
            currentMessageId.value = data.id
          }
        }
      }
    }
  }
}

onMounted(async () => {
  if (!dataSources.value.length) {
  } else {
    scrollToBottom()
  }
  if (inputRef.value && !isMobile.value) inputRef.value?.focus()
  // 获取未完成的任务
  if (taskStore.getLocalTask(+uuid)) {
    const taskQueue = taskStore.getLocalTask(+uuid)
    if (taskQueue && taskQueue.messageId && taskQueue.type === 'imagine') {
      getMessageId(taskQueue.messageId, taskQueue.time)
    }
    if (
      taskQueue &&
      taskQueue.messageId &&
      (taskQueue.type === 'variation' || taskQueue.type === 'upscale')
    ) {
      if (drawMessageRef.value && drawMessageRef.value.length > 0) {
        drawMessageRef.value[0].getMessageId(
          taskQueue.messageId,
          taskQueue.type,
          taskQueue.time,
        )
      }
    }
  }

  loadSuggestion("")

})

onUnmounted(() => {
  if (loading.value) controller.abort()
})
const getMobileClass = computed<CSSProperties>(() => {
  if (isMobile.value) {
    return {
      height: 'calc(100% - 100px)'
    }
  }
  return {
    height: '100%'
  }
})
</script>

<template>
  <div class="flex flex-col w-full h-full">
    <HeaderComponent v-if="isMobile" :using-context="usingContext" @handleMembers="setMembers" @export="handleExport"
      @toggle-using-context="toggleUsingContext" />
    <main class="flex-1 overflow-hidden">
      <div id="scrollRef" ref="scrollRef" class="overflow-y-auto relative" :style="getMobileClass">
        <div id="image-wrapper" class="w-full  m-auto dark:bg-[#101014]" :class="[isMobile ? 'p-2' : 'p-4']">
          <template v-if="!dataSources.length">
            <div>
              <div v-if="false" class="text-[24px] sm:text-[18px] text-center mt-[80px] sm:mt-2 font-bold">
                midjourney 官方API ，AI绘图优秀模型
              </div>
              <div class="text-[24px] sm:text-[18px] text-center mt-[80px] sm:mt-2 font-bold">
                AI绘图优秀模型
              </div>
              <Waterfall @set-inputvalue="handSetInput" />
            </div>
          </template>
          <template v-else>
            <div class="mt-[60px]">
              <DrawMessage v-for="(item, index) of dataSources" ref="drawMessageRef" :key="index" :index="index"
                :date-time="item.dateTime" :text="item.text" :inversion="item.inversion" :error="item.error"
                :loading="item.loading" :is-limit="item.isLimit" :img-obj="item.imgObj"
                @regenerate="onRegenerateMj(index)" @delete="handleDelete(index)" @update-vip="handleUpdateVip()" />
            </div>
          </template>
        </div>
      </div>
    </main>
    <div :class="footerClass">
      <div class="w-full m-auto">
        <div class="flex sm:flex-col sm:justify-start items-center justify-between space-x-2 2xl:px-12">
          <div class="flex items-center sm:w-full">
            <HoverButton @click="handleMjList">
              <span class="text-xl text-[#4f555e] hover:text-[#3ebba2] dark:text-white">
                <SvgIcon icon="emojione-monotone:open-file-folder" />
              </span>
            </HoverButton>
            <HoverButton @click="handleSetting">
              <span class="text-xl text-[#4f555e] hover:text-[#3ebba2] dark:text-white">
                <SvgIcon icon="ri:settings-4-line" />
              </span>
            </HoverButton>
            <HoverButton @click="handleUploadImg">
              <span class="text-xl text-[#4f555e] hover:text-[#3ebba2] dark:text-white">
                <ImageMultiple16Filled class="w-[22px] h-[22px]" />
              </span>
            </HoverButton>
            <HoverButton @click="handleClear">
              <span class="text-xl text-[#4f555e] hover:text-[#3ebba2] dark:text-white">
                <Delete16Regular class="w-[22px] h-[22px]" />
              </span>
            </HoverButton>
          </div>
          <div class="flex w-full">
            <NAutoComplete v-model:value="prompt" size="large" :options="searchOptions" :render-label="renderOption">
              <template #default="{ handleInput, handleBlur, handleFocus }">
                <NInput ref="inputRef" class="flex-1 mr-2" v-model:value="prompt" type="textarea"
                  :placeholder="placeholder" :disabled="loading" :autosize="{ minRows: 1, maxRows: isMobile ? 4 : 8 }"
                  @input="handleInput" @focus="handleFocus" @blur="handleBlur" @keypress="handleEnter" />
              </template>
            </NAutoComplete>
            <NButton type="primary" color="#3dbaa1" :disabled="buttonDisabled" @click="handleSubmit(false)">
              <template #icon>
                <span class="dark:text-black">
                  <SvgIcon icon="ri:send-plane-fill" />
                </span>
              </template>
            </NButton>
          </div>
        </div>
      </div>
    </div>
    <Member v-if="showMember" v-model:visible="showMember" />
    <SettingDraw v-model:visible="showSetting" />
    <MjList v-if="mjList" v-model:visible="mjList" />
    <Upload v-if="showUpload" v-model:visible="showUpload" />
    <Permission v-if="needPermission" v-model:visible="needPermission" />
  </div>
</template>
<style lang="less">
@import url(./index.less);
</style>
