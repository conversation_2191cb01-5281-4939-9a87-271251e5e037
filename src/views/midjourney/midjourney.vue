<script lang="ts" setup>
import { ref } from 'vue';
import { DrawMessage, Waterfall } from './components'

const mj_options = ref([
    {
        id: 1,
        name: '全部',
        active: true
    },
    {
        id: 2,
        name: '绘画',
        active: false
    },
    {
        id: 3,
        name: '设计',
        active: false
    },
    {
        id: 4,
        name: '编程',
        active: false
    },
    {
        id: 5,
        name: '音乐',
        active: false
    },
    {
        id: 6,
        name: '其他',
        active: false
    }
])
const handSetInput = (msg: string) => {
    console.log(msg)
}
const handleCategoryClick = (item: any) => {
    mj_options.value.forEach((option) => {
        option.active = option.id === item.id
    })
}
</script>

<template>
    <main>
        <section>
            <span>一起开始绘图灵感之旅吧！</span>
            <div class=" mt-[24px] mb-[26px]">
                <Tabs :options="mj_options" :on-click="handleCategoryClick" :container-width="1250" />
            </div>

        </section>
        <section>
            <Waterfall @set-inputvalue="handSetInput" />
        </section>
    </main>
</template>

<style lang="less"></style>