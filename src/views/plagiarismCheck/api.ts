import { get, post } from "@/utils/request";

// 获取查重报告链接
export function fetchPlagiarismCheckReportUrl<T>(params: { id: string }) {
	return post<T>({
		url: "/api3/aiwork/papercheck/getUrl",
		data: params,
	});
}


// 获取查重平台
export function fetchPlagiarismCheckPlatforms<T>(params: any) {
	return post<T>({
		url: "/api3/aiwork/papercheck/platforms",
		data: params,
	});
}

// 获取查重记录列表
export function fetchPlagiarismCheckList<T>(params: {
	page?: number;
	pageSize?: number;
	title?: string;
	ispay?: boolean;
}) {
	return post<T>({
		url: "/api3/aiwork/papercheck/list",
		data: params,
	});
}

// 创建查重订单
export function createPaperCheckOrder<T>(params: {
	type: string;
	file?: File;
	content?: string;
	title: string;
	author: string;
}) {
	const formData = new FormData();
	formData.append("type", params.type);
	formData.append("title", params.title);
	formData.append("author", params.author);

	if (params.content) {
		formData.append("content", params.content);
	} else if (params.file) {
		formData.append("file", params.file);
	}

	return post<T>({
		url: "/api3/aiwork/order/createPaperCheck",
		data: formData,
		headers: { "Content-Type": "multipart/form-data" },
	});
}

// 粘贴文本计算专用
export function fetchPlagiarismCheckPrice<T>(params: {
	type: string;
	content?: string;
}) {
	const formData = new FormData();
	formData.append("type", params.type);

	if (params.content) {
		formData.append("content", params.content);
	}
	return post<T>({
		url: "/api3/aiwork/file/previewPaper",
		data: formData,
		headers: { "Content-Type": "multipart/form-data" },
	});
}


export function fetchPaperPreview(params: {
	url: string;
}) {
	return fetch(params.url, {
		method: 'GET',
	}).then(response => response.blob());
}
