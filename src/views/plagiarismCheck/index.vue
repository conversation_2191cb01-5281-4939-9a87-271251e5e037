<template>
	<main
		class="h-full w-full mx-auto px-[50px] py-[50px] flex flex-col items-center bg-gradient-to-r to-[#f5f7ff] via-[rgba(236,241,255,0.8858)] from-[#e5f1ff]"
	>
		<div class="max-w-[1600px]">
			<!-- tabs -->
			<div class="tabs">
				<div
					class="tab"
					v-for="tab in tabs"
					:key="tab.key"
					:class="{ active: tab.key === store.activeKey }"
					@click="store.setActiveKey(tab.key)"
				>
					{{ tab.title }}
				</div>
			</div>
			<!-- 子标签 -->
			<div
				v-if="store.activeKey === 'plagiarism-check'"
				class="flex flex-row gap-x-[16px]"
			>
				<div class="left-container bg-[#fff] rounded-[4px] p-[23px] w-[990px]">
					<NTabs
						v-model:value="subActiveKey"
						type="bar"
						animated
						class="sub-tabs"
						@update:value="handleSubTabChange"
					>
						<NTabPane
							v-for="tab in recordTabs"
							:key="tab.key"
							:name="tab.key"
							:tab="tab.title + '(' + recordCounts[tab.key] + ')'"
						>
						</NTabPane>
					</NTabs>
					<CheckRecord />
				</div>
				<div
					class="right-container rounded-[4px] flex flex-col gap-y-[16px] max-w-[285px]"
				>
					<CommonQaA />
					<div
						class="flex flex-row gap-x-[11px] px-[25px] py-[12px] bg-[#fff] items-center"
					>
						<NImage
							:src="AcademicQrcode"
							class="w-[81px] h-[81px]"
							preview-disabled
							object-fit="cover"
						/>
						<span
							class="text-[#3D3D3D] text-[14px] leading-[18px] line-clamp-3 flex-1"
						>
							扫码关注AIWork365学术官方公众号，获取更多资讯
						</span>
					</div>
					<n-image
						:src="AIWriter"
						class="w-full cursor-pointer"
						@click="handleJump(1)"
						preview-disabled
					/>
					<n-image
						:src="LowerRate"
						class="w-full cursor-pointer"
						@click="handleJump(2)"
						preview-disabled
					/>
				</div>
			</div>
			<div class="flex flex-row gap-x-[16px]" v-else>
				<div class="left-container bg-[#fff] rounded-[4px] p-[23px] w-[990px]">
					<CheckForm />
					<div class="flex flex-row justify-between">
						<span class="text-[16px]text-[#333333] leading-[46px]"
							>应付
							<span class="text-[#FF1A00] text-[24px]"
								>¥{{ computedPrice }}</span
							>
						</span>
						<div
							:class="{
								'btn-disabled': !canSubmitOrder,
								'opacity-40': !canSubmitOrder,
							}"
							class="text-[#fff] w-[150px] h-[46px] rounded-[4px] bg-gradient-to-r to-[#7E4AFF] from-[#009DFF] flex items-center justify-center cursor-pointer"
							@click="store.submitOrder()"
						>
							提交订单
						</div>
					</div>
				</div>
				<div
					class="right-container rounded-[4px] flex flex-col gap-y-[16px] max-w-[285px]"
				>
					<PlatformDetail :current-platform="store.currentPlatform" />
					<div
						class="flex flex-row gap-x-[11px] px-[25px] py-[12px] bg-[#fff] items-center"
					>
						<NImage
							:src="AcademicQrcode"
							class="w-[81px] h-[81px]"
							preview-disabled
							object-fit="cover"
						/>
						<span
							class="text-[#3D3D3D] text-[14px] leading-[18px] line-clamp-3 flex-1"
						>
							扫码关注AIWork365学术官方公众号，获取更多资讯
						</span>
					</div>
					<n-image
						:src="AIWriter"
						class="w-full cursor-pointer"
						@click="handleJump(1)"
						preview-disabled
					/>
					<n-image
						:src="LowerRate"
						class="w-full cursor-pointer"
						@click="handleJump(2)"
						preview-disabled
					/>
				</div>
			</div>
		</div>

		<!-- 文档预览模态框 -->
		<preview-modal
			:visible="store.showPreviewModal"
			@update:visible="updateModalVisible"
			title="论文预览"
		>
			<div ref="previewContainer" class="w-full h-[600px] overflow-auto"></div>
		</preview-modal>
	</main>
</template>

<script setup lang="ts">
import CheckForm from "./components/check-form.vue";
import CheckRecord from "./components/check-record.vue";
import { usePlagiarismStore } from "./store/plagiarism";
import { NImage, NTabs, NTabPane } from "naive-ui";
import PlatformDetail from "./components/platform-detail.vue";
import CommonQaA from "./components/commonQaA.vue";
import AcademicQrcode from "@/assets/images/academic-qrcode.png";
import { storeToRefs } from "pinia";
import { computed, ref, watch, nextTick } from "vue";
import PreviewModal from "./components/preview-modal.vue";
import { fetchPaperPreview } from "./api";
import { renderAsync } from "docx-preview";
import AIWriter from "@/assets/lower/ai-writer.png";
import LowerRate from "@/assets/lower/lower-rate.png";
import { router } from "@/router";
const store = usePlagiarismStore();

// 初始化时获取平台列表
store.fetchPlatforms();

const {
	previewDocUrl,
	activeName,
	textPrice,
	filePrice,
	recordTabs,
	tabs,
	subActiveKey,
	recordCounts,
	activeKey,
	form,
	uploadStatus,
	contentText,
	contentParsable,
	currentPlatform,
	previewUrl,
} = storeToRefs(store);

// 是否正在提交订单
const isSubmitting = ref(false);

const handleJump = (type: number) => {
	if (type === 1) {
		router.push({
			name: "LowerAigcRate",
			query: {
				type: "aigcAmend",
			},
		});
	} else {
		router.push({
			name: "LowerPaperSimilarityRate",
		});
	}
};

const handleSubTabChange = () => {
	nextTick(() => {
		store.loadRecords();
	});
};

watch(form.value, (newVal) => {
	console.log("form.value", newVal);
});

// 方案一：返回字符串确保两位小数（推荐）
const computedPrice = computed(() => {
	const price =
		activeName?.value === "upload" ? filePrice?.value : textPrice?.value;
	return (Number(price) || 0).toFixed(2); // 处理 NaN 情况
});
// 判断是否可以提交订单
const canSubmitOrder = computed(() => {
	// 必须选择平台
	if (!currentPlatform.value || !currentPlatform.value.type) {
		return false;
	}

	// 必须填写标题和作者
	if (!form.value.title || !form.value.author) {
		return false;
	}

	// 根据activeName判断不同的提交方式
	if (activeName.value === "upload") {
		// 上传文件方式：必须有文件且上传成功
		if (!form.value.file || uploadStatus.value !== "success") {
			return false;
		}
	} else {
		// 粘贴文本方式：必须有内容且长度足够
		if (
			!contentText.value ||
			contentText.value.length < 1000 ||
			!contentParsable.value
		) {
			return false;
		}
	}

	// 不能正在提交中
	if (isSubmitting.value) {
		return false;
	}

	return true;
});

// 文档预览相关
const previewContainer = ref(null);
// docx-preview模块
const docxModule = ref(null);

const updateModalVisible = (value: boolean) => {
	store.showPreviewModal = value;
};

// 监听预览模态框显示状态
watch(
	() => store.showPreviewModal,
	async (newVal) => {
		if (newVal && previewDocUrl.value) {
			try {
				// 获取文档内容
				const response = await fetchPaperPreview({ url: previewDocUrl.value });

				// 清空容器
				if (previewContainer.value) {
					previewContainer.value.innerHTML = "";

					// 渲染文档
					await renderAsync(response, previewContainer.value, null, {
						className: "text-docx",
						inWrapper: true,
						ignoreWidth: false,
						ignoreHeight: false,
						ignoreFonts: false,
						breakPages: true,
						ignoreLastRenderedPageBreak: true,
						experimental: true,
						trimXmlDeclaration: true,
						useBase64URL: false,
						useMathMLPolyfill: true,
						debug: false,
						renderHeaders: true,
						renderFooters: true,
						renderFootnotes: true,
						renderEndnotes: true,
						ignoreEmptyParagraphs: false,
						styleMap: {
							table: "border-collapse: collapse; width: 100%;",
							td: "border: 1px solid #ddd; padding: 8px;",
							th: "border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;",
						},
					});
				}
			} catch (error) {
				console.error("预览文档失败:", error);
			}
		}
	}
);
</script>

<style lang="less" scoped>
.tabs {
	display: flex;
	flex-direction: row;
	column-gap: 10px;

	.tab {
		width: 194px;
		height: 44px;
		background: #f0f6fa;
		position: relative;
		line-height: 44px;
		text-align: center;
		box-shadow: inset 14px 0px 8px 0px rgba(240, 246, 250, 0.3);
		border-top-left-radius: 4px;
		border-top-right-radius: 4px;
		cursor: pointer;

		&::after {
			content: "";
			position: absolute;
			top: 0;
			right: -20px;
			width: 0;
			height: 0;
			border-bottom: 44px solid #f0f6fa;
			border-right: 20px solid transparent;
		}

		&.active {
			background: #fff;
			z-index: 2;

			&::after {
				border-bottom: 44px solid #fff;
			}
		}
	}
}

.btn-disabled {
	opacity: 0.4;
	cursor: not-allowed;
}

:deep(.text-docx) {
	table {
		width: 100% !important;
	}
}
</style>
