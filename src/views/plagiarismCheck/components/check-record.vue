<template>
	<div class="check-record-list" v-if="records.length > 0">
		<div v-for="record in records" :key="record.id" class="record-item">
			<div class="record-header">
				<div class="title">{{ record.title }}</div>
				<div :class="['status', getStatusClass(record.status)]">{{ getStatusText(record.status) }}</div>
			</div>
			<div class="record-content">
				<div class="info-row">
					<div class="info-item">
						<span class="label">下单时间：</span>
						<span>{{ dayjs(record.createdAt).format('YYYY-MM-DD HH:mm:ss') }}</span>
					</div>
					<div class="info-item">
						<span class="label">查重系统：</span>
						<span>{{ record.type }}</span>
					</div>
					<div class="info-item-right">
						<span class="label">查重金额：</span>
						<span class="text-[#FF1C1C]">¥{{ record.price }}</span>
					</div>
					<div class="info-item">
						<span class="label">重复率：</span>
						<span class="rate" v-if="record.status === Status.Completed">{{ record.similar }}</span>
						<span v-else>-</span>
					</div>
					<!-- <div class="info-item">
						<span class="label">报告有效期：</span>
						<span v-if="record.status === Status.Completed">{{ record.updatedAt }}</span>
						<span v-else>-</span>
					</div> -->
				</div>
				<div class="action-row">
					<n-button class="h-[32px]" v-if="record.status === Status.Pending" style="--n-color:#21AF3D;
					--n-color-hover:#21AF3D;
					color:#FFFFFF;" @click="store.handlePay(record)">立即支付</n-button>
					<n-button class="h-[32px]" v-if="record.status === Status.Pending"
						style="--n-border: 1px solid #0E69FF;color:#0E69FF;"
						@click="store.previewDocumentWithUrl(record.key)">查看文件内容</n-button>
					<n-button class="h-[32px]" v-if="record.status === Status.Completed || record.status === Status.Processing"
						type="primary" style="--n-text-color-hover:#fff;" @click="store.handleRecheck(record)">再查一单</n-button>
					<n-button class="h-[32px]" v-if="record.status === Status.Completed"
						style="--n-border: 1px solid #0E69FF;color:#0E69FF;" @click="downloadRecord(record)">下载报告</n-button>
				</div>
			</div>
		</div>
		<div class="pagination">
			<n-pagination v-model:page="recordsPage" v-model:page-size="recordsPageSize" :item-count="recordsTotal"
				@update:page="store.setPage" @update:page-size="store.setPageSize">
				<template #prefix="{ itemCount, startIndex }">
					共 {{ itemCount }} 条
				</template>
			</n-pagination>
		</div>
	</div>
	<div v-else class="flex justify-center items-center h-full">
		<n-empty description="您的检测记录为空，快去提交论文开始检测吧~" style="--n-icon-size: 100px">
			<template #icon>
				<n-image
					:src="EmptyIcon"
					preview-disabled
					object-fit="contain"
				/>
			</template>
		</n-empty>
	</div>
</template>

<script setup lang="ts">
import { NButton, NPagination, NEmpty, NImage } from 'naive-ui'
import { usePlagiarismStore } from '../store/plagiarism';
import { storeToRefs } from 'pinia';
import { onMounted } from 'vue';
import dayjs from 'dayjs';
import { statusMap, Status, statusClassMap } from '../types';
import EmptyIcon from '@/assets/images/empty-icon.png';
const store = usePlagiarismStore();

const { records, recordsPage, recordsPageSize, recordsTotal } = storeToRefs(store)

onMounted(() => {
	store.loadRecords();
})
const downloadRecord = (record: any) => {
	// store.getDownloadUrl(record.id).then((url) => {
	// 	window.open(url)
// })
	window.open(record.zipUrl)
}
const getStatusText = (status: string) => {
	return statusMap[status] || status
}
const getStatusClass = (status: string) => {
	return statusClassMap[status] || status
}
</script>

<style scoped lang="less">
.check-record-list {
	width: 100%;

	.record-item {
		height: 174px;
		background: #FFFFFF;
		border-radius: 4px;
		margin-bottom: 16px;
		border: 1px solid #0E69FF;

		.record-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10px 16px;
			background: #E2EDFF;
			border-radius: 4px;
			margin-bottom: 16px;

			.title {
				color: #0E69FF;
				font-size: 16px;
				font-weight: 500;
			}

			.status {
				font-size: 14px;

				&.pending {
					color: #FF641C;
				}

				&.checking {
					color: #0E69FF;
				}

				&.completed {
					color: #138C42;
				}

				&.failed {
					color: #FF1A00;
				}
			}
		}

		.record-content {
			.info-row {
				display: flex;
				flex-wrap: wrap;
				gap: 6px;
				margin-bottom: 16px;
				padding-left: 12px;
				padding-right: 28px;

				.info-item-right {
					flex: 1;
					text-align: right;
				}

				.info-item {
					width: 35%;

					.label {
						color: #666666;
					}

					.rate {
						color: #FF1A00;
					}
				}
			}

			.action-row {
				display: flex;
				gap: 16px;
				padding-left: 13px;
				justify-content: flex-start;
			}
		}
	}

	.pagination {
		margin-top: 24px;
		display: flex;
		justify-content: flex-end;
	}
}
</style>
