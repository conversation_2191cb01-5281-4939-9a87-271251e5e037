<template>
	<div class="system-card-container" :class="selected ? 'system-card-selected' : ''" @click="handleSelect">
		<div v-if="platform.selected"
			class="w-[62px] h-[22px] z-[1] text-center leading-[22px] absolute left-0 top-[-11px] bg-gradient-to-r from-[#0EABFF] to-[#966CFF] rounded-tl-[6px] rounded-br-[6px] text-[12px] text-[#fff]">
			热门首选
		</div>
		<div
			class="absolute border-tr-[6px] right-0 top-0 w-0 h-0 border-t-[35px] border-t-[#8157FF] border-l-[35px] border-l-transparent"
			v-if="selected">
			<div class="absolute top-[-30px] right-[4px] text-white">
				<IconCheckOne class="w-3 h-3" />
			</div>
		</div>
		<div class="system-card-brand">
			<NImage :src="platform.icon" preview-disabled class="h-[22px]" object-fit="contain" />
		</div>
		<!-- 实现一条divider -->
		<div class="border-b border-gray-200 mb-[14px]"></div>
		<div class="user-select-none text-[13px] font-normal leading-normal tracking-normal text-[#666666] line-clamp-2">
			{{ platform.desc }}
		</div>

		<div class="mt-auto flex flex-row justify-between">
			<span class="text-[14px] font-bold text-[#333333]">{{ platform.priceDesc }}</span>
			<div class="flex gap-x-[4px]">
				<span v-for="tag in platform.tags" :key="tag"
					class="flex items-center px-[2px] text-[12px] leading-[12px] rounded-[2px] bg-[#F0E6FF] text-[#8157FF]">
					{{ tag }}
				</span>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { NImage } from "naive-ui";
import { PlagiarismCheckPlatform } from "../types";
import { usePlagiarismStore } from "../store/plagiarism";
import { computed } from "vue";

interface Props {
	platform: PlagiarismCheckPlatform;
}

const store = usePlagiarismStore();
const props = defineProps<Props>();

const handleSelect = () => {
	store.setCurrentPlatform(props.platform);
};
const selected = computed(
	() => store.currentPlatform?.type === props.platform.type
);
</script>

<style lang="less" scoped>
.system-card-container {
	width: calc(25% - 7px);
	height: auto;
	aspect-ratio: 228/168;
	border-radius: 4px;
	background: linear-gradient(180deg, #f9f5ff 0%, #f7f3ff 100%);
	padding: 12px;
	flex-shrink: 0;
	position: relative;
	display: flex;
	flex-direction: column;
	cursor: pointer;
	transition: all 0.3s ease;
	box-sizing: border-box;
	position: relative;

	&:hover {
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(129, 87, 255, 0.1);
	}
}

.system-card-selected {
	border-radius: 4px;
	border-image-slice: 1;
	transition: all 0.3s ease;

	&::after {
		content: "";
		position: absolute;
		inset: 0;
		border-radius: 4px;
		padding: 2px;
		background: linear-gradient(270deg, #8157ff 0%, #a53eff 100%);
		-webkit-mask: linear-gradient(#fff 0 0) content-box,
			linear-gradient(#fff 0 0);
		-webkit-mask-composite: xor;
		mask-composite: exclude;
		pointer-events: none;
	}
}

.system-card-brand {
	display: flex;
	justify-content: center;
	margin-top: 21px;
	margin-bottom: 18px;
}
</style>
