<template>
	<NForm :rules="rules" label-placement="left">
		<!-- 自定义表单组件 -->
		<div class="flex flex-col">
			<div>
				<div class="text-[14px] text-[#3D3D3D]">
					<span class="text-[14px] text-[#FF5100]">*</span>
					查重系统
				</div>
			</div>
			<div class="flex flex-row">
				<SystemCard class="mr-[9px] last:mr-0 mt-[18px]" v-for="platform in platforms" :key="platform.type"
					:platform="platform" />
			</div>
			<div class=" text-[#3D3D3D] text-[13px] bg-[#F2F5F9] h-[45px] leading-[45px] mt-[28px] px-[10px] relative"
				v-if="currentPlatform?.chargeDesc">
				<!-- 三角形指示器 - 两个选项的情况 -->
				<!-- <div v-if="platforms.length === 2 && currentPlatform"
					class="absolute top-[-20px] w-0 h-0 border-[10px] border-transparent border-t-[#F2F5F9] transform rotate-180 transition-all duration-300"
					:style="{
						left: currentPlatform.type === platforms[0]?.type ? '15%' : '65%'
					}">
				</div> -->

				<!-- 三角形指示器 - 四个选项的情况 -->
				<div v-if="platforms.length === 4 && currentPlatform"
					class="absolute top-[-20px] w-0 h-0 border-[10px] border-transparent border-t-[#F2F5F9] transform rotate-180 transition-all duration-300"
					:style="{
						left: getTrianglePosition(currentPlatform.type)
					}">
				</div>
				<span class="text-[14px] text-[#FF5100]">*</span>
				收费说明：
				<span class=" text-[#848484] text-[13px]">
					{{ currentPlatform?.chargeDesc }}
				</span>
			</div>

		</div>
		<div class="border-b border-gray-200 my-[24px]"></div>
		<div class="flex flex-row gap-x-[16px] justify-between">
			<NFormItem label="论文题目" required class="w-[55%]">
				<NInput v-model:value="form.title" placeholder="请输入论文题目" show-count :maxlength="30"
					@update:value="(val) => store.updateForm('title', val)" />
			</NFormItem>
			<NFormItem label="论文作者" required class="w-[40%]">
				<NInput v-model:value="form.author" placeholder="请输入论文作者" show-count :maxlength="10"
					@update:value="(val) => store.updateForm('author', val)" />
			</NFormItem>
		</div>
		<NFormItem label="论文摘要" path="abstract" required class="w-[100%]">
			<SubmitContainer />
		</NFormItem>
	</NForm>
</template>

<script lang="ts" setup>
import { NForm, NFormItem, FormRules, NInput } from 'naive-ui';
import SystemCard from './system-card.vue';
import SubmitContainer from './submit-container.vue';
import { usePlagiarismStore } from '../store/plagiarism';
import { storeToRefs } from 'pinia';

const store = usePlagiarismStore();
const { platforms, form, currentPlatform } = storeToRefs(store);

const rules: FormRules = {
	content: {
		required: true,
		message: '请选择查重系统',
		trigger: ['blur', 'input'],
	},
	title: {
		required: true,
		message: '请输入论文题目',
		trigger: ['blur', 'input'],
	},
	author: {
		required: true,
		message: '请输入论文作者',
		trigger: ['blur', 'input'],
	},
};
// 获取三角形位置的函数 - 四个选项的情况
const getTrianglePosition = (type: string) => {
	if (!platforms.value || platforms.value.length !== 4) return 'calc(25% - 10px)';

	const index = platforms.value.findIndex(p => p.type === type);
	switch (index) {
		case 0: return '10%';  // 第一个位置
		case 1: return '35%';  // 第二个位置
		case 2: return '60%';  // 第三个位置
		case 3: return '85%';  // 第四个位置
		default: return '25%';
	}
};
</script>

<style lang="less" scoped></style>
