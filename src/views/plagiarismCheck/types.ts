// 定义剽窃检查平台的接口
export interface PlagiarismCheckPlatform {
	title: string; // 平台标题
	type: string; // 平台类型
	desc: string; // 平台描述
	subDesc: string; // 平台子描述
	icon: string; // 平台图标
	priceDesc: string; // 价格描述
	price: number; // 价格
	selected: boolean; // 是否被选中
	tags: string[]; // 标签数组
	chargeDesc?: string;
	detail?: {
		title: string;
		list: Array<{
			title: string;
			content: string;
			icon?: string;
			icons?: string[];
			subContent?: string;
		}>;
	};
}

export type UploadStatus =
	| "idle"
	| "uploading"
	| "success"
	| "error"
	| "wordCountError";

/**
 * 剽窃检查结果接口
 */
export interface PlagiarismCheckResult {
	total: number; // 结果总数
	pending: number; // 未支付数量
	count: number; // 结果总数
	timeout: number; // 超时时间
	rows: Array<{
		id: string; // 唯一标识符
		userId: number; // 用户ID
		orgUserId: number; // 组织用户ID
		tempId: string | null; // 临时ID
		title: string; // 文档标题
		content: string | null; // 文档内容
		author: string; // 作者
		type: string; // 文档类型
		status: number; // 检查状态
		key: string; // 检查密钥
		zipUrl: string | null; // ZIP文件URL
		price: string; // 检查价格
		words: string; // 字数
		identify: string; // 识别码
		ispay: boolean; // 是否已支付
		task_id: string | null; // 任务ID
		similar: string | null; // 相似度
		payDate: string | null; // 支付日期
		createdAt: string; // 创建时间
		updatedAt: string; // 更新时间
		deletedAt: string | null; // 删除时间
	}>;
}

export enum Status {
	Pending = 0,
	Processing = 1,
	Completed = 2,
	Failed = 3,
}

export const statusMap = {
	[Status.Pending]: "待支付",
	[Status.Processing]: "检测中",
	[Status.Completed]: "检测完成",
	[Status.Failed]: "失败",
};

export const statusClassMap = {
	[Status.Pending]: "pending",
	[Status.Processing]: "processing",
	[Status.Completed]: "completed",
	[Status.Failed]: "failed",
};

/**
 * 论文详情接口，包含论文基本信息、模板信息和增值服务列表
 */
export interface PaperDetail {
	paper?: Paper;
	template?: Template;
	subjoins?: Subjoin[];
}

/**
 * 增值服务接口，描述论文相关的附加服务
 */
export interface Subjoin {
	id: number;                  // 服务ID
	title: string;               // 服务标题，如"任务书"、"开题报告"
	price: string;               // 当前价格
	originalPrice: string;       // 原价
	power: number;               // 所需算力
	words: number | null;        // 字数，可为空
	tag: string | null;          // 标签，如"导师看到都称赞"
	description: string | null;  // 描述信息
	exampleUrl: string | null;   // 示例图片URL
	type: string;                // 服务类型，如"books"、"proposal"
	templateId: number | null;   // 关联的模板ID
	isGenerate: boolean;         // 是否可生成
	createdAt: string;           // 创建时间
	updatedAt: string;           // 更新时间
	deletedAt: null;             // 删除时间
	isSelect?: boolean;          // 是否被选中
}

/**
 * 论文模板接口，包含模板名称和扩展信息
 */
export interface Template {
	name: string;                // 模板名称，如"论文助手"
	extend: Extend;              // 模板扩展信息
}

export interface Extend {
	icons: string[]; // 左侧图标列表
	chapter: Chapter[]; // 论文功能章节
	content: Content[]; // 文字描述
	examples: string[]; // 案例图片链接
}

export interface Content {
	label: string; // 例如："期刊论文"
	values: string[]; // 例如：["优先近5年", "权威期刊"]
}

export interface Chapter {
	title: string; // 章节标题，例如："封面"、"中英文摘要"等
	icon?: string; // 章节图标，可选
}

export interface Paper {
	id: number; // 论文ID
	title: string; // 论文标题
	list: string[]; // 论文信息列表，例如：["论文助手", "本科", "8000字", "Pro联网版"]
	power: number; // 所需算力
	price: number // 价格
}