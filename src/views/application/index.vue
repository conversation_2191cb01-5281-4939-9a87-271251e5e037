<script setup lang="ts">
import { Createbot } from '@/views/search/types';
import { NImage, NIcon, NModal, NCard, NSkeleton } from 'naive-ui'
import { ref } from 'vue';
import { useRouter } from 'vue-router'
import Crown3 from '@/assets/images/crown3.png'
const router = useRouter()
interface Props extends Partial<Createbot> {
	width: number;
	title: string;
	description: string;
	imgSrc: string;
	count?: number | null;
	showUnCollect?: boolean;
	type: string;
	free?: boolean;
}
interface Emit {
	(ev: 'delete-card', item: Props): void
}
const showModal = ref(false);
const props = withDefaults(defineProps<Props>(), {
	width: 0,
	id: null,
	categoryId: null,
	title: '',
	description: '',
	imgSrc: '',
	count: null,
	showUnCollect: false,
	free: true,
})
const emit = defineEmits<Emit>()
const current = ref()
const handleClick = () => {
	if (props.href) {
		router.push(props.href)
	} else {
		if (props.type === 'app') {
			router.push(`/apps/${props.categoryId}?appid=${props.id}`)
		} else if (props.type === 'long') [
			router.push(`/apps/${props.categoryId}?appid=${props.id}&type=long`)
		]
	}
}
const handleUnCollect = (event: Event) => {
	event.stopPropagation();
	showModal.value = true
}
const handlePosClick = () => {
	emit('delete-card', props)
	showModal.value = false;
}
const handleNegClick = () => {
	showModal.value = false;
}

</script>
<template>
	<div v-if="id && title"
		class="card-container relative flex flex-row gap-x-[20px] justify-start items-center rounded-[12px] pl-[28px] pr-[28px] cursor-pointer 2xl:w-[calc(25%-18px)] xl:w-[calc(33%-14px)] lg:w-[calc(50%-13px)] md:w-[100%] sm:w-[100%]"
		@click="handleClick">
		<NImage preview-disabled class=" w-[60px] h-[60px] object-fill" :src="imgSrc" />
		<div class="card-content-container pt-[5px] pb-[5px] gap-y-[9px] flex flex-1 flex-col justify-start">
			<div class=" text-[16px] text-[#3d3d3d] leading-[21px] title" :alt="title" v-replace>{{ title }}
				<template v-if="type === 'long'">
					<span
						class=" bg-[#CBDFFF] leading-[21px] text-[12px] text-[#0E69FF] rounded-tl-[8px] rounded-br-[8px] px-[7px] py-[2px]">长文</span>
				</template>
			</div>
			<div class=" text-[14px] text-[#676767] leading-[18px] desc" :alt="title" v-replace>{{ description }}</div>
		</div>
		<!-- 角标 -->
		<div v-if="count && free"
			class="corner-mark absolute top-0 right-0 h-[35px] min-w-[100px] bg-[#ff6d6d] bg-opacity-5 rounded-bl-[12px] rounded-tr-[12px]">
			<span class=" text-[14px] text-[#ff5100] leading-[35px] pl-[16px] pr-[11px]">最近生成{{ count
				}}次</span>
		</div>
		<div v-else-if="!free"
			class="corner-mark absolute top-0 right-0 h-[35px] min-w-[100px] bg-gradient-to-r to-[#6BA1FF] from-[#9E83FF] rounded-bl-[12px] rounded-tr-[12px] flex flex-row items-center justify-center">
			<NImage :src="Crown3" class=" w-[12px] h-[10px]" />
			<span class=" text-[14px] text-[#fff] leading-[35px] pl-[5px]">会员专属</span>
		</div>

		<div v-if="showUnCollect" class=" absolute bottom-[-20px] right-[43px] py-[12px] px-[20px]
            w-[118px] uncollect flex flex-row items-center justify-center
            bg-[#ffffff] rounded-[4px]" @click="handleUnCollect">
			<NIcon>
				<IconDelete class="text-[#FF5100]" />
			</NIcon>
			<div class=" pl-1 text-[#FF5100] text-[14px] w-full">移除收藏</div>
		</div>
		<n-modal v-model:show="showModal">
			<n-card style="width: 244px;" :bordered="false" size="huge" role="dialog" aria-modal="true">
				<template #default>
					<div class=" text-[14px] text-[#3D3D3D] text-center">确认移除该收藏吗？</div>
				</template>
				<template #footer>
					<div class=" w-full flex flex-row gap-x-[10px]">
						<div class=" bg-[#53A0FF] text-[#ffffff] text-[12px] w-[94px] h-[36px] flex justify-center items-center cursor-pointer"
							@click="handlePosClick">
							确认移除</div>
						<div class=" bg-[#E5E5E5] text-[#6D6D6D] text-[12px] w-[94px] h-[36px] flex justify-center items-center cursor-pointer"
							@click="handleNegClick">
							再想想</div>
					</div>
				</template>
			</n-card>
		</n-modal>
	</div>
	<NSkeleton v-else :width="width" height="130px"></NSkeleton>
</template>
<style lang="less" scoped>
@import './index.less';

:deep(.n-card) {
	> :deep(.n-card__content) {
		padding-top: 24px !important;
		padding-bottom: 14px !important;
	}
}
</style>
