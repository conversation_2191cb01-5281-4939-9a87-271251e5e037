<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { NSplit, NImage, NIcon, useMessage } from 'naive-ui'
import { useRequest } from 'vue-hooks-plus'
import { fetchChatAPIProcess, riskControl, getAppDetailByAppId, getHistoryList, collectApp, unCollectApp, getCollectedCreatebotList, generateLongArticleOutline, generateLongArticleContent, getLongArticleOutlineByMessageId, generateLongArticleOutline2, fetchChatAPIProcess2, generatePPT } from '@/chatgpt'
import { computed, ref, watch } from 'vue'
import { CreateBot } from './types'
import { captchaInit } from '@/utils/functions'
import { useAuthStore } from '@/store'
import DynamicForm from '@/views/application/scenedetail/components/DynamicForm.vue'
import MarkdownArea from '@/views/application/scenedetail/components/MarkdownArea.vue'
import { getUser } from '@/store/modules/auth/helper'
import { useContent } from '@/hooks/useContent'
import { SelfRequestCode } from '@/utils/request'
import { useGpt, useLongContentGpt } from '@/hooks/useGpt'
import GlobalFloat from "@/components/common/GlobalFloat/index.vue";
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { replaceText } from '@/plugins/directive';

const { isMobile, isPC, isIpad } = useBasicLayout()

interface ChatProps {
	text: string,
	inversion: boolean,
	loading: boolean,
	uuid: number,
	isCompute: boolean,
	isLimit: boolean,
	error?: boolean
}
interface CollectApp {
	id: number;
	name: string;
	profile: string;
	href: null;
	welcome_message: string;
	hot_num: number;
	type: string;
	free: boolean;
	createdAt: string;
	categoryId: number;
}
const route = useRoute()
const { id } = route.params
const { appid, type = "app" } = route.query as unknown as { appid: string, type: string }
const curId = ref(Number(appid))
const message = useMessage()
const currentApps = ref<Partial<CreateBot>>({})
const $router = useRouter();

const dataSources = ref<any[]>([])

const extraDataSources = ref("")

const historyData = ref<any[]>([])
const collectData = ref<CollectApp[]>([])
const contentLoading = ref(false)
const openLongReply = import.meta.env.VITE_GLOB_OPEN_LONG_REPLY === 'true'
const markdownAreaRef = ref<any>()
let uuid: number;
const user = getUser()
let controller = new AbortController()
const authStore = useAuthStore()
// const outline = ref<string>("")
const outlineId = ref<number | null>(null)
const ispptGenerated = ref(false)
const messageId = ref<number | string | null>(null)
const deepseek = ref(false)



const { increaseContent, insertContent, clearContentList, clearExtraContentList, insertRawContent, incerateExtraContent, insertExtraContent } = useContent()

useRequest(() => getAppDetailByAppId<CreateBot>({ id: appid }), {
	onSuccess: (data) => {
		currentApps.value = data
	}
})

const { run: runGetHistoryList } = useRequest(() => getHistoryList<any>({ createbotId: appid }), {
	ready: user.type !== 'temp',
	onSuccess: (data) => {
		historyData.value = data?.rows;
	}
})

const { run: runCollectApp } = useRequest<{ status: Boolean }>(() => collectApp({
	id: appid
}), {
	ready: user.type !== 'temp',
	manual: true,
	onSuccess: (data) => {
		if (data?.status) {
			message.success('收藏成功')
			runGetCollectedCreatebotList()
		}
	}
})
const { run: runGeneratePPT } = useRequest(generatePPT, {
	manual: true,
	onSuccess: (res, params) => {
		const { href } = res
		// window.location.href = href
		window.open(href, '_blank')
		ispptGenerated.value = true
	},
	onError: (error) => {
		message.error(error.message || error?.errmsg || '生成失败')
	}
})



const { run: runUnCollectApp } = useRequest<{ status: Boolean }>(() => unCollectApp({
	id: appid
}), {
	manual: true,
	onSuccess: (data) => {
		if (data?.status) {
			message.success('取消收藏成功')
			runGetCollectedCreatebotList()
		}
	}
})
const { run: runGetCollectedCreatebotList } = useRequest<CollectApp[]>(() => getCollectedCreatebotList({
	id: appid
}), {
	ready: !!appid && user.type !== 'temp',
	onSuccess: (data) => {
		collectData.value = data
	}
})

const { run: runGenerateLongArticleOutline, loading: outlineLoading, streamingText: outline, cancel: outlineCancel } = useGpt({
	api: generateLongArticleOutline2,
	onSuccess: (data: string) => {
		try {
			const lines = data.trim().split('\n');
			const { outlineId: otid } = JSON.parse(lines[0]);
			outlineId.value = otid
			outlineCancel();
		} catch (error) {
			console.error(error)
		} finally {
			outlineCancel();
		}
	},
	onError: (error) => {
		if (error.errcode === SelfRequestCode.NeedPay) {
			window.$aiwork.openRecharge?.({ type: "ai" });
			message.error('余额不足，请充值')
			return
		}
		outlineCancel();
		message.error('生成失败')
	}
})
// const {} = useGpt({
//     api: fetchChatAPIProcess,


// })

// const { run: runGenerateLongArticleOutline, loading: outlineLoading, cancel: cancelGenerateLongArticleOutline2 } = useRequest((params: any) =>
//     generateLongArticleOutline2({
//         createbotId: params.createbotId,
//         formData: params.formData,
//         onDownloadProgress: ({ event }) => {
//             const xhr = event.target
//             const { responseText } = xhr
//             try {
//                 const chunkArr = responseText.trim().split('\n') as any[]
//                 outline.value = chunkArr.map(item => JSON.parse(item).text || {}).join('').replace('[object Object]', '')
//                 const lastItem = chunkArr.at(-1)
//                 const isCompute = lastItem?.detail?.choices?.[0]?.finish_reason;
//                 if (isCompute === 'stop') {
//                     contentLoading.value = false;
//                     cancelGenerateLongArticleOutline2();
//                 }
//             } catch (error) {
//                 console.log(error);
//             }
//         }
//     }),
//     {
//         manual: true,
//         onSuccess: (data: string) => {
//             try {
//                 const lines = data.trim().split('\n');
//                 const { outlineId: otid } = JSON.parse(lines[0]);
//                 outlineId.value = otid
//             } catch (error) {
//                 console.error(error)
//             }
//         },
//         onError: (error) => {
//             if (error.errcode === SelfRequestCode.NeedPay) {
//                 window.$aiwork.openRecharge?.({ type: "ai" });
//                 message.error('余额不足，请充值')
//                 return
//             }
//             message.error('生成失败')
//         }
//     }
// )


const generateContentLoading = ref(false)
// const { run: runGetLongArticleOutlineByMessageId, cancel } = useRequest(getLongArticleOutlineByMessageId, {
//     manual: true,
//     pollingInterval: 5000,
//     onSuccess: (data: { status?: 'success' | 'pending' | 'fail' | 'queue', content?: string }) => {
//         if (data?.status === 'success') {
//             insertRawContent(data.content)
//             cancel()
//             generateContentLoading.value = false
//         } else if (data?.status === 'fail') {
//             message.error('生成失败')
//             cancel()
//             generateContentLoading.value = false
//         } else if (data?.status === 'queue') {
//             message.info('正在队列中')
//         } else {
//             message.info('正在生成中')
//         }
//     },
//     onError: (error) => {
//         // if (error.errcode === SelfRequestCode.NeedPay) {
//         //     window.$aiwork.openRecharge?.({ type: "ai" });
//         //     message.error('余额不足，请充值')
//         //     return
//         // }
//         message.error('生成失败')
//     }
// })

const { run: runGenerateLongArticleContent, streamingText: longContent, loading: generateLongArticleContentLoading, cancel: generateLongArticleContentCancel } = useLongContentGpt({
	api: fetchChatAPIProcess2,
	onSuccess: () => {
	},
	onError: () => {
		message.error('生成失败')
	}
})

const handleGenerateLongContent = () => {
	markdownAreaRef.value.handleCreateNewFile()
	runGenerateLongArticleContent({
		outlineId: outlineId.value as number,
		// outline: outline.value
	})
}
// const runGenerateLongArticleContent = async (values: any[]) => {
//     debugger
//     if (contentLoading.value) return;
//     controller = new AbortController()
//     uuid = Date.now()
//     dataSources.value = []
//     contentLoading.value = true
//     clearContentList();
//     try {
//         const fetchChatAPIOnce = async (captchaData = {}) => {
//             await fetchChatAPIProcess2<any>({
//                 data: {
//                     ...values,
//                 },
//                 signal: controller.signal,
//                 onDownloadProgress: ({data}) => {
//                     console.log('data: ', data);
//                     // const xhr = event.target
//                     // const { responseText } = xhr
//                     // let isLimit = false
//                     // try {
//                     //     if (responseText) {
//                     //         isLimit = JSON.parse(responseText)?.data?.isLimit
//                     //     }
//                     // } catch (error) { }

//                     // try {
//                     //     const chunkArr = responseText.split('\n') as any[]
//                     //     insertContent(chunkArr.map(item => JSON.parse(item) || {}))
//                     //     const lastItem = chunkArr.at(-1)
//                     //     const isCompute = lastItem?.detail?.choices?.[0]?.finish_reason;
//                     //     if (isCompute === 'stop') {
//                     //         contentLoading.value = false;
//                     //         controller.abort()
//                     //     }
//                     // } catch (error) {
//                     //     console.log(error, responseText);
//                     // }
//                 },
//             })
//         }
//         const isCaptcha: any = await riskControl();
//         if (isCaptcha.captcha) {
//             captchaInit(authStore.captchaAppId, async function (captchaData: any) {
//                 if (captchaData && captchaData.randstr) {
//                     try {
//                         await fetchChatAPIOnce(captchaData)
//                     } catch (error) {
//                         failHandler(error)
//                     }
//                 } else {
//                     failHandler(captchaData)
//                 }
//             })
//         } else {
//             try {
//                 await fetchChatAPIOnce()
//             } catch (error) {
//                 if ((error as unknown as Error).message === 'canceled') {
//                     return cancelHandler()
//                 }
//                 // else if ((error as unknown as any).errcode === SelfRequestCode.NeedPay) {
//                 //     window.$aiwork.openRecharge?.({ type: "ai" });
//                 //     message.error('余额不足，请充值')
//                 // }
//                 failHandler(error)
//             }
//         }
//     } catch (error: any) {
//         failHandler(error)
//     }
//     finally {
//         contentLoading.value = false
//     }
// }

// const { run: runGenerateLongArticleContent } = useRequest(generateLongArticleContent, {
//     manual: true,
//     onSuccess: ({ messageId: mid }: { messageId: string }) => {
//         messageId.value = mid
//         runGetLongArticleOutlineByMessageId({
//             messageId: mid
//         })
//         generateContentLoading.value = true
//     },
//     onError: (error) => {
//         // if (error.errcode === SelfRequestCode.NeedPay) {
//         //     window.$aiwork.openRecharge?.({ type: "ai" });
//         //     message.error('余额不足，请充值')
//         //     return
//         // }
//         message.error('生成失败')
//     }
// })
// const handleGenerateLongContent = () => {
//     runGenerateLongArticleContent({
//         outlineId: outlineId.value as number,
//         // outline: outline.value
//     })
// }

const isCollected = computed(() => {
	return collectData.value?.some(item => item.id === Number(appid))
})

const updateChat = ({ loading, ...rest }: ChatProps) => {
	const index = dataSources.value.findIndex(item => item.uuid === uuid)
	if (index === -1) {
		dataSources.value.push(rest)
	} else {
		dataSources.value[index] = rest
	}
	if (!loading) {
		contentLoading.value = false
	}
}

const cancelHandler = () => {
	controller.abort()
	contentLoading.value = false
}
const failHandler = (error: any) => {
	const errorMessage = error?.message
	const isLimit = error.data?.isLimit ? error.data?.isLimit : false
	updateChat({
		text: errorMessage,
		inversion: false,
		loading: false,
		uuid,
		error: true,
		isLimit,
		isCompute: false
	})
}

// 这个函数用来实现利用aigc扩写/重写/简写/续写文章
// 扩写/重写/简写是把选中的内容当作prompt发送给aigc服务器,服务器生成后续内容,然后再插入到原文中
// 续写是把光标位置前一句话发送给aigc服务器,服务器生成后续内容,然后再插入到原文中
const aigcContent = async (content: string, type: any) => {
	if (contentLoading.value) return;
	clearExtraContentList();
	controller = new AbortController()
	uuid = Date.now()
	contentLoading.value = true
	try {
		const fetchChatAPIOnce = async (captchaData = {}) => {
			contentLoading.value = true
			await fetchChatAPIProcess<any>({
				prompt: content,
				...captchaData,
				createbotId: Number(curId.value),
				signal: controller.signal,
				type,
				onDownloadProgress: ({ event }) => {
					const xhr = event.target
					const { responseText } = xhr
					let isLimit = false
					try {
						if (responseText) {
							isLimit = JSON.parse(responseText)?.data?.isLimit
						}
					} catch (error) { }

					try {
						const chunkArr = responseText.split('\n') as any[]
						insertExtraContent(chunkArr.map(item => JSON.parse(item) || {}))
						const lastItem = chunkArr.at(-1)
						const isCompute = lastItem?.detail?.choices?.[0]?.finish_reason;
						if (isCompute === 'stop') {
							contentLoading.value = false;
							controller.abort()
						}
					} catch (error) {
						console.log(error);
					}
				},
			})
		}
		const isCaptcha: any = await riskControl('apps');
		if (isCaptcha.captcha) {
			captchaInit(authStore.captchaAppId, async function (captchaData: any) {
				if (captchaData && captchaData.randstr) {
					try {
						await fetchChatAPIOnce(captchaData)
					} catch (error) {
						failHandler(error)
					}
				} else {
					failHandler(captchaData)
				}
			})
		} else {
			try {
				await fetchChatAPIOnce()
			} catch (error) {
				if ((error as unknown as Error).message === 'canceled') {
					return cancelHandler()
				}
				// else if ((error as unknown as any).errcode === SelfRequestCode.NeedPay) {
				//     window.$aiwork.openRecharge?.({ type: "ai" });
				//     message.error('余额不足，请充值')
				// }
				failHandler(error)
			}
		}
	} catch (error: any) {
		failHandler(error)
	}
	finally {
		contentLoading.value = false
	}
}


const createForm = async (values: any[], isDeepseek = false) => {
	if (contentLoading.value) return;
	controller = new AbortController()
	uuid = Date.now()
	dataSources.value = []
	contentLoading.value = true
	clearContentList();
	try {
		deepseek.value = isDeepseek
		const fetchChatAPIOnce = async (captchaData = {}) => {
			contentLoading.value = true
			await fetchChatAPIProcess<any>({
				prompt: '',
				...captchaData,
				createbotId: Number(curId.value),
				formData: values,
				signal: controller.signal,
				isDeepseek,
				// responseType: 'stream',
				onDownloadProgress: ({ event }) => {
					const xhr = event.target
					const { responseText } = xhr
					let isLimit = false
					try {
						if (responseText) {
							isLimit = JSON.parse(responseText)?.data?.isLimit
						}
					} catch (error) { }

					try {
						const chunkArr = responseText.split('\n') as any[]
						insertContent(chunkArr.map(item => JSON.parse(item) || {}))
						const lastItem = chunkArr.at(-1)
						const isCompute = lastItem?.detail?.choices?.[0]?.finish_reason;
						if (isCompute === 'stop') {
							contentLoading.value = false;
							controller.abort()
						}
					} catch (error) {
						console.log(error, responseText);
					}
				},
			})
		}
		const isCaptcha: any = await riskControl('apps');
		if (isCaptcha.captcha) {
			captchaInit(authStore.captchaAppId, async function (captchaData: any) {
				if (captchaData && captchaData.randstr) {
					try {
						await fetchChatAPIOnce(captchaData)
					} catch (error) {
						failHandler(error)
					}
				} else {
					failHandler(captchaData)
				}
			})
		} else {
			try {
				await fetchChatAPIOnce()
			} catch (error) {
				if ((error as unknown as Error).message === 'canceled') {
					return cancelHandler()
				}
				// else if ((error as unknown as any).errcode === SelfRequestCode.NeedPay) {
				//     window.$aiwork.openRecharge?.({ type: "ai" });
				//     message.error('余额不足，请充值')
				// }
				failHandler(error)
			}
		}
	} catch (error: any) {
		failHandler(error)
	}
	finally {
		contentLoading.value = false
	}
}
const handleCreateForm = async (values: any[], isDeepseek?: boolean) => {
	markdownAreaRef.value.handleCreateNewFile()
	await createForm(values, isDeepseek)
	console.log('创作结束');
	runGetHistoryList()
}
const handleCollect = (isCollect: boolean) => {
	if (isCollect) {
		runUnCollectApp()
	} else {
		runCollectApp()
	}
}
const handleStop = () => {
	if (contentLoading.value) {
		controller.abort()
		contentLoading.value = false
	} else if (generateLongArticleContentLoading.value) {
		generateLongArticleContentCancel()
	}
}
const handleExpand = (content: string, type: any) => {
	extraDataSources.value = ""
	aigcContent(content, type)
}

const handleRewrite = (content: string, type: any) => {
	extraDataSources.value = ""
	aigcContent(content, type)
}
const handleAbbreviate = (content: string, type: any) => {
	extraDataSources.value = ""
	aigcContent(content, type)
}
const handleContinue = (content: string, type: any) => {
	extraDataSources.value = ""
	aigcContent(content, type)
}

// 计算屏幕宽度下 split的数值
// 1920 分辨率下 max 078 min 022
// 1366 分辨率下 max 0.65 min 0.35
// 1024 分辨率下 max 0.6 min 0.4
// 希望是动态计算得出的
const splitValue = computed(() => {
	const width = window.innerWidth
	let max = 0.4
	let min = 0.4
	let defaultSize = 0.4
	if (width >= 1920) {
		max = 0.78
		min = 0.22
	} else if (width >= 1366) {
		max = 0.65
		min = 0.35
	} else {
		max = 0.6
		min = 0.4
	}
	console.log({
		max,
		min,
		defaultSize
	});

	return {
		max,
		min,
		defaultSize
	}
})



const handleGeneratePPT = (id: string) => {
	if (!outlineId.value) {
		message.error('请先生成大纲')
		return
	}
	if (!ispptGenerated.value) {
		runGeneratePPT({
			chatId: String(outlineId.value || ''),
			templateId: id
		})
	} else {
		// 已生成PPT的情况下 直接跳转workspace home
		$router.push('/ppt/workspace/home')
	}
}

</script>
<template>
	<div class=" h-[100%] pt-[85px] pb-[20px] px-[20px]  bg-[#F5F7FF] sm:pt-4 sm:h-auto">
		<NSplit v-if="isPC" direction="horizontal" class="h-full w-full rounded-[4px] sm:h-auto" :max="splitValue.max"
			:min="splitValue.min" :default-size="splitValue.defaultSize" :resize-trigger-size="1">
			<template #1>
				<div class=" flex flex-col h-full w-full bg-[#fff] pt-[40px] px-[46px] gap-y-[30px] relative">
					<div class="create-header-info flex flex-row min-h-[47px] gap-x-[13px]">
						<NImage :src="currentApps.profile" class="rounded-[50%] w-[47px] h-[47px]" />
						<div class="flex flex-col gap-y-[8px] flex-1">
							<div class=" text-[16px] text-[#3d3d3d] leading-[21px]">{{ replaceText(currentApps.name) }}</div>
							<div class=" text-[14px] text-[#676767] leading-[18px]">{{ replaceText(currentApps.welcome_message) }}
							</div>
						</div>
					</div>
					<DynamicForm :outline-loading="outlineLoading" :content-loading="generateLongArticleContentLoading"
						:type="type" :create-bot-id="appid" :list="currentApps.formList"
						:is-network="currentApps.isNetwork" @create="handleCreateForm" :loading="contentLoading"
						v-on:generate-content="runGenerateLongArticleContent"
						v-on:generate-outline="runGenerateLongArticleOutline" :outline-content="outline"
						:outline-id="outlineId" v-on:re-generate-content="handleGenerateLongContent" />
				</div>
			</template>
			<template #2>
				<div class=" h-full bg-[#F9FAFF] border-r-[#dcdcdc] relative">
					<MarkdownArea ref="markdownAreaRef" :type="type" :content-loading="generateContentLoading"
						:outline-loading="outlineLoading" :data-sources="dataSources"
						:data-source="increaseContent || longContent" :extra-data-source="incerateExtraContent"
						:collect="handleCollect" :collect-data="collectData" :history-data="historyData"
						:isppt-generated="ispptGenerated"
						:request-loading="contentLoading || generateLongArticleContentLoading" :stop="handleStop"
						:expand="handleExpand" :rewrite="handleRewrite" :abbreviate="handleAbbreviate"
						:continue="handleContinue" :is-collected="isCollected" @generate-ppt="handleGeneratePPT" />
				</div>
			</template>
			<template #resize-trigger>
				<div class="h-full bg-[#dcdcdc] flex justify-center items-center rounded-md w-[1px]">
					<NIcon
						class=" w-[42px] h-[42px] border rounded-[50%] bg-[#ffffff] text-[42px] border-[#a1a1a1] expand-icon z-10">
						<svg class="p-[10px]" xmlns="http://www.w3.org/2000/svg"
							xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="22.39990234375"
							height="13.300003051757812" viewBox="0 0 22.39990234375 13.300003051757812">
							<g>
								<g>
									<path
										d="M2.561430408935547,5.949999237060547L9.099950408935547,5.949999237060547L9.099950408935547,7.350039237060547L2.561430408935547,7.349999237060547L4.651060408935547,9.519999237060546L3.7073604089355467,10.499999237060546L-0.000049591064453125,6.6499992370605465L3.7073604089355467,2.799999237060547L4.651060408935547,3.779999237060547L2.561430408935547,5.949999237060547Z"
										fill="#444444" fill-opacity="1" style="mix-blend-mode:passthrough" />
								</g>
								<g transform="matrix(-1,0,0,1,44.799896240234375,0)">
									<path
										d="M24.961428120117187,5.949999237060547L31.49994812011719,5.949999237060547L31.49994812011719,7.350039237060547L24.961428120117187,7.349999237060547L27.051058120117187,9.519999237060546L26.107358120117187,10.499999237060546L22.399948120117188,6.6499992370605465L26.107358120117187,2.799999237060547L27.051058120117187,3.779999237060547L24.961428120117187,5.949999237060547Z"
										fill="#444444" fill-opacity="1" style="mix-blend-mode:passthrough" />
								</g>
								<g>
									<rect x="10.499950408935547" y="0" width="1.399999976158142"
										height="13.300000190734863" rx="0" fill="#444444" fill-opacity="1" />
								</g>
							</g>
						</svg>
					</NIcon>
				</div>
			</template>
		</NSplit>
		<div v-else>
			<div class=" flex flex-col h-full w-full bg-[#fff] pt-4 px-4 gap-y-4 relative">
				<div class="create-header-info flex flex-row min-h-[47px] gap-x-[13px]">
					<NImage :src="currentApps.profile" class="rounded-[50%] w-[47px] h-[47px]" />
					<div class="flex flex-col gap-y-[8px] flex-1">
						<div class=" text-[16px] text-[#3d3d3d] leading-[21px]">{{ currentApps.name }}</div>
						<div class=" text-[14px] text-[#676767] leading-[18px]">{{ currentApps.welcome_message
							}}
						</div>
					</div>
				</div>
				<DynamicForm :outline-loading="outlineLoading" :content-loading="generateLongArticleContentLoading"
					:type="type" :create-bot-id="appid" :list="currentApps.formList" :is-network="currentApps.isNetwork"
					@create="handleCreateForm" :loading="contentLoading"
					v-on:generate-content="runGenerateLongArticleContent"
					v-on:generate-outline="runGenerateLongArticleOutline" :outline-content="outline"
					:outline-id="outlineId" v-on:re-generate-content="handleGenerateLongContent" />
			</div>
			<div class=" h-full bg-[#F9FAFF] border-r-[#dcdcdc] relative">
				<MarkdownArea ref="markdownAreaRef" :type="type" :content-loading="generateContentLoading"
					:outline-loading="outlineLoading" :data-sources="dataSources"
					:data-source="increaseContent || longContent" :extra-data-source="incerateExtraContent"
					:collect="handleCollect" :collect-data="collectData" :history-data="historyData"
					:request-loading="contentLoading || generateLongArticleContentLoading"
					:isppt-generated="ispptGenerated" :stop="handleStop" :expand="handleExpand" :rewrite="handleRewrite"
					:abbreviate="handleAbbreviate" :continue="handleContinue" :is-collected="isCollected"
					@generate-ppt="handleGeneratePPT" />
			</div>
		</div>
	</div>
	<GlobalFloat />

</template>
<style lang="less" scoped>
@import './application.less';
</style>
