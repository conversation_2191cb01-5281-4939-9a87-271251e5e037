<script setup lang='ts'>
import { computed } from 'vue'
import { NLayout, NLayoutHeader, NLayoutContent,NLayoutFooter } from 'naive-ui'
import { Header,CopyRight } from '@/components/common'
import Header1 from '@/components/common/Header1/index.vue'
import Sider from './sider/index.vue'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { useAppStore } from '@/store'
import { useRoute } from 'vue-router'
const appStore = useAppStore()

const route = useRoute()

const { isMobile, isIpad } = useBasicLayout()

const collapsed = computed(() => appStore.siderCollapsed)

const getMobileClass = computed(() => {
	if (isMobile.value)
		return ['rounded-none', 'shadow-none']
	return ['border', 'rounded-md', 'shadow-md', 'dark:border-neutral-800']
})

const getContainerClass = computed(() => {
	return [
		'h-full',
	]
})
</script>

<template>
	<div class="h-full dark:bg-[#24272e] transition-all" :class="[isMobile || isIpad ? 'p-0 overflow-x-hidden' : 'p-0']">
		<div class="h-full" :class="getMobileClass">
			<NLayoutHeader>
				<Header1 />
			</NLayoutHeader>
			<NLayout class="z-40 transition" :class="getContainerClass" has-sider>
				<Sider />
				<NLayoutContent class="h-full">
					<RouterView v-slot="{ Component, route }">
						<component :is="Component" :key="route.fullPath" />
					</RouterView>
				</NLayoutContent>
			</NLayout>
			<NLayoutFooter v-if="!route.meta.hideFooter">
				<CopyRight />
			</NLayoutFooter>
		</div>
	</div>
</template>
