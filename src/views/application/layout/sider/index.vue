<script setup lang='ts'>
import { getCategoryAndCreatesByCategoryId } from '@/chatgpt';
import { MenuOption, NLayoutSider, NMenu, NImage, NInput } from 'naive-ui';
import { h, onMounted, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { RouterLink, useRoute, useRouter } from 'vue-router';
import { CreateCategory } from '../../types';
import { renderIcon } from '@/utils/utils'
import { AiworkType } from '@/typings';
import { getUser } from '@/store/modules/auth/helper';
import { debounce } from '@/utils/functions/debounce';
import { isString } from '@/utils/is';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import HistoryImg from '@/assets/History.png';
import CollectionImg from '@/assets/collection.png';
const menuOption = ref<MenuOption[]>([])

const route = useRoute()
const router = useRouter()
const user = getUser();
const { id } = route.params
const { appid } = route.query
const expandKey = ref<string[]>([])
const selectedKey = ref<string>("")
const { isMobile, isIpad } = useBasicLayout()

const { run } = useRequest<CreateCategory[]>(getCategoryAndCreatesByCategoryId, {
	ready: !!id,
	manual: true,
	onSuccess: data => {
		menuOption.value = data.map(item => {
			expandKey.value.push(item.id.toString())
			return {
				title: item.name,
				key: item.id.toString(),
				icon: renderIcon(item.icon),
				children: item.Createbots.filter(i => i.type === 'app' || i.type === 'long').map(bot => {
					let label
					if (bot.id.toString() === appid) {
						selectedKey.value = bot.id.toString()
					}
					if (bot.href) {
						label = () =>
							h(
								RouterLink,
								{
									to: bot.href || ''
								},
								{ default: () => bot.name }
							)
					} else {
						label = () =>
							h(
								RouterLink,
								{
									to: {
										name: 'AppDetail',
										params: {
											id
										},
										query: {
											appid: bot.id.toString(),
											type: bot.type === 'long' ? 'long' : 'app'
										}
									}
								},
								{ default: () => bot.name }
							)
					}
					return {
						label,
						name: bot.name,
						show: true,
						key: bot.id.toString(),
						icon: renderIcon(bot.profile),
					}
				})
			}
		})
	}
})
onMounted(() => {
	if (id) {
		run({ categoryId: id })
	}
})

const handleGotoCollection = () => {
	return new Promise((resolve, reject) => {
		const u = getUser();
		if (u.type === 'temp') {
			(window.$aiwork as AiworkType).openLogin?.()
			return reject({ message: "请先登录" });
		}
		router.push({ name: 'Collection' })
	})
}
const handleFilterMenuOptions = debounce((v: string) => {
	if (v) {
		// 把children里面的show设置为false
		menuOption.value = menuOption.value.map(item => {
			item.children = item.children ?? [];
			item.children = item.children.map(i => {
				const name = (i.name as string).toLocaleLowerCase();
				i.show = name.includes(v.toLocaleLowerCase())
				return i
			})
			return item
		})

	} else {

		menuOption.value = menuOption.value.map(item => {
			item.children = item.children ?? [];
			item.children = item.children.map(i => {
				i.show = true
				return i
			})
			return item
		})
	}
}, 300)
const handleGotoHistory = () => {
	return new Promise((resolve, reject) => {
		const u = getUser()
		if (u.type === 'temp') {
			(window.$aiwork as AiworkType).openLogin?.()
			return reject({ message: "请先登录" });
		}
		router.push({ name: 'History' })
	})
}
</script>

<template>
	<NLayoutSider class=" !z-20 flex flex-col" bordered :width="250" :collapsed-width="0" collapse-mode="width"
		:native-scrollbar="false" :show-trigger="isMobile || isIpad ? 'arrow-circle' : false"
		:position="isMobile ? 'absolute' : 'static'" :default-collapsed="isMobile ? true : false"
		collapsed-trigger-class="collapsed-trigger" content-style="z-index: 9;" trigger-class="trigger">
		<NInput placeholder="请输入" class="mt-[80px] mx-[15px] mb-[11px] !w-[calc(100%-30px)] sm:mt-4"
			@input="handleFilterMenuOptions">
			<template #prefix>
				<IconSearch />
			</template>
		</NInput>
		<div class=" flex-1 overflow-y-scroll HideScrollbar">
			<NMenu :options="menuOption" v-model:value="selectedKey" :default-expanded-keys="expandKey" />
		</div>
		<div class=" h-[100px] ipad:pb-[136px]">
			<div class=" w-[calc(100%-34px)] ml-[17px] h-[1px] bg-[#DCDCDC]"></div>
			<div class=" w-[calc(100%-34px)] ml-[17px] h-[42px] pl-[16px] flex flex-row text-[16px] text-[#3D3D3D] items-center justify-start cursor-pointer"
				@click="handleGotoCollection">
				<NImage :src="CollectionImg" width="26px" height="26px" preview-disabled />
				<span class=" pl-[10px]">收藏夹</span>
			</div>
			<div class=" w-[calc(100%-34px)] ml-[17px] h-[42px] pl-[16px] flex flex-row text-[16px] text-[#3D3D3D] items-center justify-start cursor-pointer"
				@click="handleGotoHistory">
				<NImage :src="HistoryImg" width="26px" height="26px" preview-disabled />
				<span class=" pl-[10px]">创作历史</span>
			</div>
		</div>
	</NLayoutSider>
</template>


<style scoped lang="less">
@media screen and (max-width: 767px) {
	:deep(.trigger.trigger) {
		top: 25px;
	}

	:deep(.collapsed-trigger.collapsed-trigger) {
		top: 25px;
		right: -25px;
		width: 30px;
		height: 30px;
		transition: all 0.3s;
	}
}
</style>
