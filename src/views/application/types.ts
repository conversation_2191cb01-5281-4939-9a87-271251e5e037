export interface Category {
	id: number; // ID
	name: string; // 名称
	type: string; // 类型
	index: number; // 索引
	parentId: null; // 父级 ID
	status: number; // 状态
	icon: string; // 图标
	description: null; // 描述
	createdAt: string; // 创建时间
	updatedAt: string; // 更新时间
	deletedAt: null; // 删除时间
}

export interface CreateCategory {
	id: number; // ID
	name: string; // 名称
	type: string; // 类型
	index: number; // 索引
	parentId: number; // 父级 ID
	status: number; // 状态
	icon: string; // 图标
	description: string; // 描述
	createdAt: string; // 创建时间
	updatedAt: string; // 更新时间
	deletedAt: null; // 删除时间
	Createbots: CreateBot[]; // 创建的机器人列表
}

export interface CreateBot {
	id: number; // ID
	type: string; // 类型
	name: string; // 名称
	profile: string; // 配置文件
	href: string; // 链接
	welcome_message: string; // 欢迎消息
	hot_num: number; // 热度
	formList: FormList[]; // 表单列表
	isNetwork: boolean; // 是否联网
	createdAt: string; // 创建时间
	updatedAt: string; // 更新时间
	CategoryCreatebot: CategoryCreateBot; // 分类与机器人关联
}

export interface CategoryCreateBot {
	categoryId: number; // 分类 ID
	createbotId: number; // 机器人 ID
	createdAt: string; // 创建时间
	updatedAt: string; // 更新时间
	deletedAt: null; // 删除时间
}

export interface FormList {
	maxWord: string | number | undefined;
	target: unknown;
	defaultContent: unknown;
	label: string; // 标签
	formType: string; // 表单类型
	isRequired: boolean; // 是否必填
	placeholder: string; // 占位符
	defaultValue?: string[] | string; // 默认值（可选）
	options?: string[]; // 选项（可选）
}
export interface Response<T> {
	errcode: number;
	data: T;
	errmsg: string;
}

export interface CatalogData {
	color: string[];
	style: string[];
	usage: string[];
}