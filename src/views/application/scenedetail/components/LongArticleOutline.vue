<script lang="ts" setup>
import { markdown2TreeOutline } from '@/utils/markdown/paper';
import { NTree } from 'naive-ui';
import { onMounted, ref, watch } from 'vue';
interface Props {
    outline?: string
}
const props = withDefaults(defineProps<Props>(), {
    outline: ''
})
// console.log("props.outline", props.outline);
// const content = markdown2outline(props.outline as string)
// 生成{label: "",key: "",children: []}格式的数据

const outline = ref<any>([])
const treeRef = ref<HTMLDivElement | null>(null)
onMounted(() => {
    try {
        outline.value = markdown2TreeOutline(props.outline)
    } catch (error) {
        console.error(error)
    }
})
watch(() => props.outline, (newVal) => {
    if (!newVal) return
    try {
        outline.value = markdown2TreeOutline(newVal)
        // 滚动到滚动条底部
        treeRef.value?.scrollTo(0, treeRef.value.scrollHeight)
    } catch (error) {
        console.error(error)
    }
})
</script>

<template>
    <div ref="treeRef"
        class=" w-full h-[80%] border-[1px] border-[#d8d8d8] rounded-[4px] relative p-[15px] mt-[42px] overflow-y-scroll HideScrollbar"
        v-if="props.outline">
        <n-tree block-line :data="outline" :default-expand-all="true" expand-on-click />

    </div>
    <div v-else class="w-full h-[80%] border-[1px] border-[#d8d8d8] rounded-[4px] relative flex flex-col gap-y-[9px] items-center justify-center">
        <span class="loader1"></span>
        <span class=" text-[#7C7C7C] text-[14px] leading-[18px] ">正在生成中...</span>
    </div>
</template>

<style lang="less" scoped>
/* HTML: <div class="loader"></div> */
.loader1 {
    width: 40px; /* Increased the size for a larger center circle */
    aspect-ratio: 1;
    display: grid;
    border-radius: 50%;
    background:
        linear-gradient(0deg, rgb(14 105 255/50%) 20%, #0000 0 80%, rgb(14 105 255/100%) 0) 50%/8% 100%, /* Made the strips thinner */
        linear-gradient(90deg, rgb(14 105 255/20%) 20%, #0000 0 80%, rgb(14 105 255/75%) 0) 50%/100% 8%; /* Made the strips thinner */
    background-repeat: no-repeat;
    animation: l23 1s infinite steps(8);
}

.loader1::before,
.loader1::after {
    content: "";
    grid-area: 1/1;
    border-radius: 50%;
    background: inherit;
    opacity: 0.915;
    transform: rotate(45deg);
}

.loader1::after {
    opacity: 0.83;
    transform: rotate(90deg);
}

@keyframes l23 {
    100% {
        transform: rotate(1turn)
    }
}
</style>