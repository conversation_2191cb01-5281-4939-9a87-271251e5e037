<script setup lang='ts'>
import { ref,computed } from 'vue'
import AvatarComponent from './Avatar.vue'
import TextComponent from './Text.vue'
import { DocumentCopy48Regular } from '@vicons/fluent'
import { NTooltip,useMessage, NButton } from 'naive-ui'
import useClipboard from 'vue-clipboard3'
import { useAuthStore } from '@/store'
import { Member } from '@/components/common'

interface Props {
	text: string
	inversion?: boolean
	error?: boolean
	loading?: boolean,
	isCompute?: boolean,
	isLimit?: boolean,
}

const userStore = useAuthStore()
const user = ref(userStore.getUser())
const message = useMessage()
const props = defineProps<Props>()
const { toClipboard } = useClipboard()
const textRef = ref<HTMLElement>()
const copyText = ref<any>('')
const asRawText = ref(props.inversion)
const messageRef = ref<HTMLElement>()
const showMember = ref(false)

const renderText = (msg:string) => {
	copyText.value = msg
}
const islimit = computed(()=> {
	return props.isLimit
})
function handleCopy() {
	toClipboard(copyText.value)
	message.success('复制文案成功')
}
function handleUpgradation() {
	showMember.value = true
}
</script>

<template>
	<div ref="messageRef" class="flex w-full mb-6 ml-6 mr-6 pr-12 overflow-hidden">
		<div class="flex items-center justify-center flex-shrink-0 h-8 overflow-hidden rounded-full basis-8 mt-[10px]"
			:class="[inversion ? 'ml-2' : 'mr-2']">
			<AvatarComponent :image="inversion" />
		</div>
		<div class="overflow-hidden text-sm " :class="[inversion ? 'items-end' : 'items-start']">
			<div class="flex items-end gap-1 mt-2" :class="[inversion ? 'flex-row-reverse' : 'flex-row']">
				<TextComponent ref="textRef" :inversion="inversion" :error="error" :text="text" :loading="loading"
					:as-raw-text="asRawText" @render-text="renderText"/>

			</div>
			<div class="mt-[10px]" v-if="isCompute">
				<div class="w-[30px] h-[30px] flex justify-center items-center rounded-[100px] bg-[#fff]">
					<NTooltip trigger="hover">
						<template #trigger>
							<div class="cursor-pointer" @click="handleCopy()">
								<DocumentCopy48Regular class="w-[20px] h-[20px] text-[#999]" />
							</div>
						</template>
						复制
					</NTooltip>
				</div>
			</div>

			<div class="pt-[5px]" v-if="user && user.type === 'temp' && !islimit && isCompute">
				为了您的账户安全，请登录使用！
				<NButton type="primary" size="small">立即登录</NButton>
			</div>
		</div>
		<div v-if="islimit" class="flex items-start ml-[20px] mt-4">
			<NButton type="primary" color="#3dbaa1" size="small" @click="handleUpgradation">升级会员</NButton>
		</div>
		<Member v-if="showMember" v-model:visible="showMember" />
	</div>
</template>
