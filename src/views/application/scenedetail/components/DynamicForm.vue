<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useMessage, NSwitch, NImage, NForm, NFormItem, NInput, NSelect, NDynamicTags, NButton, FormInst, NSteps, NStep, NSpace } from 'naive-ui'
import { FormList } from '../../types'
import UploadAndExtractText from './UploadAndExtractText.vue';
import GetUrlContent from './GetUrlContent.vue';
import LongArticleForm from './LongArticleForm.vue'
import type { StepsProps } from 'naive-ui'
import TipsImg from '@/assets/images/tips.png'
import StepProgress from '@/components/common/Steps/index.vue';
import { replaceText } from '@/plugins/directive';
const message = useMessage()
interface IProps {
	createBotId: string
	type: string
	list?: FormList[]
	messageId?: string
	outlineId?: number | null
	outlineContent?: string
	loading: boolean
	outlineLoading: boolean
	contentLoading: boolean
	isNetwork?: boolean
	isDeepseek?: boolean
	onGenerateOutline: (data: { createbotId: number, formData: any, isNetwork?: boolean, isDeepseek?: boolean }) => void
	onGenerateContent: (data: { outlineId: number, outline?: string }) => void
	onReGenerateContent: (data: { messageId: number }) => void
}

interface Emit {
	(e: 'create', array: Partial<FormList>[], isDeepseek?: boolean): void
}

const emit = defineEmits<Emit>()

const props = withDefaults(defineProps<IProps>(), {
	type: 'app',
	list: () => [],
	loading: false
})
const formRef = ref<FormInst | null>(null)
const formData: any = reactive({})
const networkRef = ref(props.isNetwork);
const deepseekRef = ref(false);
// 长文设置
const currentRef = ref<number>(1)

const outlineCreated = computed(() => {
	return props.outlineId && props.outlineContent?.length ? true : false
})

const allowedMaxStep = ref<number>(1)

// const outlineCreated = ref(false);
const currentStatus = ref<StepsProps['status']>('process')
const isLong = computed(() => props.type === 'long')
const handleUpdateCurrentStep = (current: number) => {
	if (current <= allowedMaxStep.value) {
		currentRef.value = current
	}
}
// 第一步 检查基本信息并生成大纲
const handleLongArticleFormValidate = (data: any, isNetwork?: boolean, isDeepseek?: boolean) => {
	let formData: Record<string, any>[] = []
	for (let [label, value] of Object.entries(data)) {
		formData.push({
			label,
			value
		})
	}
	allowedMaxStep.value = 2
	currentRef.value = 2
	props.onGenerateOutline({
		createbotId: Number(props.createBotId),
		formData,
		isNetwork,
		isDeepseek
	})
}
// 第二步 根据大纲生成文章
const handleOutlineGenerateContent = () => {
	props.onGenerateContent({
		outlineId: props.outlineId as number,
		// outline: props.outlineContent as string
	})
	allowedMaxStep.value = 3
	currentRef.value = 3
}

// 第三步 重新生成文章
const handleContentGenerate = () => {
	props.onReGenerateContent({
		messageId: Number(props.messageId!)
	})
}

/// 非长文

const renderOptions = (opts: string[]) => {
	let options: { label: string; value: string }[] = [];
	const uniqueOptions = Array.from(new Set(opts));
	uniqueOptions.forEach((item) => {
		options.push({
			label: item,
			value: item
		});
	});
	return options;
};

const handleValidateClick = (e: MouseEvent) => {
	e.preventDefault()
	let formValue: Record<string, any>[] = []
	formRef.value?.validate((errors) => {
		if(errors?.length) {
			try {
				return message.error(errors.flat().map(item => item?.message)?.join(','))
			} catch {
				return message.error('表单验证失败, 请输入完整内容')
			}
		}
		if (!errors) {
			for (let item in formData) {
				formValue.push({
					label: item,
					value: formData[item]
				})
			}
			emit('create', formValue, deepseekRef.value)
		}
	})
}
const handleFormReset = () => {
	props.list?.map(field => {
		if (field.formType === 'input') {
			formData[field.label] = null
		} else if (field.formType === 'select') {
			formData[field.label] = null
		} else if (field.formType === 'multiSelect') {
			formData[field.label] = []
		} else if (field.formType === 'textarea') {
			formData[field.label] = null
		} else if (field.formType === 'keywords') {
			formData[field.label] = []
		}
	})
}
const handleFileParsed = (content: string) => {
	const currentItem = props.list?.find(item => item.target)
	if (currentItem?.target as string) {
		const targetItem = props.list?.find(item => item.label === currentItem?.target)
		if (content?.length > Number(targetItem?.maxWord)) {
			message.warning(`最多输入${targetItem?.maxWord}个字, 超出部分将被截断`)
			formData[currentItem!.target as string] = content.slice(0, Number(targetItem?.maxWord))
		} else {
			formData[currentItem!.target as string] = content
		}
	}
}

const handleUrlParsed = (content: string) => {
	const currentItem = props.list?.find(item => item.target)
	if (currentItem?.target as string) {
		const targetItem = props.list?.find(item => item.label === currentItem?.target)
		if (content?.length > Number(targetItem?.maxWord)) {
			message.warning(`最多输入${targetItem?.maxWord}个字, 超出部分将被截断`)
			formData[currentItem!.target as string] = content.slice(0, Number(targetItem?.maxWord))
		} else {
			formData[currentItem!.target as string] = content
		}
	}
}


const showHelpInputContent = computed(() => {
	return !!props.list?.some(item => item.defaultContent)
})
const handleHelpMeWrite = () => {
	props.list?.forEach(item => {
		if (item.defaultContent) {
			formData[item.label] = item.defaultContent
		}
	})
}
const steps = ref([
	'基本信息',
	'大纲',
	'文章',
]);


</script>
<template>
	<div class=" w-full h-full flex flex-col HideScrollbar overflow-scroll">
		<template v-if="isLong">
			<step-progress :steps="steps" :current-step="currentRef" @update-current="handleUpdateCurrentStep" />
		</template>

		<template v-if="showHelpInputContent && currentRef === 1">
			<div class=" flex justify-center w-full mt-[40px] mb-[27px]">
				<div
					class=" border-[1px] border-[#D8D8D8] rounded-[4px] w-full flex flex-row items-center gap-x-[12px] justify-between bg-gradient-to-r to-[#FFF7E3] from-[#FDEEC8] h-[35px]  leading-[35px] px-[18px]">
					<NImage :src="TipsImg" class=" w-[16px] h-[16px]" />
					<span class=" text-[#5C2504] flex-1 truncate ">不知道怎么写？点击填入范例</span>
					<div class=" bg-[#5c2504] rounded-[20px] px-[8px] h-[24px] text-[#fff] text-[12px] leading-[24px] cursor-pointer"
						@click="handleHelpMeWrite">帮我写</div>
				</div>
			</div>
		</template>
		<!-- 长文创作 -->
		<LongArticleForm v-if="isLong" :outline-loading="outlineLoading" :content-loading="contentLoading" :list="list"
			:current-ref="currentRef" :allowed-max-step="allowedMaxStep" :current-status="currentStatus"
			:form-data="formData" :handle-file-parsed="handleFileParsed" :handle-form-reset="handleFormReset"
			:on-form-validate="handleLongArticleFormValidate"
			:on-outline-generate-content="handleOutlineGenerateContent" :outline-content="outlineContent"
			v-on:content-generate="handleContentGenerate" :outline-created="outlineCreated" :is-network="isNetwork" />

		<NForm v-else :model="formData" ref="formRef"
			class="dynamic-form-content h-full relative overflow-y-scroll HideScrollbar">
			<div class=" h-[calc(100%-84px)] overflow-y-scroll HideScrollbar ">
				<template v-for="(field) in list">
					<template v-if="field.formType === 'input'">
						<NFormItem :label="replaceText(field.label)" :path="field.label" :rule="{
							required: field.isRequired,
							message: `请输入${field.label}`
						}">
							<NInput v-model:value="formData[field.label]" :placeholder="field.placeholder" />
						</NFormItem>
					</template>
					<template v-if="field.formType === 'select'">
						<NFormItem :label="replaceText(field.label)" :path="field.label" :rule="{
							required: field.isRequired,
							message: `请选择${field.label}`
						}">
							<NSelect v-model:value="formData[field.label]" :placeholder="field.placeholder"
								:options="renderOptions(field.options as string[])" />
						</NFormItem>
					</template>
					<template v-if="field.formType === 'getContentByUrl'">
						<NFormItem :label="replaceText(field.label)" :path="field.label">
							<GetUrlContent :placeholder="field.placeholder" @url-parsed="handleUrlParsed" />
						</NFormItem>
					</template>
					<template v-if="field.formType === 'multiSelect'">
						<NFormItem :label="replaceText(field.label)" :path="field.label" :rule="{
							required: field.isRequired,
							message: `请选择${field.label}`
						}">
							<NSelect v-model:value="formData[field.label]" :placeholder="field.placeholder"
								:multiple="true" :options="renderOptions(field.options as string[])" />
						</NFormItem>
					</template>
					<template v-if="field.formType === 'textarea'">
						<NFormItem :label="replaceText(field.label)" :path="field.label" :rule="{
							required: field.isRequired,
							message: `请输入${field.label}`
						}">
							<NInput type="textarea" :maxlength="field.maxWord" v-model:value="formData[field.label]"
								:placeholder="field.placeholder" :rows="formData[field.label]?.length > 300 ? 13 : 6"
								:show-count="true" />
						</NFormItem>
					</template>
					<template v-if="field.formType === 'keywords'">
						<NFormItem :label="replaceText(field.label)" :path="field.label" :rule="{
							required: field.isRequired,
							message: `请输入${field.label}`
						}">
							<NDynamicTags v-model:value="formData[field.label]" :placeholder="field.placeholder" />
						</NFormItem>
					</template>
					<template v-if="field.formType === 'file'">
						<NFormItem :label="replaceText(field.label)" :path="field.label">
							<!-- <NDynamicTags v-model:value="formData[field.label]" :placeholder="field.placeholder" /> -->
							<UploadAndExtractText :place-holder="field.placeholder" @file-parsed="handleFileParsed" />
						</NFormItem>
					</template>
				</template>
				<NFormItem :show-feedback="false">
					<n-switch v-model:value="deepseekRef" /> <span class="text-[16px] leading-[22px] text-[#383838] relative left-[10px]">DeepSeek
						模型</span>
				</NFormItem>
				<div class=" text-[#ADADAD] text-[14px] leading-[34px]"> 开启后将使用DeepSeek大模型进行创作
				</div>
			</div>

			<NFormItem class=" sticky bottom-0 w-full bg-[#fff] z-[10]">
				<div class="w-full sm:mb-0 flex flex-row gap-x-[11px] justify-center" v-if="list?.length">
					<n-button type="success" color="#0E69FF" :loading="loading"
						class=" max-w-[290px] flex-1 rounded-[4px]" :disabled="loading"
						@click="handleValidateClick">开始创作</n-button>
					<n-button type="default" color="#0066FE" ghost class=" max-w-[173px] w-[36%] rounded-[4px]"
						:disabled="loading" @click="handleFormReset">
						<IconDelete />清空录入
					</n-button>
				</div>
			</NFormItem>
		</NForm>
	</div>
</template>
<style lang="less" scoped>
@import './DynamicForm.less';
</style>
