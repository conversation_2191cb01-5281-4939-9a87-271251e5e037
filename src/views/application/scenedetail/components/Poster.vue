<template>
	<div class="flex flex-col min-h-0 h-full">
		<div class="flex justify-between px-4 items-center py-4 grow-0 shrink-0">
			<div class="flex gap-2 items-center">
				<NButton @click="emit('onBack')">返回</NButton>
				<NPopover trigger="click">
					<template #trigger>
						<NButton>自定义</NButton>
					</template>
					<div>
						<NForm>
							<NFormItem label="背景">
								<NRadioGroup class="flex flex-wrap gap-2 max-w-[300px] min-h-0 !h-auto hide-splitor" @update:value="handleBackgroundChange">
									<!-- 'blue' | 'pink' | 'purple' | 'green' | 'yellow' | 'gray' | 'red' | 'indigo' | 'SpringGradientWave' -->
									<NRadioButton :class="cardClasses" value="blue"
										class=" !bg-gradient-to-br !from-blue-500 !via-cyan-600 !to-blue-700 outline">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="pink"
										class=" !bg-gradient-to-br !from-pink-400 !via-red-400 !to-pink-600/80">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="purple"
										class=" !bg-gradient-to-br !from-purple-500 !via-indigo-500/90 !to-purple-800">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="green"
										class=" !bg-gradient-to-br !from-teal-600 !via-green-700/70 !to-teal-700">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="yellow"
										class=" !bg-gradient-to-br !from-yellow-400 !via-orange-400 !to-yellow-500">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="gray"
										class=" !bg-gradient-to-br !from-zinc-500 !via-zinc-950 !to-zinc-500">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="red"
										class=" !bg-gradient-to-b !from-orange-500 !via-red-400 !to-orange-500">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="indigo"
										class=" !bg-gradient-to-b !from-red-400 !to-red-500/90">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="SpringGradientWave"
										class=" bg-spring-gradient-wave">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="bg-gray-wave"
										class="bg-gray-wave_icon">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="bg-sunset-gradient-wave"
										class="bg-sunset-gradient-wave_icon">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="bg-blue-frosted-wave"
										class=" bg-blue-frosted-wave_icon">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="bg-sport-wave"
										class=" bg-sport-wave_icon">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="bg-stripe-speed"
										class=" bg-stripe-speed_icon">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="bg-black-mountain"
										class=" bg-black-mountain_icon">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="bg-green-integrated-circuit"
										class=" bg-green-integrated-circuit_icon">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="bg-sales-man"
										class=" bg-sales-man_icon">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="bg-business"
										class=" bg-business_icon">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="bg-ink-painting"
										class=" bg-ink-painting_icon">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="bg-colorful-shape"
										class=" bg-colorful-shape_icon">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="bg-geometric-birds"
										class=" bg-geometric-birds_icon">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="bg-cartoon"
										class=" bg-cartoon_icon">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="bg-chrysanthemum-and-love"
										class=" bg-chrysanthemum-and-love_icon">
									</NRadioButton>
									<NRadioButton :class="cardClasses" value="bg-cute"
										class=" bg-cute_icon">
									</NRadioButton>
								</NRadioGroup>
							</NFormItem>
							<NFormItem label="排版主题">
								<NRadioGroup class="flex flex-wrap max-w-[300px]" v-model:value="themeClass">
									<NRadioButton value="font-sans prose-zinc text-zinc-800 prose-headings:text-zinc-800">Base</NRadioButton>
									<NRadioButton value="font-serif prose-zinc text-zinc-700 prose-headings:text-zinc-700 prose-strong:text-red-800 prose-em:text-gray-950 prose-em:bg-gray-950/10 prose-blockquote:text-zinc-700  prose-blockquote:bg-gray-300/50 prose-blockquote:p-1 prose-img:rounded-none prose-img:shadow-lg">Classic</NRadioButton>
									<NRadioButton value="font-sans prose-neutral text-neutral-700  prose-headings:text-neutral-700 prose-strong:decoration-wavy prose-strong:underline prose-strong:decoration-red-400  prose-strong:bg-red-400/40 prose-em:text-neutral-950 prose-em:bg-yellow-400/50 prose-blockquote:not-italic  prose-blockquote:border-l-0 prose-blockquote:shadow prose-blockquote:border-r-4 prose-blockquote:border-b-4  prose-blockquote:border-black/50 prose-blockquote:bg-neutral-800/5 prose-blockquote:text-neutral-900 prose-blockquote:rounded-2xl prose-blockquote:py-1 prose-blockquote:pl-5 prose-blockquote:pr-2">Vibrant</NRadioButton>
								</NRadioGroup>
							</NFormItem>
							<NFormItem label="文字大小">
								<NRadioGroup class="flex flex-wrap max-w-[300px]" v-model:value="proseSize">
									<NRadioButton value="prose-base">Base</NRadioButton>
									<NRadioButton value="prose-lg">Lg</NRadioButton>
									<NRadioButton value="prose-xl">Xl</NRadioButton>
									<NRadioButton value="prose-2xl">2Xl</NRadioButton>
								</NRadioGroup>
							</NFormItem>
							<NFormItem label="边距">
								<NSlider @update-value="handleMarginChange" :default-value="6" :min="1" :max="10" :step="1" />
							</NFormItem>
						</NForm>
					</div>
				</NPopover>
			</div>
			<div class="flex gap-2">
				<NButton :loading="triggerCopy" @click="triggerCopy = true">复制</NButton>
				<NButton :loading="triggerDownload" @click="triggerDownload = true">下载</NButton>
			</div>
		</div>
		<div class="flex-1 flex justify-center poster-wrapper overflow-auto px-4">
			<Poster :theme="themeType" :bgClass="bgClass" :themeClass="themeClass" :marginClass="marginClass" :proseSize="proseSize" :content="markdownContent" :triggerCopy="triggerCopy" :triggerDownload="triggerDownload" :copySuccessCallback="handleCopySuccess" :copyFailCallback="handleCopyFail" :downloadSuccessCallback="handleDownloadSuccess" :downloadFailCallback="handleCopyFail" />
		</div>
	</div>
</template>

<script lang="ts">
import { Poster as _Poster } from "@/components/react_app/Poster";
import { applyReactInVue, applyPureReactInVue } from 'veaury'
import { download } from "naive-ui/es/_utils";

export default {
	components: {
		// 使用高阶组件 'applyReactInVue'
		// Basic: applyReactInVue(_Poster),
		// 现在推荐使用纯净模式的 'applyPureReactInVue'
		Poster: applyPureReactInVue(_Poster)
	}
}

</script>

<script lang="ts" setup>
import { NButton, NForm, NFormItem, NPopover, NRadioGroup, NRadio, NRadioButton, NSlider, useMessage } from "naive-ui";
import { ref } from "vue";

const emit = defineEmits<{
	onBack: []
}>()
const props = defineProps<{
	"markdownContent"?: string
}>()

const message = useMessage()

const themeType = ref('blue')
const themeClass = ref('font-sans prose-zinc text-zinc-800 prose-headings:text-zinc-800')
const cardClasses = ref("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-primary text-primary-foreground !hover:bg-primary/90 px-4 py-2 w-8 h-10 outline-yellow-900 dark:outline-yellow-400 outline-2")
const bgClass = ref('')
const marginClass = ref('p-12')
const proseSize = ref('prose-base')
const triggerCopy = ref(false)
const triggerDownload = ref(false)


const handleBackgroundChange = (value: string) => {
	themeType.value = value
	if(['blue','pink','purple','green','yellow','gray','red','indigo','SpringGradientWave'].includes(value)) {
		bgClass.value = ''
	}
	else bgClass.value = `${value}`
}
const handleMarginChange = (value: number) => {
	marginClass.value = `p-${value*2}`
}

const handleCopySuccess = () => {
	message.success('复制成功')
	triggerCopy.value = false
}
const handleCopyFail = () => {
	message.error('当前浏览器不支持此功能, 请跟换浏览器或更新到最新版本后尝试')
	triggerCopy.value = false
	triggerDownload.value = false
}

const handleDownloadSuccess = () => {
	message.success('下载成功')
	triggerDownload.value = false
}
</script>



<style lang="less">
@import url(./poster.css);
.poster-wrapper>div {
	// all: inherit !important;
	width: 100% !important;
}
.hide-splitor .n-radio-group__splitor {
	display: none !important;
}
.p-10 {
	padding: 2.5rem;
}
.p-14 {
	padding: 3.5rem;
}
.p-18 {
	padding: 4.5rem;
}
.p-20 {
	padding: 5rem;
}
.bg-spring-gradient-wave {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/BlackMountain.jpg) !important;
    background-size: cover !important;
}
.bg-gray-wave {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/GrayWave.webp) !important;
    background-size: cover !important;
}
.bg-sunset-gradient-wave {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/SunsetGradientWave.webp) !important;
    background-size: cover !important;
}
.bg-blue-frosted-wave {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/BlueFrostedWave.webp) !important;
    background-size: cover !important;
}
.bg-sport-wave {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/SportWave.webp) !important;
    background-size: cover !important;
}
.bg-stripe-speed {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/StripeSpeed.webp) !important;
    background-size: cover !important;
}
.bg-black-mountain {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/BlackMountain.jpg) !important;
    background-size: cover !important;
}
.bg-green-integrated-circuit {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/GreenIntegratedCircuit.webp) !important;
    background-size: cover !important;
}
.bg-sales-man {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/SalesMan.webp) !important;
    background-size: cover !important;
}
.bg-business {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/Business.webp) !important;
    background-size: cover !important;
}
.bg-ink-painting {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/InkPainting.webp) !important;
    background-size: cover !important;
}
.bg-colorful-shape {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/ColorfulShape.webp) !important;
    background-size: cover !important;
}
.bg-geometric-birds {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/GeometricBirds.webp) !important;
    background-size: cover !important;
}
.bg-cartoon {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/Cartoon.webp) !important;
    background-size: cover !important;
}
.bg-chrysanthemum-and-love {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/ChrysanthemumAndLove.webp) !important;
    background-size: cover !important;
}
.bg-cute {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/Cute.webp) !important;
    background-size: cover !important;
}
.bg-spring-gradient-wave_icon {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/BlackMountain.jpg?imageMogr2/thumbnail/100x100) !important;
    background-size: cover !important;
}
.bg-gray-wave_icon {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/GrayWave.webp?imageMogr2/thumbnail/100x100) !important;
    background-size: cover !important;
}
.bg-sunset-gradient-wave_icon {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/SunsetGradientWave.webp?imageMogr2/thumbnail/100x100) !important;
    background-size: cover !important;
}
.bg-blue-frosted-wave_icon {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/BlueFrostedWave.webp?imageMogr2/thumbnail/100x100) !important;
    background-size: cover !important;
}
.bg-sport-wave_icon {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/SportWave.webp?imageMogr2/thumbnail/100x100) !important;
    background-size: cover !important;
}
.bg-stripe-speed_icon {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/StripeSpeed.webp?imageMogr2/thumbnail/100x100) !important;
    background-size: cover !important;
}
.bg-black-mountain_icon {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/BlackMountain.jpg?imageMogr2/thumbnail/100x100) !important;
    background-size: cover !important;
}
.bg-green-integrated-circuit_icon {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/GreenIntegratedCircuit.webp?imageMogr2/thumbnail/100x100) !important;
    background-size: cover !important;
}
.bg-sales-man_icon {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/SalesMan.webp?imageMogr2/thumbnail/100x100) !important;
    background-size: cover !important;
}
.bg-business_icon {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/Business.webp?imageMogr2/thumbnail/100x100) !important;
    background-size: cover !important;
}
.bg-ink-painting_icon {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/InkPainting.webp?imageMogr2/thumbnail/100x100) !important;
    background-size: cover !important;
}
.bg-colorful-shape_icon {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/ColorfulShape.webp?imageMogr2/thumbnail/100x100) !important;
    background-size: cover !important;
}
.bg-geometric-birds_icon {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/GeometricBirds.webp?imageMogr2/thumbnail/100x100) !important;
    background-size: cover !important;
}
.bg-cartoon_icon {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/Cartoon.webp?imageMogr2/thumbnail/100x100) !important;
    background-size: cover !important;
}
.bg-chrysanthemum-and-love_icon {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/ChrysanthemumAndLove.webp?imageMogr2/thumbnail/100x100) !important;
    background-size: cover !important;
}
.bg-cute_icon {
    background-image: url(https://cdn2.weimob.com/static/aiwork365-web-stc/images/poster/Cute.webp?imageMogr2/thumbnail/100x100) !important;
    background-size: cover !important;
}

</style>
