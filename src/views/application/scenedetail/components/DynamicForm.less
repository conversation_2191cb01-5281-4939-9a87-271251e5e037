.create-container-steps {
	.dynamic-form-content {
		.n-form-item-label--right-mark {
			&::before {
				content: " ";
				width: 2px;
				height: calc(100% - 5px);
				line-height: 100%;
				background-color: #0066fe;
				margin-top: 5px;
				margin-right: 7px;
			}
		}
	}
}
// :deep(.n-steps) {
// 	.n-step-content {
// 		flex: unset;
// 		:nth-child(2) {
// 			flex: 1;
// 		}
// 	}
// 	.n-step {
// 		justify-content: center;
// 		&:first-child {
// 			justify-content: start;
// 		}
// 		&:last-child {
// 			justify-content: end;
// 		}
// 	}
// 	.n-step-content-header {
// 		height: 1px;
// 		width: 100%;
// 	}
// }
