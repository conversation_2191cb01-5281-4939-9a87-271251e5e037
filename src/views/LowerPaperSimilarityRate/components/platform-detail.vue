<template>
	<div class="platform-detail" v-if="currentPlatform?.detail">
		<div class="title">{{ currentPlatform?.detail?.title }}</div>
		<div class="divider"></div>
		<template v-for="(item, index) in currentPlatform?.detail?.list" :key="index">
			<div class="subtitle">{{ item.title }}</div>
			<div v-if="!item.subContent" class="content">{{ item.content }}</div>
			<div v-else class="content-with-icon">
				<a class="link" @click="togglePreview(true)">{{ item.content }} </a> (展示部分报告样例)
			</div>
		</template>
		<!-- <PreviewModal :visible="show" @update:visible="togglePreview(false)" title="报告样例">
			<div class="h-[70vh] overflow-auto">
				<NImage v-for="(img, index) in previewImgs" :key="index" :src="img" object-fit="cover" class="w-full"
					preview-disabled />
			</div>
		</PreviewModal> -->
	</div>
</template>

<script setup lang="ts">
import { computed, defineProps, ref, watch } from 'vue'
import { NImage } from 'naive-ui'
import { PaperrepeatPlatform } from '../types';
import PreviewModal from './preview-modal.vue';
const props = defineProps<{
	currentPlatform?: PaperrepeatPlatform
}>()
const show = ref(false)

const togglePreview = (visible: boolean) => {
	show.value = visible
}
</script>

<style scoped lang="less">
.platform-detail {
	padding: 27px 19px 34px 19px;
	background: #fff;
	border-radius: 4px;

	.title {
		font-size: 18px;
		color: #121519;
		margin-bottom: 21px;
	}

	.divider {
		width: 100%;
		height: 1px;
		background: #E8E8E8;
	}

	.subtitle {
		font-size: 16px;
		color: #121519;
		margin-top: 19px;
		margin-bottom: 8px;
	}

	.content,
	.content-with-icon {
		font-size: 14px;
		color: #666666;
		line-height: 1.5;
	}

	.content-with-icon {
		display: flex;
		align-items: center;
	}

	.link {
		font-size: 14px;
		color: #1890ff;
		text-decoration: none;

		&:hover {
			text-decoration: underline;
		}
	}

	.sub-content {
		margin-left: 5px;
	}
}
</style>
