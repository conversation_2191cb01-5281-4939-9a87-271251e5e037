<template>
	<div class="common-qa">
		<div class="title">
			降重说明
		</div>
		<div class="divider"></div>

		<div class="qa-list">
			<div class="qa-item">
				<!-- <div class="question">1、提交论文后多久获得检测报告？</div> -->
				<div class="answer">AIWork365精准降重使用机器大模型深度学习技术，生成高相关度学术术语，阿里云加密存储，隐私安全无忧</div>
			</div>

			<div class="qa-item">
				<!-- <div class="question">2、如何计算查重论文的字数？</div> -->
				<div class="answer">对话内客由人工智能大模型输出，仅供参考，并不代表平台立场</div>
			</div>

			<div class="qa-item">
				<div class="question">检测语种：</div>
				<div class="answer">中文+英文</div>
			</div>
			<div class="qa-item">
				<div class="question">论文纠错：</div>
				<div class="answer">独创算法加持，避免多次返稿</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
// 不再需要props，使用固定内容
</script>

<style scoped lang="less">
.common-qa {
	padding: 20px;
	background: #fff;
	border-radius: 4px;

	.title {
		font-size: 16px;
		color: #121519;
		font-weight: 500;
		margin-bottom: 15px;
		display: flex;
		align-items: center;

		.icon {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 20px;
			height: 20px;
			border-radius: 50%;
			background-color: #1890ff;
			color: white;
			font-size: 14px;
			margin-right: 8px;
		}
	}

	.divider {
		width: 100%;
		height: 1px;
		background: #E8E8E8;
		margin-bottom: 15px;
	}

	.qa-list {
		.qa-item {
			margin-bottom: 15px;

			&:last-child {
				margin-bottom: 0;
			}

			.question {
				font-size: 16px;
				color: #121519;
				font-weight: 400;
				margin-bottom: 8px;
			}

			.answer {
				font-size: 14px;
				color: #666666;
				line-height: 1.5;
			}
		}
	}
}
</style>
