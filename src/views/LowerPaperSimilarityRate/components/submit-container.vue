<template>
	<div class="w-full  bg-[#F2F5F9] px-[20px] pt-[20px] border border-dashed border-[#CFDAEA]" :class="{ 'h-[314px]': submitType === 'paper', 'h-[270px]': submitType === 'report' }">
		<div class="w-[150px] mx-[auto]">
			<NTabs v-model:value="activeName" @active-name-change="handleTabChange" class="flex justify-center"
				style="--n-pane-padding-top: 0px" v-if="submitType === 'paper'">
				<NTabPane name="upload" tab="上传文档" />
				<NTabPane name="paste" tab="粘贴文本" />
			</NTabs>
		</div>

		<template v-if="activeName === 'upload'">
			<!-- 未上传状态 -->
			<NUpload v-if="uploadStatus === 'idle'" directory-dnd accept=".doc,.docx" :custom-request="handleUpload"
				:headers="headers" :show-file-list="false">
				<n-upload-dragger class="flex flex-col items-center !bg-[#fff]">
					<NImage :src="ADDFileIcon" class="w-[66px] h-[66px] mt-[12px] mb-[12px]" preview-disabled />
					<n-text class="text-[16px] !text-[#0E69FF] leading-[18px] mb-[5px]">
						点击或者拖动文件到虚线框内上传
					</n-text>
					<n-p depth="3" style="margin: 3px 0 0 0">
						点击或者拖拽Word文件到虚线框内上传
					</n-p>
					<n-p depth="3" style="margin: 3px 0 21px 0">
						目前仅支持docx、doc后缀的文件，文件小于30M
					</n-p>
				</n-upload-dragger>
			</NUpload>

			<!-- 上传中状态 -->
			<div v-else-if="uploadStatus === 'uploading'"
				class="bg-[white] h-[218px] pt-[24px] pb-[26px] rounded-lg flex flex-col justify-center">
				<div class="text-center">
					<div class="w-[170px] mx-[auto]">
						<NProgress type="line" color="#0173FE" indicator-text-color="#0173FE" :percentage="uploadProgress"
							:height="4" :border-radius="4" processing />
					</div>
					<div class="my-4 text-[#666]">
						正在上传文档中，请耐心等待…
					</div>
					<n-button size="small" bordered style="--n-border: 1px solid #0E69FF;color:#0E69FF;"
						@click="handleCancelUpload">取消上传</n-button>
				</div>
			</div>

			<!-- 上传成功状态 -->
			<div v-else-if="uploadStatus === 'success'" class="bg-[white] pt-[24px] pb-[23px] rounded-lg">
				<div class="flex flex-col items-center justify-center">
					<NImage :src="RightIcon" class="w-[36px] h-[36px] mx-[auto] mb-[11px]" preview-disabled />
					<div class>
						<div class="text-[12px] leading-[16px] text-center font-medium text-[#0E69FF]">解析成功！请点击下方提交订单按钮</div>
						<div class="text-[#9F9F9F] mt-1 flex flex-row items-center gap-x-[4px]">
							<span class="text-[#6C6C6C]">文件名称: </span>
							<NEllipsis :tooltip="false" class="w-[150px]"> {{ currentFile?.file?.name }}</NEllipsis>
							<button class="bg-[#0E69FF] text-white px-[9px] py-[4px] text-[12px] rounded"
								@click="resetUpload">重新上传</button>
						</div>
						<div class="text-[#9F9F9F] mt-1">
							<span class="text-[#6C6C6C]">文件大小:</span>
							{{ fileSize }}
						</div>
						<div class="text-[#9F9F9F] mt-1">
							<span class="text-[#6C6C6C]">论文字数:</span>
							共{{ previewFile?.words || 1000 }}字(不足千字按千字计费)
						</div>
					</div>
				</div>
			</div>

			<!-- 上传失败状态 -->
			<div v-else-if="uploadStatus === 'error'" class="bg-white p-6 rounded-lg">
				<div class="flex flex-col items-center justify-center">
					<NImage :src="WrongIcon" class="w-[36px] h-[36px] mx-[auto] mb-[11px]" preview-disabled />
					<div class="flex flex-col items-center">
						<div class="text-[12px] leading-[16px] text-center font-medium text-[#0E69FF]">文档解析失败，请重新上传</div>
						<div class="text-[#9F9F9F] mt-1 flex flex-row items-center gap-x-[4px]">
							<button class="bg-[#0E69FF] text-white px-[9px] py-[4px] text-[12px] rounded"
								@click="resetUpload">重新上传</button>
						</div>
					</div>
				</div>
			</div>

			<!-- 字数不够状态 -->
			<div v-else-if="uploadStatus === 'wordCountError'" class="bg-white p-6 rounded-lg">
				<div class="flex flex-col items-center justify-center">
					<NImage :src="WrongIcon" class="w-[36px] h-[36px] mx-[auto] mb-[11px]" preview-disabled />
					<div class="flex flex-col items-center">
						<div class="text-[12px] leading-[16px] text-center font-medium text-[#0E69FF]">文字少于1000，请重新上传</div>
						<div class="text-[#9F9F9F] mt-1 flex flex-row items-center gap-x-[4px]">
							<span class="text-[#6C6C6C]">文件名称: </span>
							<NEllipsis :tooltip="false" class="w-[150px]"> {{ currentFile?.file?.name }}</NEllipsis>
							<button class="bg-[#0E69FF] text-white px-[9px] py-[4px] text-[12px] rounded"
								@click="resetUpload">重新上传</button>
						</div>
						<div class="text-[#9F9F9F] mt-1">
							<span class="text-[#6C6C6C]">文件大小:</span>
							{{ fileSize }}
						</div>
						<div class="text-[#9F9F9F] mt-1">
							<span class="text-[#6C6C6C]">论文字数:</span>
							共{{ previewFile?.words || 1000 }}字(不足千字按千字计费)
						</div>
					</div>
				</div>
			</div>
		</template>

		<NInput v-else @blur="handleInputBlur" v-model:value="contentText" type="textarea" rows="10" placeholder="请输入文本"
			:show-count="true">
			<template #count="props">
				<span class="text-[#0E69FF] text-[12px] leading-[16px]">{{ props.value.length }}</span>
				<span class="text-[#B0B8C8] text-[12px] leading-[16px]">/100,000(不足千字按千字计费)</span>
			</template>
		</NInput>
		<!-- <div class="flex flex-row justify-between mt-[17px] leading-[16px]"
			v-if="uploadStatus === 'success' && activeName === 'upload'">
			<span class="text-[#FF4A4A]">温馨提示：上传后请点击"预览论文"检査内容是否完整；若内容缺失，可选择粘贴文本方式提交！</span>
			<span class="flex flex-row items-center text-[#0E69FF] gap-x-[4px] text-[14px] cursor-pointer"
				@click="store.previewDocument()">
				<IconEye /> 预览论文
			</span>
		</div>
		<div v-else-if="activeName === 'paste'" class="mt-[17px] leading-[16px]">
			<span v-if="contentText.length < 1000 && contentText !== ''" class="text-[#FF4A4A]">温馨提示：文字少于1000，请重新输入</span>
			<span v-else-if="!contentParsable" class="text-[#FF4A4A]">温馨提示：文本内容解析失败，请重新输入内容</span>
		</div> -->
	</div>
</template>

<script lang="ts" setup>
import { NButton, NEllipsis, NTabs, NTabPane, NUpload, NUploadDragger, NText, NP, NInput, NImage, useMessage, NProgress } from 'naive-ui';
import { computed, ref, watch } from 'vue';
import { useAuthStore } from '@/store';
import { useLowerPaperSimilarityRateStore } from '../store/lowerPaperSimilarityRate';
import { storeToRefs } from 'pinia';
import ADDFileIcon from '@/assets/images/add-file-icon.png';
import RightIcon from '@/assets/images/right-icon.png';
import WrongIcon from '@/assets/images/wrong-icon.png';

interface UploadResponse {
	errcode: number;
	errmsg?: string;
	data?: any;
}

const store = useLowerPaperSimilarityRateStore()
const { activeName, uploadStatus, contentText, contentParsable, currentFile, submitType } = storeToRefs(store)

const authStore = useAuthStore()
const message = useMessage()

const handleTabChange = (key: string) => {
	store.setActiveName(key)
}
// 字数必须大于1000
const handleInputBlur = () => {
	// 失焦就校验
	if (contentText.value.length < 1000) {
		return
	}
	store.checkContentPrice()
}

// 上传状态：未上传、上传中、上传成功、上传失败、字数不够
type UploadStatus = 'idle' | 'uploading' | 'success' | 'error' | 'wordCountError'
const uploadProgress = ref(0)
const previewFile = ref<any>({})
const fileSize = computed(() => {
	if (currentFile.value) {
		const sizeInBytes = currentFile.value?.file?.size || 0
		return `${Math.round(sizeInBytes / 1024)} KB`
	}
	return 0
})

const headers = ref({
	Authorization: `Bearer ${authStore.token}`
})

const handleUpload = async ({ file, onFinish, onError }) => {
	try {
		const fileSizeInMB = file.file.size / (1024 * 1024);
		if (fileSizeInMB > 15) {
			message.error('文件大小不能超过15MB');
			onError('File size exceeds 15MB limit');
			return;
		}

		currentFile.value = file;
		uploadProgress.value = 0;

		const data = await store.handleUpload({
			file,
			onFinish,
			onError,
			onProgress: (progress) => {
				uploadProgress.value = progress;
			}
		});

		previewFile.value = data;
	} catch (error) {
		message.error(error.message);
		throw error;
	}
}

const resetUpload = () => {
	store.resetUpload()
	uploadProgress.value = 0
	currentFile.value = null
	previewFile.value = {}
}

const handleCancelUpload = () => {
	store.cancelUpload()
	uploadProgress.value = 0
	currentFile.value = null
}

// 检查文本内容是否满足要求
const checkContentValid = () => {
	// 使用 store 中的方法检查字数是否满足要求
	if (!store.checkContentValid()) {
		message.error('文字少于1000，请重新输入')
		return false
	}
	return true
}

// 检查内容是否可以解析
const checkContentParsable = async () => {
	try {
		// 使用 store 中的方法检查内容是否可以解析
		return await store.submitContentForParsing()
	} catch (error) {
		message.error('文本内容解析失败，请重新输入内容')
		return false
	}
}

// 重置文本内容
const resetContent = () => {
	store.resetContent()
}

// 文本解析状态
const textParsingStatus = ref('idle') // 'idle' | 'parsing' | 'success' | 'error'

// 监听文本内容变化，重置解析状态
watch(contentText, () => {
	if (!contentParsable.value) {
		store.setContentParsable(true)
	}
})


// 监听 store.form.file 变化，当为 null 时清除当前文件
watch(() => currentFile.value, (newVal) => {
	if (newVal === null) {
		uploadProgress.value = 0
		previewFile.value = {}
		uploadStatus.value = 'idle'
	}
})

</script>

<style lang="less" scoped></style>
