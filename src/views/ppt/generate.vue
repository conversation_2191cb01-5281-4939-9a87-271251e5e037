<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, watch } from "vue";
import { usePptChatStore, useUserStore } from "@/store";
import { useScroll } from '@/hooks/useScroll'
import { fetchChatAPIProcess, fetchAddPPT, fetchPPTGenerate } from './api'
import { useRoute, useRouter } from "vue-router";
import Layout from "./components/Message/Layout.vue"
import Message from "./components/Message/index.vue"
import Tree from "./components/Message/Tree.vue"
import Loading from "./components/Message/Loading.vue"
import Template from "./components/Template/index.vue"
import { markdownToJson } from "@/utils/markdown/md2json";
import { useMessage } from 'naive-ui'
import { caseList } from "./mocks/case"
import { useRequest } from "vue-hooks-plus";
const message = useMessage()
const { scrollToBottom, scrollToBottomIfAtBottom } = useScroll()
// @ts-ignore
const openLongReply = import.meta.env.VITE_GLOB_OPEN_LONG_REPLY === 'true'
// 数据是否加载完成
const loading = ref<boolean>(false);
const chatPptStore = usePptChatStore();
let controller = new AbortController();
const route = useRoute()
const router = useRouter()

let uuid: number;
const step = ref<number>(2)
const isLoading = ref<boolean>(false)
const compute = ref<boolean>(false)
const selectTemp = ref<boolean>(false)
const dataSources = computed(() => chatPptStore.chat)
const { outlineId } = route.query;
const outlineNodes = ref<any>([])
onMounted(() => {
	generateChat(outlineId as string);
})

onUnmounted(() => {
	if (controller) controller.abort?.()
})

const { run } = useRequest(fetchPPTGenerate, {
	manual: true,
	onSuccess: (data) => {
		console.log('data: ', data);
	}
})



const onCreatePPTByTemplate = () => {
	selectTemp.value = true;
}

function extractJSONStrings(text) {
	const results = [];
	let depth = 0;
	let inString = false;
	let start = -1;
	let escape = false;

	for (let i = 0; i < text.length; i++) {
		const char = text[i];

		if (char === '"' && !escape) {
			inString = !inString;
		}

		if (inString && char === '\\' && !escape) {
			escape = true;
			continue;
		}

		if (char === '{' && !inString) {
			if (depth === 0) {
				start = i;
			}
			depth++;
		}

		if (char === '}' && !inString) {
			depth--;
			if (depth === 0) {
				const jsonString = text.slice(start, i + 1);
				results.push(jsonString);
			}
		}

		escape = false;
	}

	return results;
}

const generateChat = async (outlineId: string) => {
	if (loading.value) return;
	controller = new AbortController();
	chatPptStore.recordChat();
	let lastText = "";


	uuid = Date.now();
	loading.value = true;
	isLoading.value = true;
	step.value = 2;
	scrollToBottom(); try {
		await fetchChatAPIProcess<any>({
			outlineId,
			signal: controller.signal,
			onDownloadProgress: ({ event }) => {
				const xhr = event.target;
				isLoading.value = false;
				const { responseText } = xhr;
				// const chunks = responseText.split("\n");
				const chunks = extractJSONStrings(responseText)
				const chunk = chunks[chunks.length - 1]
				try {
					const data = JSON.parse(chunk);
					if (['outlineNodes', 'startSearch', 'endSearch', 'endOfProcess'].includes(data?.text)) {
						if (data?.text === 'outlineNodes') {
							outlineNodes.value = data?.detail?.nodes
						} else if (data?.text === 'startSearch') {

						} else if (data?.text === 'endSearch') {

						} else if (data?.text === "endOfProcess") {

						}
					} else {
						const text = chunks.map(x => JSON.parse(x).text).join("");
						const isCompute = data?.detail?.choices?.[0]?.finish_reason;
						chatPptStore.updateChat(
							{
								// text: lastText + (data.text || ""),
								text,
								inversion: false,
								loading: isCompute == "stop" ? false : true,
								uuid,
								isCompute: isCompute == "stop" ? true : false,
							},
							uuid
						);

						if (isCompute == "stop") {
							compute.value = true;
						}
						if (
							openLongReply &&
							data?.detail?.choices?.[0]?.finish_reason === "length"
						) {
							lastText = data.text;
						}
						scrollToBottomIfAtBottom();
					}
				} catch (error) {
					console.error(error);
					isLoading.value = false;
					loading.value = false;
				}
			},
		});
	} catch (error: any) {
		// if (error.errcode == "10000001") {
		// 	// needPermission.value = true;
		// 	step.value = 1;
		// } else if (error.errcode == "************") {
		// 	// needVip.value = true;
		// 	step.value = 1;
		// } else {
		// 	// message.error(error?.errmsg);
		// }
		console.log(error);
		isLoading.value = false;
		loading.value = false;
		window.history.back();
	} finally {
		loading.value = false;
	}
};

const onRegenerate = () => {
	compute.value = false
	generateChat(outlineId as string)
}

// 创建ppt loading
const createLoading = ref<boolean>(false)

const onCreatePPT = async (templateId = 1) => {
	try {
		if (dataSources.value && dataSources.value.length > 0) {
			// const markdown = markdownToJson(dataSources.value[0]?.text)
			const markdown = markdownToJson(dataSources.value[0].text.replace(/```markdown/g, "").replace(/```/g, ""))
			createLoading.value = true
			const data: any = await fetchAddPPT({ title: prompt, sourceData: markdown[0], templateId })
			if (data) {
				const protocol = window.location.protocol
				const host = window.location.host.includes('chat-qa.mjmobi.com') ? 'ppt-chat-qa.mjmobi.com' : 'ppt.aiwork365.cn'
				// router.push(`/editor/${data.id}`)
				// window.open(`${host}/ppt/editor/${data.id}?token=${getToken()}`, '_blank')
				window.location.href = `${protocol}//${host}/ppt/editor/${data.id}?token=${Math.random()}&ai-team=${useUserStore().curTeam?.id}`
				createLoading.value = false
				// window.location.href = `/ppt/editor/${data.id}`
			}
		}
	} catch (error: any) {
		console.log(error);
		// if (error.errcode == '10000001') {
		// 	// needPermission.value = true
		// } else if (error.errcode == '************') {
		// 	// needVip.value = true
		// } else if (error.errcode == '************' || error.message.includes("充值")) {
		// 	// window?.$aiwork?.openRecharge?.({ type: 'ppt' })
		// 	return
		// }
		createLoading.value = false
	}
}

let chatTxt = ''
let timer = null
const writing = (msg: any, index: number) => {
	const data = msg
	if (index < data.length) {
		chatTxt += data[index]
		chatPptStore.updateChat({
			text: chatTxt,
			inversion: false,
			loading: true,
			uuid,
			isCompute: false
		}, uuid)
		timer = setTimeout(writing, 8, msg, ++index)

		scrollToBottomIfAtBottom()
	}
	if (index === data.length) {
		loading.value = false
		clearTimeout(timer)
		compute.value = true
	}
}

const handleCase = (id: number) => {
	const current = caseList.find(item => item.id === id)
	if (current) {
		chatTxt = ''
		step.value = 2
		uuid = Date.now()
		chatPptStore.recordChat()
		loading.value = true
		scrollToBottom()
		writing(current?.markdown, 0)
	}
}

const handleSelect = async (templateId: string) => {
	run({
		outlineId: outlineId,
		nodes: outlineNodes.value,
		templateId: templateId,
	})
}
</script>

<template>
	<div class="bg-gray-100 flex-1">
		<div class="max-w-5xl mx-auto h-full" v-if="step === 2">
			<Layout :loading="loading" :isloading="isLoading" @create="onCreatePPTByTemplate" @regenerate="onRegenerate"
				@back="$router.back()">
				<Message v-if="!compute" v-for="(item, index) of dataSources" :key="index" :text="item.text"
					:index="index" :inversion="item.inversion" :error="item.error" :loading="item.loading"
					:is-compute="item.isCompute" />
				<Tree v-if="compute" :text="dataSources[0]?.text" />
				<Loading :loading="createLoading" />
			</Layout>
		</div>
		<Template v-if="selectTemp" :id="outlineId" v-model:visible="selectTemp" @select="handleSelect" />
	</div>
</template>
