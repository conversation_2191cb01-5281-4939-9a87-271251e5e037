.container {
	background: linear-gradient(
		180deg,
		#f5f7ff 0%,
		rgba(236, 241, 255, 0.8858) 60%,
		#e5f1ff 100%
	);
}
.input {
	box-shadow: 0px 5px 24px 0px #c8dcff;
}
.ppt-tab {
	font-size: 18px;
	font-weight: 500;
	color: #ffffff;
	margin-bottom: 10px;
	background: linear-gradient(
		90deg,
		#89B8FF 0%,
		#8994FF 100%
	);
}
.PPTActive {
	color: #0066ff;
	background: #ffffff;
}
.left {
	&::after {
		content: " ";
		position: absolute;
		bottom: 0;
		right: -12px;
		border-width: 0 13px 45px 0;
		border-style: solid;
		border-color: transparent transparent #cae9ff transparent;
	}
}

.left.active {
	&::after {
		content: " ";
		position: absolute;
		bottom: 0;
		right: -45px;
		border-width: 0 45px 45px 0;
		border-style: solid;
		border-color: transparent transparent #fff transparent;
		z-index: 2;
	}
}

.right {
	&::before {
		content: " ";
		position: absolute;
		bottom: 0;
		left: -12px;
		border-width: 45px 13px 0 0;
		border-style: solid;
		border-color: transparent #ffffff transparent transparent;
	}
}

.right.active {
	&::before {
		content: " ";
		position: absolute;
		bottom: 0;
		left: -44px;
		border-width: 45px 45px 0 0;
		border-style: solid;
		border-color: transparent #fff transparent transparent;
	}
}
