<script setup lang="ts">
import { NImage, NPopover, NTabs, NTabPane } from "naive-ui";

import Logo from "@/assets/images/logo1.png";
import Input from "@/components/common/PaperAirplaneInput/index.vue";
import PPTImg from "@/assets/images/sec2-pic-1.png";
import PPTCard from "@/components/common/PPTCard/index.vue";
import InputExtra from "./components/InputExtra/index.vue";
import ImportComponent from "./components/ImportComponent/index.vue";
import { useRouter } from "vue-router";
import { caseList } from "./mocks/case";
import { useRequest } from "vue-hooks-plus";
import { fetchPptList, fetchPPTTemplateTopV2, fetchPreviewOutline } from "./api";
import { ref } from "vue";
import { isBoolean } from "lodash";
import { TypeEnums } from "./types";
import { useUserStore } from "@/store";

const router = useRouter();
const searchParams = ref<{
	title: string,
	webSearch: boolean,
	enableRemark: boolean,
	fileUrl?: string,
	extraContent?: string,
	imageMode?: string,
	language?: string,
	isDeepSeek?: boolean
}>({
	// type: TypeEnums.TEXT,
	title: '',
	webSearch: false,
	enableRemark: false,
	language: 'zh',
	fileUrl: undefined,
	extraContent: '',
	imageMode: 'off',
	isDeepSeek: false,
	// pageCountType: "middle"
})
interface PPT {
	cover: string;
	title: string;
}
const { data } = useRequest<PPT[]>(fetchPPTTemplateTopV2);

const activeTab = ref('theme')
const showPopover = ref(false);
const handlePopoverShow = (value: boolean) => {
	showPopover.value = value;
}

const { run } = useRequest(() => fetchPreviewOutline<{ id: string }>(searchParams.value), {
	manual: true,
	onSuccess: (data) => {
		const { id: outlineId } = data
		if (outlineId) {
			const protocol = window.location.protocol
			const host = window.location.host.includes('localhost')
				? 'localhost:1025'
				: window.location.host.includes('chat-qa.mjmobi.com')
					? 'ppt2-chat-qa.mjmobi.com'
					: 'ppt2.aiwork365.cn'
			// const aiTeamId = sessionStorage.getItem('ai-team')
			const aiTeamId = useUserStore().curTeam?.id
			if (aiTeamId) {
				window.location.href = `${protocol}//${host}/ppt/generate?outlineId=${outlineId}&aiTeamId=${aiTeamId}`
			} else {
				window.location.href = `${protocol}//${host}/ppt/generate?outlineId=${outlineId}`
			}
		}
	}
})

const handleSearchParams = (value: { fileUrl?: string, webSearch?: boolean, enableRemark?: boolean, extraContent?: string, type?: TypeEnums, imageMode?: string, language?: string, isDeepSeek?: boolean }) => {
	if (value.type) {
		searchParams.value.title = ''
		searchParams.value.fileUrl = undefined
		searchParams.value.extraContent = ''
		searchParams.value.webSearch = false
		searchParams.value.enableRemark = false
		searchParams.value.isDeepSeek = false
		if (value.type === TypeEnums.UPLOAD) {
			searchParams.value.fileUrl = value.fileUrl as unknown as string
		} else if (value.type === TypeEnums.TEXT) {
			searchParams.value.extraContent = value.extraContent || ''
		}
		run()
	} else {
		if (isBoolean(value.webSearch)) {
			searchParams.value.webSearch = value.webSearch
		}
		if (isBoolean(value.enableRemark)) {
			searchParams.value.enableRemark = value.enableRemark
		}
		if (value.extraContent) {
			searchParams.value.extraContent = value.extraContent
		}
		if (value.imageMode) {
			searchParams.value.imageMode = value.imageMode
		}
		if (value.language) {
			searchParams.value.language = value.language
		}
		if (value.isDeepSeek) {
			searchParams.value.isDeepSeek = value.isDeepSeek
		}
	}

}

const handleChange = (value: string) => {
	if (value) {
		showPopover.value = false
	} else {
		showPopover.value = true
	}
	console.log(value);
};
const handleSubmit = (value: string) => {
	if (!value) {
		return window.$notification?.error({
			title: "输入错误",
			content: "请输入主题",
			duration: 1500,
		});
	}
	searchParams.value.title = value
	run()
};
const handleMockSubmit = (item) => {
	const protocol = window.location.protocol
	const host = window.location.host.includes('localhost')
		? 'localhost:1025'
		: window.location.host.includes('chat-qa.mjmobi.com')
			? 'ppt2-chat-qa.mjmobi.com'
			: 'ppt2.aiwork365.cn' // 统一使用ppt2
	const aiTeamId = useUserStore().curTeam?.id
	if (aiTeamId) {
		window.location.href = `${protocol}//${host}/ppt/generate?outlineId=${item.id}&m=1&aiTeamId=${aiTeamId}`
	} else {
		window.location.href = `${protocol}//${host}/ppt/generate?outlineId=${item.id}&m=1`
	}
}
</script>
<template>
	<div
		class="w-full flex flex-col items-center pt-[159px] pb-[100px] bg-gradient-to-b from-[#f5f7ff] via-[rgba(236, 241, 255, 0.8858)] to-[#e5f1ff]">
		<main
			class="xl:w-[1360px] lg:w-[1015px] md:w-[670px] sm:w-[350px] flex flex-col items-center gap-y-[30px] mx-[50px]">
			<section class="flex flex-col items-center gap-x-[15px]">
				<NImage :src="Logo" preview-disabled width="268" height="45" object-fit="fill" />
				<span class="font-bold text-[46px] text-[#333333] leading-[61px]">AI 一键生成 PPT</span>
				<span class="font-normal text-[28px] text-[#3d3d3d] leading-[37px] mt-[25px]">输入你的主题，让 AI 为你<span
						class="text-[#0e69ff]">一站式服务到底</span></span>
			</section>
			<section class="w-[865px] flex flex-col justify-center border rounded-[10px] opacity-100 bg-white input">
				<div class="flex flex-row justify-center">
					<div class="w-[50%] h-[44px] ppt-tab relative rounded-tl-[10px] rounded-tr-[10px] left flex items-center justify-center gap-x-[4px] cursor-pointer"
						:class="activeTab === 'theme' ? 'PPTActive active' : ''" @click="activeTab = 'theme'">
						主题生成
					</div>
					<div class="w-[50%] h-[44px] ppt-tab relative rounded-tl-[10px] rounded-tr-[10px] right flex items-center justify-center gap-x-[4px] cursor-pointer"
						:class="activeTab === 'import' ? 'PPTActive active' : ''" @click="activeTab = 'import'">
						导入文档生成PPT
					</div>
				</div>

				<div v-if="activeTab === 'theme'" class=" px-[13px] pt-[9px]">
					<NPopover trigger="click" placement="bottom" :show="showPopover">
						<template #trigger>
							<Input :placeholder="'请输入需要生成的PPT主题'" @change="handleChange"
								@submit="(v) => handleSubmit(v)" @update:show="handlePopoverShow" />
						</template>
						<ul class="w-[838px] mt-[10px] overflow-auto h-[300px] 2xl:h-full">
							<li class="flex items-center h-[40px] text-sm rounded-md transition-colors hover:bg-gray-100 cursor-pointer px-2"
								v-for="(item, index) of caseList" :key="index" @click="handleMockSubmit(item)">
								<IconComment class="mr-2 mt-[4px] text-[#36bd94]"></IconComment>
								<span class="text-[14px]">{{ item.title }}</span>
							</li>
						</ul>
					</NPopover>
					<div class=" h-[58px] w-full bg-white rounded-[10px] shadow-box">
						<InputExtra @update:search="handleSearchParams" />
					</div>

				</div>
				<div v-else class=" px-[13px] pb-[10px]">
					<ImportComponent @update:search="handleSearchParams" />
				</div>




				<!-- <NTabs v-model:value="activeTab" type="line" size="large" animated>
					<NTabPane name="theme" tab="主题生成">
						<NPopover trigger="click">
							<template #trigger>
								<Input :placeholder="'请输入需要生成的PPT主题'" @change="handleChange"
									@submit="(v) => handleSubmit(v)" />
							</template>
<ul class="w-[850px] mt-[10px] overflow-auto h-[300px] 2xl:h-full">
	<li class="flex items-center h-[40px] text-sm rounded-md transition-colors hover:bg-gray-100 cursor-pointer px-2"
		v-for="(item, index) of caseList" :key="index" @click="handleMockSubmit(item)">
		<i class="fi fi-rr-comment-alt mr-2 mt-[4px] text-[#36bd94]"></i>
		<span class="text-[14px]">{{ item.title }}</span>
	</li>
</ul>
</NPopover>
<div class=" h-[58px] w-full bg-white rounded-[10px] shadow-box">
	<InputExtra @update:search="handleSearchParams" />
</div>
</NTabPane>
<NTabPane name="import" tab="导入文档生成ppt">
	<Input :placeholder="'请输入需要生成的PPT主题'" @change="handleChange" @submit="(v) => handleSubmit(v)" />
</NTabPane>
</NTabs>-->
			</section>
			<section class="flex flex-col gap-y-[20px] items-center mt-[80px]">
				<span class="font-normal text-[#333333] text-[24px] leading-[32px]">
					<span class="text-[#3680f9]">10 </span>秒完成，全场景一键匹配海量高质PPT模版
				</span>
				<span class="text-[16px] text-[#676767] leading-[21px]">输入任意主题内容，秒级生成您的专属PPT</span>
				<div class="flex flex-row flex-wrap gap-x-[20px] gap-y-[20px] mt-[50px]">
					<PPTCard v-for="(item, index) in data" :key="index + item.cover" :id="index" :src="item.cover"
						:title="item.title" @update:search="handleSubmit" />
				</div>
			</section>
		</main>
		<div class="fixed right-[94px] top-[118px]">
			<div class="w-[107px] h-[37px] relative rounded-[50px] flex justify-center items-center text-white cursor-pointer"
				style="background: linear-gradient(143deg, #414CF9 39%, #012BED 103%);"
				@click="$router.push('/ppt/workspace/home')">
				<i>
					<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none"
						version="1.1" width="15" height="18.5" viewBox="0 0 15 18.5">
						<g>
							<g>
								<rect x="0.5" y="3" width="14" height="15" rx="1.5" fill-opacity="0" stroke-opacity="1"
									stroke="#FFFFFF" fill="none" stroke-width="1" />
							</g>
							<g>
								<path
									d="M0.5,1.5L0.5,3.5L1.5,3.5L1.5,1.5Q1.5,1.2928929999999998,1.646447,1.146447Q1.7928929999999998,1,2,1L10,1Q10.20711,1,10.35355,1.146447Q10.5,1.2928929999999998,10.5,1.5L10.5,3.5L11.5,3.5L11.5,1.5Q11.5,0.87868,11.0607,0.4393398Q10.62132,0,10,0L2,0Q1.3786800000000001,0,0.9393399,0.4393398Q0.5,0.87868,0.5,1.5Z"
									fill-rule="evenodd" fill="#FFFFFF" fill-opacity="1" />
							</g>
							<g>
								<path
									d="M3,7.5L11,7.5Q11.04924,7.5,11.09754,7.509607Q11.14584,7.519215,11.19134,7.53806Q11.23684,7.556906,11.27778,7.584265Q11.31873,7.611625,11.35355,7.646447Q11.38837,7.681269,11.41573,7.722215Q11.44309,7.763161,11.46194,7.808658Q11.48078,7.854155,11.49039,7.9024549Q11.5,7.9507543,11.5,8Q11.5,8.0492457,11.49039,8.0975451Q11.48078,8.145845,11.46194,8.191342Q11.44309,8.236839,11.41573,8.277785Q11.38837,8.318731,11.35355,8.353553Q11.31873,8.388375,11.27778,8.415735Q11.23684,8.443094,11.19134,8.46194Q11.14584,8.480785000000001,11.09754,8.490393Q11.04924,8.5,11,8.5L3,8.5Q2.9507543,8.5,2.9024549,8.490393Q2.854155,8.480785000000001,2.808658,8.46194Q2.763161,8.443094,2.722215,8.415735Q2.681269,8.388375,2.646447,8.353553Q2.611625,8.318731,2.584265,8.277785Q2.556906,8.236839,2.5380599999999998,8.191342Q2.519215,8.145845,2.509607,8.0975451Q2.5,8.0492457,2.5,8Q2.5,7.9507543,2.509607,7.9024549Q2.519215,7.854155,2.5380599999999998,7.808658Q2.556906,7.763161,2.584265,7.722215Q2.611625,7.681269,2.646447,7.646447Q2.681269,7.611625,2.722215,7.584265Q2.763161,7.556906,2.808658,7.53806Q2.854155,7.519215,2.9024549,7.509607Q2.9507543,7.5,3,7.5Z"
									fill-rule="evenodd" fill="#FFFFFF" fill-opacity="1" />
							</g>
							<g>
								<path
									d="M3,11.5L7.5,11.5Q7.54925,11.5,7.59755,11.509607Q7.64584,11.519214999999999,7.69134,11.53806Q7.73684,11.556906,7.77778,11.584265Q7.81873,11.611625,7.85355,11.646447Q7.88837,11.681269,7.91573,11.722215Q7.94309,11.763161,7.96194,11.808658Q7.98078,11.854155,7.99039,11.9024549Q8,11.9507543,8,12Q8,12.0492457,7.99039,12.0975451Q7.98078,12.145845,7.96194,12.191342Q7.94309,12.236839,7.91573,12.277785Q7.88837,12.318731,7.85355,12.353553Q7.81873,12.388375,7.77778,12.415735Q7.73684,12.443094,7.69134,12.46194Q7.64584,12.480785000000001,7.59755,12.490393Q7.54925,12.5,7.5,12.5L3,12.5Q2.9507543,12.5,2.9024549,12.490393Q2.854155,12.480785000000001,2.808658,12.46194Q2.763161,12.443094,2.722215,12.415735Q2.681269,12.388375,2.646447,12.353553Q2.611625,12.318731,2.584265,12.277785Q2.556906,12.236839,2.5380599999999998,12.191342Q2.519215,12.145845,2.509607,12.0975451Q2.5,12.0492457,2.5,12Q2.5,11.9507543,2.509607,11.9024549Q2.519215,11.854155,2.5380599999999998,11.808658Q2.556906,11.763161,2.584265,11.722215Q2.611625,11.681269,2.646447,11.646447Q2.681269,11.611625,2.722215,11.584265Q2.763161,11.556906,2.808658,11.53806Q2.854155,11.519214999999999,2.9024549,11.509607Q2.9507543,11.5,3,11.5Z"
									fill-rule="evenodd" fill="#FFFFFF" fill-opacity="1" />
							</g>
						</g>
					</svg>
				</i>
				<span class="ml-[5px]">我的作品</span>
			</div>
		</div>
	</div>
</template>
<style lang="less" scoped>
@import "./index.less";
</style>
