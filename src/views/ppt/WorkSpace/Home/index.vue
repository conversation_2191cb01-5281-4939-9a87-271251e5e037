<template>
  <div class="h-full pl-5 sm:pl-0">
    <div class="max-w-[1328px] p-[20px] mx-auto">
      <n-h2 class=" !font-bold">快速创建</n-h2>
      <ul class="grid grid-cols-4 gap-6">
        <li class="relative overflow-hidden aspect-[314/220]">
          <a href="/ppt">
            <NImage :src="createBg" class="rounded transition-all duration-300 ease-linear hover:scale-105"
              preview-disabled />
            <span class="absolute top-3 left-3 text-white font-bold">智能生成PPT</span>
          </a>
        </li>
      </ul>
      <n-tabs v-model:value="activeTab" type="line" size="large" animated @update:value="onTabChange" class="mb-[10px]">
        <n-tab-pane name="1">
          <template #tab>
            <span class=" mr-[6px] !font-bold">创意Pro版</span>
            <div class="relative flex justify-center items-center">
              <n-image :src="BadgeImg" class="w-[30px] h-[19px]" />
              <span
                class="text-[12px] text-[#fff] absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">NEW</span>
            </div>

          </template>

        </n-tab-pane>
        <n-tab-pane name="2">
          <template #tab>
            <span class=" mr-[6px] !font-bold">基础模板</span>
          </template>
        </n-tab-pane>
      </n-tabs>
      <n-spin :show="show || myPPTLoading" size="large">
        <ul
          class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 sm:pb-60 max-w-[1256px] mx-auto !m-0"
          v-if="list.length > 0">
          <li class="max-w-[314px] aspect-[314/220] rounded-lg overflow-hidden relative shadow-box cursor-pointer"
            @mouseover="handleMouseOver(index, item.id)" @mouseleave="handleMouseLeave()" v-for="(item, index) of list">
            <a target="_blank" @click.prevent="goEditor(item.id)" class="block h-full">
              <div class="relative h-[80%] overflow-hidden">
                <NImage :src='activeTab !== "1" ? getImage(item.templateId, item.extend) : item.previewImage'
                  class="w-full h-full object-cover transition-all duration-300 ease-linear hover:scale-105"
                  preview-disabled :fallback-src="cover" show-loading-placeholder />
              </div>
              <div class="h-[20%] flex items-center px-4">
                <h3 class="text-[20px] font-bold text-[#171717] line-clamp-1">{{ item.title }}</h3>
              </div>
              <div :class="getClass(item.templateId, item.extend)" v-if="!item.extend && activeTab !== '1'">
                <span class="text-[14px] sm:text-[12px] font-bold text-center">{{ item.title }}</span>
              </div>
              <div :class="getClass(item.templateId, item.extend)" v-if="item.extend"
                :style="{ color: item.extend.color }">
                <span class="text-[14px] sm:text-[12px] font-bold text-center">{{ item.title }}</span>
              </div>
            </a>
            <n-dropdown trigger="hover" size="medium" placement="right-start" :options="options" @select="handleSelect">
              <div class="absolute top-2 right-3 bg-black/[0.6] w-[26px] flex justify-center items-center rounded">
                <IconMore class="text-white pt-1" />
              </div>
            </n-dropdown>
          </li>
        </ul>
      </n-spin>
      <div class="flex justify-center items-center flex-col" v-if="count === 0">
        <n-image :src="empty" width="250" preview-disabled />
        <div>哎呀，什么都没有</div>
      </div>
      <div class="flex justify-end mt-5 mr-5" v-if="count > 20">
        <n-pagination v-model:page="page" :page-count="pageCount" @update-page="onPageChange" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, h, computed } from 'vue'
import type { Component } from 'vue'
import { NH2, NImage, NIcon, NDropdown, NPagination, useDialog, useMessage, NSpin, NTabs, NTabPane } from 'naive-ui'
import {
  TrashOutline,
  Pencil as EditIcon
} from '@vicons/ionicons5'
import { fetchPPTList, fetchPPTDelete, fetchMyPPTListV2, fetchMyPPTDelV2 } from '../../api'
import cover from '@/assets/images/cover.png'
import createBg from '@/assets/images/create.png'
import empty from '@/assets/images/empty.png'
import BadgeImg from '@/assets/badge.png'
import { useRequest } from 'vue-hooks-plus'
import { useAuthStore, useUserStore } from '@/store'
import { template } from 'lodash'
import { getToken } from '@/store/modules/auth/helper'
// import defaultCoverImage from '@/assets/images/cover.png'
interface IListProps {
  rows: IProps[];
  count: number
}

interface IProps {
  id: number;
  title: string;
  reported: string;
  createdAt: string;
  templateId?: number;
  extend?: any;
  previewImage?: string;
}
const dialog = useDialog()
const message = useMessage()
const curIndex = ref<number>(-1)
const list = ref<IProps[]>([])
const count = ref<number>(-1)
const page = ref<number>(1)
const show = ref<boolean>(false)
const currentId = ref<number>(0)
const activeTab = ref<string>('1')
const authStore = useAuthStore()

const renderIcon = (icon: Component) => {
  return () => {
    return h(NIcon, null, {
      default: () => h(icon)
    })
  }
}

const onTabChange = (value: string) => {
  list.value = []
  page.value = 1
  if (value === '2') {
    getList()
  } else {
    getMyPPTList()
  }
}
const pageCount = computed(() => {
  return Math.ceil(count.value / 20)
})

const options = ref([
  {
    label: '编辑作品',
    key: 'edit',
    icon: renderIcon(EditIcon)
  },
  {
    label: '删除作品',
    key: 'delete',
    icon: renderIcon(TrashOutline)
  },
])
const getImage = (templateId: any, extend: any) => {
  const id = templateId || 1;
  return extend ? extend.coverUrl : `https://cdn2.weimob.com/static/aiwork365-web-stc/ppt/img/cover${id}.jpg`

}
const getList = async () => {
  show.value = true
  const data: IListProps = await fetchPPTList({ page: page.value, pageSize: 20, name: '', title: '' })
  list.value = data.rows
  count.value = data.count
  show.value = false
}

const { run: getMyPPTList, loading: myPPTLoading } = useRequest(() => fetchMyPPTListV2<{ rows: IProps[], count: number }>({ page: page.value, pageSize: 20, name: '', title: '' }), {
  manual: true,
  onSuccess: (res) => {
    list.value = res.rows
    count.value = res.count
  }
})

const { run: deleteMyPPT } = useRequest(fetchMyPPTDelV2, {
  manual: true,
  onSuccess: () => {
    getMyPPTList()
  }
})


onMounted(async () => {
  getMyPPTList()
})

const handleMouseOver = (index: number, id: number) => {
  curIndex.value = index
  currentId.value = id
}
const handleMouseLeave = () => {
  curIndex.value = -1
}

const handleSelect = (type: string) => {
  if (type == 'delete') {
    dialog.success({
      title: '删除提示',
      content: '确定删除该作品吗？',
      positiveText: '确定',
      negativeText: '取消',
      maskClosable: false,
      onPositiveClick: async () => {
        if (list.value.length === 1 && page.value > 1) {
          page.value--
        }

        if (activeTab.value === '1') {
          deleteMyPPT({ id: currentId.value })
        } else {
          const response = await fetchPPTDelete({ id: currentId.value })
          if (response) {
            message.success('删除成功')
            getList()
          }
        }
      },
      onNegativeClick: () => { }
    })
  } else {
    // window.open(`/ppt/editor/${currentId.value}`)
    goEditor(currentId.value)
  }
}

const goEditor = (id) => {
  const protocol = window.location.protocol
  const isQA = window.location.href.includes('chat-qa.mjmobi.com')
  const isLocalhost = window.location.host.includes('localhost')
  const qaHost = `ppt${activeTab.value === '1' ? '2-' : '-'}chat-qa.mjmobi.com`
  const onlineHost = `ppt${activeTab.value === '1' ? '2' : ''}.aiwork365.cn`
  // 根据不同环境和tab选择对应的域名
  const host = isLocalhost
    ? 'localhost:1025'
    : isQA
      ? qaHost
      : onlineHost
  const token = authStore.token
  // const aiTeamId = sessionStorage.getItem('ai-team')
  const aiTeamId = useUserStore().curTeam?.id
  if (aiTeamId) {
    window.location.href = `${protocol}//${host}/ppt/editor/${id}?token=${token}&aiTeamId=${aiTeamId}`
  } else {
    window.location.href = `${protocol}//${host}/ppt/editor/${id}?token=${token}`
  }
}
const onPageChange = (p: number) => {
  page.value = p
  if (activeTab.value === '1') {
    getMyPPTList()
  } else {
    getList()
  }
}
const getClass = (templateId: any, extend: any) => {
  const id = templateId || 1;
  let classes = "absolute top-[30%] sm:top-[20%] right-0 "
  if (extend) {

    classes = classes + " " + extend.classes + " ";
    if (extend.color) {
      classes = `${classes} text-[#${extend.color}]`
    }
  } else {
    switch (id) {
      case 1:
        classes += ' left-0 text-center'
        break;
      case 2:
        classes += ' left-5 text-left text-[#7A19EB]'
        break;
      case 4:
        classes += ' left-5 text-left  text-[#F2DBB5]'
        break;
      case 5:
        classes += ' left-5 text-left text-[#fff]'
        break;
      default:
        break;
    }
  }

  return classes
}
</script>

<style scoped>
.n-card__footer {
  padding: 0 0 0 0 !important;
}
</style>
