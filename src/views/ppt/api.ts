import type { AxiosProgressEvent, GenericAbortSignal } from "axios";
import { post } from "@/utils/request";

// AI 生成ppt
export function fetchChatAPIProcess<T = any>(params: {
	outlineId: string;
	signal?: GenericAbortSignal;
	onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void;
}) {
	return post<T>({
		url: "/api3/ppt/v2/core/chat-ppt",
		data: {
			outlineId: params.outlineId,
		},
		signal: params.signal,
		onDownloadProgress: params.onDownloadProgress,
	}, {payParams: {type: "ppt"}});
}

export function fetchAddPPT<T>(params: any) {
	return post<T>({
		url: "/api3/ppt/pptx/add",
		data: params,
	}, {
		payParams: {type: "ppt"}
	});
}

export function fetchPPTTemplate<T>(params: any) {
	return post<T>({
		url: "/api3/ppt/template/list",
		data: params,
	});
}

export function fetchPPTDetail<T>(params: any) {
	return post<T>({
		url: "/api3/ppt/pptx/detail",
		data: params,
	});
}

export function fetchPptList<T>(params: any) {
	return post<T>({
		url: "/api3/ppt/template/top",
		data: params,
	});
}

export function fetchPPTList<T>(params: any) {
  return post<T>({
		url: "/api3/ppt/pptx/list",
		data: params,
	});
}

export function fetchPPTDelete<T>(params: any) {
  return post<T>({
		url: "/api3/ppt/pptx/del",
		data: params,
	});
}

/**
 * 上传文件V2
 * @param params 
 * @returns 
 */
export function fetchPreviewOutline<T>(params: any) {
	return post<T>({
		url: "/api3/ppt/v2/pptx/add",
		data: params,
	});
}


/**
 * 获取ppt模板列表
 * @param params 
 * @returns 
 */
export function fetchPPTListV2<T>(params: any) {
	return post<T>({
		url: "/api3/ppt/v2/template/list",
		data: params,
	});
}

/**
 * 获取ppt模板目录
 * @param params 
 * @returns 
 */
export function fetchPPTTemplateCatalogV2<T>(params: any) {
	return post<T>({
		url: "/api3/ppt/v2/template/catalog",
		data: params,
	});
}

/**
 * 生成ppt
 * @param params 
 * @returns 
 */
export function fetchPPTGenerate<T>(params: any) {
	return post<T>({
		url: "/api3/ppt/v2/pptx/generate",
		data: params,
	});
}

/**
 * 上传文件
 * @param params 
 * @returns 
 */
export function fetchFileUploadV2<T>(params: { file: File }) {
	const formData = new FormData();
	formData.append('file', params.file);
	
	return post<T>({
		url: "/api3/ppt/v2/pptx/upload",
		data: formData,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}

/**
 * 获取ppt模板列表
 * @param params 
 * @returns 
 */
export function fetchPPTTemplateTopV2<T>(params: any) {
	return post<T>({
		url: "/api3/ppt/v2/template/top",
		data: params,
	});
}

/**
 * 获取我的ppt列表
 * @param params 
 * @returns 
 */
export function fetchMyPPTListV2<T>(params: any) {
	return post<T>({
		url: "/api3/ppt/v2/pptx/list",
		data: params,
	});
}

/**
 * 删除我的ppt
 * @param params 
 * @returns 
 */
export function fetchMyPPTDelV2<T>(params: any) {
	return post<T>({
		url: "/api3/ppt/v2/pptx/del",
		data: params,
	});
}