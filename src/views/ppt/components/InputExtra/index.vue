<template>
    <div class="w-full h-[58px] bg-white rounded-[10px] shadow-box flex justify-between items-center">
        <div class="text-[16px] text-[#3d3d3d] flex flex-row gap-x-[5px]">
            <n-select v-model:value="imageMode" :options="options" :show-arrow="true" placeholder="请选择图片模式"
                @update:value="handleImageModeSelect" style="--n-color: #D8D8D8 !important;min-width: 145px"
                :render-option="renderOption">
            </n-select>
            <!-- <n-select v-model:value="language" :options="languageOptions" :show-arrow="true" placeholder="请选择语言" @update-value="handleLanguageSelect" style="min-width: 100px;"></n-select> -->
        </div>
        <div class="flex items-center gap-x-[25px]">

            <div class="text-[16px] text-[#3d3d3d]">DeepSeek
                <NSwitch v-model:value="isDeepSeek" @update:value="handleDeepSeek" />
            </div>
            <div class="text-[16px] text-[#3d3d3d]">演讲备注
                <NSwitch v-model:value="enableRemark" @update:value="handleEnableRemark" />
            </div>
            <div class="text-[16px] text-[#3d3d3d]">联网搜索
                <NSwitch v-model:value="webSearch" @update:value="handleWebSearch" />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { NSwitch, NSelect, SelectOption, NTooltip } from 'naive-ui';
import { h, ref, VNode } from 'vue';

interface Emit {
    (e: 'update:search', value: { webSearch: boolean, enableRemark: boolean, imageMode: string, language: string, isDeepSeek: boolean }): void
}

const emit = defineEmits<Emit>()

const enableRemark = ref(false)
const webSearch = ref(false)
const isDeepSeek = ref(false)
// imageMode: "off" 关闭 "normal" 正常模式 "advanced" 多图模式
/**
 * 无需AI配图
    所有内容均为文本排版，无AI图片
    普通AI图片模型
    少量AI图文排版，内容更美观，排版更灵活
    高级AI图片模型G
    丰富的AI图文排版，图片质量更高，生成速度更快
 * 
 */
const imageMode = ref('off')
const language = ref('zh')
const renderOption = ({ node, option }: { node: VNode, option: SelectOption }) =>
    h(NTooltip, {
        placement: 'right',
    }, {
        trigger: () => node,
        default: () => option.subTitle,
    })

//  ['zh', 'en', 'ja', 'ko', 'fr', 'de', 'it', 'ar']
// 中文，英文，日文，韩文，法文，德文，意大利文，阿拉伯文
const languageOptions = ref([
    {
        value: 'zh',
        label: '中文'
    },
    {
        value: 'en',
        label: '英文'
    },
    {
        value: 'ja',
        label: '日文'
    },
    {
        value: 'ko',
        label: '韩文'
    },
    {
        value: 'fr',
        label: '法文'
    },
    {
        value: 'de',
        label: '德文'
    },
    {
        value: 'it',
        label: '意大利文'
    },
    {
        value: 'ar',
        label: '阿拉伯文'
    }
])

const options = ref([
    {
        value: 'off',
        label: '无需AI配图',
        subTitle: '所有内容均为文本排版，无AI图片'
    },
    {
        value: 'normal',
        label: '普通AI图片模型',
        subTitle: '少量AI图文排版，内容更美观，排版更灵活'
    },
    {
        value: 'advanced',
        label: '高级AI图片模型',
        subTitle: '丰富的AI图文排版，图片质量更高，生成速度更快'
    }
])

const handleDeepSeek = (value: boolean) => {
    isDeepSeek.value = value;
    emit('update:search', { webSearch: webSearch.value, enableRemark: enableRemark.value, imageMode: imageMode.value, language: language.value, isDeepSeek: value });
}
const handleEnableRemark = (value: boolean) => {
    enableRemark.value = value;
    emit('update:search', { webSearch: webSearch.value, enableRemark: value, imageMode: imageMode.value, language: language.value, isDeepSeek: isDeepSeek.value });
}

const handleWebSearch = (value: boolean) => {
    webSearch.value = value;
    emit('update:search', { webSearch: value, enableRemark: enableRemark.value, imageMode: imageMode.value, language: language.value, isDeepSeek: isDeepSeek.value });
}

const handleImageModeSelect = (value: string) => {
    imageMode.value = value;
    emit('update:search', { webSearch: webSearch.value, enableRemark: enableRemark.value, imageMode: value, language: language.value, isDeepSeek: isDeepSeek.value });
}
const handleLanguageSelect = (value: string) => {
    language.value = value;
    emit('update:search', { webSearch: webSearch.value, enableRemark: enableRemark.value, imageMode: value, language: value, isDeepSeek: isDeepSeek.value })
}

</script>

<style lang="less" scoped></style>
