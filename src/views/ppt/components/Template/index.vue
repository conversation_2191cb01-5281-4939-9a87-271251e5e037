<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import {
	NModal,
	NImage,
	NSpin,
	NSpace,
	NTag
} from 'naive-ui'
import { fetchPPTListV2, fetchPPTTemplateCatalogV2 } from '../../api'
import { useRequest } from 'vue-hooks-plus';
import { CatalogData } from '../../types'
import AllColor from '@/assets/images/all_color.png'
import { useMessage } from 'naive-ui'
const message = useMessage()

interface Props {
	id: any
	visible: boolean
}

interface Emit {
	(e: 'update:visible', visible: boolean): void,
	(ev: 'select', id: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emit>()
const curCategory = ref<number>(-1)
const list = ref()
const selectedId = ref<string | null>(null)
const catelogs = ref<CatalogData>({
	color: [],
	style: [],
	usage: []
})

const curusage = ref<string[]>([])
const curStyle = ref<string[]>([])
const curColor = ref<string[]>([])
const params = ref({
	page: 1,
	pageSize: 100,
	usage: curusage.value,
	style: curStyle.value,
	color: curColor.value
})
// ["蓝色", "绿色", "红色", "紫色", "黑色", "灰色", "黄色", "橙色", "粉色"]
const colors = {
	蓝色: '#5DB0FF',
	绿色: '#004D43',
	红色: '#D75300',
	紫色: '#C56CFF',
	黑色: '#121519',
	灰色: '#818181',
	黄色: '#FFC161',
	橙色: '#FF8248',
	白色: '#E6F1FF',
	粉色: '#FF5C8A'
}
const show = computed({
	get: () => {
		return props.visible
	},
	set: (visible: boolean) => emit('update:visible', visible)
})

onMounted(async () => {
	// const data = await getCptCategory()
	// categorys.value = data
	getModels(-1)
})

const { data: catalog } = useRequest<CatalogData>(fetchPPTTemplateCatalogV2, {
	onSuccess: (data) => {
		catelogs.value = data
	}
})


const { run, loading } = useRequest(() => fetchPPTListV2(params.value), {
	manual: true,
	onSuccess: (data) => {
		console.log(data)
		list.value = data.rows
	}
})


const getModels = async (categoryId: number) => {
	run()
}

const handleCategory = (id: number) => {
	curCategory.value = id
	getModels(id)
}

const handleSelect = (id: string) => {
	selectedId.value = id
}

const handleTagChange = (item: string, type: keyof CatalogData) => {
	const targetRef = type === 'usage' ? curusage : type === 'style' ? curStyle : curColor;
	const index = targetRef.value.indexOf(item);
	if (index > -1) {
		targetRef.value.splice(index, 1);
		params.value[type] = targetRef.value;
		run();
	}
}

const handleTagToggle = (item: string, type: keyof CatalogData) => {
	const targetRef = type === 'usage' ? curusage : type === 'style' ? curStyle : curColor;
	const index = targetRef.value.indexOf(item);
	if (index === -1) {
		targetRef.value.push(item);
	} else {
		targetRef.value.splice(index, 1);
	}
	params.value[type] = targetRef.value;
	run();
}

const handleNext = () => {
	if (selectedId.value) {
		emit('select', selectedId.value)
	} else {
		message.error('请选择模板')
	}
}

</script>
<template>
	<NModal v-model:show="show" style="max-width:60%; width: 1125px; height: 816px;" preset="card">
		<!-- 选择 -->
		<n-space class="mb-[14px] flex-nowrap overflow-x-auto hide-scrollbar">
			<span class="leading-[28px] whitespace-nowrap relative top-[-1px]">模板场景:</span>
			<n-tag style="--n-text-color-checked: #0E69FF;--n-color-checked: #D0E2FF; --n-color-checked-hover: #D0E2FF;"
				v-for="item in catelogs.usage" :key="item" :checked="curusage.includes(item)" type="success" checkable
				@close="() => handleTagChange(item, 'usage')" @click="() => handleTagToggle(item, 'usage')">
				{{ item }}
			</n-tag>
		</n-space>
		<n-space class="mb-[14px] flex-nowrap overflow-x-auto hide-scrollbar">
			<span class="leading-[28px] whitespace-nowrap relative top-[-1px]">设计风格:</span>
			<n-tag v-for="item in catelogs.style" :key="item" :checked="curStyle.includes(item)" type="success"
				style="--n-text-color-checked: #0E69FF;--n-color-checked: #D0E2FF; --n-color-checked-hover: #D0E2FF;"
				checkable @close="() => handleTagChange(item, 'style')" @click="() => handleTagToggle(item, 'style')">
				{{ item }}
			</n-tag>
		</n-space>
		<n-space class="mb-[17px] flex-nowrap overflow-x-auto hide-scrollbar">
			<span class="leading-[28px] whitespace-nowrap relative top-[-1px]">主题颜色:</span>
			<div class="inline-flex items-center cursor-pointer border-[2px] rounded-[4px]"
				:class="{ 'border-[#0E69FF]': curColor.length === 0 }" @click="() => {
					if (curColor.length === 0) return;
					curColor.length = 0;
					params.color = [];
					run();
				}">
				<NImage :src="AllColor" class="w-[28px] h-[28px] rounded-[4px]" preview-disabled />
			</div>
			<div v-for="item in catelogs.color" :key="item"
				class="inline-flex items-center cursor-pointer border-[2px] rounded-[4px]"
				:class="{ 'border-[#0E69FF]': curColor.includes(item), 'border-transparent': !curColor.includes(item) }"
				@click="() => handleTagToggle(item, 'color')">
				<div class="w-[28px] h-[28px] rounded-[2px]" :style="{ backgroundColor: colors[item] }"></div>
			</div>
		</n-space>

		<div class=" text-[#3d3d3d] text-[16px] mb-[10px]">选择一套模板, 开始创建PPT</div>
		<n-spin :show="loading">
			<div v-if="list?.length > 0"
				class="w-full bg-bg-primary border-1 border-stroke-secondary rounded-[12px] flex flex-col gap-[20px]">
				<div class="overflow-auto p-[2px] lg:max-h-[481px] md:max-h-[400px] sm:max-h-[350px] max-h-[300px]">
					<ul class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-[16px] gap-y-[20px]"
						id="img">
						<li class="border overflow-hidden cursor-pointer transition-all duration-300 hover:border-[#0066FF]"
							:class="{ 'border-[2px] border-[#0066FF]': selectedId === item.id, 'border-[2px] border-[#eee]': selectedId !== item.id }"
							v-for="(item, index) in list" :key="index" @click="handleSelect(item.id)">
							<div class="relative w-full aspect-[247/215]">
								<NImage :src="item.previewImages.titleCoverImageLarge || item.previewImage"
									class="w-full h-[66.67%] object-cover" preview-disabled />
								<div class="flex h-[33.33%]">
									<NImage :src="item.previewImages.catalogueCoverImage"
										class="w-1/2 h-full object-cover" preview-disabled />
									<NImage :src="item.previewImages.chapterCoverImage"
										class="w-1/2 h-full object-cover" preview-disabled />
								</div>
							</div>
						</li>
					</ul>
				</div>
			</div>
			<!-- 暂无 -->
			<div v-else-if="!loading && list?.length === 0" class="flex justify-center items-center h-full">
				<div class="text-[#3d3d3d] text-[16px]">暂无模板</div>
			</div>
		</n-spin>
		<template #footer>
			<div class="flex justify-end">
				<div class="next-btn relative" @click="handleNext">
					下一步
				</div>
			</div>
		</template>
	</NModal>
</template>
<style>
#img img {
	object-position: center top;
}

.next-btn {
	width: 102px;
	height: 40px;
	box-sizing: border-box;
	background: linear-gradient(90deg, #2079ff 0%, #af53ff 100%);
	border-radius: 4px;
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 7px;
	font-size: 14px;
	color: #ffffff;
	cursor: pointer;

	transition: all 0.3s ease-in-out;

	&:hover {
		background: linear-gradient(-90deg, #2079ff 0%, #af53ff 100%);
	}
}

/* #img img:hover {
	transition-timing-function: linear;
	transition-duration: 300ms;
	transition-property: all;
	height: auto;
	transform: translate(0, 0) rotate(0) skewX(0) skewY(0) scaleX(1.05) scaleY(1.05);
} */
</style>
