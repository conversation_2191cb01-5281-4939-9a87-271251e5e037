<template>
    <div v-if="loading" class="w-full h-full rounded-[10px] absolute bg-white/[0.5] inset-0 z-50">
        <n-image :src="loadingIcon" width="100" height="100"
            class="absolute left-1/2 top-1/2 -ml-[50px] -mt-[50px] bg-white rounded-[10px]" />
    </div>
</template>
  
<script lang="ts" setup>
import { ref } from "vue";
import { NImage } from 'naive-ui'
import loadingIcon from '@/assets/images/loading.gif'

interface Props {
    loading: boolean;
}
defineProps<Props>();

</script>
  
<style scoped></style>
  