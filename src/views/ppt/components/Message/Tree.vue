<template>
  <div>
    <TreeNode :tree-data="treeData" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { markdownToJson } from "@/utils/markdown/md2json";
import TreeNode from './TreeNode.vue'

interface Props {
  text: string;
}
const props = defineProps<Props>();
// let markdown = props.text;
let markdown = props.text.replace(/```markdown/g, "").replace(/```/g, "");

const treeData = ref(markdownToJson(markdown));

</script>

<style scoped></style>
