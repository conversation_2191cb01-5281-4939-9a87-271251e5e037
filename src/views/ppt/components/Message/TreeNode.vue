<template>
  <div>
    <ul class="tree">
      <li v-for="(item, index) in treeData" :key="index">
        <summary :class="item.depth == 1 ? 'font-bold text-[16px]' : ''">{{ item.text }}
          <n-tag type="info" size="small" round v-if="item.depth === 1">
            主题
          </n-tag>
          <n-tag type="primary" size="small" round v-if="item.depth === 2">
            章节
          </n-tag>
          <n-tag type="warning" size="small" round v-if="item.depth === 3">
            标题
          </n-tag>
        </summary>
        <tree-node v-if="item.children && item.type !== 'list_item'" :tree-data="item.children"></tree-node>
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { NTag } from "naive-ui";

interface Props {
  treeData: any[]
}
const props = defineProps<Props>()
</script>

<style scoped>
.tree {
  --spacing: 1.5rem;
  --radius: 3px;
}

.tree li {
  display: block;
  position: relative;
  padding-left: calc(2 * var(--spacing) - var(--radius) - 2px);
}

.tree ul {
  margin-left: calc(var(--radius) - var(--spacing));
  padding-left: 0;
}

.tree ul li {
  border-left: 1px solid #E0E0E0;
}

.tree ul li:last-child {
  border-color: transparent;
}

.tree ul li::before {
  content: '';
  display: block;
  position: absolute;
  top: calc(var(--spacing) / -2);
  left: -1px;
  width: calc(var(--spacing) + 2px);
  height: calc(var(--spacing) + 1px + 0.5rem);
  border: solid #E0E0E0;
  border-width: 0 0 1px 1px;
}

.tree summary {
  display: block;
  cursor: pointer;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.tree summary::marker,
.tree summary::-webkit-details-marker {
  display: none;
}

.tree summary:focus {
  outline: none;
}

.tree summary:focus-visible {
  outline: 1px dotted #000;
}

.tree li::after,
.tree summary::before {
  content: '';
  display: block;
  position: absolute;
  top: calc(var(--spacing) / 2 - var(--radius));
  left: calc(var(--spacing) - var(--radius) - 1px);
  width: calc(2 * var(--radius));
  height: calc(2 * var(--radius));
  border-radius: 50%;
  background: #E0E0E0;
  margin-top: 0.5rem;
}

.tree summary::before {
  content: '';
  z-index: 1;
  background: #fff;
  outline: 2px solid #3dbaa1;
  color: #fff;
  line-height: calc(2 * var(--radius) - 2px);
  text-align: center;
}

.tree details[open]>summary::before {
  content: '';
}
</style>
