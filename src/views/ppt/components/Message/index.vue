<template>
    <TextComponent ref="textRef" :error="error" :text="text" :loading="loading" :as-raw-text="asRawText" @render-text="renderText" />
</template>
  
<script lang="ts" setup>
import { ref,onBeforeUnmount } from 'vue'
import TextComponent from './Text.vue'

interface Props {
    text: string
    inversion?: boolean
    error?: boolean
    loading?: boolean,
    isCompute?: boolean
}

const props = defineProps<Props>()
const textRef = ref<HTMLElement>()
const asRawText = ref(props.inversion)
const copyText = ref<any>('')
const renderText = (msg: string) => {
    copyText.value = msg
}

onBeforeUnmount(()=> {
    console.log('销毁组件');
    
})

</script>

<style scoped></style>
  