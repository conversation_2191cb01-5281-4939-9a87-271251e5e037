<template>
    <div class="flex flex-row justify-center items-center gap-x-[10px]">
        <!-- 点击导入文档 -->
        <div class="flex flex-col items-center justify-center cursor-pointer import-card w-[50%]"
            @click="showUploadModal = true">
            <n-image :src="DocumentImg" preview-disabled class="w-[26px] h-[26px]" />
            <span class="text-[16px] text-[#3D3D3D]">导入文档</span>
            <span class="text-[12px] text-[#B5B5B5]">支持doc、docx、pptx格式</span>
        </div>
        <!-- 点击输入文本 -->
        <div class="flex flex-col items-center justify-center cursor-pointer import-card w-[50%]"
            @click="showTextModal = true">
            <n-image :src="FontImg" preview-disabled class="w-[26px] h-[26px]" />
            <span class="text-[16px] text-[#3D3D3D]">点击输入文本</span>
            <span class="text-[12px] text-[#B5B5B5]">支持10000字文本</span>
        </div>
    </div>

    <n-modal v-model:show="showTextModal" :on-after-leave="handleAfterLeave" :style="{ width: '804px' }">
        <n-card title="文本输入" role="dialog" aria-modal="true" style="width: 804px;">
            <n-input type="textarea" v-model:value="extraContent" placeholder="请输入文本内容" rows="10" show-count
                maxlength="10000" />
            <template #footer>
                <div class="flex justify-center">
                    <!-- 最大宽度318px -->
                    <n-button type="primary" :disabled="!extraContent" @click="handleTextSubmit"
                        class="!max-w-[318px] !w-[40%]">确认</n-button>
                </div>
            </template>
        </n-card>
    </n-modal>
    <!-- 文件上传 -->
    <n-modal v-model:show="showUploadModal" title="上传文件" :on-after-leave="handleAfterLeave" :style="{ width: '804px' }">
        <n-card title="上传文档" role="dialog" aria-modal="true" style="width: 804px;">
            <n-upload action="/api3/ppt/v2/pptx/upload" :headers="headers" :max="1" accept=".doc,.docx,.pptx"
                :max-size="10 * 1024 * 1024" @finish="handleUploadFinish" @before-upload="handleBeforeUpload">
                <n-upload-dragger>
                    <div class="flex flex-col items-center">
                        <n-icon size="48" depth="3">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <path
                                    d="M11 14.9861C11 15.5384 11.4477 15.9861 12 15.9861C12.5523 15.9861 13 15.5384 13 14.9861V7.82831L16.2428 11.0711L17.657 9.65685L12.0001 4L6.34326 9.65685L7.75748 11.0711L11 7.82854V14.9861Z">
                                </path>
                                <path
                                    d="M4 14H6V18H18V14H20V18C20 19.1046 19.1046 20 18 20H6C4.89543 20 4 19.1046 4 18V14Z">
                                </path>
                            </svg>
                        </n-icon>
                        <p>将文件拖到此处或 
                            <span class="text-[#0066FF] cursor-pointer">点击上传</span>
                        </p>
                        <p class="text-gray-400 text-sm">上传文件支持doc、docx、pptx格式的文档， 文件大小不能超过10MB</p>
                    </div>
                </n-upload-dragger>
            </n-upload>
        </n-card>
    </n-modal>
</template>

<script lang="ts" setup>
import { NImage, NModal, NCard, NUpload, NUploadDragger, NButton, NInput, NIcon, useMessage } from 'naive-ui';
import DocumentImg from '@/assets/images/document.png'
import FontImg from '@/assets/images/font.png'
import { ref } from 'vue';
import { TypeEnums } from '../../types';
import { useAuthStore } from '@/store';

const message = useMessage();

const showTextModal = ref(false)
const showUploadModal = ref(false)
const extraContent = ref('')
interface Emit {
    (e: 'update:search', value: { type: TypeEnums, extraContent?: string, fileUrl?: string }): void
}

const emit = defineEmits<Emit>()
const authStore = useAuthStore()

const headers = ref({
    Authorization: `Bearer ${authStore.token}`
})
const handleTextSubmit = () => {
    showTextModal.value = false
    emit('update:search', {
        type: TypeEnums.TEXT,
        extraContent: extraContent.value
    })
}

const handleUploadFinish = ({ event }: { event?: ProgressEvent<EventTarget> }) => {
    showUploadModal.value = false
    if (event && (event.currentTarget as XMLHttpRequest)?.response) {
        try {
            const { data, errcode, errmsg } = JSON.parse((event.currentTarget as XMLHttpRequest).response)
            if (errcode) {
                message.error(errmsg || '上传失败')
                return
            }
            const { key } = data
            emit('update:search', {
                type: TypeEnums.UPLOAD,
                fileUrl: key
            })
        } catch (error) {
            console.log(error);
        }
    }
}

const handleBeforeUpload = (data: { file: File }) => {
    if (data.file.size > 10 * 1024 * 1024) {
        message.error('文件大小不能超过10MB')
        return false
    }
    return true
}

const handleAfterLeave = () => {
    extraContent.value = ''
}
</script>

<style lang="less" scoped>
.import-card {
    height: 110px;
    background: #F8FBFF;
    border: 1px dashed #0E69FF;
    border-radius: 8px;
}
</style>