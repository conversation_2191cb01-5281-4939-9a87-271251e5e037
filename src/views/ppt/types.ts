export const enum TypeEnums {
    TEXT = 'text',
    UPLOAD = 'upload',
}

export interface TreeData {
    type: string;
    title: string;
    text?: string;
    depth?: number;
    children?: TreeData[];
    chapterNum: number;
    paragraphNum?: number;
}

export interface TreeNodeProps {
    treeData: TreeData[];
    isEdit?: boolean;
}

export interface CatalogData {
    color: string[];
    style: string[];
    usage: string[];
}