const _currentYear = new Date().getFullYear();
const currentMonth = new Date().getMonth() + 1;
const currentYear = currentMonth >5 ? _currentYear : _currentYear - 1;

export const caseList = [
		{
			"id": 110,
			"title": `${currentYear}年终述职报告`,
			"markdown": `
# ${currentYear}年终述职报告

## 1. 项目概述
### 1.1 ${currentYear}年本人参与的主要项目
#### 1.1.1 项目一：AI估值模型开发

#### 1.1.2 项目二：自动化测试平台建设

#### 1.1.3 项目三：分布式系统优化


### 1.2 总结本人在项目实施中的主要工作
#### 1.2.1 编写和维护代码

#### 1.2.2 解决技术问题和bug修复

#### 1.2.3 参与需求分析和系统设计

#### 1.2.4 参与代码评审和团队合作


## 2. 技术能力进阶
### 2.1 掌握的新技术和工具
#### 2.1.1 新编程语言和框架

#### 2.1.2 新的开发和测试工具

#### 2.1.3 AI和大数据相关技术


### 2.2 技术难题与解决方案
#### 2.2.1 遇到的主要技术难题

#### 2.2.2 独立或协同解决的技术问题


## 3. 团队协作与领导力
### 3.1 在团队中扮演的角色和贡献
#### 3.1.1 我在团队中的主要角色

#### 3.1.2 对团队的主要贡献


### 3.2 领导能力的提升
#### 3.2.1 我的领导风格与实践

#### 3.2.2 遇到的领导挑战和解决方法


## 4. 个人职业发展规划
### 4.1 ${currentYear}年自我专业能力提升总结
#### 4.1.1 技术能力方面的提升

#### 4.1.2 管理和领导能力的提升


### 4.2 ${currentYear + 1}年职业发展目标与计划
#### 4.2.1 ${currentYear + 1}年的职业发展目标

#### 4.2.2 实现职业发展目标的具体计划

`,
			"nodes": {
				"title": `${currentYear}年终述职报告`,
				"subTitle": '砥砺前行，续写华章',
				"nodes": [
					{
						"type": "chapter",
						"title": "项目概述",
						"chapterNum": 1
					},
					{
						"type": "paragraph",
						"title": `${currentYear}年本人参与的主要项目`,
						"chapterNum": 1,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "总结本人在项目实施中的主要工作",
						"chapterNum": 1,
						"paragraphNum": 2
					},
					{
						"type": "chapter",
						"title": "技术能力进阶",
						"chapterNum": 2
					},
					{
						"type": "paragraph",
						"title": "掌握的新技术和工具",
						"chapterNum": 2,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "技术难题与解决方案",
						"chapterNum": 2,
						"paragraphNum": 2
					},
					{
						"type": "chapter",
						"title": "团队协作与领导力",
						"chapterNum": 3
					},
					{
						"type": "paragraph",
						"title": "在团队中扮演的角色和贡献",
						"chapterNum": 3,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "领导能力的提升",
						"chapterNum": 3,
						"paragraphNum": 2
					},
					{
						"type": "chapter",
						"title": "个人职业发展规划",
						"chapterNum": 4
					},
					{
						"type": "paragraph",
						"title": `${currentYear}年自我专业能力提升总结`,
						"chapterNum": 4,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": `${currentYear + 1}年职业发展目标与计划`,
						"chapterNum": 4,
						"paragraphNum": 2
					}
				]
			}
		},
		{
			"id": 111,
			"title": "金融行业年终总结",
			"markdown": `
# 金融行业年终总结
## 1. 行业概述
### 1.1 行业背景
#### 1.1.1 金融行业的发展历程


#### 1.1.2 金融行业的重要性及地位


### 1.2 年度市场回顾
#### 1.2.1 宏观经济形势回顾


#### 1.2.2 金融市场表现回顾


#### 1.2.3 金融政策变化回顾


### 1.3 行业发展趋势
#### 1.3.1 技术创新对金融行业的影响


#### 1.3.2 金融监管政策趋势分析


#### 1.3.3 金融市场竞争格局展望


#### 1.3.4 金融科技的发展前景


## 2. 公司业绩总结
### 2.1 公司背景介绍
#### 2.1.1 公司规模与业务范围

#### 2.1.2 公司竞争优势分析

### 2.2 年度业绩回顾
#### 2.2.1 公司财务状况总结

#### 2.2.2 业务成果与发展情况

#### 2.2.3 关键项目与合作伙伴介绍

### 2.3 业绩分析与评估
#### 2.3.1 业务增长情况分析

#### 2.3.2 利润与成本控制分析

#### 2.3.3 客户满意度与市场反应评估


## 3. 风险管理与合规总结
### 3.1 风险管理体系建设
#### 3.1.1 风险管理框架介绍


#### 3.1.2 风险管理流程与控制措施


### 3.2 合规与监管情况回顾
#### 3.2.1 相关法律法规及政策变化


#### 3.2.2 公司合规情况总结


### 3.3 风险与合规问题解决
#### 3.3.1 风险事件及处理措施回顾


#### 3.3.2 合规问题解决方案总结


## 4. 市场营销与客户服务总结
### 4.1 市场营销策略回顾
#### 4.1.1 品牌推广与传播策略


#### 4.1.2 市场定位与目标客户策略


#### 4.1.3 产品与服务创新策略


### 4.2 客户服务与满意度分析
#### 4.2.1 客户需求分析与反馈总结


#### 4.2.2 客户投诉处理与改进措施


#### 4.2.3 客户满意度评估与提升计划


## 5. 未来展望与规划
### 5.1 行业趋势与发展预测
#### 5.1.1 金融行业未来发展趋势分析




#### 5.1.2 新技术对金融行业的影响预测




#### 5.1.3 政策环境对金融行业的影响预测




### 5.2 公司发展规划设想
#### 5.2.1 本年度工作总结


#### 5.2.2 未来一年发展目标设定


#### 5.2.3 关键策略与措施规划


#### 5.2.4 人力资源与资金需求评估

            `,
			"nodes": {
				"title": "金融行业年终总结",
				"subTitle": '砥砺前行，铸就未来',
				"nodes": [
					{
						"type": "chapter",
						"title": "行业概述",
						"chapterNum": 1
					},
					{
						"type": "paragraph",
						"title": "行业背景",
						"chapterNum": 1,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "年度市场回顾",
						"chapterNum": 1,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "行业发展趋势",
						"chapterNum": 1,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "公司业绩总结",
						"chapterNum": 2
					},
					{
						"type": "paragraph",
						"title": "公司背景介绍",
						"chapterNum": 2,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "年度业绩回顾",
						"chapterNum": 2,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "业绩分析与评估",
						"chapterNum": 2,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "风险管理与合规总结",
						"chapterNum": 3
					},
					{
						"type": "paragraph",
						"title": "风险管理体系建设",
						"chapterNum": 3,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "合规与监管情况回顾",
						"chapterNum": 3,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "风险与合规问题解决",
						"chapterNum": 3,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "市场营销与客户服务总结",
						"chapterNum": 4
					},
					{
						"type": "paragraph",
						"title": "市场营销策略回顾",
						"chapterNum": 4,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "客户服务与满意度分析",
						"chapterNum": 4,
						"paragraphNum": 2
					},
					{
						"type": "chapter",
						"title": "未来展望与规划",
						"chapterNum": 5
					},
					{
						"type": "paragraph",
						"title": "行业趋势与发展预测",
						"chapterNum": 5,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "公司发展规划设想",
						"chapterNum": 5,
						"paragraphNum": 2
					}
				]
			}
		},
		{
			"id": 112,
			"title": "大学生毕业答辩",
			"markdown": `
# 大学生毕业答辩
## 1. 项目概述
### 1.1 项目背景与意义
#### 1.1.1 项目发起背景分析

#### 1.1.2 项目存在的意义阐述

#### 1.1.3 针对性问题的指出


### 1.2 项目目标与要求
#### 1.2.1 详述项目目标

#### 1.2.2 列举项目实施要求

#### 1.2.3 拟定实施方案和时间表


### 1.3 项目开发技术及工具介绍
#### 1.3.1 描述使用的开发语言或框架

#### 1.3.2 介绍软件开发和管理工具

#### 1.3.3 解释选用该技术栈的理由


## 2. 项目实施过程
### 2.1 需求分析与设计
#### 2.1.1 项目需求收集和整理

#### 2.1.2 提出项目功能模块划分

#### 2.1.3 搭建项目架构设计图


### 2.2 代码编写与维护
#### 2.2.1 展示核心代码和算法

#### 2.2.2 介绍代码优化和重构过程

#### 2.2.3 阐述异常处理和BUG修复策略


### 2.3 系统测试与评估
#### 2.3.1 设定系统性能指标

#### 2.3.2 阐述系统性能测试过程

#### 2.3.3 分析系统评估结果


## 3. 项目实施成果与反思
### 3.1 展示项目最终效果
#### 3.1.1 演示项目界面及功能

#### 3.1.2 分析项目达成目标程度

            `,
			"nodes": {
				"title": "大学生毕业答辩",
				"subTitle": '青春飞扬，梦想起航',
				"nodes": [
					{
						"type": "chapter",
						"title": "项目概述",
						"chapterNum": 1
					},
					{
						"type": "paragraph",
						"title": "项目背景与意义",
						"chapterNum": 1,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "项目目标与要求",
						"chapterNum": 1,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "项目开发技术及工具介绍",
						"chapterNum": 1,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "项目实施过程",
						"chapterNum": 2
					},
					{
						"type": "paragraph",
						"title": "需求分析与设计",
						"chapterNum": 2,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "代码编写与维护",
						"chapterNum": 2,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "系统测试与评估",
						"chapterNum": 2,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "项目实施成果与反思",
						"chapterNum": 3
					},
					{
						"type": "paragraph",
						"title": "展示项目最终效果",
						"chapterNum": 3,
						"paragraphNum": 1
					}
				]
			}
		},
		{
			"id": 113,
			"title": "成就与挑战之年度回顾",
			"markdown": `
# 成就与挑战之年度回顾
## 1. 成就回顾
### 1.1 完成的目标
#### 1.1.1 目标1的达成情况

#### 1.1.2 目标2的达成情况

#### 1.1.3 目标3的达成情况

### 1.2 重要里程碑
#### 1.2.1 里程碑1的完成情况

#### 1.2.2 里程碑2的完成情况

#### 1.2.3 里程碑3的完成情况

### 1.3 获得的荣誉与奖励
#### 1.3.1 获得的荣誉1

#### 1.3.2 获得的荣誉2

#### 1.3.3 获得的荣誉3


## 2. 挑战总结
### 2.1 面临的挑战
#### 2.1.1 挑战1的具体情况


#### 2.1.2 挑战2的具体情况


#### 2.1.3 挑战3的具体情况


### 2.2 应对策略与经验教训
#### 2.2.1 应对挑战1的策略与效果


#### 2.2.2 应对挑战2的策略与效果


#### 2.2.3 应对挑战3的策略与效果


## 3. 成长与收获
### 3.1 个人成长与提升
#### 3.1.1 成长经历1的总结与收获


#### 3.1.2 成长经历2的总结与收获


#### 3.1.3 成长经历3的总结与收获


### 3.2 技能与知识的积累
#### 3.2.1 技能1的学习与应用情况


#### 3.2.2 技能2的学习与应用情况


#### 3.2.3 技能3的学习与应用情况


## 4. 未来展望与规划
### 4.1 下一阶段目标设定
#### 4.1.1 目标1的设定与具体计划


#### 4.1.2 目标2的设定与具体计划


#### 4.1.3 目标3的设定与具体计划


### 4.2 发展规划与策略
#### 4.2.1 发展规划1的制定与实施


#### 4.2.2 发展规划2的制定与实施


#### 4.2.3 发展规划3的制定与实施

`,
			"nodes": {
				"title": "成就与挑战之年度回顾",
				"subTitle": '总结过往，砥砺前行',
				"nodes": [
					{
						"type": "chapter",
						"title": "成就回顾",
						"chapterNum": 1
					},
					{
						"type": "paragraph",
						"title": "完成的目标",
						"chapterNum": 1,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "重要里程碑",
						"chapterNum": 1,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "获得的荣誉与奖励",
						"chapterNum": 1,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "挑战总结",
						"chapterNum": 2
					},
					{
						"type": "paragraph",
						"title": "面临的挑战",
						"chapterNum": 2,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "应对策略与经验教训",
						"chapterNum": 2,
						"paragraphNum": 2
					},
					{
						"type": "chapter",
						"title": "成长与收获",
						"chapterNum": 3
					},
					{
						"type": "paragraph",
						"title": "个人成长与提升",
						"chapterNum": 3,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "技能与知识的积累",
						"chapterNum": 3,
						"paragraphNum": 2
					},
					{
						"type": "chapter",
						"title": "未来展望与规划",
						"chapterNum": 4
					},
					{
						"type": "paragraph",
						"title": "下一阶段目标设定",
						"chapterNum": 4,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "发展规划与策略",
						"chapterNum": 4,
						"paragraphNum": 2
					}
				]
			}
		},
		{
			"id": 114,
			"title": "入职培训PPT",
			"markdown": `
# 入职培训PPT

## 1. 入职培训概述
### 1.1 入职培训的意义
#### 1.1.1 为什么需要入职培训


#### 1.1.2 入职培训的目标和内容


### 1.2 入职培训的形式和方法
#### 1.2.1 基本的培训形式和方法


#### 1.2.2 适合不同岗位和职级的培训形式和方法


### 1.3 入职培训的考核和评估
#### 1.3.1 培训考核的目的和标准


#### 1.3.2 培训评估的方法和周期

#### 1.3.3 培训反馈的收集和处理


## 2. 入职培训内容
### 2.1 公司介绍
#### 2.1.1 公司历史和文化


#### 2.1.2 公司业务和产品


### 2.2 岗位介绍
#### 2.2.1 岗位职责和工作流程


#### 2.2.2 岗位技能和能力要求


### 2.3 工作规范和流程
#### 2.3.1 公司的工作流程和规范


#### 2.3.2 工作中需要遵守的法律法规和道德规范


### 2.4 培训证书和考试
#### 2.4.1 培训证书的颁发和使用


#### 2.4.2 培训考试的形式和内容


## 3. 入职培训后续支持
### 3.1 培训后的常规支持
#### 3.1.1 常规问题的解答和支持


#### 3.1.2 常见问题的解决方法和技巧


### 3.2 培训后的个人成长
#### 3.2.1 个人成长的目标和计划


#### 3.2.2 个人发展的机会和资源


### 3.3 培训后的团队合作
#### 3.3.1 团队合作的基本原则和技巧


#### 3.3.2 团队合作的挑战和解决方法

            `,
			"nodes": {
				"title": "入职培训PPT",
				"subTitle": '开启职业生涯新征程',
				"nodes": [
					{
						"type": "chapter",
						"title": "入职培训概述",
						"chapterNum": 1
					},
					{
						"type": "paragraph",
						"title": "入职培训的意义",
						"chapterNum": 1,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "入职培训的形式和方法",
						"chapterNum": 1,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "入职培训的考核和评估",
						"chapterNum": 1,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "入职培训内容",
						"chapterNum": 2
					},
					{
						"type": "paragraph",
						"title": "公司介绍",
						"chapterNum": 2,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "岗位介绍",
						"chapterNum": 2,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "工作规范和流程",
						"chapterNum": 2,
						"paragraphNum": 3
					},
					{
						"type": "paragraph",
						"title": "培训证书和考试",
						"chapterNum": 2,
						"paragraphNum": 4
					},
					{
						"type": "chapter",
						"title": "入职培训后续支持",
						"chapterNum": 3
					},
					{
						"type": "paragraph",
						"title": "培训后的常规支持",
						"chapterNum": 3,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "培训后的个人成长",
						"chapterNum": 3,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "培训后的团队合作",
						"chapterNum": 3,
						"paragraphNum": 3
					}
				]
			}
		},
		{
			"id": 115,
			"title": `${currentYear + 1}年职业目标`,
			"markdown": `
# ${currentYear + 1}年职业目标
## 1. 当前职业状态分析
### 1.1 目前职业发展情况
#### 1.1.1 目前所从事的行业和职位


#### 1.1.2 工作经验和技能积累情况


#### 1.1.3 职业发展的瓶颈和不足之处


### 1.2 职业目标的确定
#### 1.2.1 对未来职业发展的期望和愿景


#### 1.2.2 职业目标的可行性和可实现性评估


#### 1.2.3 目标的明确性和具体性分析


### 1.3 目标达成的重要性
#### 1.3.1 为何要设定明确的职业目标

#### 1.3.2 目标对个人职业发展的影响和意义

#### 1.3.3 目标达成对个人成就感和满足感的作用


## 2. 实现职业目标的路径规划
### 2.1 现有资源和条件分析
#### 2.1.1 分析个人的优势和劣势


#### 2.1.2 现有的人脉和社交资源


#### 2.1.3 学历和专业背景对目标的影响评估


### 2.2 增强职业能力和技能
#### 2.2.1 所需的核心职业能力和技能


#### 2.2.2 学习和提升能力的方法和途径


#### 2.2.3 专业培训和认证的价值和选择


### 2.3 拓宽职业发展的机会和平台
#### 2.3.1 行业协会和组织的参与


#### 2.3.2 参加行业会议和交流活动的重要性


#### 2.3.3 参与社区和公益活动的意义和价值


### 2.4 寻找合适的职业机会和岗位
#### 2.4.1 职业市场的调研和分析


#### 2.4.2 个人简历和求职材料的准备


#### 2.4.3 面试和沟通技巧的提升


## 3. 职业目标实施与评估
### 3.1 设定明确的阶段性目标
#### 3.1.1 长期职业目标的分解和划分


#### 3.1.2 设定合理的时间和进度要求


#### 3.1.3 目标达成的评估和反馈机制


### 3.2 实施职业目标的行动计划
#### 3.2.1 制定具体的行动计划和时间表


#### 3.2.2 分配资源和任务的优先级和重要性


#### 3.2.3 风险评估和应对策略的制定


### 3.3 目标达成的跟踪和调整
#### 3.3.1 目标达成情况的定期检查和总结


#### 3.3.2 根据实际情况调整目标和计划


#### 3.3.3 持续学习和反思的重要性


## 4. 职业目标实现的影响和意义
### 4.1 个人成长和发展
#### 4.1.1 职业目标实现对个人能力的提升


#### 4.1.2 经验和技能积累对职业发展的促进


#### 4.1.3 职业目标实现对个人自信心的增强


### 4.2 社会价值和贡献
#### 4.2.1 职业目标实现对社会的影响和贡献


#### 4.2.2 个人职业目标与社会发展的关联


#### 4.2.3 职业目标实现对他人的激励和启示


### 4.3 生活意义和幸福感
#### 4.3.1 职业目标实现对个人生活的改善和满足


#### 4.3.2 平衡职业目标与家庭、健康的关系


#### 4.3.3 职业目标实现对个人幸福感的影响

`,
			"nodes": {
				"title": `${currentYear + 1}年职业目标`,
				"subTitle": '迈向成功的职业征程',
				"nodes": [
					{
						"type": "chapter",
						"title": "当前职业状态分析",
						"chapterNum": 1
					},
					{
						"type": "paragraph",
						"title": "目前职业发展情况",
						"chapterNum": 1,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "职业目标的确定",
						"chapterNum": 1,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "目标达成的重要性",
						"chapterNum": 1,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "实现职业目标的路径规划",
						"chapterNum": 2
					},
					{
						"type": "paragraph",
						"title": "现有资源和条件分析",
						"chapterNum": 2,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "增强职业能力和技能",
						"chapterNum": 2,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "拓宽职业发展的机会和平台",
						"chapterNum": 2,
						"paragraphNum": 3
					},
					{
						"type": "paragraph",
						"title": "寻找合适的职业机会和岗位",
						"chapterNum": 2,
						"paragraphNum": 4
					},
					{
						"type": "chapter",
						"title": "职业目标实施与评估",
						"chapterNum": 3
					},
					{
						"type": "paragraph",
						"title": "设定明确的阶段性目标",
						"chapterNum": 3,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "实施职业目标的行动计划",
						"chapterNum": 3,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "目标达成的跟踪和调整",
						"chapterNum": 3,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "职业目标实现的影响和意义",
						"chapterNum": 4
					},
					{
						"type": "paragraph",
						"title": "个人成长和发展",
						"chapterNum": 4,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "社会价值和贡献",
						"chapterNum": 4,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "生活意义和幸福感",
						"chapterNum": 4,
						"paragraphNum": 3
					}
				]
			}
		},
		{
			"id": 117,
			"title": "竞品分析全方位解析",
			"markdown": `
# 竞品分析全方位解析
## 1. 竞品分析概述
### 1.1 什么是竞品分析
#### 1.1.1 定义与概念


#### 1.1.2 目的与意义


#### 1.1.3 分析对象与范围


### 1.2 竞品分析的方法论
#### 1.2.1 竞品信息的收集方法


#### 1.2.2 竞品信息的分析方法


#### 1.2.3 竞品信息的应用方法


### 1.3 竞品分析的注意事项
#### 1.3.1 数据的真实性与可靠性


#### 1.3.2 比较的客观性与合理性


#### 1.3.3 结论的准确性与全面性


## 2. 竞品分析的实施步骤
### 2.1 确定分析目标与范围
#### 2.1.1 确定分析的产品/服务类型

#### 2.1.2 确定分析的市场/行业范围

#### 2.1.3 确定分析的关键指标和因素

### 2.2 收集竞品信息
#### 2.2.1 竞品产品/服务的基本信息

#### 2.2.2 竞品市场/行业的趋势信息

#### 2.2.3 竞品营销/推广的策略信息

#### 2.2.4 竞品用户/消费者的反馈信息

### 2.3 分析竞品信息
#### 2.3.1 竞品产品/服务的特点分析

#### 2.3.2 竞品市场/行业的竞争分析

#### 2.3.3 竞品营销/推广的效果分析

#### 2.3.4 竞品用户/消费者的需求分析

### 2.4 应用竞品信息
#### 2.4.1 制定产品/服务的优化策略

#### 2.4.2 制定市场/行业的进攻策略

#### 2.4.3 制定营销/推广的创新策略

#### 2.4.4 制定用户/消费者的满意策略


## 3. 竞品分析的案例分享
### 3.1 电商行业竞品分析
#### 3.1.1 分析对象的选择与范围


#### 3.1.2 竞品信息的收集与分析


#### 3.1.3 应用竞品信息的实践效果


### 3.2 金融行业竞品分析
#### 3.2.1 分析对象的选择与范围


#### 3.2.2 竞品信息的收集与分析


#### 3.2.3 应用竞品信息的实践效果


### 3.3 互联网行业竞品分析
#### 3.3.1 分析对象的选择与范围


#### 3.3.2 竞品信息的收集与分析


#### 3.3.3 应用竞品信息的实践效果


## 4. 竞品分析的进阶应用
### 4.1 竞品战略的制定与执行
#### 4.1.1 竞品战略的定位与选择


#### 4.1.2 竞品战略的实施与跟踪


### 4.2 竞品创新的探索与实践
#### 4.2.1 竞品创新的思考与方法


#### 4.2.2 竞品创新的案例与实践


### 4.3 竞品合作的机会与挑战
#### 4.3.1 竞品合作的背景与前景


#### 4.3.2 竞品合作的模式与实践


#### 4.3.3 竞品合作的风险与规避


## 5. 竞品分析的未来展望
### 5.1 竞品分析的趋势与预测
#### 5.1.1 数据化竞品分析的趋势


#### 5.1.2 AI技术在竞品分析中的应用


#### 5.1.3 行业/领域竞品分析的发展趋势


### 5.2 竞品分析的发展机遇与挑战
#### 5.2.1 竞品分析的市场机遇与前景


#### 5.2.2 竞品分析的技术挑战与瓶颈


#### 5.2.3 竞品分析的人才需求与培养


### 5.3 竞品分析的规范与标准化
#### 5.3.1 竞品分析的规范化建设需求


#### 5.3.2 竞品分析的标准化制定挑战


#### 5.3.3 竞品分析的行业/领域标准化实践

            `,
			"nodes": {
				"title": "竞品分析全方位解析",
				"subTitle": '洞察市场，决胜千里',
				"nodes": [
					{
						"type": "chapter",
						"title": "竞品分析概述",
						"chapterNum": 1
					},
					{
						"type": "paragraph",
						"title": "什么是竞品分析",
						"chapterNum": 1,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "竞品分析的方法论",
						"chapterNum": 1,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "竞品分析的注意事项",
						"chapterNum": 1,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "竞品分析的实施步骤",
						"chapterNum": 2
					},
					{
						"type": "paragraph",
						"title": "确定分析目标与范围",
						"chapterNum": 2,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "收集竞品信息",
						"chapterNum": 2,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "分析竞品信息",
						"chapterNum": 2,
						"paragraphNum": 3
					},
					{
						"type": "paragraph",
						"title": "应用竞品信息",
						"chapterNum": 2,
						"paragraphNum": 4
					},
					{
						"type": "chapter",
						"title": "竞品分析的案例分享",
						"chapterNum": 3
					},
					{
						"type": "paragraph",
						"title": "电商行业竞品分析",
						"chapterNum": 3,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "金融行业竞品分析",
						"chapterNum": 3,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "互联网行业竞品分析",
						"chapterNum": 3,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "竞品分析的进阶应用",
						"chapterNum": 4
					},
					{
						"type": "paragraph",
						"title": "竞品战略的制定与执行",
						"chapterNum": 4,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "竞品创新的探索与实践",
						"chapterNum": 4,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "竞品合作的机会与挑战",
						"chapterNum": 4,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "竞品分析的未来展望",
						"chapterNum": 5
					},
					{
						"type": "paragraph",
						"title": "竞品分析的趋势与预测",
						"chapterNum": 5,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "竞品分析的发展机遇与挑战",
						"chapterNum": 5,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "竞品分析的规范与标准化",
						"chapterNum": 5,
						"paragraphNum": 3
					}
				]
			}
		},
		{
			"id": 118,
			"title": "活动策划方案",
			"markdown": `
# 活动策划方案

## 1. 活动策划前期准备

### 1.1 确定活动目的和主题

#### 1.1.1 分析市场和受众需求


#### 1.1.2 制定活动目标和指标


### 1.2 确定活动时间和地点

#### 1.2.1 考虑季节和天气因素


#### 1.2.2 选择适宜的场地和设施


### 1.3 确定活动预算和资源

#### 1.3.1 制定详细的活动预算表


#### 1.3.2 确定所需人力、物力和财力资源


### 1.4 策划活动内容和形式

#### 1.4.1 制定活动流程和时间表


#### 1.4.2 确定活动主题和亮点


#### 1.4.3 确定活动形式和互动方式


## 2. 活动执行阶段
### 2.1 确定活动执行团队和分工
#### 2.1.1 确定各个部门和人员的职责和任务


#### 2.1.2 协调各方面资源和安排工作流程


### 2.2 筹备活动场地和设备
#### 2.2.1 确认场地和设备是否符合要求


#### 2.2.2 安排场地布置和设备调试


### 2.3 确保活动顺利进行
#### 2.3.1 组织活动前的培训和演练


#### 2.3.2 确认活动物资和人员到位


#### 2.3.3 协调处理突发事件和问题



## 3. 活动后续工作

### 3.1 活动总结和回顾

#### 3.1.1 收集并整理相关数据和反馈


#### 3.1.2 分析活动效果和问题


#### 3.1.3 撰写活动总结和报告


### 3.2 活动后续跟进

#### 3.2.1 联系参与者并进行跟进


#### 3.2.2 维护参与者关系和营销渠道


#### 3.2.3 策划下一步的活动和规划

            `,
			"nodes": {
				"title": "活动策划方案",
				"subTitle": '精心打造，完美呈现',
				"nodes": [
					{
						"type": "chapter",
						"title": "活动策划前期准备",
						"chapterNum": 1
					},
					{
						"type": "paragraph",
						"title": "确定活动目的和主题",
						"chapterNum": 1,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "确定活动时间和地点",
						"chapterNum": 1,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "确定活动预算和资源",
						"chapterNum": 1,
						"paragraphNum": 3
					},
					{
						"type": "paragraph",
						"title": "策划活动内容和形式",
						"chapterNum": 1,
						"paragraphNum": 4
					},
					{
						"type": "chapter",
						"title": "活动执行阶段",
						"chapterNum": 2
					},
					{
						"type": "paragraph",
						"title": "确定活动执行团队和分工",
						"chapterNum": 2,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "筹备活动场地和设备",
						"chapterNum": 2,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "确保活动顺利进行",
						"chapterNum": 2,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "活动后续工作",
						"chapterNum": 3
					},
					{
						"type": "paragraph",
						"title": "活动总结和回顾",
						"chapterNum": 3,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "活动后续跟进",
						"chapterNum": 3,
						"paragraphNum": 2
					}
				]
			}
		},
		{
			"id": 119,
			"title": "人事岗位晋级攻略",
			"markdown": `
# 人事岗位晋级攻略
## 1. 岗位晋级概述
### 1.1 晋升的意义与作用
#### 1.1.1 晋升对个人职业发展的意义


#### 1.1.2 晋升对企业的作用


### 1.2 晋升的前提条件
#### 1.2.1 岗位职责与绩效评估


#### 1.2.2 个人素质与能力要求


#### 1.2.3 组织文化与价值观念


### 1.3 晋升的分类与级别
#### 1.3.1 岗位晋升的分类


#### 1.3.2 岗位晋升的级别


## 2. 岗位晋级的准备工作
### 2.1 自我认知与职业规划
#### 2.1.1 自我认知的意义与方法


#### 2.1.2 职业规划的重要性与步骤


### 2.2 个人能力与素质提升
#### 2.2.1 专业技能的提升


#### 2.2.2 综合素质的提升


### 2.3 工作成果与贡献积累
#### 2.3.1 工作成果的意义与评价标准


#### 2.3.2 工作贡献的体现与积累

## 3. 岗位晋级的申请与评审
### 3.1 申请流程与材料准备
#### 3.1.1 申请岗位晋升的流程

#### 3.1.2 申请材料的准备与注意事项

### 3.2 评审标准与流程
#### 3.2.1 评审标准的制定与解读

#### 3.2.2 评审委员会的组建与职责

### 3.3 晋升结果与后续跟踪
#### 3.3.1 晋升结果的通知与反馈

#### 3.3.2 晋升后的职责与权责变化

#### 3.3.3 晋升后的职业发展规划与建议


## 4. 岗位晋级过程中的注意事项
### 4.1 管理者与同事的支持与反馈
#### 4.1.1 管理者的意见与建议


#### 4.1.2 同事的支持与评价


### 4.2 沟通与反馈的重要性
#### 4.2.1 沟通技巧的应用与实践


#### 4.2.2 反馈的意义与方法


### 4.3 情绪管理与自我调节
#### 4.3.1 情绪管理的重要性与方法


#### 4.3.2 自我调节的技能与实践


## 5. 岗位晋级后的职业发展规划
### 5.1 职业生涯规划与目标设定
#### 5.1.1 职业生涯规划的方法与工具


#### 5.1.2 目标设定的原则与技巧


### 5.2 继续学习与成长
#### 5.2.1 继续学习的意义与方法


#### 5.2.2 成长的途径与机会


### 5.3 职业发展的策略与建议
#### 5.3.1 职业发展策略的制定与实施


#### 5.3.2 职业发展建议的获取与应用

            `,
			"nodes": {
				"title": "人事岗位晋级攻略",
				"subTitle": '开启职业生涯新篇章',
				"nodes": [
					{
						"type": "chapter",
						"title": "岗位晋级概述",
						"chapterNum": 1
					},
					{
						"type": "paragraph",
						"title": "晋升的意义与作用",
						"chapterNum": 1,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "晋升的前提条件",
						"chapterNum": 1,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "晋升的分类与级别",
						"chapterNum": 1,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "岗位晋级的准备工作",
						"chapterNum": 2
					},
					{
						"type": "paragraph",
						"title": "自我认知与职业规划",
						"chapterNum": 2,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "个人能力与素质提升",
						"chapterNum": 2,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "工作成果与贡献积累",
						"chapterNum": 2,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "岗位晋级的申请与评审",
						"chapterNum": 3
					},
					{
						"type": "paragraph",
						"title": "申请流程与材料准备",
						"chapterNum": 3,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "评审标准与流程",
						"chapterNum": 3,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "晋升结果与后续跟踪",
						"chapterNum": 3,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "岗位晋级过程中的注意事项",
						"chapterNum": 4
					},
					{
						"type": "paragraph",
						"title": "管理者与同事的支持与反馈",
						"chapterNum": 4,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "沟通与反馈的重要性",
						"chapterNum": 4,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "情绪管理与自我调节",
						"chapterNum": 4,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "岗位晋级后的职业发展规划",
						"chapterNum": 5
					},
					{
						"type": "paragraph",
						"title": "职业生涯规划与目标设定",
						"chapterNum": 5,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "继续学习与成长",
						"chapterNum": 5,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "职业发展的策略与建议",
						"chapterNum": 5,
						"paragraphNum": 3
					}
				]
			}
		},
		{
			"id": 120,
			"title": `${currentYear + 1}年数据营销前瞻`,
			"markdown": `
# ${currentYear + 1}年数据营销前瞻
## 1. 前言
### 1.1 研究背景
#### 1.1.1 数据营销的概念


#### 1.1.2 数据营销的发展历程


### 1.2 研究目的和意义
#### 1.2.1 数据营销的重要性


#### 1.2.2 未来数据营销的趋势


### 1.3 研究内容和方法
#### 1.3.1 数据营销的核心技术


#### 1.3.2 数据营销的实践案例


#### 1.3.3 数据营销的未来发展趋势

## 2. 数据营销的核心技术
### 2.1 数据收集和分析技术
#### 2.1.1 人工智能技术在数据营销中的应用


#### 2.1.2 大数据技术在数据营销中的应用


#### 2.1.3 数据分析工具的应用和发展


### 2.2 营销策略和内容创意技术
#### 2.2.1 数据驱动的营销策略


#### 2.2.2 人工智能在内容创意中的应用


#### 2.2.3 VR/AR在数据营销中的应用


### 2.3 营销渠道和技术
#### 2.3.1 社交媒体在数据营销中的应用


#### 2.3.2 移动营销在数据营销中的应用


#### 2.3.3 电商平台在数据营销中的应用


## 3. 数据营销的实践案例
### 3.1 企业营销案例
#### 3.1.1 阿里巴巴的数据营销实践


#### 3.1.2 腾讯的数据营销实践


#### 3.1.3 百度的数据营销实践


### 3.2 行业营销案例
#### 3.2.1 餐饮行业的数据营销实践


#### 3.2.2 教育行业的数据营销实践


#### 3.2.3 金融行业的数据营销实践


## 4. 未来数据营销的趋势
### 4.1 数据营销的新趋势
#### 4.1.1 个性化营销趋势


#### 4.1.2 社交化营销趋势


#### 4.1.3 移动化营销趋势


### 4.2 数据营销的新挑战
#### 4.2.1 数据隐私和安全问题


#### 4.2.2 跨境营销的挑战


#### 4.2.3 数据营销人才的培养


## 5. 总结与展望
### 5.1 数据营销的总结
#### 5.1.1 数据营销的发展历程


#### 5.1.2 数据营销的核心技术和应用


#### 5.1.3 数据营销的实践案例和效果


### 5.2 数据营销的展望
#### 5.2.1 数据营销的未来发展趋势


#### 5.2.2 数据营销的挑战和解决方案


#### 5.2.3 数据营销人才的培养和发展

            `,
			"nodes": {
				"title": `${currentYear + 1}年数据营销前瞻`,
				"subTitle": '洞悉未来营销新趋势',
				"nodes": [
					{
						"type": "chapter",
						"title": "前言",
						"chapterNum": 1
					},
					{
						"type": "paragraph",
						"title": "研究背景",
						"chapterNum": 1,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "研究目的和意义",
						"chapterNum": 1,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "研究内容和方法",
						"chapterNum": 1,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "数据营销的核心技术",
						"chapterNum": 2
					},
					{
						"type": "paragraph",
						"title": "数据收集和分析技术",
						"chapterNum": 2,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "营销策略和内容创意技术",
						"chapterNum": 2,
						"paragraphNum": 2
					},
					{
						"type": "paragraph",
						"title": "营销渠道和技术",
						"chapterNum": 2,
						"paragraphNum": 3
					},
					{
						"type": "chapter",
						"title": "数据营销的实践案例",
						"chapterNum": 3
					},
					{
						"type": "paragraph",
						"title": "企业营销案例",
						"chapterNum": 3,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "行业营销案例",
						"chapterNum": 3,
						"paragraphNum": 2
					},
					{
						"type": "chapter",
						"title": "未来数据营销的趋势",
						"chapterNum": 4
					},
					{
						"type": "paragraph",
						"title": "数据营销的新趋势",
						"chapterNum": 4,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "数据营销的新挑战",
						"chapterNum": 4,
						"paragraphNum": 2
					},
					{
						"type": "chapter",
						"title": "总结与展望",
						"chapterNum": 5
					},
					{
						"type": "paragraph",
						"title": "数据营销的总结",
						"chapterNum": 5,
						"paragraphNum": 1
					},
					{
						"type": "paragraph",
						"title": "数据营销的展望",
						"chapterNum": 5,
						"paragraphNum": 2
					}
				]
			}
		}
]


