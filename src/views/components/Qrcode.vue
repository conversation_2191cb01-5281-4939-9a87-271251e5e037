<script setup lang='ts'>
import { ref, onMounted, onUnmounted } from 'vue'
import { NSkeleton, NImage, useMessage, NText } from 'naive-ui'
import { getAuthUrl, checkAuth } from '@/chatgpt'
import { useAuthStore, useUserStore } from '@/store'
import refresh from '@/assets/refresh.svg'
import wechat from '@/assets/wechat.png'
import { router } from '@/router'
import { setUser } from '@/store/modules/auth/helper'

interface Props {
	isbind: boolean,
}
interface Emit {
	(ev: 'close'): void
	(ev: 'success'): void
}
const emit = defineEmits<Emit>()
const props = defineProps<Props>()
const message = useMessage()
let expireTimer: NodeJS.Timeout
let timer: NodeJS.Timeout

const authStore = useAuthStore()
const userStore = useUserStore()
const qrcodeUrl = ref<string>('')
const expireTime = ref<boolean>(false)
const isWx = ref(false)
const isWxBind = ref(props.isbind)

onMounted(() => {
	InitialData()
	const isWechat = /MicroMessenger/.test(navigator.userAgent);
	if (isWechat) {
		isWx.value = true
	} else {
		isWx.value = false
	}
})
onUnmounted(() => {
	clearInterval(expireTimer)
	clearTimeout(timer)
})

const handleRefresh = async () => {
	InitialData()
}
async function InitialData() {
	const res: any = await getAuthUrl()
	if (res) {
		qrcodeUrl.value = res?.url
		expireTime.value = false
		fetchIsLogin(res?.messageId)
		IntervalTimer()
	}
}
async function IntervalTimer() {
	expireTimer = setInterval(() => {
		expireTime.value = true
		clearInterval(expireTimer)
		clearTimeout(timer)
	}, 600000)
}

async function fetchIsLogin(messageId: string) {
	try {
		const checkAuthFunc = async () => {
			try {
				const data: any = await checkAuth({ messageId, type: props.isbind ? 'bind' : undefined })
				if (data) {
					authStore.setToken(data.token)
					setUser(data)
					userStore.updateUserInfo({
						name: data.nickname || data.username || data.phone,
						description: "用户信息",
						uid: data.uid
					})
					clearTimeout(timer)
					// window.location.reload()
					emit('success')
				} else {
					timer = setTimeout(() => {
						checkAuthFunc()
					}, 1000)
				}
			} catch (error: any) {
				message.error(error.errmsg)
				setTimeout(() => {
					emit('close')
				}, 2000)
			}
		}
		checkAuthFunc()
	} catch (error) {
		clearTimeout(timer)
	}
}
const gotoPrivacyPolicy = () => {
	router.push({ name: 'PrivacyPolicy' })
}
const gotoUserAgreement = () => {
	router.push({ name: 'UserAgreement' })
}

</script>

<template>
	<div>
		<NSkeleton v-if="!qrcodeUrl" height="165px" width="165px" size="large" class="mx-auto" />
		<div id="qr-container" class="flex justify-center items-center relative w-[160px] h-[160px] mx-auto mt-[5px]"
			v-if="qrcodeUrl">
			<NImage :src="qrcodeUrl" class="w-full h-full" preview-disabled />
			<div v-if="expireTime" @:click="handleRefresh"
				class="absolute top-0 left-0 right-0 bottom-0 bg-[#fff]/[0.9] flex items-center justify-center flex-col border border-[#fff] cursor-pointer">
				<NImage :src="refresh" preview-disabled class="w-[24px] h-[24px] mb-[8px]" />
				<div class="text-[16px] font-medium">二维码已失效</div>
				<div class="block text-[#006aff] pt-[5px]">点击刷新</div>
			</div>
		</div>
		<h1 class="text-center mt-[20px] text-[14px] font-normal flex items-center justify-center" v-if="!isWxBind">
			<NImage :src="wechat" class="w-[18px] h-[18px] mr-[10px]" preview-disabled />
			<span v-if="!isWx">使用微信扫一扫，快速登录</span>
			<span v-if="isWx">长按识别二维码，即可登录</span>
		</h1>
		<div class="text-center text-[10px] mt-[40px]">登录即表示同意<NText class="cursor-pointer" type="primary"
				@click="gotoUserAgreement">《用户协议》
			</NText>和<NText class="cursor-pointer" type="primary" @click="gotoPrivacyPolicy">《隐私条款》</NText>
		</div>
	</div>
</template>
