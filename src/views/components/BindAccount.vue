<script setup lang='ts'>
import { computed, ref } from 'vue'
import { NButton, NForm, NFormItem, NInput, NInputNumber, NModal, NTag, useMessage, NText, NImage } from 'naive-ui'
import { fetchGetBind, fetchGetBindCode, bindPhone, bindPay } from '@/chatgpt'
import { useAuthStore, useUserStore } from '@/store'
import { getUser } from '@/store/modules/auth/helper'
import { isPhoneNumber } from '@/utils/is'
import logoImage from "@/assets/aiwork/logo.png";
import { useRequest } from 'vue-hooks-plus'

interface Props {
  visible: boolean
}

interface Emit {
  (e: 'bind-success'): void
  (e: 'update:visible', visible: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emit>()

const authStore = useAuthStore()
const userStore = useUserStore()
const ms = useMessage()
const user = getUser()
const intervalTimer = ref<NodeJS.Timeout | null>(null)

let formValue = ref<{ username: number | null; password: number | null }>({
  username: null,
  password: null,
});
const countdownSeconds = ref(60)
const countdownActive = ref(false)
// 验证码按钮是否禁用
// 当倒计时未结束时，按钮禁用
const codeDisabled = computed(() => {
  return countdownActive.value && countdownSeconds.value > 0 || !formValue.value.username
})
const disabled = computed(() => {
  return !formValue.value.username || !formValue.value.password
})
const show = computed({
  get: () => {
    return props.visible
  },
  set: (visible: boolean) => emit('update:visible', visible),
})


// 获取验证码
const { run: runFetchGetPhoneBindCode } = useRequest(fetchGetBindCode, {
  manual: true,
  onSuccess: () => {
    ms.success('发送成功')
  },
  onError: (error) => {
    countdownSeconds.value = 60
    countdownActive.value = false
    ms.error('发送失败')
    clearInterval(intervalTimer.value as NodeJS.Timeout)
  }
})
// 绑定手机号
const { run: runBindPhone, loading } = useRequest(bindPhone, {
  manual: true,
  onSuccess: (data) => {
    ms.success('绑定成功')
    authStore.setToken(user.token)
    userStore.updateUserInfo({
      name: user.nickname || user.username || user.phone,
      description: '用户信息',
      uid: user.uid
    })
    window.location.reload()
    emit('bind-success')
  },
  onError: () => {
    ms.error('绑定失败')
  }
})

const countdownLabel = computed(() => {
  if (countdownActive.value && countdownSeconds.value > 0) {
    return `重新获取(${countdownSeconds.value})`
  }
  else if (countdownActive.value && countdownSeconds.value == 0) {
    return '重新获取'
  }
  else {
    return '获取验证码'
  }
})


// 手机号绑定
async function handleMobileVerify(event: Event) {
  event.preventDefault()
  const name = formValue.value.username
  const verifyCode = formValue.value.password
  console.log(name, verifyCode);
  if (!name || !verifyCode) {
    ms.error('请输入手机号和验证码')
    return
  }
  runBindPhone(name, verifyCode)
}
// 获取手机验证码
async function handlePhoneCode(event: Event) {
  event.preventDefault()
  const phone = formValue.value?.username!.toString()
  if (!isPhoneNumber(phone)) {
    ms.error('请输入正常的手机号码')
    return
  }
  countdownActive.value = true

  intervalTimer.value = setInterval(() => {
    countdownSeconds.value--
    if (countdownSeconds.value <= 0) {
      clearInterval(intervalTimer.value as NodeJS.Timeout)
      countdownSeconds.value = 60
      countdownActive.value = false
    }
  }, 1000)
  runFetchGetPhoneBindCode(phone)
}
const handClose = () => {
  show.value = false
}

function handlePress(event: KeyboardEvent) {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    handleMobileVerify(event)
  }
}
</script>

<template>
  <NModal v-model:show="show" style="width: 80%; height: 454px; max-width: 450px" @close="handClose">
    <div class="bg-white rounded dark:bg-slate-800 relative flex flex-col items-center">
      <div class="flex justify-start p-[30px] w-full">
        <NImage :src="logoImage" class="h-[28px]" object-fit="cover" alt="logo" preview-disabled />
      </div>

      <div class="w-[80%]">
        <h1 class="text-[16px] font-normal text-left my-[20px]">绑定手机号</h1>
        <NForm ref="formRef" :model="formValue">
          <NFormItem label="手机号" :rule="{
            required: true,
            message: `请输入手机号码`,
          }">
            <n-input-number type="number" v-model:value="formValue.username" placeholder="请输入手机号"
              @keypress="handlePress" :show-button="false" />
          </NFormItem>
          <NFormItem label="验证码" :rule="{
            required: true,
            message: `请输入手机号码`,
          }">
            <div class="flex justify-between items-center w-full">
              <n-input-number :show-button="false" v-model:value="formValue.password" type="text" maxlength="4"
                placeholder="请输入验证码" @keypress="handlePress" class="w-[70%]" />
              <NButton quaternary type="primary" class="ml-[10px]" :disabled="codeDisabled" @click="handlePhoneCode">{{
                countdownLabel }}</NButton>
            </div>
          </NFormItem>
          <NFormItem style="border: none !important; margin-top: 60px; margin-bottom: 0; height: 40px;">
            <div class="w-full mb-[40px]">
              <NButton type="primary" size="large" :loading="loading" :disabled="disabled" style="width: 100%"
                @click="handleMobileVerify">
                {{ $t("common.verify") }}</NButton>
            </div>
          </NFormItem>
        </NForm>
      </div>
    </div>
  </NModal>
</template>

<style lang="less" scoped>
:deep(.n-form-item-label),
:deep(.n-form-item-feedback-wrapper) {
  opacity: 0;
  display: none !important;
}

:deep(.n-form-item.n-form-item--top-labelled) {
  grid-template-areas: "blank";
}

:deep(.n-input__border),
:deep(.n-input__state-border) {
  display: none;
}

:deep(.n-form-item) {
  margin: 30px 0;
  border-bottom: 1px solid #d8d8d8;
}
</style>
