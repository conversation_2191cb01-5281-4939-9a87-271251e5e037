<script setup lang='ts'>
import { ref } from 'vue'
import { useUserStore } from '@/store'
import BindAccount from './BindAccount.vue'
import Permission from './Permission.vue'
import { Member } from '@/components/common'
import { useRoute } from 'vue-router'
const route = useRoute()
const userStore = useUserStore();
const showMember = ref(false)
const needBind = ref(false)
const memberType = ref(0)
const needPermission = ref(false)
const pathname = ref(route.name)
const handleMember = () => {
	showMember.value = true
}
const changeFn = async () => {
	needBind.value = true
}

const handleLogin = () => {
	needPermission.value = true
}

if (userStore && userStore.userInfo && userStore.userInfo.member) {
	const member: any = userStore.userInfo.member
	//code 100001
	memberType.value = member
} else if (userStore && userStore.userInfo) {
	//code 200001
	memberType.value = 4
}

const getClass = (type: string | number) => {
	if(!type)
		return 'ckButtonNew ckButtonblue ckAnimate cursor-pointer transition duration-300 w-full flex items-center'
	return 'ckButtonNew ckButtonblue cursor-pointer transition duration-300 w-full flex items-center'
}

</script>

<template>
	<footer class="flex flex-col min-w-0 p-4 overflow-hidden border-t dark:border-neutral-800">
		<a :class="getClass(memberType)" @click="handleMember" v-if="memberType != 7&&false">
			<i class="fi fi-rs-chess-queen w-[18px] h-[18px] text-[#fff] mr-3"></i>
		  <span>{{ !memberType ?pathname=='Paper'?"充值":$t('vip.siderButton') : pathname=='Paper'?"充值":"续费VIP" }}</span>
		</a>
	</footer>
	<Member v-if="showMember" v-model:visible="showMember" @change-fn="changeFn" />
	<BindAccount v-if="needBind" v-model:visible="needBind" @login="handleLogin"/>
	<Permission v-if="needPermission" v-model:visible="needPermission" />
</template>
<style lang="less">
.n-divider:not(.n-divider--vertical) {
	margin: 10px 0 !important;
}
</style>
