<script lang="ts" setup>
import { NImage, NModal } from "naive-ui";
import { computed } from "vue";
import logoImage from "@/assets/aiwork/logo.png";
import Qrcode from "./Qrcode.vue";

interface Props {
	visible: boolean;
}
interface Emit {
	(e: "update:visible", visible: boolean): void;
	(e: "bind-success"): void;
}
const props = defineProps<Props>();
const emit = defineEmits<Emit>();
const show = computed({
	get: () => props.visible,
	set: (visible: boolean) => emit("update:visible", visible),
});
const handClose = () => {
	show.value = false;
};
</script>

<template>
	<NModal
		v-model:show="show"
		v-on:close="handClose"
		style="width: 80%; height: 454px; max-width: 450px"
	>
		<div
			class="bg-white rounded dark:bg-slate-800 relative flex flex-col items-center"
		>
			<div class="flex justify-start p-[25px] w-full">
				<NImage :src="logoImage" class="h-[40px]" alt="logo" />
			</div>

			<div>
				<h1 class="text-[16px] font-normal text-center mt-[20px]">绑定微信</h1>
				<Qrcode
					:isbind="true"
					@close="handClose"
					@success="() => emit('bind-success')"
				/>
			</div>
		</div>
	</NModal>
</template>

<style lang="less" scoped></style>
