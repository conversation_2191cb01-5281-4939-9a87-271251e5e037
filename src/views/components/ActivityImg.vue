<script setup lang="ts">
import { NModal, NImage, NIcon } from 'naive-ui';

interface Props {
    visible: boolean;
    imgSrc: string;
}
interface Emit {
    (ev: 'update-visible', visible: boolean): void
    (ev: 'open-recharge'): void
}
withDefaults(defineProps<Props>(), {
    visible: false,
    imgSrc: '',
});
const emit = defineEmits<Emit>();

</script>

<template>

    <NModal v-bind:show="visible" style="--n-box-shadow: any;" :closable="true" class="visible " @mask-click="() => emit('update-visible', false)">
        <div class="relative">
            <NImage :src="imgSrc" width="436px" preview-disabled @click="emit('open-recharge')" />
            <NIcon style=" position: absolute; top: 50px; right: 60px">
                <i class="fi fi-rs-cross-small text-[#fff] cursor-pointer" @click="() => emit('update-visible', false)"></i>
            </NIcon>
        </div>
    </NModal>

</template>

<style lang="less" scoped></style>
