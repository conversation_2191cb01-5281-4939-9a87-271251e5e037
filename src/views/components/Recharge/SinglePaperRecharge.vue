<script lang="ts" setup>
import { ValueAddedService } from "@/views/paper/types";
import { NEllipsis } from "naive-ui";
import { computed } from "vue";
import ValueAddedCard from "./ValueAddedCard.vue";

interface Paper {
	price: string;
	type: string;
	power: number;
	status: number;
	title: string;
	id: number;
	mode: string;
	space: string;
	subjoins?: ValueAddedService[];
}
interface Props {
	recharge: { paperInfo: Paper };
}
const props = defineProps<Props>();
const modeName = computed(() => {
	return props?.recharge?.paperInfo?.mode === "base" ? "标准版" : "高级版";
});
const paperTypeName = computed(() => {
	return props?.recharge?.paperInfo?.typeName
});
const payPrice = computed(() => {
	// return props?.recharge?.paperInfo?.power
	// 	? props?.recharge?.paperInfo?.power + "算力"
	// 	: "" + props?.recharge?.paperInfo?.power &&
	// 		props?.recharge?.paperInfo?.price
	// 		? "+"
	// 		: "" + props?.recharge?.paperInfo?.price
	// 			? props?.recharge?.paperInfo?.price + "元"
	// 			: "";
	return props?.recharge?.paperInfo?.price + "元"
});
</script>

<template>
	<div class="bg-[#FFFFFF] rounded-tl-[10px] rounded-tr-[10px] px-[36px] pt-[22px]  h-[296px] flex flex-col justify-start">
		<div class="flex flex-row gap-x-[10px] mb-[13px]">
			<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1"
				width="16" height="18" viewBox="0 0 16 18">
				<defs>
					<filter id="master_svg0_119_08392" filterUnits="objectBoundingBox"
						color-interpolation-filters="sRGB" x="0" y="0" width="8" height="9">
						<feFlood flood-opacity="0" result="BackgroundImageFix" />
						<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
						<feGaussianBlur in="BackgroundImage" stdDeviation="2.5" />
						<feComposite in2="SourceAlpha" operator="in" result="effect1_foregroundBlur" />
						<feBlend mode="normal" in="SourceGraphic" in2="effect1_foregroundBlur" result="shape" />
					</filter>
				</defs>
				<g>
					<g>
						<path
							d="M10.0397,0.28912C10.0399,0.289307,10.0401,0.289493,10.0403,0.28968L13.5428,3.89838C13.543,3.89857,13.5432,3.89875,13.5434,3.89894C13.5648,3.9211,13.5477,3.95636,13.5168,3.95636L10.176,3.95636C10.07,3.95636,9.984,3.8678,9.984,3.75855L9.984,0.313936C9.984,0.283849,10.0188,0.267553,10.0397,0.28912M9.216,3.758L9.216,1C9.216,0.447715,8.76829,0,8.216,0L0.960572,0C0.960191,0,0.960056,-1.39222e-7,0.959674,4.33021e-7C0.429974,0.000794562,0.000752306,0.443035,3.70079e-7,0.988791C-1.31388e-7,0.989155,0,0.989273,0,0.989637L0,16.0584C0,16.0588,-1.31285e-7,16.0589,3.69622e-7,16.0593C0.000751468,16.605,0.429935,17.0472,0.959635,17.048C0.960017,17.048,0.960152,17.048,0.960533,17.048L12.8632,17.048C12.8636,17.048,12.8637,17.048,12.8641,17.048C13.3938,17.0472,13.8235,16.605,13.8242,16.0593C13.8242,16.0589,13.8242,16.0588,13.8242,16.0584L13.824,5.74762C13.824,5.19534,13.3763,4.74764,12.824,4.74764L10.1766,4.74764C10.1762,4.74764,10.1761,4.74764,10.1757,4.74764C9.64597,4.74684,9.21675,4.3046,9.216,3.75885C9.216,3.75848,9.216,3.75836,9.216,3.758"
							fill="#3680F9" fill-opacity="1" />
					</g>
					<g filter="url(#master_svg0_119_08392)">
						<rect x="8" y="9" width="8" height="9" rx="1" fill="#C0D8FF" fill-opacity="0.5" />
						<rect x="8.25" y="9.25" width="7.5" height="8.5" rx="0.75" fill-opacity="0" stroke-opacity="1"
							stroke="#D1E2FF" fill="none" stroke-width="0.5" />
					</g>
					<g style="opacity: 0.6000000238418579">
						<rect x="1" y="3" width="5" height="1.5" rx="0.75" fill="#FFFFFF" fill-opacity="1" />
					</g>
					<g style="opacity: 0.5">
						<rect x="1" y="6" width="8" height="1.5" rx="0.75" fill="#FFFFFF" fill-opacity="1" />
					</g>
					<g>
						<path
							d="M11.46087,14.982610000000001L10.342029,14.982610000000001Q10.185507,14.97101,10.101449,14.852170000000001Q10.0173913,14.73333,10,14.58841Q10.0173913,14.443480000000001,10.101449,14.350719999999999Q10.185507,14.25797,10.342029,14.24638L11.46087,14.24638L11.46087,13.88116L10.342029,13.87536Q10.185507,13.86377,10.101449,13.76522Q10.0173913,13.66667,10,13.51594Q10.0173913,13.37101,10.101449,13.27826Q10.185507,13.18551,10.342029,13.17391L11.17681,13.17391L10.4,11.805797Q10.342029,11.736232,10.289855,11.628986Q10.237681,11.521739,10.243478,11.394203Q10.272464,11.231884,10.35942,11.130435Q10.446377,11.0289856,10.684058,11.0000000431918Q10.823188,11.0115942,10.933333,11.0956522Q11.04348,11.17971,11.11304,11.278261L11.9942,12.88986L12.96232,11.266667Q13.031880000000001,11.168116,13.14203,11.0927536Q13.25217,11.0173913,13.391300000000001,11Q13.47826,11.00579709,13.55072,11.0231884Q13.623190000000001,11.0405797,13.67826,11.0811594Q13.73333,11.121739,13.77101,11.197101Q13.80869,11.272464,13.82609,11.394203Q13.82609,11.562319,13.710139999999999,11.713043L12.811589999999999,13.17391L13.66377,13.17391Q13.81449,13.18551,13.90145,13.27826Q13.98841,13.37101,14,13.51594Q13.98841,13.66667,13.89855,13.77101Q13.8087,13.87536,13.65797,13.88696L12.55072,13.89275L12.55072,14.24638L13.66377,14.24638Q13.81449,14.25797,13.90145,14.36232Q13.98841,14.46667,14,14.61159Q13.98841,14.762319999999999,13.90145,14.86377Q13.81449,14.96522,13.66377,14.97681L12.55072,14.97101L12.55072,15.58551Q12.52754,16.07826,12.01159,16.07826Q11.75652,16.07826,11.61449,15.956520000000001Q11.47246,15.83478,11.46087,15.58551L11.46087,14.982610000000001Z"
							fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode: passthrough" />
					</g>
				</g>
			</svg>
			<span class="text-[16px] text-[#3d3d3d] leading-[21px] font-bold">订单详情</span>
		</div>
		<div class="divider"></div>
		<div class="grid grid-cols-3 gap-[10px] flex-wrap mt-[16px]">
			<div class="flex flex-row gap-x-[10px] flex-[33%]">
				<span
					class="inline-block text-[16px] text-[#3d3d3d] leading-[21px] w-[80px] text-justify TextAlignLastJustify whitespace-nowrap">写作题目：</span>
				<NEllipsis class="text-[16px] text-[#3d3d3d] leading-[21px] whitespace-pre-wrap">{{
					recharge?.paperInfo?.title
					}}</NEllipsis>
			</div>
			<div class="flex flex-row gap-x-[5px] flex-[33%]">
				<span
					class="inline-block text-[16px] text-[#3d3d3d] leading-[21px] w-[80px] text-justify TextAlignLastJustify">写作类型：</span>
				<span class="text-[16px] text-[#3d3d3d] leading-[21px]">{{
					paperTypeName
					}}</span>
			</div>
			<div class="flex flex-row gap-x-[5px] flex-[33%]">
				<span
					class="inline-block text-[16px] text-[#3d3d3d] leading-[21px] w-[80px] text-justify TextAlignLastJustify">写作模式：</span>
				<span class="text-[16px] text-[#3d3d3d] leading-[21px]">{{
					modeName
					}}</span>
			</div>
			<div class="flex flex-row gap-x-[5px] flex-[33%]" v-if="recharge?.paperInfo?.words">
				<span
					class="inline-block text-[16px] text-[#3d3d3d] leading-[21px] w-[80px] text-justify TextAlignLastJustify">字
					数：</span>
				<span class="text-[16px] text-[#3d3d3d] leading-[21px]">约{{ recharge?.paperInfo?.words }}字</span>
			</div>
			<div class="flex flex-row gap-x-[10px] flex-[33%]">
				<span
					class="inline-block text-[16px] text-[#3d3d3d] leading-[21px] w-[80px] text-justify TextAlignLastJustify">实付金额：</span>
				<span class="text-[16px] text-[#FC6A00] leading-[21px]">{{
					payPrice
					}}</span>
			</div>
		</div>
		<div v-if="props?.recharge?.paperInfo?.subjoins?.length"
			class=" text-[14px] text-[#8D8D8D] mt-[20px] mb-[17px] ">
			您选择了 <span class=" text-[#FF5100]">{{ props?.recharge?.paperInfo?.subjoins?.length }}</span> 项增值服务(仅限单次生成)
		</div>
		<div v-if="props?.recharge?.paperInfo?.subjoins?.length"
			class=" grid grid-cols-4 gap-y-[10px] gap-x-[19px] flex-wrap ">
			<ValueAddedCard v-for="(item, index) in props?.recharge?.paperInfo?.subjoins " :key="index"
				:original-price="item.originalPrice" :title="item.title" :price="item.price" />
		</div>
	</div>
</template>

<style lang="less" scoped>
.divider {
	width: 100%;
	height: 1px;
	opacity: 1;
	background: #e5e5e5;
}
</style>
