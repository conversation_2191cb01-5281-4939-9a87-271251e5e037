<script lang="ts" setup>
import { ref } from 'vue';
import { NImage } from 'naive-ui';
// import ActiveVip from '@/assets/images/active-vip.png';
// import Vip from '@/assets/images/vip.png';
import Vip from '@/assets/aiwork/images/vip.png'
import RechargeCardList from './RechargeCardList.vue';
import { replaceText } from '@/plugins/directive';

interface Props {
	categoryId?: number;
	rechargeList: any;
	isTeam?: number
}
interface Emit {
	(ev: "update-program", item: "ai" | "ppt" | "paper" | "mj" | "write"): void;
	(ev: "update-select-good", item: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emit>();
const activeKey = ref(props.categoryId || 1);

const handleSwitchTab = (key: number) => {
	activeKey.value = key;
	if (key === 1) {
		emit("update-program", "ai");
	} else if (key === 2) {
		emit("update-program", "ppt");
	} else if (key === 3) {
		emit("update-program", "paper");
	} else if (key === 4) {
		emit("update-program", "mj");
	} else if (key === 5) {
		emit("update-program", "write");
	}
};
</script>

<template>
	<div>
		<!-- 三个tab切换 -->
		<div v-if="!isTeam" class=" flex flex-row justify-between sm:w-full gap-3" style="border-bottom: 1px solid #D8D8D8;">
			<!-- 文字会员 -->
			<div class="w-[calc(25%-20px)] h-[50px] relative rounded-tl-[10px] rounded-tr-[10px] left flex items-center justify-center gap-x-[4px] cursor-pointer"
				:class="activeKey === 1 ? 'PaymentTabActive active' : 'PaymentTab'" @click="handleSwitchTab(1)">
				<span :class="activeKey === 1 ? 'tab-text-active' : 'tab-text'">文字会员</span>
				<NImage :src="Vip" width="14" height="14" preview-disabled
					review-disabled>
				</NImage>
			</div>
			<!-- 文字畅享包 -->
			<div class=" w-[calc(25%-20px)] h-[50px] relative rounded-tl-[10px] rounded-tr-[10px] center flex items-center justify-center gap-x-[4px] cursor-pointer"
				:class="activeKey === 5 ? 'PaymentTabActive active' : 'PaymentTab'" @click="handleSwitchTab(5)">
				<span :class="activeKey === 5 ? 'tab-text-active' : 'tab-text'">文字畅享包</span>
				<NImage :src="Vip" width="14" height="14" preview-disabled
					review-disabled>
				</NImage>
			</div>
			<!-- PPT会员 -->
			<div class=" w-[calc(25%-20px)] h-[50px] relative rounded-tl-[10px] rounded-tr-[10px] center flex items-center justify-center gap-x-[4px] cursor-pointer"
				:class="activeKey === 2 ? 'PaymentTabActive active' : 'PaymentTab'" @click="handleSwitchTab(2)">
				<span :class="activeKey === 2 ? 'tab-text-active' : 'tab-text'">PPT会员</span>
				<NImage :src="Vip" width="14" height="14" preview-disabled
					review-disabled>
				</NImage>
			</div>
			<!-- 论文算力包 -->
			<div class=" w-[calc(25%-20px)] h-[50px] relative rounded-tl-[10px] rounded-tr-[10px] center flex items-center justify-center gap-x-[4px] cursor-pointer"
				:class="activeKey === 3 ? 'PaymentTabActive active' : 'PaymentTab'" @click="handleSwitchTab(3)">
				<span :class="activeKey === 3 ? 'tab-text-active' : 'tab-text'">{{replaceText('论文算力包')}}</span>
				<NImage :src="Vip" width="14" height="14" preview-disabled
					review-disabled>
				</NImage>
			</div>
			<!-- 绘图会员 -->
			<div class=" w-[calc(25%-20px)] h-[50px] relative rounded-tl-[10px] rounded-tr-[10px] right flex items-center justify-center gap-x-[4px] cursor-pointer"
				:class="activeKey === 4 ? 'PaymentTabActive active' : 'PaymentTab'" @click="handleSwitchTab(4)">
				<span :class="activeKey === 4 ? 'tab-text-active' : 'tab-text'">绘图会员</span>
				<NImage :src="Vip" width="14" height="14" preview-disabled review-disabled>
				</NImage>
			</div>
		</div>
		<RechargeCardList :recharge-list="rechargeList"
			@update-select-good="($event) => emit('update-select-good', $event)" />
	</div>
</template>

<style lang="less" scoped>
.PaymentTab {
	// background: linear-gradient(90deg, #fff 0%, #cae9ff 100%);
	background: #fff !important;
}
.PaymentTabActive {
	z-index: 10;
	// background: linear-gradient(90deg, #B9D3FF 0%, #9e51f7 94%);
	background: linear-gradient(90deg, #B9D3FF 0%, #D3E8FF 100%), #FFFFFF;
}

.left {
	&::after {
		content: " ";
		position: absolute;
		bottom: 0;
		right: -12px;
		border-width: 0 13px 45px 0;
		border-style: solid;
		border-color: transparent transparent white transparent;
	}
}

.left.active {
	&::after {
		content: " ";
		position: absolute;
		bottom: 0;
		right: -12px;
		border-width: 0 13px 45px 0;
		border-style: solid;
		border-color: transparent transparent #D3E8FF transparent;
	}
}

.center {
	// &::before {
	// 	content: " ";
	// 	position: absolute;
	// 	bottom: 0;
	// 	left: -12px;
	// 	border-width: 45px 13px 0 0;
	// 	border-style: solid;
	// 	border-color: transparent #fff transparent transparent;
	// }

	&::after {
		content: " ";
		position: absolute;
		bottom: 0;
		right: -12px;
		border-width: 0 13px 45px 0;
		border-style: solid;
		border-color: transparent transparent white transparent;
	}
}

.center.active {
	// &::before {
	// 	content: " ";
	// 	position: absolute;
	// 	bottom: 0;
	// 	left: -12px;
	// 	border-width: 45px 13px 0 0;
	// 	border-style: solid;
	// 	border-color: transparent #B9D3FF transparent transparent;
	// }

	&::after {
		content: " ";
		position: absolute;
		bottom: 0;
		right: -12px;
		border-width: 0 13px 45px 0;
		border-style: solid;
		border-color: transparent transparent #D3E8FF transparent;
	}
}

.right {
	&::before {
		content: " ";
		position: absolute;
		bottom: 0;
		left: -12px;
		border-width: 45px 13px 0 0;
		border-style: solid;
		border-color: transparent #fff transparent transparent;
	}
}

.right.active {
	&::before {
		content: " ";
		position: absolute;
		bottom: 0;
		left: -12px;
		border-width: 45px 13px 0 0;
		border-style: solid;
		border-color: transparent #B9D3FF transparent transparent;
	}
}

.tab-text {
	font-size: 16px;
	color: #212f46;
}

.tab-text-active {
	font-size: 16px;
	// color: #ffffff;
	color: #212f46;
}

@media screen and (max-width: 767px) {
	.tab-text {
		font-size: 12px;
		white-space: nowrap;
	}

	.tab-text-active {
		font-size: 12px;
		white-space: nowrap;
	}
}
</style>
