<script lang="ts" setup>
import { computed } from "vue";
import { NImage } from "naive-ui";
import BillIcon from "@/assets/images/bill.png";
interface RechargeList {
	id: string;
	name: string;
	originalPrice: string;
	price: string;
	discount: number;
	markUrl: string;
	mark: string;
	isEmphasize: boolean;
	description: string;
	subDesc: string;
	discountDesc: string;
	alias: string;
	chargeDesc: string;
	paperInfo: {
		title: string;
		id: string;
		author: string;
		type: string;
		key: string;
	};
	allDesc: string;
}
interface Props {
	rechargeList?: RechargeList[];
}

const props = defineProps<Props>();

const singleCheck = computed(() => {
	return props.rechargeList?.[0]
});

</script>

<template>
	<div class="bg-[#FFFFFF] rounded-tl-[10px] rounded-tr-[10px]  flex flex-col justify-start ">
		<!-- header -->
		<div
			class=" flex flex-row items-center justify-start gap-x-[10px] px-[17px] rounded-tl-[10px] rounded-tr-[10px] h-[49px] bg-gradient-to-r  to-[#DBECFF] from-[#D8E6FF]">
			<n-image :src="BillIcon" width="18" height="18" />
			<span class="text-[#232323] text-[16px]">订单详情</span>
		</div>
		<div
			class="flex flex-row flex-wrap justify-between items-center text-[14px] leading-[40px] py-[30px] px-[20px] text-[#333]">
			<div class=" w-[50%]">
				<span>论文题目: {{ singleCheck?.name }}</span>
			</div>
			<div class=" w-[50%]">
				<span>字数: {{ singleCheck?.description }}
					<span class=" text-[#ACB5BF]">{{ singleCheck?.subDesc }}</span>
				</span>
			</div>
			<div class=" w-[50%]">
				<span>AIGC检测系统: {{ singleCheck?.paperInfo?.type }}
					<span class=" text-[#0E69FF]">{{ singleCheck?.discountDesc }}</span>
				</span>
			</div>
			<div class=" w-[50%]">
				<span>AIGC检测价格:
					<span class=" text-[#FF1A00] text-[20px]"><span class=" text-[12px]  relative bottom-[0px]">￥</span>{{
						singleCheck?.price }}</span>
				</span>
			</div>
			<div
				class=" text-[#848484] text-[13px] bg-[#F2F5F9] mt-[10px] h-[28px] leading-[28px] px-[10px] relative w-[100%]"
				v-if="singleCheck?.chargeDesc">
				<div class="absolute top-[-20px] w-0 h-0 border-[10px] border-transparent border-t-[#F2F5F9]  rotate-180 "
					:style="{
						left: '3%'
					}">
				</div>
				{{ singleCheck?.chargeDesc }}
			</div>
		</div>
	</div>
</template>

<style lang="less" scoped>
.active {
	background: #212f46;
	color: #f6cf8d;
}

.default {
	background: #ffffff;
	color: #3d3d3d;
}

.divider {
	background-color: #fff;

	>div {
		width: 100%;
		height: 1px;
		background: #EEEEEE;
	}
}

.up-arrow {
	position: absolute;
	top: -16px;
	left: 57px;
	border-left: 10px solid transparent;
	border-right: 10px solid transparent;
	border-top: 10px solid transparent;
	border-bottom: 10px solid #F1F7FF;
	z-index: 10;
}

.up-arrow-border {
	position: absolute;
	top: -14px;
	left: 60px;
	border-left: 7px solid transparent;
	border-right: 7px solid transparent;
	border-top: 7px solid transparent;
	border-bottom: 7px solid #D2E3FF;
}

// .hot {
//     &::before {
//         content: 'HOT';
//         color: #ffffff;
//         font-size: 12px;
//         position: absolute;
//         top: -2px;
//         right: 15px;
//     }
// }</style>
