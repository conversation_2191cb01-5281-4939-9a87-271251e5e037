<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { NImage, NEllipsis } from "naive-ui";
import BillIcon from "@/assets/images/bill.png";
interface Props {
	show: boolean;
	rechargeList: any;
	paperComputilityList: any;
}
interface Emit {
	(ev: "update-select-good", item: any, selected?: boolean): void;
}
const props = withDefaults(defineProps<Props>(), {
	rechargeList: [],
	paperComputilityList: [],
});
const emit = defineEmits<Emit>();

watch(() => props.paperComputilityList, (value) => {
	console.log('paperComputilityList', value);
});

const singlePaper = computed(() => {
	return props.rechargeList?.[0]
});
const modeName = computed(() => {
	return singlePaper?.value?.paperInfo?.mode === "base" ? "标准版" : "高级版";
});
const paperTypeName = computed(() => {
	return singlePaper?.value?.paperInfo?.typeName
});
const payPrice = computed(() => {
	return singlePaper?.value?.paperInfo?.price + "元"
});
const handleSelectComputility = (item: any) => {
	emit('update-select-good', item, !item.selected)
};

const calcDisabled = (name: string, power: number) => {
	// 算力300 裁剪成300 和power比较
	const itemPower = Number(name.replace("算力", ""));
	return itemPower < power;
}
</script>

<template>
	<div class="bg-[#FFFFFF] rounded-tl-[10px] rounded-tr-[10px]  flex flex-col justify-start"
		:class="{ ' h-[490px]': singlePaper?.paperInfo?.subjoins?.length, ' h-[390px]': !singlePaper?.paperInfo?.subjoins?.length }">
		<!-- header -->
		<div
			class=" flex flex-row items-center justify-start gap-x-[10px] px-[17px] rounded-tl-[10px] rounded-tr-[10px] h-[49px] bg-gradient-to-r  to-[#DBECFF] from-[#D8E6FF]">
			<n-image :src="BillIcon" width="18" height="18" />
			<span class="text-[#232323] text-[16px]">订单详情</span>
		</div>
		<span class=" mt-[15px] mb-[12px] mx-[20px] text-[#333333] text-[14px]">仅单篇购买:</span>
		<div class="mx-[20px] inline-block">
			<div class="rounded-[6px] flex flex-col justify-center h-[78px] bg-[#FFF6F1] w-fit px-[20px]">
				<span class="text-[#FF5303] text-[16px] rounded-[6px]">{{ singlePaper?.paperInfo?.typeName }}</span>
				<div class="flex flex-row items-end gap-x-[8px] whitespace-nowrap">
					<span class="flex items-end">
						<span class="text-[12px] text-[#3D3D3D] relative bottom-[2px]">￥</span>
						<span class="text-[28px] font-bold leading-[36px] text-[#3D3D3D]">{{ singlePaper?.paperInfo?.price }}</span>
					</span>
					<span class="text-xs text-gray-400 line-through relative bottom-[3px]">￥{{ singlePaper?.originalPrice
					}}</span>
					<span class="text-[#3D3D3D] text-[12px]">共需消耗：{{ singlePaper?.paperInfo?.totalPower }}算力</span>
				</div>
			</div>
		</div>
		<span class="mt-[12px] mx-[20px] text-[#232323] text-[14px]">
			<span class="text-[#FF5100]">*</span>
			需生成多篇论文，购买算力包，性价比更高
			<!-- <span class="text-[#939393]">（购买即生效抵扣当前订单）</span> -->
		</span>
		<div class="flex flex-row flex-nowrap gap-x-[10px] mx-[20px] mt-[17px]">
			<div v-for="item in paperComputilityList" :key="item.id"
				class="flex flex-col items-center justify-between w-[25%] h-[110px] rounded-[6px] bg-[#FBF8FF] relative cursor-pointer"
				@click="calcDisabled(item.name, singlePaper?.paperInfo?.totalPower) ? () => { } : handleSelectComputility(item)">
				<!-- 勾选部分 -->
				<div class="absolute top-[0px] right-[0px]">
					<div class="w-[24px] h-[24px] rounded-tr-[8px] rounded-bl-[8px] flex items-center justify-center"
						:class="{ 'bg-[#8157FF]': item.selected, 'bg-[#E7DFFF]': !item.selected, '!bg-[#E4E4E4] cursor-not-allowed': calcDisabled(item.name, singlePaper?.paperInfo?.totalPower) }">
						<IconSelect class="w-[14px] h-[14px] text-[#fff]"></IconSelect>
					</div>
				</div>
				<span class=" text-[#FF5303] text-[16px] rounded-[6px] mt-[11px]">{{ item?.name }}</span>
				<div class="flex flex-row items-end">
					<span class="flex flex-row items-end">
						<span class="text-[12px] text-[#3D3D3D] relative bottom-[2px]">￥</span>
						<span class="text-[28px] font-bold leading-[36px] text-[#3D3D3D]">{{
							Number(item?.originalPrice) - Number(item?.discount) }}</span>
					</span>
					<span class="text-xs text-gray-400 line-through relative bottom-[3px]">
						￥{{ item?.originalPrice }}
					</span>
				</div>
				<div
					class=" w-full h-[30px] leading-[30px] text-center rounded-bl-[6px] rounded-br-[6px] bg-[#EADAFF] text-[#883DFF] text-[12px]">
					{{ item?.description }}</div>
			</div>
		</div>
		<div class="divider px-[20px]"></div>
		<div class="grid grid-cols-3 gap-[10px] flex-wrap mt-[16px] mx-[19px]">
			<div class="flex flex-row gap-x-[10px] flex-[33%]">
				<span
					class="inline-block text-[16px] text-[#3d3d3d] leading-[21px] w-[80px] text-justify TextAlignLastJustify whitespace-nowrap">写作题目:</span>
				<NEllipsis class="text-[16px] text-[#3d3d3d] leading-[21px] whitespace-pre-wrap">{{
					singlePaper?.paperInfo?.title
				}}</NEllipsis>
			</div>
			<div class="flex flex-row gap-x-[5px] flex-[33%] ">
				<span
					class="inline-block text-[16px] text-[#3d3d3d] leading-[21px] w-[80px] text-justify TextAlignLastJustify shrink-0">写作类型：</span>
				<span
					class="text-[16px] text-[#3d3d3d] leading-[21px] whitespace-nowrap overflow-hidden text-ellipsis max-w-[120px]">{{
						paperTypeName }}</span>
				<span
					class=" rounded-[2px] w-[50px] h-[20px] leading-[20px] bg-[#F1F7FF] text-center text-[#0E69FF] text-[12px] shrink-0">{{
						modeName }}</span>
			</div>
			<!-- <div class="flex flex-row gap-x-[5px] flex-[33%]">
				<span
					class="inline-block text-[16px] text-[#3d3d3d] leading-[21px] w-[80px] text-justify TextAlignLastJustify">写作模式：</span>
				<span class="text-[16px] text-[#3d3d3d] leading-[21px]">{{ modeName }}</span>
			</div> -->
			<div class="flex flex-row gap-x-[5px] flex-1" v-if="singlePaper?.paperInfo?.words">
				<span class="inline-block text-[16px] text-[#3d3d3d] leading-[21px] w-[50px] ">字
					数:</span>
				<span class="text-[16px] text-[#3d3d3d] leading-[21px]">约{{ singlePaper?.paperInfo?.words }}字</span>
			</div>

		</div>
		<div class=" w-full mx-[19px] mt-[13px] mb-[19px]" v-if="singlePaper?.paperInfo?.subjoins?.length">
			<div class="text-[16px] text-[#3d3d3d] leading-[21px]">您选择了<span class=" text-[#FF5100]">{{
				singlePaper?.subjoinCount }}</span>
				项增值服务(仅限单次生成)</div>
		</div>
		<div v-if="singlePaper?.paperInfo?.subjoins?.length"
			class=" h-[45px] leading-[45px] mx-[19px] px-[10px] border-[#D2E3FF] border-[1px] rounded-[4px] bg-[#F1F7FF] relative">
			<div class="up-arrow"></div>
			<div class="up-arrow-border"></div>
			<span class="truncate">{{singlePaper?.paperInfo?.subjoins?.map((item: any) => item.title).join("、")}}</span>
			<!-- <ValueAddedCard v-for="(item, index) in singlePaper?.paperInfo?.subjoins " :key="index"
				:original-price="item.originalPrice" :title="item.title" :price="item.price" /> -->
		</div>
	</div>
</template>

<style lang="less" scoped>
.active {
	background: #212f46;
	color: #f6cf8d;
}

.default {
	background: #ffffff;
	color: #3d3d3d;
}

.divider {
	background-color: #fff;

	>div {
		width: 100%;
		height: 1px;
		background: #EEEEEE;
	}
}

.up-arrow {
	position: absolute;
	top: -16px;
	left: 57px;
	border-left: 10px solid transparent;
	border-right: 10px solid transparent;
	border-top: 10px solid transparent;
	border-bottom: 10px solid #F1F7FF;
	z-index: 10;
}

.up-arrow-border {
	position: absolute;
	top: -14px;
	left: 60px;
	border-left: 7px solid transparent;
	border-right: 7px solid transparent;
	border-top: 7px solid transparent;
	border-bottom: 7px solid #D2E3FF;
}

// .hot {
//     &::before {
//         content: 'HOT';
//         color: #ffffff;
//         font-size: 12px;
//         position: absolute;
//         top: -2px;
//         right: 15px;
//     }
// }
</style>
