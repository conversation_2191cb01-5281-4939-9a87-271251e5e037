<script lang="ts" setup>
import RechargeCard from './RechargeCard.vue';
import RechargeCardList from './RechargeCardList.vue';

interface Props {
    rechargeList: any[]
}
interface Emit {
    (ev: 'update-program', item: 'ai' | 'ppt' | 'paper'): void
    (ev: 'update-select-good', item: any): void
}
const props = defineProps<Props>();
const emit = defineEmits<Emit>();

</script>

<template>
    <RechargeCardList :recharge-list="rechargeList"
        @update-select-good="($event) => emit('update-select-good', $event)" />
</template>

<style lang="less"></style>