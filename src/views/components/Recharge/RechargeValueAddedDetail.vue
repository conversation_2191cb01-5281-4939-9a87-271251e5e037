<script setup lang="ts">
import { NImage } from 'naive-ui'
import RobotImg from '@/assets/images/robot.gif'
import CheckedImg from '@/assets/images/checked.png'
import { replaceText } from '@/plugins/directive';
import { computed } from 'vue'


interface Props {
    showRobot: boolean
    selectedGood: any
}
const props = defineProps<Props>()

const ValueAddedTitle = computed(() => {
    return props?.selectedGood?.mark || ''
})

const ValueAddedList = computed(() => {
    const desc = props?.selectedGood.allDesc || '';
    return desc.split(/<br>|\n/).filter(item => item.trim());
})

</script>

<template>
    <div class=" flex flex-col w-[full] h-[100%] p-[18px]">
        <!-- Hi~我是你的智能办公助 手，一站式办公神器 -->
        <div v-if="showRobot" class="robot-container " style="box-shadow: 0px 4px 11px 0px rgba(185, 204, 255, 0.64);">
            <span class=" text-[#0000FF] ">Hi~</span>我是你的智能办公助手，一站式办公神器
            <NImage :src="RobotImg" class=" w-[35px] h-[35px] absolute bottom-[-22px] right-[-6px] z-[2]"
                preview-disabled />
        </div>
        <div v-if="showRobot" class="divider"></div>
        <div class=" text-[16px] font-bold text-[#3D3D3D] leading-[21px] mt-[30px] mb-[19px] text-center">{{	 replaceText(ValueAddedTitle) }}
        </div>
        <div class=" flex flex-col gap-y-[14px]">
            <div v-for="(item, index) in ValueAddedList" :key="item + index"
                class=" flex flex-row items-start gap-x-[8px]">
                <div class=" h-[full] pt-[3px] flex items-start">
                    <NImage :src="CheckedImg" class=" w-[10px] h-[10px]" preview-disabled />
                </div>
                <span class="  flex-1 text-[#3D3D3D] text-[12px] leading-[16px]">{{ replaceText(item) }}</span>
            </div>
        </div>
    </div>


</template>

<style lang="less" scoped>
.robot-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0px 4px 11px 0px rgba(185, 204, 255, 0.64);
    position: relative;
    font-size: 12px;
    line-height: 18px;
    color: #333333;
    padding: 9px;
    &::after {
        // 写一个三角形
        content: '';
        position: absolute;
        bottom: -7px;
        left: 60%;
        transform: translateX(30%);
        border-top: 0px solid transparent;
        border-right: 15px solid #fff;
        border-bottom: 9px solid transparent;
        border-left: 5px solid transparent;
    }
}
.divider {
    width: 100%;
    height: 1px;
    background-color: #CCDEE7;
    margin-top: 31px;
}
</style>
