<script lang="ts" setup>
import { computed } from "vue";
import { NImage,NEllipsis } from "naive-ui";
import BillIcon from "@/assets/images/bill.png";

import RechargeCardList from './RechargeCardList.vue';
import { DescKeyValues } from "../types";

interface MemberOutItem {
	id?: number; // 会员ID
	name?: string; // 会员名称
	alias?: string; // 会员别名
	price?: number; // 会员价格
	originalPrice?: number; // 会员原价
	discount?: number; // 折扣金额
	description?: string; // 权益描述
	discountDesc?: string; // 折扣描述
	markUrl?: string; // 角标URL
	giftDesc?: string; // 赠品描述
	allDesc?: string; // 完整描述
	selected?: boolean; // 是否选中
	keyValues?: DescKeyValues[]
}

interface Props {
	// 会员类型(DOC_MEMBER-文档处理会员，DOC_TRANS_MEMBER文档翻译会员，DOC_DECODE_MEMBER文档解密会员)
	memberType: string;
	rechargeList: MemberOutItem[];
}
interface Emit {
	(ev: "update-select-good", item: any, selected?: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
	rechargeList: () => [],
});

const emit = defineEmits<Emit>();

const singlePaper = computed(() => {
	return props.rechargeList?.length === 1 ? props.rechargeList[0] : null;
});

const singlePaperDesc= computed<DescKeyValues[]>(() => {
	if (props.memberType === "DOC_DECODE_MEMBER" && props.rechargeList?.length === 1) {
		return singlePaper.value?.keyValues! || []
	} else if (props.memberType === "DOC_TRANS_MEMBER" && props.rechargeList?.length === 1) {
		return singlePaper.value?.keyValues! || []
	}
	return []
});

const handleSelectComputility = (item: any) => {
	emit('update-select-good', item, !item.selected)
};
</script>

<template>
	<div class="bg-[#FFFFFF] rounded-tl-[10px] rounded-tr-[10px]  flex flex-col justify-start"
		:class="{ ' h-[320px]': !singlePaper, ' h-[280px]': singlePaper }">
		<!-- header -->
		<div
			class=" flex flex-row items-center justify-start gap-x-[10px] px-[17px] py-[10px] rounded-tl-[10px] rounded-tr-[10px] h-[49px] bg-gradient-to-r  to-[#DBECFF] from-[#D8E6FF]">
			<n-image :src="BillIcon" width="18" height="18" />
			<span class="text-[#232323] text-[16px]">订单详情</span>
		</div>
		<template v-if="singlePaper">
			<span class=" mt-[15px] mb-[12px] mx-[20px] text-[#333333] text-[14px]">仅单次购买:</span>
			<div
				class=" mx-[20px] rounded-[6px] flex flex-col items-center justify-center gap-x-[10px] w-[190px] h-[78px] bg-[#FFF6F1]">
				<div class="flex flex-col items-center justify-start gap-x-[10px] py-[8px]">
					<span class=" text-[#FF5303] text-[16px] rounded-[6px]">{{ singlePaper?.name }}</span>
					<div class="flex flex-row items-end">
						<span class="flex flex-row items-end">
							<span class="text-[12px] text-[#3D3D3D] relative bottom-[2px]">￥</span>
							<span class="text-[28px] font-bold leading-[36px] text-[#3D3D3D]">{{
								singlePaper?.price }}</span>
						</span>
						<span class="text-xs text-gray-400 line-through relative bottom-[3px]">
							￥{{ singlePaper?.originalPrice }}
						</span>
					</div>
				</div>
			</div>
			<div
				class="flex flex-row flex-wrap text-[14px] leading-[40px] py-[15px] px-[20px] text-[#333]">
				<div v-for="(item, index) in singlePaperDesc" :key="index" class="w-1/3">
					<NEllipsis class="truncate text-center">{{ item.key }}: {{ item.value || '-' }}</NEllipsis>
				</div>
			</div>
		</template>
		<RechargeCardList v-else :recharge-list="rechargeList"
			@update-select-good="($event) => emit('update-select-good', $event)" />
	</div>
</template>
<style lang="less" scoped>
.active {
	background: #212f46;
	color: #f6cf8d;
}

.default {
	background: #ffffff;
	color: #3d3d3d;
}

.divider {
	background-color: #fff;

	>div {
		width: 100%;
		height: 1px;
		background: #EEEEEE;
	}
}

.up-arrow {
	position: absolute;
	top: -16px;
	left: 57px;
	border-left: 10px solid transparent;
	border-right: 10px solid transparent;
	border-top: 10px solid transparent;
	border-bottom: 10px solid #F1F7FF;
	z-index: 10;
}

.up-arrow-border {
	position: absolute;
	top: -14px;
	left: 60px;
	border-left: 7px solid transparent;
	border-right: 7px solid transparent;
	border-top: 7px solid transparent;
	border-bottom: 7px solid #D2E3FF;
}
</style>
