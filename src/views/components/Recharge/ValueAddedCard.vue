<script lang="ts" setup>
import { NEllipsis } from 'naive-ui';
interface Props {
    title: string;
    price: string;
    originalPrice: string
}

defineProps<Props>();
</script>

<template>

    <div
        class=" w-full] rounded-[3px] h-[40px] bg-[#F1F7FF] border-[#D2E3FF] border-[1px] px-[9px] leading-[40px] flex flex-row justify-between">
        <NEllipsis class=" text-[#3D3D3D] text-[14px] flex-1 "> {{ title }}</NEllipsis>
        <span class=" flex justify-end">
            <span class=" text-[#FC6A00] text-[14px] mr-[7px]">{{ price }}<span class=" text-[10px]">元</span> </span>
            <span class=" text-[#AAAAAA] text-[10px] line-through leading-[46px]">{{ originalPrice }}</span>
        </span>
    </div>

</template>

<style lang="less" scoped></style>