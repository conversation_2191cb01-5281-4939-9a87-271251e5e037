<script lang="ts" setup>
import {
	computed,
	defineProps,
	defineEmits,
	ref,
	onMounted,
	watch,
	nextTick,
	onUnmounted,
	inject,
} from "vue";
import { NModal, useMessage, NImage, NSpin } from "naive-ui";
import Logo from "@/assets/images/logo1.png";
import CrownImg from "@/assets/images/crown_gold.png";

import ComputingPowerRecharge from "./ComputingPowerRecharge.vue";
import PaperRecharge from "./PaperRecharge.vue";
import PaymentDetail from "./PaymentDetail.vue";
import { useRequest } from "vue-hooks-plus";
import { createAigcCheckOrder, createPaperCheckOrder, fetchCheckRecharge, fetchMemberList, fetchPaperCheckDetail, fetchRechargeTemplate } from "./apis";
import {
	AiworkType,
	BaseRechargeDetail,
	RechargeDetailType,
	ReChargeAiDetail,
} from "@/typings";
import AggregateRecharge from "./AggregateRecharge.vue";
import PPTRecharge from "./PPTRecharge.vue";
import RechargeValueAddedDetail from './RechargeValueAddedDetail.vue';
import { isArray } from "@/utils/is";
import { useUserStore } from "@/store";
import ChangePersonOrTeam from "./ChangePersonOrTeam.vue";
import RechargeHeaderUser from "./RechargeHeaderUser.vue";
import CheckRecharge from './CheckRecharge.vue';
import OfficeRecharge from './OfficeRecharge.vue';
import PaperRepeatRecharge from './PaperRepeatRecharge.vue';
import PaperAIGCCheckRecharge from "./PaperAIGCCheckRecharge.vue";
import PaperAIGCRepeatRecharge from "./PaperAIGCRepeatRecharge.vue";
import { CheckRechargeProps, PaperRepeatProps } from "../types";
// interface Props {
//     visible: boolean;
//     detail: RechargeDetailType & BaseRechargeDetail
// }

// interface Emit {
//     (e: "update:visible", visible: boolean): void;
// }

// const props = defineProps<Props>();
// const emit = defineEmits<Emit>();
const changeTeamProvide = inject<any>('changeTeamProvide')
const defaultPayParam: ReChargeAiDetail = { type: "ai" };
const data = ref<any[] | any>([]);
const paperComputilityList = ref<any[]>([]);
const selectedGood = ref<any>({});
const checkedPayment = ref(1);
const activeKey = ref(1);
const payParam = ref<RechargeDetailType & BaseRechargeDetail>(defaultPayParam);
const show = ref(false);
const paymentList = ref([
	{
		id: 1,
		name: "微信支付",
		url: "https://cdn2.weimob.com/static/aiwork365-web-stc/assets/wxpay.png",
	},
	{
		id: 2,
		name: "支付宝支付",
		url: "https://cdn2.weimob.com/static/aiwork365-web-stc/assets/alipay.png",
	},
]);
const message = useMessage();
const userStore = useUserStore();
const promiseResolvers = ref<any>();
const isTeam = ref<number>(0);
const prevTeamId = ref(userStore.curTeam?.id || null);
// const paymentInfoList = ref<any[]>([])
// const show = computed({
//     get: () => props.visible,
//     set: (visible: boolean) => emit("update:visible", visible),
// });

(window.$aiwork as AiworkType).openRecharge = (detail: RechargeDetailType) => {
	promiseResolvers.value = Promise.withResolvers();
	// if(!userStore.userInfo?.uid) {
	// 	return (window.$aiwork as AiworkType).openLogin?.().then(() => {
	// 		(window.$aiwork as AiworkType).openRecharge(detail)
	// 	})
	// }
	show.value = true;
	payParam.value = detail;
	activeKey.value = 1;
	openPayment(detail);
	return promiseResolvers.value.promise;
};

watch(() => show.value, (value) => {
	if (!value) return
	isTeam.value = userStore.curTeam ? 1 : 0
	prevTeamId.value = userStore.curTeam?.id || null
	window.rprm.rec({
		elementid: 'recharge_start',
		payment_scene: calculateType.value,
		payment_paper_id: payParam.value?.paperId,
		payment_category_id: payParam.value?.categoryId,
	})
})

watch(() => userStore.curTeam, (value) => {
	refreshFetchRechargeTemplate()
})

const handleClose = () => {
	show.value = false;
	activeKey.value = 1;
	payParam.value = defaultPayParam;
	promiseResolvers.value?.reject({ type: "cancel", message: "取消支付" });
	promiseResolvers.value = null;
	nextTick(() => {
		data.value = [];
		selectedGood.value = {};
		activeKey.value = 1;
		if (prevTeamId.value != userStore.curTeam?.id) changeTeamProvide()
	});
};

// 论文降aigc专用
const { run: runCreateAigcCheckOrder } = useRequest(createAigcCheckOrder, {
	manual: true,
	debounceWait: 100,
	onSuccess: (res) => {
		if (isArray(res)) {
			let hasSelected = false;
			// 有tag的 选中tag 没有tag的选中第一项
			res.forEach((item: RechargeProps) => {
				if (item.tag) {
					item.selected = true;
					hasSelected = true;
					selectedGood.value = item;
				} else {
					item.selected = false;
				}
			});
			if (!hasSelected && res.length > 0) {
				res[0].selected = true;
				selectedGood.value = res[0];
			}
			data.value = res;
		} else {
			data.value = [];
			selectedGood.value = res;
		}
	}
})
// 论文降重专用
const { run: runCreatePaperCheckOrder } = useRequest(createPaperCheckOrder, {
	manual: true,
	debounceWait: 100,
	onSuccess: (res) => {
		if (isArray(res)) {
			let hasSelected = false;
			// 有tag的 选中tag 没有tag的选中第一项
			res.forEach((item: RechargeProps) => {
				if (item.tag) {
					item.selected = true;
					hasSelected = true;
					selectedGood.value = item;
				} else {
					item.selected = false;
				}
			});
			if (!hasSelected && res.length > 0) {
				res[0].selected = true;
				selectedGood.value = res[0];
			}
			data.value = res;
		} else {
			data.value = [];
			selectedGood.value = res;
		}
	}
})

// 论文查重专用
const { run: runCheckRecharge, loading: checkRechargeLoading } = useRequest(fetchCheckRecharge, {
	manual: true,
	debounceWait: 100,
	onSuccess: (res) => {
		if (isArray(res)) {
			let hasSelected = false;
			// 有tag的 选中tag 没有tag的选中第一项
			res.forEach((item: RechargeProps) => {
				if (item.tag) {
					item.selected = true;
					hasSelected = true;
					selectedGood.value = item;
				} else {
					item.selected = false;
				}
			});
			if (!hasSelected && res.length > 0) {
				res[0].selected = true;
				selectedGood.value = res[0];
			}
			data.value = res;
		} else {
			data.value = [];
			selectedGood.value = res;
		}
	}
})

// office支付专用
const { run: runFetchMemberList, loading: fetchMemberListLoading } = useRequest(fetchMemberList, {
	manual: true,
	debounceWait: 100,
	onSuccess: (res) => {
		if (isArray(res)) {
			let hasSelected = false;
			// 有tag的 选中tag 没有tag的选中第一项
			res.forEach((item: RechargeProps) => {
				if (item.tag) {
					item.selected = true;
					hasSelected = true;
					selectedGood.value = item;
				} else {
					item.selected = false;
				}
			});
			if (!hasSelected && res.length > 0) {
				res[0].selected = true;
				selectedGood.value = res[0];
			}
			data.value = res;
		} else {
			data.value = [];
			selectedGood.value = res;
		}
	}
})


// 论文查重重新支付专用
const { run: runPaperCheckDetail, loading: paperCheckDetailLoading } = useRequest(fetchPaperCheckDetail, {
	manual: true,
	debounceWait: 100,
	onSuccess: (res) => {
		if (isArray(res)) {
			let hasSelected = false;
			// 有tag的 选中tag 没有tag的选中第一项
			res.forEach((item: RechargeProps) => {
				if (item.tag) {
					item.selected = true;
					hasSelected = true;
					selectedGood.value = item;
				} else {
					item.selected = false;
				}
			});
			if (!hasSelected && res.length > 0) {
				res[0].selected = true;
				selectedGood.value = res[0];
			}
			data.value = res;
		} else {
			data.value = [];
			selectedGood.value = res;
		}
	}
})

interface RechargeProps {
	id: number;
	name: string;
	originalPrice: string;
	discount: string;
	description: null;
	discountDesc: null;
	alias: string;
	selected: boolean;
	tag?: string;
}
const { run: runFetchRechargeTemplate, loading, refresh: refreshFetchRechargeTemplate } = useRequest(
	(data: {
		source?: string;
		goodsType: string;
		paperId?: number;
		multiple?: boolean;
	}) => {
		return fetchRechargeTemplate({ ...data, isTeam: isTeam.value });
	},
	{
		manual: true,
		debounceWait: 100,
		onSuccess: (res: any, params) => {
			if (params?.[0]?.multiple) {
				res.forEach((item: any) => {
					item.selected = false
				})
				paperComputilityList.value = res as any[];
			} else {
				if (isArray(res)) {
					let hasSelected = false;
					// 有tag的 选中tag 没有tag的选中第一项
					res.forEach((item: RechargeProps) => {
						if (item.tag) {
							item.selected = true;
							hasSelected = true;
							selectedGood.value = item;
						} else {
							item.selected = false;
						}
					});
					if (!hasSelected && res.length > 0) {
						res[0].selected = true;
						selectedGood.value = res[0];
					}
					data.value = res;
				} else {
					data.value = [];
					selectedGood.value = res;
				}
			}
		},
	}
);

// const runFetchRechargeTemplate = (params) => {
// 	return fetchRechargeTemplate<any>(params).then((data) => {
// 		console.log(666, data)
// 		paymentInfoList.value = [...data]
// 	})
// }
const calculateType = computed(() => {
	if (payParam.value.type === "ai") {
		return "AI";
	} else if (payParam.value.type === "paper") {
		return "PaperAll";
	} else if (payParam.value.type === "ppt") {
		return "PPT";
	} else if (payParam.value.type === "mj") {
		return "MJ";
	} else if (payParam.value.categoryId === 7) {
		return "PaperRepeat"
	} else if (payParam.value.categoryId === 8) {
		return "AIGCCheck"
	} else if (payParam.value.categoryId === 9) {
		return "AIGCRepeat"
	} else if (payParam.value.author || payParam.value.categoryId === 5) {
		return "CHECK"
	} else if (payParam.value.categoryId === 6 || payParam.value.memberType) {
		return "OFFICE"
	} else {
		return "ALL";
	}
});

const openPayment = (data) => {
	let goodsType = "ai";
	if (payParam.value.type === "ai") {
		if (payParam.value.categoryId === 2) {
			goodsType = "ppt";
		} else if (payParam.value.categoryId === 3) {
			goodsType = "paper";
		} else if (payParam.value.categoryId === 4) {
			goodsType = "mj";
		} else if (payParam.value.categoryId === 5) {
			goodsType = "check";
		} else if (payParam.value.categoryId === 6) {
			goodsType = "office";
		} else if (payParam.value.categoryId === 7) {
			goodsType = "paperrepeat";
		} else if (payParam.value.categoryId === 8) {
			goodsType = "aigccheck";
		} else if (payParam.value.categoryId === 9) {
			goodsType = "aigcrepeat";
		}
	} else {
		goodsType = payParam.value.type;
	}
	if (payParam.value?.categoryId !== 5 && payParam.value?.categoryId !== 6 && payParam.value?.categoryId !== 7 && payParam.value?.categoryId !== 8 && payParam.value?.categoryId !== 9) {
		runFetchRechargeTemplate({
			goodsType,
			paperId: payParam?.value?.paperId || data?.paperId || undefined,
		});
		if (payParam.value.type === 'paper') {
			setTimeout(() => {
				runFetchRechargeTemplate({
					goodsType,
					multiple: true,
				});
			}, 1000)
		}
	} else if (payParam.value?.categoryId === 6) {
		runFetchMemberList({
			memberType: payParam.value?.memberType,
			taskId: payParam.value?.taskId
		})
	} else if (payParam.value?.categoryId === 7) {
		runCreatePaperCheckOrder({
			...payParam.value as PaperRepeatProps,
			categoryId: undefined
		})
	} else if (payParam.value?.categoryId === 8 || payParam.value?.categoryId === 9) {
		runCreateAigcCheckOrder({
			...payParam.value as PaperRepeatProps,
			categoryId: undefined
		})
	} else {
		if (payParam.value.id) {
			runPaperCheckDetail({
				id: payParam.value.id
			})
		} else {
			runCheckRecharge({
				...payParam.value as CheckRechargeProps,
				categoryId: undefined
			})
		}
	}
};

const handleSwitchPayment = (item: { id: number }) => {
	checkedPayment.value = item.id;
};
const handleChangePaperPayment = (id: number) => {
	activeKey.value = id;
	if (id === 1) {
		runFetchRechargeTemplate({
			goodsType: payParam.value.type,
			paperId: payParam.value.paperId || 10,
		});
	} else {
		runFetchRechargeTemplate({
			goodsType: payParam.value.type,
		});
	}
};
const handlePaymentSuccess = () => {
	show.value = false;
	message.success("支付成功");
	// props.detail.onSuccess?.()
	setTimeout(() => {
		promiseResolvers.value?.resolve();
		promiseResolvers.value = null;
		if (prevTeamId.value != userStore.curTeam?.id) changeTeamProvide()
	}, 300)
};

const handleUpdateProgram = (item: "ai" | "ppt" | "paper" | "mj" | 'write') => {
	runFetchRechargeTemplate({
		goodsType: item,
	});
};
const handleUpdateSelectGood = (item: any, selected?: boolean) => {
	if (!paperComputilityList.value.length) {
		selectedGood.value = item;
		data.value.map((item: RechargeProps) => {
			if (item.id === selectedGood.value.id) {
				item.selected = true;
			} else {
				item.selected = false;
			}
		});
	} else {
		if (selected) {
			selectedGood.value = item;
		} else {
			selectedGood.value = data?.value?.[0]
		}
		paperComputilityList.value.map((item: any) => {
			if (item.id === selectedGood.value.id) {
				item.selected = selected
			} else {
				item.selected = false
			}
		});
	}
};

const showChangeTeam = ref(false)
const handleTeamChange = async () => {
	if (isTeam.value == 1 && !userStore.curTeam) {
		await userStore.getTeamList()
		if (userStore.teamList?.length === 1) userStore.changeTeam(userStore.teamList[0].id)
		if (userStore.teamList?.length > 1) return showChangeTeam.value = true
	}
	refreshFetchRechargeTemplate()
}
</script>

<template>
	<NModal :show="show" style="
			width: 1058px;
			background: linear-gradient(
				180deg,
				#eff6ff 0%,
				#e7f3ff 54%,
				#fdfeff 114%
			);
			--n-padding-top:0;
			--n-padding-bottom:0;
			--n-padding-left:0;
		" class="recharge-container relative sm:!w-[96vw] sm:rounded-md sm:overflow-hidden ipad:!w-[100vw]" :closable="false"
		reset="card" @mask-click="handleClose" @close="handleClose" :on-esc="handleClose">
		<!-- <template #header-main> -->
		<!-- <div class="absolute top-[-30px] right-[-40px] cursor-pointer" @click="handleClose">
				<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none"
					version="1.1" width="30" height="30.000001907348633" viewBox="0 0 30 30.000001907348633">
					<g>
						<path
							d="M15,0C6.85714,0,0,6.85714,0,15C0,23.1429,6.85714,30,15,30C23.1429,30,30,23.1429,30,15C30,6.85714,23.1429,0,15,0ZM21.4286,19.2857C22.0714,19.9286,22.0714,20.7857,21.4286,21.2143C20.7857,21.8571,19.9286,21.8571,19.5,21.2143L15.2143,16.9286L10.7143,21.4286C10.0714,22.0714,9.21429,22.0714,8.57143,21.4286C7.92857,20.7857,7.92857,19.7143,8.57143,19.2857L13.0714,14.7857L8.78571,10.5C8.14286,10.0714,8.14286,9.21429,8.78571,8.57143C9.42857,7.92857,10.2857,7.92857,10.7143,8.57143L15,12.8571L19.5,8.35714C20.1429,7.71429,21,7.71429,21.6429,8.35714C22.2857,9,22.2857,9.85714,21.6429,10.5L17.1429,15L21.4286,19.2857Z"
							fill="#FFFFFF"
							fill-opacity="0.6000000238418579"
							style="mix-blend-mode: passthrough"
						/>
					</g>
				</svg>
			</div> -->
		<!-- </template> -->

		<template #default>
			<NSpin :show="loading"> <!-- <div class="flex flex-col gap-y-[5px] justify-center items-center">
					<NImage :src="Logo" class="w-[193px] h-[37px]" />
					<span class="text-[16px] text-[#3d3d3d]">您的专家级私人助理，无论有什么问题，都会给您满意的答案</span>
				</div> -->

				<div
					class="flex flex-row items-center justify-between pl-[38px] pr-[29px] recharge-header-container sm:hidden ipad:hidden">
					<!-- <NImage :src="Logo" class=" h-[30px]" preview-disabled /> -->
					<RechargeHeaderUser v-model:show-change="showChangeTeam" :isTeam="isTeam"
						:disabled="calculateType == 'PaperAll'" />
					<div class=" flex items-center gap-x-[26px]">
						<div class=" flex flex-row gap-x-[5px] items-center flex-1" v-if="calculateType !== 'PaperAll'">
							<NImage :src="CrownImg" class="w-[15px] h-[13px]" preview-disabled />
							<span class="text-[#0066FF] text-[16px] leading-[24px]">开通会员畅享AI功能，解锁更多会员专属权益</span>
						</div>
						<div class=" flex flex-row gap-x-[5px] items-center flex-1" v-else>
							<span class="text-[#0066FF] text-[16px] leading-[24px]">标准学术格式，超低查重</span>
						</div>
						<IconClose class=" w-[14px] h-[14px] text-[14px] relative cursor-pointer text-[#404040] "
							@click="handleClose" />
					</div>
				</div>
				<!-- 个人/团队切换 -->
				<ChangePersonOrTeam
					v-if="calculateType !== 'PaperAll' && calculateType !== 'CHECK' && calculateType !== 'OFFICE' && calculateType !== 'PaperRepeat' && calculateType !== 'AIGCCheck' && calculateType !== 'AIGCRepeat'"
					v-model:value="isTeam" @onTabChange="handleTeamChange" />

				<div class="flex flex-row justify-start sm:w-full ipad:w-full">
					<div
						class=" w-[220px] bg-[#fff] pt-[1px] pr-[1px] sm:hidden ipad:hidden flex justify-center bg-gradient-to-r from-[#ECF5FF] to-[#EAF4FF] shrink-0 grow-0">
						<RechargeValueAddedDetail :selected-good="selectedGood"
							:show-robot="calculateType === 'PaperAll' || calculateType === 'CHECK' || calculateType === 'OFFICE' || calculateType === 'PaperRepeat' || calculateType === 'AIGCCheck' || calculateType === 'AIGCRepeat'" />
					</div>
					<div
						class=" flex-1 w-[831px] bg-gradient-to-r from-[#ECF5FF] to-[#EAF4FF] p-[22px] sm:p-3 sm:w-full ipad:w-full min-w-0">
						<OfficeRecharge v-if="calculateType === 'OFFICE'" :recharge-list="data || []"
							:member-type="payParam?.memberType" @update-select-good="handleUpdateSelectGood" />
						<PaperRepeatRecharge v-if="calculateType === 'PaperRepeat'" :recharge-list="data || []" />
						<PaperAIGCCheckRecharge v-if="calculateType === 'AIGCCheck'" :recharge-list="data || []" />
						<PaperAIGCRepeatRecharge v-if="calculateType === 'AIGCRepeat'" :recharge-list="data || []" />
						<CheckRecharge v-if="calculateType === 'CHECK'" :recharge-list="data || []" />
						<PaperRecharge v-if="calculateType === 'PaperAll' && !isTeam" :show="show" :recharge-list="data || []"
							:paper-computility-list="paperComputilityList" @update-select-good="handleUpdateSelectGood" />
						<PPTRecharge v-if="calculateType === 'PPT'" :recharge-list="data || []"
							@update-select-good="handleUpdateSelectGood" />
						<AggregateRecharge v-if="calculateType === 'AI' || isTeam" :isTeam="isTeam" :recharge-list="data"
							:category-id="payParam.categoryId" @update-program="handleUpdateProgram"
							@update-select-good="handleUpdateSelectGood" />
						<div class="divider px-[20px]">
							<div></div>
						</div>
						<NSpin :show="loading">
							<PaymentDetail v-if="show" :payment-detail="selectedGood" :payment-list="paymentList"
								@payment-success="handlePaymentSuccess" :isTeam="isTeam" @emit-select-team="handleTeamChange"
								:type="calculateType === 'PaperAll' ? 'PaperAll' : calculateType === 'OFFICE' ? 'OFFICE' : calculateType === 'AIGCCheck' || calculateType === 'AIGCRepeat' ? 'AIGC' : undefined"
								:task-id="payParam?.taskId" />
						</NSpin>
					</div>
				</div>
			</NSpin>
		</template>
	</NModal>
</template>

<style lang="less" scoped>
.recharge-header-container {
	height: 80px;
	background-image: url(@/assets/images/recharge_bg.png);
	border-top-right-radius: 8px
}

.recharge-container {
	.n-base-close {
		display: none !important;
	}
}

.divider {
	background-color: #fff;

	>div {
		width: 100%;
		height: 1px;
		background: #EEEEEE;
	}
}
</style>
