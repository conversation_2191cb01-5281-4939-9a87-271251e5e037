<!-- 这个页面是只充值算力的 -->
<script lang="ts" setup>
import { NCard } from 'naive-ui';
import { ref } from 'vue';
import RechargeCard from './RechargeCard.vue';
import RechargeCardList from './RechargeCardList.vue';

interface Props {
    rechargeList: any
}
interface Emit {
    (ev: 'update-select-good', item: any): void
}
/**
 * interface Props {
    id: number;
    name: string;
    originalPrice: string;
    discount: string;
    description: null;
    discountDesc: null;
    alias: string;
    selected: boolean
}
 */
const emit = defineEmits<Emit>();
const props = defineProps<Props>();
</script>
<template>
    <RechargeCardList :recharge-list="rechargeList"
        @update-select-good="($event) => emit('update-select-good', $event)" />
</template>
<style lang="less" scoped></style>