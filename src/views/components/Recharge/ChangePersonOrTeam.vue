<template>
	<div class=" flex flex-row justify-between sm:w-full gap-[10px]">
		<!-- 个人版本 -->
		<div
			class="flex-1 h-[50px] relative rounded-tl-[10px] rounded-tr-[10px] left flex items-center justify-center gap-x-[4px] cursor-pointer"
			:class="activeTab === 0 ? 'PaymentTabActive active' : 'PaymentTabPlain'" @click="handleSwitchTab(0)">
			<span :class="activeTab === 0 ? 'tab-text-active' : 'tab-text'">个人版本</span>
		</div>
		<!-- 团队版本 -->
		<div
			class="flex-1 h-[50px] relative rounded-tl-[10px] rounded-tr-[10px] right flex items-center justify-center gap-x-3 cursor-pointer"
			:class="activeTab === 1 ? 'PaymentTabActive active' : 'PaymentTabPlain'" @click="handleSwitchTab(1)">
			<span :class="activeTab === 1 ? 'tab-text-active' : 'tab-text'">团队版本</span>
		</div>
	</div>
</template>

<script setup lang="ts">
import { inject, onMounted, ref } from 'vue';
import { useUserStore } from '@/store';

const activeTab = defineModel<number>('value') // 0: personal, 1: team

const emit = defineEmits<{
	onTabChange: [activeTab?: number, teamId?: number, prevTeamId?: number]
}>()

const userStore = useUserStore()
const prevTeam = ref(userStore.curTeam)

onMounted(() => {
	if(userStore.curTeam) activeTab.value = 1
})

const handleSwitchTab = async (value: number) => {
	if(value === activeTab.value) return
	activeTab.value = value
	if(value === 0) {
		prevTeam.value = userStore.curTeam
		userStore.changeTeam(null)
	}
	else if(value === 1) {
		if(!prevTeam.value) {
			switch(userStore.teamList?.length) {
				case 0:
					break;
				default:
			}
		}
		else userStore.changeTeam(prevTeam.value?.id)
	}
	emit('onTabChange', value)
}
</script>

<style lang="less" scoped>
.left {
	&::after {
		content: " ";
		position: absolute;
		bottom: 0;
		right: -12px;
		border-width: 0 13px 45px 0;
		border-style: solid;
		border-color: transparent transparent white transparent;
	}
}

.left.active {
	&::after {
		content: " ";
		position: absolute;
		bottom: 0;
		right: -12px;
		border-width: 0 13px 45px 0;
		border-style: solid;
		border-color: transparent transparent #9e51f7 transparent;
	}
}

.center {
	&::before {
		content: " ";
		position: absolute;
		bottom: 0;
		left: -12px;
		border-width: 45px 13px 0 0;
		border-style: solid;
		border-color: transparent white transparent transparent;
	}

	&::after {
		content: " ";
		position: absolute;
		bottom: 0;
		right: -12px;
		border-width: 0 13px 45px 0;
		border-style: solid;
		border-color: transparent transparent white transparent;
	}
}

.center.active {
	&::before {
		content: " ";
		position: absolute;
		bottom: 0;
		left: -12px;
		border-width: 45px 13px 0 0;
		border-style: solid;
		border-color: transparent #0044ff transparent transparent;
	}

	&::after {
		content: " ";
		position: absolute;
		bottom: 0;
		right: -12px;
		border-width: 0 13px 45px 0;
		border-style: solid;
		border-color: transparent transparent #9e51f7 transparent;
	}
}

.right {
	&::before {
		content: " ";
		position: absolute;
		bottom: 0;
		left: -12px;
		border-width: 45px 13px 0 0;
		border-style: solid;
		border-color: transparent white transparent transparent;
	}
}

.right.active {
	&::before {
		content: " ";
		position: absolute;
		bottom: 0;
		left: -12px;
		border-width: 45px 13px 0 0;
		border-style: solid;
		border-color: transparent #0044ff transparent transparent;
	}
}

.tab-text {
	font-size: 16px;
	color: #212f46;
}

.tab-text-active {
	font-size: 16px;
	color: #ffffff;
}

@media screen and (max-width: 767px) {
	.tab-text {
		font-size: 12px;
		white-space: nowrap;
	}

	.tab-text-active {
		font-size: 12px;
		white-space: nowrap;
	}
}
</style>
