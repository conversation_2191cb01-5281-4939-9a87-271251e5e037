export interface CheckRechargeProps {
	type: string;
	file: File;
	content: string;
	title: string;
	author: string;
	categoryId?: number;
}

export interface DescKeyValues {
  key: string;
  value: string;
}

export interface PaperRepeatProps {
	type: string; // 降重平台类型
	file?: File; // 论文文件
	content?: string; // 论文内容
	title: string; // 论文标题
	author: string; // 论文作者
	submitType: "paper" | "report"; // 提交类型，原文：paper,报告：report
	id?: number; // 降重记录id, 有id时优先使用id的降重记录，以上参数会忽略
}