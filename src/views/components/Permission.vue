<script setup lang="ts">
import { computed, ref, watch } from "vue";
import {
	NButton,
	NInput,
	NModal,
	useMessage,
	NTag,
	NImage,
	NForm,
	NFormItem,
	NTooltip,
	NText,
} from "naive-ui";
import { fetchVerify, fetchGetCode, fetchCodeLogin } from "@/chatgpt";
import { useAuthStore, useUserStore } from "@/store";
import { useBasicLayout } from "@/hooks/useBasicLayout";
import { isPhoneNumber } from "@/utils/is";
import Qrcode from "./Qrcode.vue";
import qrcodeIcon from "@/assets/qrcode.png";
import mobileIcon from "@/assets/phone.png";
import logoImage from "@/assets/aiwork/logo.png";
import { AiworkType } from "@/typings";
import { setUser } from "@/store/modules/auth/helper";

const promiseResolvers = ref<any>();

// interface Props {
// 	visible: boolean;
// }
// interface Emit {
// 	(e: "update:visible", visible: boolean): void;
// }

// const props = defineProps<Props>();
// const emit = defineEmits<Emit>();
const { isMobile } = useBasicLayout();
// const show = computed({
// 	get: () => props.visible,
// 	set: (visible: boolean) => emit("update:visible", visible),
// });
const show = ref(false);

(window.$aiwork as AiworkType).openLogin = () => {
	promiseResolvers.value = Promise.withResolvers()
	show.value = true;
	return promiseResolvers.value.promise;
};

watch(() => show.value, (newValue) => {
	if(newValue) {
		window.rprm.rec({
			elementid: 'login',
		})
	}
})

const authStore = useAuthStore();
const userStore = useUserStore();
const ms = useMessage();

const loading = ref(false);
const isCodeLogin = ref(true);
let countdownSeconds = ref(60);
let countdownActive = ref(false);
const loginType = ref<string>(isMobile.value ? "phone" : "qrcode");
let formValue = ref<{ username: string; password: string }>({
	username: "",
	password: "",
});

const disabled = computed(
	() =>
		!formValue.value?.username?.trim() ||
		!formValue.value.password ||
		loading.value
);
let disabled3 = computed(
	() => !formValue.value?.username?.trim() || countdownActive.value
);

const countdownLabel = computed(() => {
	if (countdownActive.value) {
		return `重新获取(${countdownSeconds.value})`;
	} else {
		return "获取验证码";
	}
});
async function handleVerify(event: MouseEvent) {
	event.preventDefault();
	const name = formValue.value?.username;
	const pwd = formValue.value?.password;

	if (!name || !pwd) return;
	try {
		loading.value = true;
		let user: any;
		if (isCodeLogin.value) {
			user = await fetchCodeLogin(name, pwd);
		} else {
			user = await fetchVerify(name, pwd);
		}
		if (!user) {
			ms.error("登录失败");
			return;
		}

		authStore.setToken(user.token);
		setUser(user);
		userStore.updateUserInfo({
			name: user.nickname || user.username || user.phone,
			description: "用户信息",
			uid: user.id,
		});
		ms.success("登录成功");
		window.rprm.rec({
			elementid: 'login_success',
			logon_type: 'phone'
		})
		show.value = false;
		promiseResolvers.value?.resolve();
		promiseResolvers.value = null;
		userStore.getTeamList()
		// window.location.reload();
	} catch (error: any) {
		ms.error(error.message || "error");
		authStore.removeToken();
		formValue.value.password = "";
		window.rprm.rec({
			elementid: 'login_fail',
			logon_type: 'phone',
			login_error: error.message || "error"
		})
	} finally {
		loading.value = false;
	}
}
async function handleCode(event: MouseEvent) {
	event.preventDefault();
	if (disabled3.value) {
		return;
	}
	if (!isPhoneNumber(formValue.value?.username.trim())) {
		ms.error("请输入正常的手机号码");
		return;
	}
	countdownActive.value = true;

	let interval = setInterval(() => {
		countdownSeconds.value--;
		if (countdownSeconds.value <= 0) {
			clearInterval(interval);
			countdownSeconds.value = 60;
			countdownActive.value = false;
		}
	}, 1000);
	try {
		await fetchGetCode(formValue.value?.username.trim());
		ms.success("发送成功");
	} catch (error) {
		countdownSeconds.value = 60;
		countdownActive.value = false;
		ms.error("发送失败");
	}
	return;
}
const handClose = () => {
	show.value = false;
	promiseResolvers.value?.reject({ type: 'cancel', message: '取消登录' });
	promiseResolvers.value = null;
	window.rprm.rec({
		elementid: 'login_cancel',
	})
};
const handSuccess = () => {
	show.value = false;
	promiseResolvers.value?.resolve();
	promiseResolvers.value = null;
	window.rprm.rec({
		elementid: 'login_success',
		logon_type: 'qrcode'
	})
}
function handlePress(event: KeyboardEvent) {
	if (event.key === "Enter" && !event.shiftKey) {
		event.preventDefault();
	}
}

const handleChange = (status: string) => {
	loginType.value = status;
};
</script>

<template>
	<NModal v-model:show="show" :on-mask-click="handClose" :on-esc="handClose" style="width: 80%; height: 454px; max-width: 450px">
		<div class="bg-white rounded dark:bg-slate-800 relative flex flex-col items-center">
			<div class="absolute right-0 top-0 z-0 w-[70px] h-[70px] overflow-hidden rounded" style="
					/* background-image: linear-gradient(
						225deg,
						#d9f7ee 50%,
						rgba(255, 255, 255, 0) 50%
					); */
					background-image: linear-gradient(
						225deg,
						#e2edff 50%,
						rgba(255, 255, 255, 0) 50%
					);
				"></div>
			<NTooltip trigger="hover">
				<template #trigger>
					<div class="absolute top-2 right-2 cursor-pointer">
						<NImage :src="qrcodeIcon" width="45" height="45" preview-disabled
							@click="handleChange('qrcode')" v-if="loginType == 'phone'" />
					</div>
				</template>
				微信扫码 快速登录
			</NTooltip>
			<NTooltip trigger="hover">
				<template #trigger>
					<div class="absolute top-2 right-2 cursor-pointer">
						<NImage :src="mobileIcon" width="45" height="45" preview-disabled @click="handleChange('phone')"
							v-if="loginType == 'qrcode'" />
					</div>
				</template>
				使用账号登录
			</NTooltip>

			<div class="flex justify-start p-[25px] w-full">
				<img :src="logoImage" class="h-[40px]" alt="logo" />
			</div>

			<div v-if="loginType == 'qrcode'">
				<h1 class="text-[16px] font-normal text-center mt-[20px]">
					微信扫码一键登录
				</h1>
				<Qrcode :isbind="false" @close="handClose" @success="handSuccess" />
			</div>
			<div class="w-[80%]" v-if="loginType == 'phone'">
				<h1 class="text-[16px] font-normal text-left my-[20px]">验证码登录</h1>
				<NForm ref="formRef" :model="formValue">
					<NFormItem label="手机号" :rule="{
						required: true,
						message: `请输入手机号码`,
					}">
						<NInput v-model:value="formValue.username" placeholder="请输入手机号" @keypress="handlePress" />
					</NFormItem>
					<NFormItem label="验证码" :rule="{
						required: true,
						message: `请输入手机号码`,
					}">
						<div class="flex justify-between items-center w-full">
							<NInput v-if="isCodeLogin" v-model:value="formValue.password" type="text" maxlength="4"
								placeholder="请输入验证码" @keypress="handlePress" class="w-[70%]" />
							<!-- <n-tag v-if="isCodeLogin" type="success" :disabled="disabled3" v-on:click="handleCode" class="ml-[10px]" style="padding: 16px;">
								{{ countdownLabel }}
							</n-tag> -->
							<NButton v-if="isCodeLogin" quaternary type="primary" class="ml-[10px]"
								:disabled="disabled3" @click="handleCode">{{ countdownLabel }}</NButton>
						</div>
					</NFormItem>
					<NFormItem style="
							border: none !important;
							margin-top: 60px;
							margin-bottom: 0;
							height: 40px;
						">
						<div class="w-full mb-[40px]">
							<NButton type="primary" size="large" :loading="loading" :disabled="disabled"
								style="width: 100%" @click="handleVerify">
								{{ $t("common.verify") }}</NButton>
						</div>
					</NFormItem>
					<div class="text-[10px]">
						未注册手机验证后自动注册并登录，登录即表示同意<NText type="primary">《用户协议》</NText>和<NText type="primary">《隐私条款》</NText>
					</div>
				</NForm>
				<!-- <div class="space-y-4">
					<n-grid x-gap="12" :cols="12">
						<n-grid-item span="12">
							<NInput v-if="isCodeLogin" v-model:value="username" type="text" placeholder="请输入手机号"
								@keypress="handlePress" />
						</n-grid-item>
					</n-grid>

					<n-grid x-gap="12" :cols="12">
						<n-grid-item span="7">
							<NInput v-if="isCodeLogin" v-model:value="password" type="text" maxlength="4" placeholder="请输入验证码"
								@keypress="handlePress" />
						</n-grid-item>
						<n-grid-item span="5">
							<n-tag v-if="isCodeLogin" type="success" :disabled="disabled3" v-on:click="handleCode"
								style="padding:16px;width: 100%; text-align: center;justify-content:center;">
								{{ countdownLabel }}
							</n-tag>
						</n-grid-item>
					</n-grid>
					<n-grid x-gap="12" :cols="12" v-if="isCodeLogin">
						<n-grid-item span="7">
						</n-grid-item>
						<n-grid-item span="5">
						</n-grid-item>
					</n-grid>
					<NButton block type="primary" :disabled="disabled" :loading="loading" @click="handleVerify">
						{{ $t('common.verify') }}
					</NButton>

					<p class="text-href text-center  text-slate-500 dark:text-slate-500">
						<a @click="isCodeLogin = true" v-if="!isCodeLogin">返回短信登录</a>
					</p>

				</div> -->
			</div>

			<!-- <NTabs type="line" justify-content="space-evenly" :default-value="isMobile ? 'code' : 'wechat'" >
				<NTabPane name="wechat" tab="微信登录">
					<div class="mt-[10px] mb-[10px]">
						<Qrcode :isbind="false" @close="handClose"/>
					</div>
				</NTabPane>
				<NTabPane name="code" tab="手机号登录">
					<div class="space-y-4">
						<n-grid x-gap="12" :cols="12">
							<n-grid-item span="12">
								<NInput v-if="isCodeLogin" v-model:value="username" type="text"  placeholder="请输入手机号" @keypress="handlePress" />
							</n-grid-item>
						</n-grid>

						<n-grid x-gap="12" :cols="12">
							<n-grid-item span="7">
								<NInput  v-if="isCodeLogin"  v-model:value="password" type="text" maxlength="4" placeholder="请输入验证码" @keypress="handlePress" />
							</n-grid-item>
							<n-grid-item span="5" >
							<n-tag  v-if="isCodeLogin" type="success" :disabled="disabled3" v-on:click="handleCode" style="padding:16px;width: 100%; text-align: center;justify-content:center;">
								{{countdownLabel}}
							</n-tag>
							</n-grid-item>

						</n-grid>


						<n-grid x-gap="12" :cols="12" v-if="isCodeLogin">
								<n-grid-item span="7">
								</n-grid-item>
								<n-grid-item span="5" >
								</n-grid-item>
						</n-grid>
						<NButton
							block
							type="primary"
							:disabled="disabled"
							:loading="loading"
							@click="handleVerify"
						>
							{{ $t('common.verify') }}
						</NButton>

						<p class="text-href text-center  text-slate-500 dark:text-slate-500">
								<a @click="isCodeLogin = true" v-if="!isCodeLogin">返回短信登录</a>
						</p>

					</div>
				</NTabPane>
			</NTabs> -->
		</div>
	</NModal>
</template>

<style lang="less" scoped>
:deep(.n-form-item-label),
:deep(.n-form-item-feedback-wrapper) {
	opacity: 0;
	display: none !important;
}

:deep(.n-form-item.n-form-item--top-labelled) {
	grid-template-areas: "blank";
}

:deep(.n-input__border),
:deep(.n-input__state-border) {
	display: none;
}

:deep(.n-form-item) {
	margin: 30px 0;
	border-bottom: 1px solid #d8d8d8;
}
</style>
