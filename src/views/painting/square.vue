<template>
	<div class="p-[28px_0]">
		<div class="text-[24px] text-[#3D3D3D] mb-[20px]">
			一起开始绘图灵感之旅吧！
		</div>
		<div class="flex flex-wrap gap-[20px] mb-[20px] sm:gap-3">
			<div :class="curruntCategory == null ? 'acitve-category' : 'hand-btn'" @click="changeCategory(null)"
					 class="height-[42px] leading-[42px] text-center min-w-[100px] bg-[#FFFFFF] rounded-[20px] cursor-pointer sm:h-auto sm:leading-[24px]">
				全部
			</div>
			<div v-for="item in categoryList" :key="item.categoryId" :class="curruntCategory == item.categoryId ? 'acitve-category' : 'hand-btn'"
					 @click="changeCategory(item.categoryId)" class="height-[42px] leading-[42px] text-center min-w-[100px] bg-[#FFFFFF] rounded-[20px] cursor-pointer sm:h-auto sm:leading-[24px]">
				{{ item.categoryName }}
			</div>
		</div>
		<div class="m-[0_-16px]">
			<Waterfall :list="pageDataList.list" row-key="imageId" :gutter="12" :has-around-gutter="false" :width="282" :breakpoints="{
				1200: {
					rowPerView: 4,
				},
				800: {
					rowPerView: 3,
				},
				500: {
					rowPerView: 2,
				},
			}" img-selector="img-content" background-color="" animation-effect="animate__fadeInUp" :animation-duration="500" :animation-delay="300" :lazyload="false"
								 :cross-origin="true">
				<template #default="{ item, url, index }">
					<div class="w-full relative hover:block parent-hover">
						<!-- <img :src="item.imageUrl +'?imageView2/2/w/280/q/90/interlace/1'" alt="" class="img-content w-full" /> -->
						<LazyImg :url="item.imageUrl + '?imageView2/2/w/320/q/100/interlace/1'" />
						<img src="https://cdn2.weimob.com/saas/saas-fe-sirius-orion-node/production/zh-CN/483/shuiyin.png" alt="" class="absolute bottom-4 right-4 w-[56px] h-[24px]" />
						<div class="absolute bottom-0 left-0 right-0 h-[120px] p-[15px_20px] flex-col justify-between hidden hover-chidle" style="
							background: rgba(129, 129, 129, 0.67);
							backdrop-filter: blur(10px);
						">
							<p class="text-[#FFFFFF] text-[14px] text-ellipsis max-h-[40px] line-clamp-2">
								{{ item.prompt }}
							</p>
							<div class="flex justify-end gap-[11px] disp">
								<div class="hand-btn w-[120px] leading-[34px] text-center rounded-[34px] cursor-pointer" style="
									background: rgba(255, 255, 255, 0.77);
									backdrop-filter: blur(10px);
								" @click="sameStyle(item.prompt)">
									画同款
								</div>
								<div class="hand-btn w-[50px] leading-[34px] text-center rounded-[34px] cursor-pointer" style="
									background: rgba(255, 255, 255, 0.77);
									backdrop-filter: blur(10px);
								" @click="copy(item.prompt)">
									复制
								</div>
							</div>
						</div>
					</div>
				</template>
			</Waterfall>
		</div>



		<LoadMore v-if="pageDataList.hasMore" @onLoadmore="onLoadmore" />
	</div>
</template>

<script setup>
import useClipboard from "vue-clipboard3";
import { useMessage } from "naive-ui";
import { useRouter } from "vue-router";
import { Waterfall, LazyImg } from "vue-waterfall-plugin-next";
import "vue-waterfall-plugin-next/dist/style.css";
import LoadMore from "./components/LoadMore.vue";

import { getStyleCategoryList, queryStyleLibrary } from "./apis";
import { nextTick, onMounted, reactive, ref } from "vue";

const categoryList = ref([]);
const curruntCategory = ref(null);

const pageDataList = reactive({
	pageNum: 1,
	pageSize: 18,
	hasMore: false,
	list: [],
});

const $message = useMessage();
const $router = useRouter();

const { toClipboard } = useClipboard();

const getPageList = () => {
	const { pageNum, pageSize } = pageDataList;

	queryStyleLibrary({
		pageNum,
		pageSize,
		categoryId: curruntCategory.value,
	}).then((res) => {
		const { pageList, totalCount } = res;

		const list = pageList || [];

		pageDataList.list = pageNum == 1 ? list : [...pageDataList.list, ...list];

		nextTick(() => {
			pageDataList.hasMore = pageDataList.list.length < totalCount;
		});
	});
};

const onLoadmore = () => {
	pageDataList.pageNum++;
	getPageList();
};

onMounted(() => {
	getStyleCategoryList().then((res) => {
		categoryList.value = res;
	});
	getPageList();
});

const changeCategory = (categoryId) => {
	curruntCategory.value =
		curruntCategory.value == categoryId ? null : categoryId;

	pageDataList.pageNum = 1;
	pageDataList.hasMore = false;

	getPageList();
};

const copy = (prompt) => {
	toClipboard(prompt);
	$message.success("复制成功");
};

// 画同款
const sameStyle = (prompt) => {
	$router.push({
		path: "/painting/text2img",
		query: {
			prompt,
		},
	});
};
</script>

<style lang="less" scoped>
.parent-hover:hover {
	.hover-chidle {
		display: flex;
	}
}

.acitve-category {
	background: linear-gradient(90deg,
			rgba(41, 159, 255, 0.76) 0%,
			rgba(69, 112, 255, 0.81) 100%),
		#ffffff;
	color: #ffffff;
}

.hand-btn:hover {
	color: #ffffff;
	background: linear-gradient(90deg,
			rgba(41, 159, 255, 0.76) 0%,
			rgba(69, 112, 255, 0.81) 100%) !important;
}
</style>
