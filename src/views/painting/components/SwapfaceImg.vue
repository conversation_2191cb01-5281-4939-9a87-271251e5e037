<template>
	<div>
		<div class="flex flex-wrap gap-[12px]">
			<template v-for="(f, index) in fileList" :key="index">
				<div
					v-if="f"
					class="w-[90px] h-[90px] rounded-[8px] overflow-hidden bg-[#cccc] relative"
				>
					<img :src="f" class="w-full h-full object-cover" alt="" />

					<div
						class="absolute right-[4px] top-[4px] cursor-pointer"
						@click="delImg(index)"
					>
						<NIcon color="#F45B1E" size="20"
							><TrashOutline></TrashOutline
						></NIcon>
					</div>
					<div
						class="w-[54px] h-[24px] absolute top-0 left-0 text-[#FFFFFF] text-[12px] flex items-center justify-center"
						style="
							background-color: rgba(0, 0, 0, 0.5);
							backdrop-filter: blur(10px);
						"
					>
						{{ index == 0 ? "被换脸图" : "模特图" }}
					</div>
				</div>
				<div
					v-else
					class="w-[90px] h-[90px] rounded-[8px] overflow-hidden border-dashed border-[1px] border-[#0E69FF] flex justify-center relative"
				>
					<div class="text-center pt-[18px]">
						<p class="inline-block text-[20px] pb-[0px]"><Add class="w-[20px] h-[20px]"></Add></p>
						<p class="text-[10px]">将{{ index == 0 ? "换脸图" : "模特图" }}拖到此处或点击上传</p>
					</div>
					<input
						type="file"
						accept="image/png,image/jpeg"
						class="absolute left-0 right-0 top-0 bottom-0 opacity-0 cursor-pointer"
						@change="(e) => fileChange(index, e)"
					/>
				</div>
			</template>
		</div>
		<div class="text-[#999999] text-[14px] pt-[20px]">
			只支持单人换脸，不支持动物或壁画类人物换脸。文件支持jpg .png.格式，最大10M
		</div>
	</div>
</template>

<script setup>
import { computed, reactive } from "vue";
import Add from "@/assets/aiwork/svg/add.svg";
import { NIcon } from "naive-ui";
import { TrashOutline } from "@vicons/ionicons5";

const propsValue = defineProps(["sourceBase64", "targetBase64"]);

const $emit = defineEmits(["update:sourceBase64", "update:targetBase64"]);

const fileList = computed(() => [
	propsValue.targetBase64,
	propsValue.sourceBase64,
]);

// 选中图片
const fileChange = (index, e) => {
	const selectedFile = e.target.files[0];

	if (selectedFile.size / 1024 / 1024 > 10) {
		return $message.warning("文件不得超过10M");
	}

	const reader = new FileReader();
	reader.onload = (e) => {
		const base64 = e.target.result;

		$emit(index == 0 ? "update:targetBase64" : "update:sourceBase64", base64);
	};
	reader.readAsDataURL(selectedFile);
};

const delImg = (index) => {
	$emit(index == 0 ? "update:targetBase64" : "update:sourceBase64", "");
};
</script>

<style lang="" scoped></style>
