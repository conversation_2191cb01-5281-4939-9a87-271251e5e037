<template>
	<div id="loadmore-el" class="h-[1px] bg-transparent"></div>
</template>

<script setup>
import { onUnmounted, onMounted, ref } from "vue";
const $emit = defineEmits(["onLoadmore"]);
const loadmore = ref("");

onMounted(() => {
	const loadMoreElement = document.querySelector("#loadmore-el");

	loadmore.value = new IntersectionObserver(
		(entries) => {
			if (entries[0].isIntersecting) {
				$emit("onLoadmore");
			}
		},
		{ threshold: 1 }
	);

	if (loadMoreElement) {
		loadmore.value.observe(loadMoreElement);
	}
});

onUnmounted(() => {
	loadmore.value && loadmore.value.disconnect();
});
</script>

<style lang="" scoped></style>
