<template>
	<n-popover trigger="focus">
		<template #trigger>
			<n-button type="tertiary" size="small">
				<span class="flex gap-[4px] items-center">
					<Setting />
					图片比例
					{{
						{ PORTRAIT: "2:3", SQUARE: "1:1", LANDSCAPE: "3:2" }[
							propsValue.modelValue
						]
					}}
				</span>
			</n-button>
		</template>
		<div>
			<div class="p-[10px_0px] flex gap-[10px] items-center">
				<Setting />
				图片比例
			</div>
			<div class="flex gap-[12px] flex-wrap  pl-[20px]">
				<div
					v-for="item in list"
					:key="item.val"
					:style="`${
						propsValue.modelValue === item.val ? 'border:1px solid #0E69FF' : ''
					}`"
					class="w-[80px] h-[116px] border-[#C1D8FF] border-[1px] rounded-[6px] flex flex-col items-center justify-end pb-[8px] text-center text-[#3D3D3D] text-[12px] cursor-pointer"
					@click="onChange(item.val, item.label)"
				>
					<div class="flex-1 flex items-center justify-center">
						<div
							:style="`width:${item.width}px;height: ${item.height}px`"
							class="bg-[#EAF1FD] relative"
						>
							<span
								class="absolute w-[8px] h-[8px] left-0 top-0 border-[#0E69FF] border-solid border-l-2 border-t-2"
							></span>
							<span
								class="absolute w-[8px] h-[8px] right-0 top-0 border-[#0E69FF] border-solid border-r-2 border-t-2"
							></span>
							<span
								class="absolute w-[8px] h-[8px] left-0 bottom-0 border-[#0E69FF] border-solid border-l-2 border-b-2"
							></span>
							<span
								class="absolute w-[8px] h-[8px] right-0 bottom-0 border-[#0E69FF] border-solid border-r-2 border-b-2"
							></span>
						</div>
					</div>
					<div>{{ item.label }}</div>
				</div>
			</div>
		</div>
	</n-popover>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { NButton, NPopover, NIcon, NInput, useMessage } from "naive-ui";
import Setting from "@/assets/setting.svg";

const propsValue = defineProps(["modelValue"]);
console.log(">>>", propsValue);
const $emit = defineEmits(["update:modelValue"]);

//2:3 PORTRAIT  1:1SQUARE 3:2LANDSCAPE
const list = ref([
	{
		label: "2:3",
		val: "PORTRAIT",
		width: 32,
		height: 43,
	},
	{
		label: "1:1",
		val: "SQUARE",
		width: 44,
		height: 44,
	},
	{
		label: "3:2",
		val: "LANDSCAPE",
		width: 43,
		height: 32,
	},
]);

const onChange = (val, label) => {
	$emit("update:modelValue", val);
};
</script>

<style lang="" scoped></style>
