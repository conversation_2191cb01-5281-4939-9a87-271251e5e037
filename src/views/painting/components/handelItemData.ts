import Reload from "@/assets/reload.svg";

import PanLeft from "@/assets/pan-left.svg";
import PanRight from "@/assets/pan-right.svg";
import PanTop from "@/assets/pan-top.svg";
import PanDown from "@/assets/pan-down.svg";
import Subtle from "@/assets/subtle.svg";
import Scale from "@/assets/scale.svg";

export default [
	{
		type: [
			"IMAGINE",
			"ACTION",
			"VARIATION",
			"BLEND",
			"REROLL",
			"PROMPTANALYZER",
			'PICREADER',
			'PAN'
		],
		btns: [
			{
				name: "放大",
				desc: "选中并放大某张图片，放大后可进行下一步的绘画操作，局部重绘、平移、扩图等",
				key: "UPSCALE",
				items: [
					{
						text: "左上",
						val: "U1",
					},
					{
						text: "右上",
						val: "U2",
					},
					{
						text: "左下",
						val: "U3",
					},
					{
						text: "右下",
						val: "U4",
					},
					{
						text: "",
						icon: Reload,
						val: "REROLL",
					},
				],
			},
			{
				name: "变化",
				desc: "以某张图片为基准重新生成 如 V1 则变换第一张图片，以此类推",
				key: "VARIATION",
				items: [
					{
						text: "左上",
						val: "V1",
					},
					{
						text: "右上",
						val: "V2",
					},
					{
						text: "左下",
						val: "V3",
					},
					{
						text: "右下",
						val: "V4",
					},
				],
			},
		],
	},
	{
		type: ["UPSCALE"],
		btns: [
			{
				name: "调整",
				desc: "以当前图片为基础调整图片",
				key: "VARIATION",
				items: [
					{
						text: "弱变化",
						val: "Vary (Subtle)",
						icon: Subtle,
					},
					{
						text: "强变化",
						val: "Vary (Strong)",
						icon: Subtle,
					},
				],
			},
			{
				name: "扩图",
				desc: "对当前图片按照倍数进行扩大，周围自动AI填充，可无限重复无限扩大",
				key: "ACTION",
				items: [
					{
						text: "扩展2x",
						val: "Zoom Out 2x",
						icon: Scale,
						iconColor: "#5C97F9",
					},
					{
						text: "扩展1.5x",
						val: "Zoom Out 1.5x",
						icon: Scale,
					},
				],
			},
			{
				name: "单边扩图",
				desc: "对当前图片进行各个方向的拓展",
				key: "PAN",
				items: [
					{
						text: "",
						val: "pan_left",
						icon: PanLeft,
					},
					{
						text: "",
						val: "pan_right",
						icon: PanRight,
					},
					{
						text: "",
						val: "pan_up",
						icon: PanTop,
					},
					{
						text: "",
						val: "pan_down",
						icon: PanDown,
					},
				],
			},
		],
	},
	{
		type: ["DESCRIBE"],
		btns: [
			{
				name: "描述绘制",
				desc: "使用解析后的提示词生成图片",
				key: "INDEX",
				items: [
					{
						text: "1",
						val: "1",
						icon: "",
					},
					{
						text: "2",
						val: "2",
						icon: "",
					},
					{
						text: "3",
						val: "3",
						icon: "",
					},
					{
						text: "4",
						val: "4",
						icon: "",
					},
					// {
					// 	text: "",
					// 	val: "Reload",
					// 	icon: Reload,
					// },
				],
			},
		],
	},
	{
		type: ["SHORTEN"],
		btns: [
			{
				name: "描述绘制",
				desc: "使用解析后的提示词生成图片",
				key: "INDEX",
			},
		],
	},
];
