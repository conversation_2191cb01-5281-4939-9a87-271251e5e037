<template>
	<div>
		<n-button type="tertiary" size="small" @click.stop="control.show = !control.show">
			<span class="flex gap-[4px] items-center">
				<Setting />参数设置
			</span>
		</n-button>
		<div v-if="control.show" class="absolute z-9 left-0 right-0 top-[-1px] -translate-y-full bg-white" style="box-shadow: 0px -1px 8px 0px #a9bad4"
				 @click.stop="">
			<div class="flex items-center gap-[12px] p-[15px_20px]">
				<NIcon size="18">
					<Menu />
				</NIcon>
				<span class="text-[#696969]">参数设置</span>
			</div>
			<div class="max-h-[340px] overflow-y-auto pb-[40px]">
				<!-- 参考图 -->
				<div class="p-[0_40px]">
					<div class="mb-[18px]">
						<span>上传参考图</span>
						<span class="text-[#868686] pl-[12px]">只支持.jpg,.png格式,，每张最大10M，最多上传5张</span>
					</div>
					<div class="flex flex-wrap gap-[12px]">
						<div v-for="(f, index) in fileList" :key="f.id" class="w-[160px] h-[160px] rounded-[8px] overflow-hidden bg-[#cccc] relative">
							<img :src="f.url" class="w-full h-full object-cover" alt="" />
							<div class="absolute bottom-0 left-0 right-0 h-[68px] p-[10px]" style="
										background-color: rgba(0, 0, 0, 0.5);
										backdrop-filter: blur(7.26px);
									">
								<div class="flex justify-between items-center text-white mb-[5px] text-[12px]">
									<span class="w-[36px] h-[22px] bg-[#0E69FF] flex items-center justify-center rounded-[2px] cursor-pointer" @click="showSilder('sref', f.id)">
										{{ f.srefCheck ? f.sref : "权重" }}
									</span>
									<span>风格一致性</span>
									<n-switch :value="f.srefCheck" size="small" @change="referenceChange('sref', f.id)" />
								</div>

								<div class="flex justify-between items-center text-white text-[12px]">
									<span class="w-[36px] h-[22px] bg-[#0E69FF] flex items-center justify-center rounded-[2px] cursor-pointer" @click="showSilder('cref', f.id)">
										{{ f.crefCheck ? f.cref : "权重" }}
									</span>
									<span>角色一致性</span>
									<n-switch :value="f.crefCheck" size="small" @update-value="referenceChange('cref', f.id)" />
								</div>
							</div>
							<div v-if="f.showSet" class="absolute top-[40px] left-[7px] right-[7px] rounded-[6px] text-center p-[10px_9px]" style="
									background: rgba(255, 255, 255, 0.79);
									backdrop-filter: blur(10px);
								">
								<div class="mb-[10px]">设置{{ f.setKey }}权重值</div>
								<div class="pb-[2px]">
									<n-slider v-model:value="f[f.setKey]" :step="10" @dragend="onDragend()" />
								</div>
							</div>
							<div class="absolute right-[4px] top-[4px] cursor-pointer" @click="delImg(index)">
								<NIcon color="#F45B1E" size="20">
									<TrashOutline></TrashOutline>
								</NIcon>
							</div>
						</div>
						<div v-if="fileList.length < 5"
								 class="w-[160px] h-[160px] rounded-[8px] overflow-hidden border-dashed border-[1px] border-[#0E69FF] flex justify-center relative">
							<div class="text-center pt-[46px]">
								<p class="inline-block text-[20px] pb-[20px]">
									<Add class="w-[20px] h-[20px]"></Add>
								</p>
								<p class="w-[98px]">将图片拖到此处或点击上传</p>
							</div>
							<input type="file" accept="image/png,image/jpeg" class="absolute left-0 right-0 top-0 bottom-0 opacity-0 cursor-pointer" multiple
										 @change="fileChange" />
						</div>
					</div>
					<div class="w-[370px] h-[32px] mt-[20px] bg-[#D9E7FF] rounded-[4px] flex justify-between items-center p-[0px_11px] text-[#3D3D3D] sm:w-full">
						<div class="flex items-center gap-[5px]">
							图片相似度
							<n-popover trigger="hover" placement="right">
								<template #trigger>
									<NIcon color="#0E69FF">
										<HelpCircleSharp />
									</NIcon>
								</template>
								<div>值越高，上传的图像对最终效果的影响就越大</div>
							</n-popover>
						</div>
						<div class="">
							<n-popover trigger="hover" placement="bottom">
								<template #trigger>
									<span class="flex items-center gap-[10px]">
										{{ (iwList.find((i) => i.val == settings.iw) || {}).text }}
										<NIcon color="#0E69FF">
											<ChevronDownOutline />
										</NIcon>
									</span>
								</template>
								<div>
									<p class="p-[4px_10px] hover:bg-[#D9E7FF] cursor-pointer" v-for="item in iwList" :key="item.val" @click.stop="onChange('iw', item.val)">
										{{ item.text }}
									</p>
								</div>
							</n-popover>
						</div>
					</div>
				</div>
				<!-- 比例 -->
				<div class="p-[0_40px]">
					<div class="mt-[18px] mb-[12px]">图片比例</div>
					<div class="flex gap-[12px] flex-wrap">
						<div v-for="item in arList" :key="item.val" :style="`${item.val === settings.ar ? 'border:1px solid #0E69FF' : ''
							}`" class="w-[80px] h-[116px] border-[#C1D8FF] border-[1px] rounded-[6px] flex flex-col items-center justify-end pb-[8px] text-center text-[#3D3D3D] text-[12px] cursor-pointer"
								 @click="onChange('ar', item.val)">
							<div class="flex-1 flex items-center justify-center">
								<div :style="`width:${item.width}px;height: ${item.height}px`" class="bg-[#EAF1FD] relative">
									<span class="absolute w-[8px] h-[8px] left-0 top-0 border-[#0E69FF] border-solid border-l-2 border-t-2"></span>
									<span class="absolute w-[8px] h-[8px] right-0 top-0 border-[#0E69FF] border-solid border-r-2 border-t-2"></span>
									<span class="absolute w-[8px] h-[8px] left-0 bottom-0 border-[#0E69FF] border-solid border-l-2 border-b-2"></span>
									<span class="absolute w-[8px] h-[8px] right-0 bottom-0 border-[#0E69FF] border-solid border-r-2 border-b-2"></span>
								</div>
							</div>
							<div>{{ item.val }}</div>
							<div>{{ item.label }}</div>
						</div>
					</div>
				</div>

				<!-- 排除元素 -->
				<div class="p-[0_40px]">
					<div class="mt-[18px] mb-[12px]">排除元素</div>
					<div class="flex gap-[12px] flex-wrap w-[448px] sm:w-full">
						<n-input placeholder="请输入不想在图片中出现的元素关键词（如：动物，植物等）" type="textarea" size="small" :autosize="{ minRows: 3, maxRows: 3 }" v-model="settings.no"
										 @change="(e) => onChange('no', e)" />
					</div>
				</div>

				<!-- 高级参数 -->
				<div class="p-[0_40px]">
					<div class="mt-[18px] mb-[12px]">高级参数</div>

					<div v-for="item in advancedList" :key="item.val" class="flex items-center gap-[10px] mb-[12px]">
						<div class="flex items-center justify-between">
							<div class="w-[60px]">{{ item.label }}</div>
							<n-popover trigger="hover" placement="right">
								<template #trigger>
									<NIcon color="#0E69FF">
										<HelpCircleSharp />
									</NIcon>
								</template>
								<div>{{ item.desc }}</div>
							</n-popover>
						</div>
						<div class="w-[184px]">
							<n-slider v-model:value="settings[item.val]" :step="10" @dragend="onDragend()" />
						</div>
						<div class="border-[1px] h-[21px] w-[50px] text-[11px] text-[#ACACAC] border-[#ACACAC] border-solid flex items-center justify-center">
							{{ settings[item.val] }}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { reactive, onMounted, onUnmounted, ref } from "vue";
import {
	NButton,
	NPopover,
	NIcon,
	NInput,
	NSwitch,
	NProgress,
	NSlider,
	useMessage,
} from "naive-ui";
import Menu from "@/assets/menu.svg";
import Setting from "@/assets/setting.svg";
import Add from "@/assets/aiwork/svg/add.svg";
import {
	HelpCircleSharp,
	ChevronDownOutline,
	TrashOutline,
} from "@vicons/ionicons5";
const $emit = defineEmits(["settingChange"]);
const control = reactive({ show: false });
const $message = useMessage();
let fileList = reactive<any[]>([]);

const settings = reactive({
	iw: 0,
	ar: "1:1",
	no: "",
	q: 0,
	s: 0,
	c: 0,
	weird: 0,
});
// 参考图
const iwList = ref([
	{
		text: "不选择",
		val: "0",
	},
	{
		text: "相似度 30%",
		val: "0.25",
	},
	{
		text: "相似度 40%",
		val: "0.5",
	},
	{
		text: "相似度 50%",
		val: "1",
	},
	{
		text: "相似度 60%",
		val: "1.25",
	},
	{
		text: "相似度 70%",
		val: "1.5",
	},
	{
		text: "相似度 80%",
		val: "1.75",
	},
	{
		text: "相似度 90%",
		val: "2",
	},
]);
// 图片尺寸
const arList = ref([
	{
		label: "头像",
		val: "1:1",
		width: 44,
		height: 44,
	},
	{
		label: "手机壁纸",
		val: "1:2",
		width: 25,
		height: 50,
	},
	{
		label: "电脑壁纸",
		val: "16:9",
		width: 50,
		height: 32,
	},
	{
		label: "宣传海报",
		val: "9:16",
		width: 32,
		height: 50,
	},
	{
		label: "媒体配图",
		val: "3:4",
		width: 32,
		height: 43,
	},
	{
		label: "文章配图",
		val: "4:3",
		width: 43,
		height: 32,
	},
	{
		label: "小红书图",
		val: "2:3",
		width: 32,
		height: 50,
	},
	{
		label: "横版名片",
		val: "3:2",
		width: 50,
		height: 32,
	},
]);
const advancedList = ref([
	{
		label: "图片质量",
		val: "q",
		desc: "通过更长的时间来处理并产生更高质量、更多细节的图像，值越大出图质量更高",
	},

	{
		label: "风格化",
		val: "s",
		desc: "低风格化值生成的图像与提示非常匹配，但艺术性较差；高风格化值创建的图像非常艺术，但与提示的联系较少",
	},
	{
		label: "多样性",
		val: "c",
		desc: "这个值越低生成的四张图风格越相似，反之差异越大",
	},
	{
		label: "奇妙性",
		val: "weird",
		desc: "生成的图像引入奇特和离奇的特质，从而产生独特而意想不到的结果",
	},
]);
const fileChange = (e) => {

	let selectedFiles = Array.from(e.target.files);

	selectedFiles = selectedFiles.filter((file) => (file.size / 1024 / 1024) <= 10);

	if (selectedFiles.length < e.target.files.length) {
		$message.warning("文件不得超过10M");
	}

	if (!selectedFiles.length) return


	selectedFiles.forEach((file) => {

		const reader = new FileReader();

		reader.onload = (e) => {
			const base64 = e.target.result;


			fileList.push({
				id: Date.now().toString() + "-" + Math.random().toString().slice(-5),
				sref: 0, //风格一致性
				srefCheck: false,
				cref: 0, //角色一致性
				crefCheck: false,
				url: base64,
				showSet: false, // 权重设置框
				setKey: "sref",
			});
			sendSettins();

		};

		reader.readAsDataURL(file);

	})
};
// 删除图片
const delImg = (index) => {
	fileList.splice(index, 1);
	sendSettins();
};
// 参考图片变化
const referenceChange = (type, id) => {
	if (type == "sref") {
		fileList.some((item) => {
			if (item.id === id) {
				item.srefCheck = !item.srefCheck;
				return true;
			}
		});
	}

	if (type == "cref") {
		fileList.forEach((item) => {
			if (type == "cref" && id != item.id) {
				item.crefCheck = false;
			} else {
				item.crefCheck = !item.crefCheck;
			}
			item.showSet = false;
		});
	}

	sendSettins();
};
const showSilder = (type, id) => {
	fileList.forEach((item) => {
		if (item.id !== id || (item.setKey == type && item.showSet == true)) {
			return (item.showSet = false);
		}

		if (!item[type + "Check"]) return (item.showSet = false);
		item.showSet = true;
		item.setKey = type;
	});
	sendSettins();
};

// 参数变化
const onChange = (type, val) => {
	settings[type] = val;
	sendSettins();
};
// 高级参数变化
const onDragend = () => {
	sendSettins();
};
// 吐出设置
const sendSettins = () => {
	let settingStr = Object.entries(settings)
		.filter(([key, val]) => val)
		.map(([key, val]) => `--${key} ${val}`);

	const base64ImageList: string[] = [];

	if (fileList && fileList.length) {
		let sref = "";
		fileList.forEach((f, index) => {
			if (f.srefCheck) sref += sref ? `:${f.sref}` : f.sref;
			if (f.crefCheck) settingStr.push(`--cref ${index + 1}:${f.cref}`);
			base64ImageList.push(f.url);
		});
		settingStr.push(`--sref ${sref}`);
	}

	$emit("settingChange", {
		customParams: settingStr.join(" "),
		base64ImageList,
	});
};

const hidePopup = () => {
	control.show = false;
};

const initSettings = () => {
	Object.assign(settings, {
		iw: "0",
		ar: "1:1",
		no: "",
		q: 0,
		s: 0,
		c: 0,
		weird: 0,
	});
	fileList.splice(0);
};

defineExpose({
	initSettings,
});

onMounted(() => {
	document.body.addEventListener("click", hidePopup);
});

onUnmounted(() => {
	document.body.removeEventListener("click", hidePopup);
});
</script>

<style lang="" scoped></style>
