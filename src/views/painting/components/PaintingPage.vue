<template>
	<div class="w-full" :class="['DESCRIBE', 'BLEND', 'SWAP_FACE'].includes(promptType)
		? 'has-img'
		: 'no-img'
		">
		<LoadMore v-if="pageParams.hasMore" @onLoadmore="onLoadmore" />

		<!-- 任务列 item -->
		<PaintingItem :id="`painting-item-${task.taskId}`" v-for="task in taskList" :key="task.taskId" :item-data="task" @handelItemAction="handelItemAction"
									@showModal="showModal" />

		<!-- 安全提示 -->
		<div v-if="riskTips.show" class="text-[#F45B1E] p-[24px] w-full bg-[#FDFEFF] mb-[16px] flex gap-[24px]">
			<div
					 class="text-[28px] dark:text-white flex items-center justify-center min-w-[52px] h-[52px] rounded-full bg-white shadow-[0px_5.82px_14.56px_0px_#CED5E5]">
				<LogoSvg class="w-[26px] h-[26px] fill-current" style="transform: translate(-1px, 2px)" />
			</div>
			<div>
				{{ riskTips.text }}
			</div>
		</div>

		<!-- 输入框 -->
		<PromptInput :prompt-type="promptType" @submitPrompt="handleSubmit" />
		<!-- 弹框编辑 -->
		<n-modal v-model:show="modalControll.show">
			<div class="w-[709px] bg-white rounded-[10px] p-[20px_30px_24px]">
				<div class="mb-[16px] flex justify-between">
					<div class="text-[18px] flex items-center gap-[6px]">
						编辑咒语
						<div class="w-[16px] h-[16px] rounded-full bg-[#0E69FF] text-[12px] leading-[14px] text-center text-white">
							{{ modalControll.index }}
						</div>
					</div>
					<div @click="closeModal">
						<NIcon color="#444444">
							<Close></Close>
						</NIcon>
					</div>
				</div>
				<div>
					<NInput type="textarea" placeholder="" color="1px solid #1874ff" :autosize="{
						minRows: 3,
						maxRows: 3,
					}" v-model:value="modalControll.prompt"></NInput>
				</div>
				<div class="mt-[16px] flex justify-end gap-[12px]">
					<NButton strong secondary type="info" style="width: 110px" @click="closeModal" :loading="modalControll.loading">取消</NButton>
					<NButton type="info" style="width: 110px" :loading="modalControll.loading" @click="executeModal">确认</NButton>
				</div>
			</div>
		</n-modal>
		<GlobalFloat :showTop="false" :showActive="false" showBottom :position="['DESCRIBE', 'BLEND', 'SWAP_FACE'].includes(promptType) ? '320px' : '220px'" />
	</div>
</template>

<script lang="ts" setup>
import { inject, nextTick, onMounted, reactive } from "vue";
import { useRoute } from "vue-router";
import PaintingItem from "./PaintingItem.vue";
import LoadMore from "./LoadMore.vue";
import PromptInput from "./PromptInput.vue";
import { NIcon, NButton, NModal, NInput } from "naive-ui";
import { Close } from "@vicons/ionicons5";
import { usePaintingStore } from "@/store";
import LogoSvg from "@/assets/aiwork/svg/logo.svg";
import GlobalFloat from "@/components/common/GlobalFloat/index.vue";

import { useTaskList } from "../hooks/taskList";
import { executeAction, submitApi, submitModal, cancelTask } from "../apis";

const $route = useRoute();
const riskTips = reactive({ show: false, text: "" });

const { promptType } = defineProps({
	promptType: {
		type: String, // 任务类型 IMAGINE、DESCRIBE、BLEND、SWAP_FACE、SHORTEN
		default: "IMAGINE",
	},
});

const PaintingStore = usePaintingStore();
// const { createType } = storeToRefs(PaintingStore);

let modalControll = reactive<any>({ show: false, loading: false, prompt: "" });

const {
	taskList,
	pageParams,
	addTaskToList,
	getPageList,
	handlePagePosition,
	deleteTaskItem,
} = useTaskList("SHORTEN");

// 提交任务
const handleSubmit = (e, type = ""): void => {
	riskTips.show = false;

	submitApi[type || promptType]({ ...e })
		.then((res: any) => {
			return res.taskId;
		})
		.then((taskId) => addTaskToList(taskId).then(() => taskId))
		.then((taskId) => {
			nextTick(() => handlePagePosition());
		})
		.catch((err) => {
			if (err.errcode == "10004") {
				riskTips.text = err.errmsg;
				riskTips.show = true;
				nextTick(() => handlePagePosition());
			}
		});
};

// 子项目操作
const handelItemAction = (payload) => {
	const { type, ...params } = payload;

	if (type === "action") {
		return executeAction(params)
			.then((res) => addTaskToList(res.taskId).then(() => res.taskId))
			.then((taskId) => {
				nextTick(() => handlePagePosition());
			});
	}
	if (type === "delete") {
		return deleteTaskItem(params.taskId).then(() => { });
	}

	if (type == "cancel") {
		cancelTask({ taskId: params.taskId }).then((res) => {
			console.log(">>>cancelTask res", res);
		});
	}
};

// 弹框显示
const showModal = (payload) => {
	console.log(">>>payload", payload);
	Object.assign(modalControll, payload, { show: true });

	// executeAction(payload)
	// 	.then((res) => {
	// 		modalControll.loading = false;
	// 		modalControll.taskId = res.taskId;
	// 	})
	// 	.catch(() => {
	// 		modalControll.loading = false;
	// 	});
};
// 关闭弹框
const closeModal = () => {
	modalControll.show = false;
};
// 执行弹框
const executeModal = () => {
	closeModal();

	// if (modalControll.action == "SHORTEN") {
	return handleSubmit(
		{
			botType: modalControll.botType,
			speedMode: modalControll.speedMode,
			prompt: modalControll.prompt,
			customId: modalControll.customId,
			...(modalControll.action == "DESCRIBE"
				? { baseImageUrlList: [modalControll.imageUrl] }
				: {}),
		},
		"IMAGINE"
	);
	// }

	// submitModal({
	// 	type: "action",
	// 	taskId: modalControll.taskId,
	// 	botType: modalControll.botType,
	// 	speedMode: modalControll.speedMode,
	// 	prompt: modalControll.prompt,
	// 	customId: modalControll.customId,
	// })
	// 	.then((res) => addTaskToList(res.taskId).then(() => res.taskId))
	// 	.then((taskId) => {
	// 		nextTick(() => handlePagePosition());
	// 	});
};

const onLoadmore = () => {
	getPageList();
};


</script>

<style lang="less" scoped>
.has-img {
	padding-bottom: 140px;
}

.no-img {
	padding-bottom: 40px;
}
</style>
