<template>
	<div class="p-[24px] w-full bg-[#FDFEFF] mb-[16px] sm:rounded-md sm:p-3">
		<div class="flex gap-[24px] items-start sm:gap-3 sm:flex-col">
			<div
				class="text-[28px] dark:text-white flex items-center justify-center w-[52px] h-[52px] shrink-0 rounded-full bg-white shadow-[0px_5.82px_14.56px_0px_#CED5E5] sm:w-5 sm:h-5 sm:hidden"
			>
				<LogoSvg
					class="w-[26px] h-[26px] fill-current sm:w-4 sm:h-4"
					style="transform: translate(-1px, 2px)"
				/>
			</div>
			<div class="flex-1 text-[#666666] sm:w-full sm:overflow-hidden">
				<div class="flex items-center mb-[14px]">
					<div
						class="hidden sm:flex dark:text-white items-center justify-center w-5 h-5 shrink-0 rounded-full bg-white shadow-[0px_5.82px_14.56px_0px_#CED5E5] mr-2"
					>
						<LogoSvg
							class="w-3 h-3 fill-current"
							style="transform: translate(0, 1px)"
						/>
					</div>
					<div
						v-if="itemData.speedMode || actionTypeDesc[itemData.action]"
						class="mr-[9px] bg-[#DDEEFF] p-[0_15px] rounded-[4px] leading-[22px]"
					>
						{{
							actionTypeDesc[itemData.action] ||
							(itemData.speedMode == "FAST" ? "快速" : "普通")
						}}
					</div>

					<div class="mr-[11px]">
						{{
							itemData.submitTime
								? new Date(itemData.submitTime)
										.toLocaleString()
										.replaceAll("/", "-")
								: ""
						}}
					</div>
				</div>

				<div
					v-if="
						!['DESCRIBE'].includes(itemData.action) &&
						!actionTypeDesc[itemData.action]
					"
					class="text-[#3D3D3D] text-[14px] leading-[18px] mb-[14px]"
				>
					{{ itemData.prompt || itemData.promptEn }}
				</div>

				<!-- 图生文 -->
				<div v-if="['DESCRIBE'].includes(itemData.action)" class="pt-[14px]">
					<div
						v-for="(pm, index) in itemData.promptList"
						class="p-[8px_10px] bg-[#F0F6FF] mb-[12px] text-[#3D3D3D] text-[14px] cursor-pointer"
						@click="copy(pm)"
					>
						{{ index + 1 }}、{{ pm }}
					</div>
				</div>
				<!-- 咒语解析 -->
				<div v-if="['SHORTEN'].includes(itemData.action)">
					<div v-if="itemData.status == 'FAILURE'">{{ itemData.status }}</div>
					<template v-else>
						<div v-for="(pm, i) in itemData.promptList">
							<div class="mb-[10px] font-bold">{{ pm.title }}</div>
							<div
								class="p-[8px_10px] bg-[#F0F6FF] mb-[12px] text-[#3D3D3D] text-[14px] cursor-pointer"
								v-for="(item, index) in pm.list"
								@click="() => i > 0 && copy(item)"
							>
								{{ index + 1 }}、{{ item }}
							</div>
						</div>
					</template>
				</div>

				<div
					v-if="!['SHORTEN'].includes(itemData.action)"
					class="w-full relative"
				>
					<div v-if="itemData.imageUrl" class="flex items-end">
						<NImage
							:src="itemData.thumbnailUrl || itemData.imageUrl"
							:preview-src="itemData.imageUrl"
							alt=""
							class="w-[352px] sm:w-full"
							show-toolbar-tooltip
						/>
						<div
							v-if="itemData.replicateSource && itemData.replicateTarget"
							class="flex items-center gap-[18px] ml-[62px]"
						>
							<img
								:src="itemData.replicateTarget"
								alt=""
								class="w-[120px] h-[120px] object-contain"
							/>
							<NIcon size="40" color="#BBD5FD">
								<ArrowBackSharp />
							</NIcon>
							<img
								:src="itemData.replicateSource"
								alt=""
								class="w-[120px] h-[120px] object-contain"
							/>
						</div>
					</div>

					<div
						v-else
						class="w-[352px] h-[352px] flex flex-wrap rounded-[5px] overflow-hidden relative"
					>
						<div
							v-for="item in 4"
							style="
								background: radial-gradient(
									165% 145% at 85% 16%,
									#e1d2ff 0%,
									#d2d8ff 48%,
									#d2efff 100%
								);
							"
							class="w-[50%]"
						></div>
						<div
							v-if="['FAILURE', 'CANCEL'].includes(itemData.status)"
							class="text-[40px] absolute top-[50%] left-[50%] -translate-y-1/2 -translate-x-1/2"
						>
							{{ itemData.status }}
						</div>
					</div>
					<div
						v-if="
							!['FAILURE', 'CANCEL'].includes(itemData.status) &&
							itemData.progress < 100
						"
						class="absolute w-[352px] top-0 left-0 bottom-0 flex flex-col justify-center items-center"
					>
						<n-progress
							type="line"
							:percentage="itemData.progress"
							:show-indicator="false"
							processing
							style="width: 260px; margin-top: 28px"
						/>
						<div class="text-center text-[#0E69FF] pt-[14px]">
							<span> {{ itemData.progress }}%</span>
						</div>
					</div>
				</div>
				<!-- 底部按钮操作区域 -->
				<div
					v-loading="loading"
					v-if="itemData.status == 'SUCCESS' && !isCollectPage"
					class="mt-[10px]"
				>
					<template v-for="item in randerHandlezItems" :key="item.name">
						<div class="flex items-center mt-[10px] sm:items-start">
							<div
								class="flex items-center mr-[8px] sm:mt-1 sm:grow-0 sm:shrink-0"
							>
								<span class="shrink-0">{{ item.name }}： </span>
								<n-popover trigger="hover" placement="right">
									<template #trigger>
										<n-icon size="14" color="#5C97F9">
											<AlertCircleSharp />
										</n-icon>
									</template>
									<span>{{ item.desc }}</span>
								</n-popover>
							</div>
							<div class="text-[12px] flex gap-[9px] sm:flex-wrap">
								<n-button
									v-for="(handle, index) in item.items"
									:key="handle.val"
									size="small"
									color="#3D3D3D"
									secondary
									class="min-w-[54px]"
									@click="handlClick(item.key, handle.val, index)"
								>
									<span class="flex gap-[4px] items-center">
										<component v-if="handle.icon" :is="handle.icon"></component>
										<span
											v-if="handle.text"
											class="text-[#3D3D3D] text-[12px]"
											>{{ handle.text }}</span
										>
									</span>
								</n-button>
							</div>
						</div>
					</template>
				</div>
			</div>
			<!-- 右上按钮 -->
			<div
				class="min-w-[120px] flex gap-[13px] justify-end text-[#666666] text-[12px] cursor-pointer"
			>
				<div
					v-if="
						itemData.status == 'IN_PROGRESS' || itemData.status == 'SUBMITTED'
					"
					class="flex items-center gap-[4px]"
					@click="cancel"
				>
					<NIcon>
						<ArrowUndoOutline /> </NIcon
					>取消
				</div>
				<template
					v-if="
						itemData.status === 'SUCCESS' ||
						itemData.status === 'FAILURE' ||
						itemData.status === 'MODAL'
					"
				>
					<div class="flex items-center gap-[4px]" @click="collect">
						<NIcon>
							<Star v-if="itemData.isFavorite" />
							<StarOutline v-else /> </NIcon
						>收藏
					</div>
					<div
						v-if="itemData.imageUrl"
						class="flex items-center gap-[4px]"
						@click="downloadIamgeFn"
					>
						<NIcon>
							<DownloadOutline /> </NIcon
						>下载
					</div>
					<div
						v-if="!isCollectPage"
						class="flex items-center gap-[4px]"
						@click="delTask"
					>
						<NIcon>
							<TrashOutline /> </NIcon
						>删除
					</div>
				</template>
			</div>
		</div>
		<div class="text-right text-[#A6A6A6] text-[12px] sm:text-left sm:mt-2">
			<template v-if="itemData.usePoint != null">
				<span>消耗积分: {{ itemData.usePoint }}</span>
				<n-divider style="height: 10px" vertical />
			</template>
			<span>ID: {{ itemData.taskId }}</span>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref, toRefs } from "vue";
import LogoSvg from "@/assets/aiwork/svg/logo.svg";
import {
	NIcon,
	NButton,
	NPopover,
	NProgress,
	useDialog,
	NImage,
	NImageGroup,
	useMessage,
	NDivider,
} from "naive-ui";
import {
	AlertCircleSharp,
	TrashOutline,
	DownloadOutline,
	StarOutline,
	Star,
	ArrowBackSharp,
	AddSharp,
	ArrowUndoOutline,
} from "@vicons/ionicons5";
import handelItemData from "./handelItemData";
import { downloadIamge } from "@/utils/functions";
import { useLoading } from "../hooks/loading";
import { addUserFavorite, deleteUserFavorite } from "../apis";
import useClipboard from "vue-clipboard3";
const { toClipboard } = useClipboard();

const $dialog = useDialog();
const $message = useMessage();

const { itemData, isCollectPage } = defineProps(["itemData", "isCollectPage"]);

const $emit = defineEmits([
	"handelItemAction",
	"showModal",
	"deleteUserFavorite",
]);

const actionTypeDesc = ref({
	PAN: "平移",
	UPSCALE: "放大",
	REROLL: "重新生成",
	ZOOM: "变焦",
	VARIATION: "变化",
	ACTION: "扩图",
});

const { loading, setLoadingWithTimeout } = useLoading();

const randerHandlezItems = computed(() => {
	if (!itemData.buttons) return [];
	const btns =
		(handelItemData.find((item) => item.type.includes(itemData.action)) || {})
			.btns || [];

	if (itemData.action == "SHORTEN") {
		return [
			{
				...btns[0],
				items: Array.from({ length: itemData.buttons.length - 1 })
					.fill(0)
					.map((_, index) => ({ text: index + 1, val: index + 1, icon: "" })),
			},
		];
	}

	return btns;
});

onMounted(() => {
	// 创建MutationObserver监听文档变化
	let isLoad = false;
	const observer = new MutationObserver((mutations) => {
		// 检查每次DOM变化
		mutations.forEach(() => {
			// 查找目标元素
			const targetElement = document.querySelector('.n-image-preview-wrapper:has(.ai-watermark-preview)') as HTMLDivElement;
			if(isLoad && !targetElement) return isLoad = false;
			if (!isLoad && targetElement) {
				// 元素已加载，执行您的方法
				isLoad = true
				// 找到目标元素的图片
				const img = targetElement.querySelector('.ai-watermark-preview') as HTMLImageElement;
				setTimeout(() => {
					//设置targetElement的css变量
					targetElement.style.setProperty('--preview-right', `${img.offsetLeft}px`);
					targetElement.style.setProperty('--preview-bottom', `${img.offsetTop}px`);
					targetElement.style.setProperty('--preview-opacity', "1");
				}, 500);


				// 在这里添加您需要执行的方法
				// 例如：添加事件监听器、修改样式等

				// 如果只需要触发一次，可以断开观察
				// observer.disconnect();
			}
		});
	});

	// 开始观察整个文档的变化
	observer.observe(document.body, {
		childList: true, // 监听子节点的添加或删除
		subtree: true, // 监听后代节点
		attributes: false // 监听属性变化
	});

	// 为了防止内存泄漏，在组件卸载时断开观察
	onUnmounted(() => {
		observer.disconnect();
	});
})

const handlClick = (key, val, index) => {
	const { buttons } = itemData;

	if (key === "INDEX") {
		showModal(index);
		return;
	}
	setLoadingWithTimeout(5000);

	const item = buttons.find((btn) => {
		if (val === "REROLL") return btn.action === "REROLL";
		if (key === "PAN") return btn.customId.indexOf(val) >= 0;

		return btn.action === key && btn.label === val;
	});

	$emit("handelItemAction", {
		type: "action",
		taskId: itemData.taskId,
		customId: item.customId,
		botType: itemData.botType,
		speedMode: itemData.speedMode,
	});
};

const delTask = () => {
	$dialog.warning({
		title: "警告",
		content: "确定要删除此任务吗？",
		positiveText: "确定",
		negativeText: "取消",
		onPositiveClick: () => {
			$emit("handelItemAction", {
				type: "delete",
				taskId: itemData.taskId,
			});
		},
		onNegativeClick: () => {},
	});
};

// 下载
const downloadIamgeFn = () => {
	if (!itemData.imageUrl) return;
	downloadIamge(itemData.imageUrl);
};
// 收藏
const collect = () => {
	if (!itemData.isFavorite) {
		addUserFavorite({ taskId: itemData.taskId }).then(() => {
			itemData.isFavorite = true;
		});
	} else {
		deleteUserFavorite({ taskId: itemData.taskId }).then(() => {
			itemData.isFavorite = false;

			if (isCollectPage)
				$emit("deleteUserFavorite", { taskId: itemData.taskId });
		});
	}
};

// 取消
const cancel = () => {
	$dialog.warning({
		title: "警告",
		content: "确定要取消此任务吗？",
		positiveText: "确定",
		negativeText: "取消",
		onPositiveClick: () => {
			$emit("handelItemAction", {
				type: "cancel",
				taskId: itemData.taskId,
			});
		},
		onNegativeClick: () => {},
	});
};

const showModal = (index) => {
	const btns = itemData.buttons[index];

	let prompt = itemData.promptList[index];

	if (itemData.action == "SHORTEN") {
		prompt = itemData.promptList[1].list[index];
	}

	$emit("showModal", {
		taskId: itemData.taskId,
		customId: btns.customId,
		botType: itemData.botType,
		speedMode: itemData.speedMode,
		prompt: prompt || "",
		index: index + 1,
		action: itemData.action,
		imageUrl: itemData.imageUrl,
	});
};

const copy = (prompt) => {
	toClipboard(prompt);
	$message.success("复制成功");
};
</script>

<style lang="less">
.n-image-preview-toolbar {
	gap: 10px;
}
</style>
