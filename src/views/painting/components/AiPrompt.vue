<template>
	<n-popover trigger="manual" :show="aiPromptText.show" placement="top">
		<template #trigger>
			<n-button
				type="tertiary"
				size="small"
				@click.stop="aiPromptText.show = true"
			>
				<span class="flex gap-[4px] items-center"><Bot />AI帮我写</span>
			</n-button>
		</template>
		<div class="sm:w-[75vw]" @click.stop="">
			<div class="flex items-center gap-[12px] pt-[10px]">
				<NIcon size="18"><Menu /></NIcon>
				<span class="text-[#696969]">AI帮我写</span>
			</div>
			<div class="p-[16px_12px] w-[420px] sm:w-full">
				<NInput
					type="textarea"
					placeholder="请输入提示词"
					:autosize="{
						minRows: 3,
						maxRows: 3,
					}"
					v-model:value="aiPromptText.text"
				></NInput>
			</div>
			<div class="p-[0_12px_20px] flex gap-[12px]">
				<NButton
					type="info"
					class="flex-1"
					@click="getPrompt"
					:loading="aiPromptText.loading"
					>{{ aiPromptText.inited ? "重新生成" : "生成描述" }}</NButton
				>
				<NButton
					v-if="aiPromptText.inited"
					:loading="aiPromptText.loading"
					type="info"
					class="flex-1"
					@click="usePrompt"
					>使用此描述</NButton
				>
			</div>
		</div>
	</n-popover>
</template>

<script lang="ts" setup>
import { reactive, onMounted, onUnmounted, ref } from "vue";
import { NButton, NPopover, NIcon, NInput, useMessage } from "naive-ui";
import Bot from "@/assets/bot.svg";
import Menu from "@/assets/menu.svg";

import { getPromptFormAI } from "../apis";

const $emit = defineEmits(["useAiPrompt"]);

const $message = useMessage();
const originPrompt = ref("");

const aiPromptText = reactive({
	show: false,
	inited: false,
	text: "",
	loading: false,
});

// 生成描述&重新生成
const getPrompt = () => {
	// if (aiPromptText.inited) {
	// 	aiPromptText.inited = false;
	// 	aiPromptText.text = originPrompt.value;
	// 	return;
	// }
	if (!aiPromptText.inited) {
		if (!aiPromptText.text) return $message.warning("描述不能为空");
		originPrompt.value = aiPromptText.text;
	}

	aiPromptText.loading = true;

	getPromptFormAI({
		prompt: aiPromptText.inited ? originPrompt.value : aiPromptText.text,
	})
		.then((res: any) => {
			aiPromptText.text = res.prompt;
			aiPromptText.inited = true;
		})
		.finally(() => {
			aiPromptText.loading = false;
		});
};

// 使用此描述
const usePrompt = () => {
	aiPromptText.show = false;
	$emit("useAiPrompt", aiPromptText.text);
};
const hidePopup = () => {
	aiPromptText.show = false;
};

onMounted(() => {
	document.body.addEventListener("click", hidePopup);
});

onUnmounted(() => {
	document.body.removeEventListener("click", hidePopup);
});

const resetAiPrompt = () => {
	aiPromptText.show = false;
	aiPromptText.inited = false;
	aiPromptText.text = "";
	aiPromptText.loading = false;
	originPrompt.value = "";
};

defineExpose({
	resetAiPrompt,
});
</script>

<style lang="" scoped></style>
