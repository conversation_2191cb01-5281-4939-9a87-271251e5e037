import { onUnmounted, ref } from "vue";

export const useLoading = () => {
	const loading = ref(false);
	const timer = ref();

	const setLoadingWithTimeout = (time = 2000) => {
		loading.value = true;

		timer.value = setTimeout(() => {
			loading.value = false;
			timer.value = null;
		}, time);
	};

	const setLoading = () => {
		loading.value = true;
	};

	const removeLoading = () => {
		loading.value = false;
	};

	onUnmounted(() => {
		if (timer.value) {
			clearTimeout(timer.value);
		}
	});

	return {
		loading,
		setLoadingWithTimeout,
		setLoading,
		removeLoading,
	};
};
