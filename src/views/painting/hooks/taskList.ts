import { onMounted, ref, nextTick, inject, onUnmounted } from "vue";
import { usePaintingStore } from "@/store";
import {
	queryTaskList,
	submitImagine,
	queryTaskDetail,
	executeAction,
	getTaskList,
	queryTaskCursorList,
	deleteTask,
} from "../apis";

const MAXCOUNT = 2000;

export const useTaskList = (taskType) => {
	const PaintingStore = usePaintingStore();

	const scrollPageTo: any = inject("scrollPageTo");

	// taskType任务类型（IMAGINE、DESCRIBE、BLEND、SWAP_FACE）
	const pageParams = ref<any>({ pageSize: 10, cursor: null, hasMore: false });
	const onGoingTaskIds = ref(new Map<number, number>());
	const timer = ref();

	const taskList = ref<any>([]);

	// 页面滚动定位
	const handlePagePosition = (taskId = null, hasMore?: any) => {
		console.log(">>>>taskId", taskId);
		if (!taskId) {
			scrollPageTo(1000000);
		} else {
			const { offsetTop, clientHeight } =
				document.querySelector(`#painting-item-${taskId}`) || {};

			scrollPageTo(offsetTop + clientHeight);
		}
		if (hasMore !== undefined) {
			pageParams.value.hasMore = hasMore;
		}
	};

	// 添加任务
	const addTaskToList = (taskId) => {
		taskList.value.push({ action: "IMAGINE", taskId, progress: 0 });
		return queryTaskDetail(taskId).then((detail) => {
			// taskList.value.push({ ...detail, progress: 0 });
			taskList.value.some((task, index) => {
				if (taskId == task.taskId) {
					taskList.value[index] = task;
					return true;
				}
			});

			pollingStatus([taskId]);
		});
	};

	// 获取页面
	const getPageList = () => {
		const { pageSize, cursor } = pageParams.value;

		return queryTaskCursorList({ pageSize, cursor }).then((res) => {
			const { hasMore, nextCursor } = res;

			let pageList = formateList(res.pageList).reverse();

			const onGoingTaskIds = pageList
				.filter((task) => needPollTask(task))
				.map((task) => task.taskId);

			pollingStatus(onGoingTaskIds);

			const firstItem = cursor ? taskList.value[0] : null;
			pageList = !cursor ? pageList : [...pageList, ...taskList.value];

			pageParams.value.cursor = nextCursor;
			taskList.value = pageList;

			nextTick(() => {
				handlePagePosition(firstItem?.taskId, hasMore);
			});
		});
	};

	// 任务轮询
	const pollingStatus = (taskIds: number[]) => {
		if (!taskIds.length) return;
		clearTimeout(timer.value);

		taskIds.forEach((taskId) => {
			if (onGoingTaskIds.value.has(taskId)) return;
			onGoingTaskIds.value.set(taskId, 0);
		});

		PaintingStore.setOnGoingTaskCount(onGoingTaskIds.value.size);

		timer.value = setTimeout(() => {
			getTaskList({
				taskIdList: Array.from(onGoingTaskIds.value.keys()),
			}).then((res) => {
				const pageList = formateList(res);

				// 更新页面数据
				pageList.forEach((task) => {
					taskList.value.some(({ taskId }, index) => {
						if (taskId == task.taskId) {
							taskList.value[index] = task;
							return true;
						}
					});
				});

				// 删除已经完成任务
				pageList
					.filter((task) => !needPollTask(task))
					.forEach(({ taskId }) => {
						onGoingTaskIds.value.delete(taskId);
					});

				// 删除超过次数的任务
				Array.from(onGoingTaskIds.value.keys()).forEach((taskId) => {
					const taskPollingCount = onGoingTaskIds.value.get(taskId);

					if (taskPollingCount === undefined) return;

					if (taskPollingCount >= MAXCOUNT) {
						onGoingTaskIds.value.delete(taskId);
					} else {
						onGoingTaskIds.value.set(taskId, taskPollingCount + 1);
					}
				});

				// 统计正在运行的任务数量
				PaintingStore.setOnGoingTaskCount(onGoingTaskIds.value.size);

				if (onGoingTaskIds.value.size)
					pollingStatus(Array.from(onGoingTaskIds.value.keys()));
			});
		}, 3000);
	};

	// 删除任务
	const deleteTaskItem = (taskId) => {
		return deleteTask({ taskId }).then(() => {
			// taskList.
			const index = taskList.value.findIndex((task) => taskId === task.taskId);

			if (index < 0) return;
			taskList.value.splice(index, 1);
		});
	};

	onUnmounted(() => {
		clearTimeout(timer.value);
	});

	onMounted(() => {
		getPageList();
		setTimeout(() => {
			scrollPageTo(100000);
		}, 1200);
	});

	return {
		pageParams,
		taskList,
		addTaskToList,
		getPageList,
		handlePagePosition,
		deleteTaskItem,
	};
};

export const formateList = (list: any[] = []): any[] => {
	if (!list) return [];

	list = list.map((task) => {
		task = {
			...task,
			progress: Number((task.progress || "").split("%")[0]),
		};

		if (task.action == "SHORTEN") {
			task.promptList = task.description
				? task.description
						.replace(/^\${/, "")
						.replace(/}$/, "")
						.split("## ")
						.filter((item) => item)
						.map((str) => {
							const [title, ...list] = str.split("\n");
							return {
								title,
								list: list.filter((item) => item).map((str) => str.slice(4)),
							};
						})
				: [];
		} else if (task.action == "DESCRIBE") {
			task.promptList = task.promptEn
				? task.promptEn.split(/\n+/).map((str) => str.slice(4))
				: [];
		} else {
			task.promptList = task.promptCn
				? (task.promptCn || "")
						.split(/\n+/)
						.map((str) => str.replace(/\d\./, ""))
				: [];
		}

		return task;
	});

	return list;
};

const needPollTask = (task) => {
	const { action, status } = task;

	if (["PROMPTANALYZER", "PICREADER"].includes(action) && status == "MODAL")
		return false;

	if (task.progress == 100) return false;

	return !["FAILURE", "CANCEL", "SUCCESS"].includes(task.status);
};
