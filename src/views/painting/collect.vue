<template>
	<div class="w-full pb-[218px]">
		<PaintingItem
			v-for="task in taskList"
			:key="task.taskId"
			:item-data="task"
			:isCollectPage="true"
			@deleteUserFavorite="deleteUserFavorite"
			@showModal="showModal"
		/>
		<LoadMore v-if="pageParams.hasMore" @onLoadmore="onLoadmore" />
	</div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import useClipboard from "vue-clipboard3";
import { useMessage } from "naive-ui";

import PaintingItem from "./components/PaintingItem.vue";
import LoadMore from "./components/LoadMore.vue";
import { queryUserFavoriteList } from "./apis";
import { formateList } from "./hooks/taskList";

const { toClipboard } = useClipboard();
const $message = useMessage();

const taskList = ref();
const pageParams = ref({ pageSize: 10, pageNum: 1, hasMore: false });

const getPageList = () => {
	queryUserFavoriteList({ ...pageParams.value }).then((res) => {
		const { totalCount } = res;
		const pageNum = pageParams.value.pageNum;
		let pageList = formateList(res.pageList);

		pageList = pageNum == 1 ? pageList : [...taskList.value, ...pageList];

		taskList.value = pageList;
		pageParams.value.hasMore = pageList.length < totalCount;
	});
};

const onLoadmore = () => {
	pageParams.value.pageNum++;
	getPageList();
};
const deleteUserFavorite = () => {
	pageParams.value.pageNum = 1;
	getPageList();
};
const showModal = (payload) => {
	console.log(">>>11", payload);

	const { prompt } = payload;

	toClipboard(prompt);
	$message.success("复制成功");
};

onMounted(() => {
	getPageList();
});
</script>

<style lang="" scoped></style>
