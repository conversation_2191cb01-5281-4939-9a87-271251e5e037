<template>
	<div class="page-layout w-full h-full flex flex-col">
		<NLayoutHeader>
			<Header1 />
		</NLayoutHeader>

		<NLayout class="pt-[64px] sm:pt-0" has-sider>
			<NLayoutSider :native-scrollbar="false" show-trigger="arrow-circle" :collapsed-width="0" :width="250" bordered :position="isPC ? 'static' : 'absolute'"
										:default-collapsed="isPC ? false : true" collapsed-trigger-class="collapsed-trigger" trigger-class="trigger">
				<Sider></Sider>
			</NLayoutSider>
			<NLayout content-class="p-[12px_20px_20px] bg-[#F5F7FF] sm:px-4" ref="contentRef">
				<div v-if="route.path !== '/painting/square'" class="pb-[12px] sm:px-6">
					<span class="text-[#3D3D3D] text-[14px]"># 创作中心</span>
					<span class="text-[12px] pl-[12px] text-[#F45B1E]">请遵守中华人民共和国网络安全法，严禁生成涉及政治人物，色情、恐怖等不良内容，如有违规封号处理</span>
				</div>
				<RouterView> </RouterView>
			</NLayout>
			<!-- <TabBar /> -->
		</NLayout>
	</div>
</template>

<script lang="ts" setup>
import { ref, provide } from "vue";
import { useRoute } from "vue-router";
import { NLayout, NLayoutHeader, NLayoutSider } from "naive-ui";
import type { LayoutInst } from "naive-ui";
// import {  TabBar, CopyRight } from "@/components/common";
import Header1 from "@/components/common/Header1/index.vue";

import Sider from "./sider.vue";
import { useBasicLayout } from "@/hooks/useBasicLayout";

const route = useRoute();
const { isMobile, isPC } = useBasicLayout()

const contentRef = ref<LayoutInst | null>(null);
const scrollPageTo = (top = 0) => {
	contentRef.value?.scrollTo({ top });
};

provide("scrollPageTo", scrollPageTo);
</script>

<style lang="less" scoped>
.page-layout {
	--n-border-focus: 1px solid #1874ff !important;
}

@media screen and (max-width: 767px) {
	:deep(.trigger.trigger) {
		top: 25px;
	}

	:deep(.collapsed-trigger.collapsed-trigger) {
		top: 25px;
		right: -25px;
		width: 30px;
		height: 30px;
		transition: all 0.3s;
	}
}

@media screen and (min-width: 768px)and(max-width: 1024px) {
	:deep(.trigger.trigger) {
		top: 25px;
	}

	:deep(.collapsed-trigger.collapsed-trigger) {
		top: 25px;
		right: -25px;
		width: 30px;
		height: 30px;
		transition: all 0.3s;
	}
}
</style>
