<template>
	<div class="pb-[64px]">
		<n-menu :options="menuOptions" :expanded-keys="['painting']" v-model:value="selectedKey" :indent="40" />

		<div class="absolute left-[12px] right-[12px] bottom-0 pt-[12px] z-10 h-[64px] bg-[#ffffff]">
			<div class="p-[16px_16px_0] border-t border-[#DCDCDC] border-solid flex items-center gap-[16px] text-[14px] cursor-pointer" @click="toCollect">
				<span class="iconfont icon-star  text-[16px]"></span>收藏夹
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { h, ref, watch } from "vue";
import { RouterLink, useRoute, useRouter } from "vue-router";
import type { MenuOption } from "naive-ui";
import { NMenu } from "naive-ui";
import AIppt from "@/assets/images/<EMAIL>";
import Painting from "@/assets/images/pasd.png";
import User from "@/assets/images/user.png";

const route = useRoute();
const $router = useRouter();

const selectedKey = ref("text2img");


function renderIcon(icon) {
	return () =>
		h("img", {
			style: "width:24px;height:24px;margin-right:16px;",
			src: icon,
		});
}

const menuOptions: MenuOption[] = [
	{
		label: "AI绘图",
		key: "painting",
		icon: renderIcon(Painting),
		children: [
			{
				key: "square",
				label: () =>
					h(
						RouterLink,
						{
							to: {
								path: "/painting/square",
							},
						},
						{ default: () => "灵感广场" }
					),
			},
			{
				key: "text2img",
				label: () =>
					h(
						RouterLink,
						{
							to: {
								path: "/painting/text2img",
							},
						},
						{ default: () => "AI绘图" }
					),
			},
			{
				key: "img2img",
				label: () =>
					h(
						RouterLink,
						{
							to: {
								path: "/painting/img2img",
							},
						},
						{ default: () => "AI混图" }
					),
			},
			{
				key: "img2text",
				label: () =>
					h(
						RouterLink,
						{
							to: {
								path: "/painting/img2text",
							},
						},
						{ default: () => "图生文" }
					),
			},
			// {
			// 	key: "faceSwapper",
			// 	label: () =>
			// 		h(
			// 			RouterLink,
			// 			{
			// 				to: {
			// 					path: "/painting/faceSwapper",
			// 				},
			// 			},
			// 			{ default: () => "AI换脸" }
			// 		),
			// },

			{
				key: "shorten",
				label: () =>
					h(
						RouterLink,
						{
							to: {
								path: "/painting/shorten",
							},
						},
						{ default: () => "咒语解析" }
					),
			},
		],
	}
];

const handleUpdateExpandedKeys = (e) => {
	console.log("handleUpdateExpandedKeys", e);
};

watch(
	() => route.path,
	(path) => {
		selectedKey.value = path.split("/").slice(-1)[0];
	},
	{ immediate: true }
);

const toCollect = () => {
	$router.push("/painting/collect");
};
</script>

<style lang="" scoped></style>
