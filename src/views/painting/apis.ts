import type { AxiosProgressEvent, GenericAbortSignal } from "axios";
import { post } from "@/utils/request";

// 提交Imagine任务（文生图）
export function submitImagine<T>(params: {
	prompt: String;
	botType: String; //"MID_JOURNEY" | "NIJI_JOURNEY";
	speedMode: String; //(RELAX,FAST)
}) {
	return post<T>(
		{
			url: "/api3/aiwork365/mj/task/submitImagine",
			data: params,
		},
		{}
	);
}

export function getPromptFormAI<T>(params: { prompt: string }) {
	return post<T>(
		{
			url: "/api3/aiwork365/mj/style/promptAssociation",
			data: params,
		},
		{}
	);
}

// 获取任务列表
export const queryTaskList = (params: {
	pageSize: Number;
	pageNum: Number;
	taskType?: String; //任务类型（IMAGINE、DESCRIBE、BLEND、SWAP_FACE）
	taskIds?: Number[];
}) => {
	return post<{ pageList: any[]; totalCount: number }>(
		{
			url: "/api3/aiwork365/mj/task/queryTaskList",
			data: params,
		},
		{}
	);
};

// 获取任务列表 滚动分页
export const queryTaskCursorList = (params: {
	pageSize: Number;
	cursor: Number | null;
	taskType?: String; //任务类型（IMAGINE、DESCRIBE、BLEND、SWAP_FACE）
	taskIds?: Number[];
}) => {
	return post<{ pageList: any[]; hasMore: Boolean; nextCursor: Number }>(
		{
			url: "/api3/aiwork365/mj/task/queryTaskCursorList",
			data: params,
		},
		{}
	);
};

// 获取任务列表
export const getTaskList = (params: {
	taskType?: String; //任务类型（IMAGINE、DESCRIBE、BLEND、SWAP_FACE）
	taskIdList?: Number[];
}) => {
	return post<any[]>(
		{
			url: "/api3/aiwork365/mj/task/getTaskList",
			data: params,
		},
		{}
	);
};

// 获取任务详情
export const queryTaskDetail = (taskId: String) => {
	return post<{ pageList: any[] }>(
		{
			url: "/api3/aiwork365/mj/task/getTask",
			data: { taskId },
		},
		{}
	);
};

// 图生文
export function submitDescribe<T>(params: {
	base64Image: String;
	botType: String; //"MID_JOURNEY" | "NIJI_JOURNEY";
	speedMode: String; //(RELAX,FAST)
}) {
	return post<T>(
		{
			url: "/api3/aiwork365/mj/task/submitDescribe",
			data: params,
		},
		{}
	);
}

//提示词分析
export function submitShorten<T>(params: {
	prompt: String;
	botType: String; //"MID_JOURNEY" | "NIJI_JOURNEY";
	speedMode: String; //(RELAX,FAST)
}) {
	return post<T>(
		{
			url: "/api3/aiwork365/mj/task/submitShorten",
			data: params,
		},
		{}
	);
}

//混图
export function submitBlend<T>(params: {
	prompt: String;
	botType: String; //"MID_JOURNEY" | "NIJI_JOURNEY";
	speedMode: String; //(RELAX,FAST)
}) {
	return post<T>(
		{
			url: "/api3/aiwork365/mj/task/submitBlend",
			data: params,
		},
		{}
	);
}

//

//换脸
export function submitSwapFace<T>(params: {
	prompt: String;
	botType: String; //"MID_JOURNEY" | "NIJI_JOURNEY";
	speedMode: String; //(RELAX,FAST)
}) {
	return post<T>(
		{
			url: "/api3/aiwork365/mj/task/submitSwapFace",
			data: params,
		},
		{}
	);
}

// submitModal
export function submitModal(params: any) {
	return post<{ taskId: string }>(
		{
			url: "/api3/aiwork365/mj/task/submitModal",
			data: params,
		},
		{}
	);
}

// 执行任务
export const executeAction = (params: {
	taskId: String;
	customId: String;
	botType: String;
	speedMode: String;
}) => {
	return post<{ taskId: String }>(
		{
			url: "/api3/aiwork365/mj/task/executeAction",
			data: params,
		},
		{}
	);
};

// 获取分类
export const getStyleCategoryList = () => {
	return post<any>(
		{
			url: "/api3/aiwork365/mj/style/getStyleCategoryList",
			data: {},
		},
		{}
	);
};

// 获取广场列表
export const queryStyleLibrary = (params: {
	pageNum: number;
	pageSize: number;
	categoryId?: number;
}) => {
	return post<any>(
		{
			url: "/api3/aiwork365/mj/style/queryStyleLibrary",
			data: params,
		},
		{}
	);
};

// 删除任务
export const deleteTask = (params: { taskId: number }) => {
	return post<any>(
		{
			url: "/api3/aiwork365/mj/task/deleteTask",
			data: params,
		},
		{}
	);
};

// 取消任务
export const cancelTask = (params: { taskId: number }) => {
	return post<any>(
		{
			url: "/api3/aiwork365/mj/task/cancelTask",
			data: params,
		},
		{}
	);
};

// 添加收藏
export const addUserFavorite = (params: { taskId: number }) => {
	return post<any>(
		{
			url: "/api3/aiwork365/mj/task/addUserFavorite",
			data: params,
		},
		{}
	);
};
// 取消收藏
export const deleteUserFavorite = (params: { taskId: number }) => {
	return post<any>(
		{
			url: "/api3/aiwork365/mj/task/deleteUserFavorite",
			data: params,
		},
		{}
	);
};

// 收藏列表
export const queryUserFavoriteList = (params: {
	pageNum: number;
	pageSize: number;
}) => {
	return post<any>(
		{
			url: "/api3/aiwork365/mj/task/queryUserFavoriteList",
			data: params,
		},
		{}
	);
};

export const submitApi = {
	IMAGINE: submitImagine,
	DESCRIBE: submitDescribe,
	BLEND: submitBlend,
	SWAP_FACE: submitSwapFace,
	SHORTEN: submitShorten,
};
