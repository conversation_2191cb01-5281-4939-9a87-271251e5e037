<template>
	<div class="assets-summary-container">
		<div class="title-section">
			<div class="title-icon">
				<n-icon size="20" color="#0E69FF">
					<WalletOutline />
				</n-icon>
			</div>
			<div class="title-text">用户资产</div>
		</div>

		<div class="assets-cards">
			<div class="assets-info">
				<div class="asset-card">
					<div class="card-label">总资产</div>
					<div class="card-value">¥{{ data?.total_amount }}</div>
				</div>

				<div class="divider"></div>

				<div class="asset-card">
					<div class="card-label">可提现佣金</div>
					<div class="card-value">¥{{ data?.withdrawable }}</div>
				</div>

				<div class="divider"></div>

				<div class="asset-card">
					<div class="card-label">
						审核中佣金
						<n-tooltip trigger="hover" placement="top">
							<template #trigger>
								<n-icon size="16" color="#999">
									<InformationCircleOutline />
								</n-icon>
							</template>
							<div class="tooltip-content">
								审核中（包含审核通过），审核通过后可提现至微信账户
							</div>
						</n-tooltip>
					</div>
					<div class="card-value">¥{{ data?.withdrawing }}</div>
				</div>
				<div class="divider"></div>

				<div class="asset-card">
					<div class="card-label">
						待生效佣金
						<n-tooltip trigger="hover" placement="top">
							<template #trigger>
								<n-icon size="16" color="#999">
									<InformationCircleOutline />
								</n-icon>
							</template>
							<div class="tooltip-content">
								用户购买会员未满7天，不可提交提现申请
							</div>
						</n-tooltip>
					</div>
					<div class="card-value">¥{{ data?.pending }}</div>
				</div>

				<div class="divider"></div>
				<div class="asset-card">
					<div class="card-label">已提现佣金</div>
					<div class="card-value">¥{{ data?.withdrawn }}</div>
				</div>
			</div>

			<div class="withdraw-button">
				<n-button
					type="primary"
					size="large"
					block
					style="width: 182px; height: 45px; --n-text-color-pressed: #fff;--n-text-color-focus: #fff;"
					@click="showWithdrawModal = true"
					>申请提现</n-button
				>
			</div>
		</div>

		<!-- 提现模态框 -->
		<n-modal
			v-model:show="showWithdrawModal"
			style="width: 432px; border-radius: 8px"
			:mask-closable="true"
			:close-on-esc="true"
			class="withdraw-modal"
			@close="clearAmount"
		>
			<div class="withdraw-modal-container">
				<!-- 标题 -->
				<div class="withdraw-modal-header">
					<span class="modal-title">申请提现</span>
					<n-icon
						size="20"
						class="close-icon"
						@click="showWithdrawModal = false"
					>
						<CloseOutline />
					</n-icon>
				</div>

				<!-- 表单内容 -->
				<n-form
					ref="formRef"
					:model="formValue"
					:rules="rules"
					label-placement="top"
					require-mark-placement="right-hanging"
					class="withdraw-form"
				>
					<n-grid :cols="1" :x-gap="24">
						<n-form-item-gi label="提现金额:" path="amount">
							<div class="flex flex-col flex-1">
								<div class="custom-input-wrapper">
									<div class="currency-symbol">¥</div>
									<input
										v-model="formValue.amount"
										:placeholder="`单次最高可提现${MAX_WITHDRAW_AMOUNT}元,将按照提现金额1%收取手续费`"
										class="custom-input"
										type="number"
										@input="validateAmount"
										@blur="validateAmount"
									/>

									<div
										v-if="formValue.amount"
										class="clear-icon-wrapper"
										@click="clearAmount"
									>
										<n-icon size="16" class="clear-icon">
											<CloseCircle />
										</n-icon>
									</div>
								</div>
								<div
									v-if="amountError"
									class="error-message"
									style="color: #ff5757; font-size: 12px; margin-top: 4px"
								>
									{{ amountError }}
								</div>
								<div
									v-else-if="apiError"
									class="error-message"
									style="color: #ff5757; font-size: 12px; margin-top: 4px"
								>
									{{ apiError }}
								</div>
							</div>
						</n-form-item-gi>
					</n-grid>
					<div class="w-full flex justify-between text-[#474747] mb-[22px]">
						<span>提现方式</span>
						<span>微信账户</span>
					</div>
					<div class="w-full flex justify-between text-[#474747]">
						<span>电话号码</span>
						<span>{{ user?.phone || "未绑定手机号" }}</span>
					</div>
					<n-form-item>
						<div class="form-actions">
							<n-button
								type="primary"
								size="large"
								block
								:loading="submitting"
								:disabled="!formIsValid"
								@click="handleSubmit"
								class="confirm-button"
								style="--n-text-color-pressed: #fff;--n-text-color-focus: #fff;"
							>
								确认提现
							</n-button>
						</div>
					</n-form-item>
				</n-form>
			</div>
		</n-modal>
	</div>
</template>

<script setup lang="ts">
const emit = defineEmits<{
	(e: "withdrawal-success"): void;
}>();

import { ref, computed } from "vue";
import { fetchWithdrawalApply } from "@/views/distribution/api";
import {
	NIcon,
	NButton,
	NTooltip,
	NModal,
	NForm,
	NGrid,
	NFormItemGi,
	NFormItem,
	useMessage,
} from "naive-ui";
import {
	WalletOutline,
	InformationCircleOutline,
	CloseOutline,
	CloseCircle,
} from "@vicons/ionicons5";

import { Distribution } from "../types";
interface User {
	nickname?: string;
	phone?: string;
	openId?: string;
}

interface Props {
	data?: Distribution;
	user?: User;
}

// 模态框显示状态
const showWithdrawModal = ref(false);
const submitting = ref(false);
const message = useMessage();
const amountError = ref("");
const apiError = ref("");

// 金额验证
const props = defineProps<Props>();
const MAX_WITHDRAW_AMOUNT = 200;

const validateAmount = () => {
	const amountStr = formValue.value.amount;
	// if (!/^\d*\.?\d{0,2}$/.test(amountStr)) {
	// 	amountError.value = "金额最多保留2位小数";
	// 	return;
	// }

	const numValue = Number(amountStr);
	if (isNaN(numValue)) {
		amountError.value = "请输入有效的金额";
	} else if (numValue <= 0) {
		amountError.value = "提现金额必须大于0";
	} else if (numValue > MAX_WITHDRAW_AMOUNT) {
		amountError.value = `单次最高可提现${MAX_WITHDRAW_AMOUNT}元`;
	} else if (props.data?.withdrawable && numValue > props.data.withdrawable) {
		amountError.value = "已超出可提现金额";
	} else {
		amountError.value = "";
	}
};

// 表单相关
const formRef = ref<any>(null);
const formValue = ref({
	amount: "",
	withdrawMethod: "微信账户",
	phoneNumber: "",
});

// 表单验证规则
const rules = {
	amount: [
		{
			required: true,
			message: "请输入提现金额",
		},
		{
			validator: (_rule: any, value: string) => {
				if (!value) return true;
				if (!/^\d*\.?\d{0,2}$/.test(value)) {
					return new Error("金额最多保留2位小数");
				}
				const numValue = Number(value);
				if (isNaN(numValue)) return new Error("请输入有效的金额");
				if (numValue <= 0) return new Error("提现金额必须大于0");
				if (numValue > MAX_WITHDRAW_AMOUNT)
					return new Error(`单次最高可提现${MAX_WITHDRAW_AMOUNT}元`);
				if (props.data?.withdrawable && numValue > props.data.withdrawable) {
					return new Error("已超出可提现金额");
				}
				return true;
			},
			trigger: ["input", "blur"],
		},
	],
	phoneNumber: [
		{
			required: true,
			message: "请输入电话号码",
		},
		{
			pattern: /^1[3-9]\d{9}$/,
			message: "请输入有效的手机号码",
			trigger: ["input", "blur"],
		},
	],
};

// 计算表单是否有效
const formIsValid = computed(() => {
	return (
		formValue.value.amount &&
		Number(formValue.value.amount) > 0 &&
		Number(formValue.value.amount) <= 200
	);
});

// 清除金额输入
const clearAmount = () => {
	formValue.value.amount = "";
	amountError.value = "";
	apiError.value = "";
};

// 清除电话号码输入
const clearPhoneNumber = () => {
	formValue.value.phoneNumber = "";
};

// 提交表单
const handleSubmit = (e: MouseEvent) => {
	e.preventDefault();
	formRef.value?.validate((errors: any) => {
		if (!errors) {
			submitWithdrawal();
		} else {
			message.error("请填写正确的表单信息");
		}
	});
};

// 提交提现申请
const submitWithdrawal = async () => {
	submitting.value = true;
	apiError.value = "";

	try {
		await fetchWithdrawalApply({
			amount: Number(formValue.value.amount),
		});

		submitting.value = false;
		message.success("提现申请已提交，请等待审核");
		showWithdrawModal.value = false;
		// 重置表单
		formValue.value = {
			amount: "",
			withdrawMethod: "微信账户",
			phoneNumber: "",
		};
		// 触发刷新提现记录并切换到提现记录tab
		emit("withdrawal-success");
	} catch (error: unknown) {
		submitting.value = false;
		apiError.value =
			(error as { errmsg?: string }).errmsg || "提现失败: 系统繁忙，请稍后再试";
	}
};
</script>

<style lang="less" scoped>
.assets-summary-container {
	width: 100%;
	background: #fff;
	border-radius: 8px;
	padding: 30px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
	border: 1px dashed #e0e7ff;
}

.title-section {
	display: flex;
	align-items: center;
	margin-bottom: 20px;
}

.title-icon {
	margin-right: 8px;
}

.title-text {
	font-size: 16px;
	font-weight: 600;
	color: #3d3d3d;
}

.assets-cards {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 30px;
}

.assets-info {
	display: flex;
	align-items: center;
	flex: 1;
}

.asset-card {
	flex: 1;
	padding: 0;
}

.divider {
	width: 1px;
	height: 40px;
	background-color: #e8e8e8;
	margin-right: 40px;
}

.card-label {
	font-size: 14px;
	color: #666666;
	margin-bottom: 8px;
	display: flex;
	align-items: center;
	gap: 4px;
}

.tooltip-content {
	max-width: 200px;
	line-height: 1.5;
}

.card-value {
	font-size: 24px;
	font-weight: 600;
	color: #3d3d3d;
}

.withdraw-button {
	min-width: 20%;
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	margin-left: 20px;
}

.withdraw-note {
	display: flex;
	align-items: center;
	gap: 4px;
	margin-top: 8px;
	font-size: 12px;
	color: #999;
}

// 提现模态框样式
.withdraw-modal {
	:deep(.n-modal) {
		border-radius: 8px;
		overflow: hidden;
	}
}

.withdraw-modal-container {
	background: #fff;
	border-radius: 8px;
	overflow: hidden;
}

.withdraw-modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px 20px;
	border-bottom: 1px solid #eee;
}

.modal-title {
	font-size: 16px;
	font-weight: 500;
	color: #333;
}

.close-icon {
	cursor: pointer;
	color: #999;
	transition: color 0.3s;

	&:hover {
		color: #666;
	}
}

.withdraw-form {
	padding: 20px;

	:deep(.n-form-item-gi) {
		margin-bottom: 16px;
	}

	:deep(.n-form-item-label) {
		font-size: 14px;
		color: #333;
		padding-bottom: 8px;
		height: auto;
	}

	:deep(.n-form-item-blank) {
		min-height: auto;
	}

	:deep(.n-input) {
		border-radius: 4px;
	}

	:deep(.n-grid) {
		width: 100%;
	}
}

.custom-input-wrapper {
	position: relative;
	display: flex;
	align-items: center;
	width: 100%;
	height: 50px;
	border-bottom: 1px solid #e8e8e8;
}

.currency-symbol {
	position: absolute;
	left: 0;
	font-size: 20px;
	color: #3d3d3d;
	padding-left: 2px;
	line-height: 34px;
}

.custom-input {
	width: 100%;
	height: 100%;
	border: none;
	outline: none;
	padding: 0 30px 0 24px;
	font-size: 14px;
	color: #333;
	background: transparent;

	&::placeholder {
		color: #b7bec8;
		font-size: 14px;
	}
}

.clear-icon-wrapper {
	position: absolute;
	right: 0;
	top: 0;
	height: 100%;
	display: flex;
	align-items: center;
	padding-right: 8px;
	cursor: pointer;
}

.clear-icon {
	color: #ccc;

	&:hover {
		color: #999;
	}
}

.withdraw-method {
	height: 34px;
	line-height: 34px;
	color: #333;
}

.form-actions {
	margin-top: 24px;
	width: 100%;
}

.confirm-button {
	height: 45px;
	font-size: 15px;
	border-radius: 4px;
	background-color: #4086ff;
	color: #fff;

	&:hover {
		background-color: #2970e6;
		color: #fff;
	}

	&:active {
		background-color: #1e5fcc;
		color: #fff;
	}
}
</style>
