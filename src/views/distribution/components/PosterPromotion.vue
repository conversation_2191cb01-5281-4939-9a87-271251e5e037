<template>
	<div class="poster-promotion-container">
		<div class="promotion-title">海报推广</div>
		<div class="divider"></div>
		<div class="promotion-content">
			<template v-if="!loading">
				<div class="poster-grid">
				<div
					class="poster-item"
					v-for="(item, index) in posters"
					:key="index"
					@click="openPosterModal(index)"
				>
					<template v-if="item">
						<div
							class="poster-image"
							:style="{
								backgroundImage: `url(${item})`,
							}"
						></div>
					</template>
					<template v-else>
						<div class="poster-image skeleton">
							<div class="skeleton-content"></div>
						</div>
					</template>
				</div>
				</div>
				<div class="view-more" @click="openPosterModal(0)">
					<span>查看更多</span>
					<span class="arrow">›</span>
				</div>
			</template>
			<template v-else>
				<div class="skeleton-grid">
					<div
						v-for="i in 6"
						:key="i"
						class="skeleton-item"
					></div>
				</div>
				<div class="view-more skeleton-view-more"></div>
			</template>
		</div>
	</div>

	<!-- 海报查看更多模态框 -->
	<n-modal
		v-model:show="showPosterModal"
		style="width: 784px"
		:mask-closable="true"
		preset="card"
	>
		<template #header>
			<div class="modal-title">
				<span class="title-text">立即邀请</span>
				<span class="title-desc">（分享好友下单，即可获得相应的分佣提成）</span>
			</div>
		</template>
		<div class="poster-modal-container">
			<!-- 关闭按钮 -->
			<!-- <div class="close-button" @click="showPosterModal = false">
        <n-icon size="16">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">
            <path fill="currentColor" d="M16 1.46015L14.5398 0L8 6.55013L1.46015 1.53225e-07L0 1.46015L6.55013 8L0 14.5398L1.46015 16L8 9.44987L14.5398 16L16 14.5398L9.44987 8L16 1.46015Z"/>
          </svg>
        </n-icon>
      </div> -->

			<!-- 标题 -->
			<!-- <div class="modal-title">

      </div> -->

			<!-- 海报展示区域 -->
			<div class="poster-display">
				<n-carousel
					class="poster-carousel"
					:current-index="currentPosterIndex"
					@update:current-index="handleSlideChange"
					:show-arrow="true"
					dot-type="line"
					effect="card"
					:prev-slide-style="prevSlideStyle"
					:next-slide-style="nextSlideStyle"
					:show-dots="false"
					draggable
				>
					<template #arrow="{ prev, next }">
						<div class="custom-arrow">
							<button type="button" class="custom-arrow--left" @click="prev">
								<IconPrev />
							</button>
							<button type="button" class="custom-arrow--right" @click="next">
								<IconNext />
							</button>
						</div>
					</template>
					<n-carousel-item
						v-for="(item, index) in posters"
						:key="index"
						class="carousel-item"
						:style="{ width: '48%' }"
					>
						<img :src="item" class="poster-image-large" />
					</n-carousel-item>
				</n-carousel>
			</div>

			<!-- 操作按钮 -->
			<div class="poster-actions">
				<!-- <n-button class="action-button copy" @click="copyPosterLink">
					复制海报链接
				</n-button> -->
				<n-button
					class="action-button download"
					type="primary"
					@click="downloadPoster"
					style="--n-text-color-pressed: #fff;--n-text-color-focus: #fff;"
				>
					下载海报
				</n-button>
			</div>
		</div>
	</n-modal>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
const loading = ref(true);

onMounted(() => {
  setTimeout(() => {
    loading.value = false;
  }, 1000); // 模拟1秒加载时间
});
import {
	NModal,
	NButton,
	NIcon,
	NCarousel,
	NCarouselItem,
	useMessage,
} from "naive-ui";

interface Props {
	posters?: string[];
	url?: string;
}
const props = defineProps<Props>();

// 消息提示
const message = useMessage();

// 模态框显示状态
const showPosterModal = ref(false);

// 当前选中的海报索引
const currentPosterIndex = ref(0);

// 设置轮播图样式
const prevSlideStyle = "transform: translateX(-174%) translateZ(-300px);";

const nextSlideStyle = "transform: translateX(74%) translateZ(-300px);";

// 打开海报模态框
const openPosterModal = (index: number) => {
	currentPosterIndex.value = index;
	showPosterModal.value = true;
};

// 处理轮播图切换
const handleSlideChange = (index: number) => {
	currentPosterIndex.value = index;
};

// 复制海报链接
const copyPosterLink = () => {
	navigator.clipboard
		.writeText(props?.url || "")
		.then(() => {
			message.success("链接已复制到剪贴板");
		})
		.catch(() => {
			message.error("复制失败，请手动复制");
		});
};

// 下载海报
const downloadPoster = () => {
	const image = props?.posters?.[currentPosterIndex.value];
	if (!image) {
		message.error("海报不存在，下载失败");
		return;
	}

	try {
		const link = document.createElement("a");
		link.href = image;
		link.download = `poster-${currentPosterIndex.value + 1}.png`;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		message.success("海报下载中...");
	} catch (error) {
		message.error("海报下载失败");
	}
};
</script>

<style lang="less" scoped>
.custom-arrow {
	display: flex;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 1;
	justify-content: center;
	column-gap: 310px;
}

.custom-arrow button {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	width: 40px;
	height: 40px;
	color: #fff;
	background-color: rgba(255, 255, 255, 0.1);
	border-width: 0;
	border-radius: 8px;
	transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	cursor: pointer;
	opacity: 0.5;
}
.modal-title {
	margin-bottom: 30px;
	margin-left: 41px;
	padding-top: 10px;

	.title-text {
		font-size: 16px !important;
		font-weight: bold !important;
		color: #3d3d3d;
	}

	.title-desc {
		font-size: 16px !important;
		color: #3d3d3d !important;
		margin-left: 5px;
	}
}
.poster-promotion-container {
	width: 100%;
	background: #fff;
	border-radius: 8px;
	padding: 24px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
	margin-top: 16px;

	.promotion-title {
		font-size: 14px;
		color: #3d3d3d;
		margin-bottom: 16px;
	}
	.divider {
		width: 100%;
		height: 1px;
		background: #e8e8e8;
		margin-bottom: 26px;
	}

	.promotion-content {
		width: 100%;
		min-height: 156px; /* 保持原有高度 */
	}

	.skeleton-view-more {
		height: 20px;
		width: 100px;
		margin: 16px auto 0;
		background: #f0f0f0;
		border-radius: 4px;
		position: relative;
		overflow: hidden;
	}

	.skeleton-view-more::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(
			90deg,
			#f0f0f0 25%,
			#e0e0e0 50%,
			#f0f0f0 75%
		);
		background-size: 200% 100%;
		animation: shimmer 1.5s infinite;
	}

	.skeleton-grid {
		display: grid;
		grid-template-columns: repeat(6, 1fr);
		gap: 16px;
		margin-bottom: 16px;
	}

	.skeleton-item {
		height: 140px;
		background: #f0f0f0;
		border-radius: 8px;
		overflow: hidden;
		position: relative;
	}

	.skeleton-item::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(
			90deg,
			#f0f0f0 25%,
			#e0e0e0 50%,
			#f0f0f0 75%
		);
		background-size: 200% 100%;
		animation: shimmer 1.5s infinite;
	}

	@keyframes shimmer {
		0% {
			background-position: 200% 0;
		}
		100% {
			background-position: -200% 0;
		}
	}

	.poster-grid {
		display: grid;
		grid-template-columns: repeat(6, 1fr);
		gap: 16px;
		margin-bottom: 16px;

		// @media (max-width: 1400px) {
		// 	grid-template-columns: repeat(3, 1fr);
		// }

		// @media (max-width: 768px) {
		// 	grid-template-columns: repeat(2, 1fr);
		// }

		// @media (max-width: 480px) {
		// 	grid-template-columns: 1fr;
		// }
	}

	.poster-item {
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		transition: transform 0.3s;
		cursor: pointer;

		&:hover {
			transform: translateY(-5px);
		}
	}

	.poster-image {
		width: 100%;
		height: 140px;
		overflow: hidden;
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		padding: 10px;
		background-size: cover;
		background-position: center;

		&.skeleton {
			background: #f0f0f0;
			overflow: hidden;
			position: relative;

			.skeleton-content {
				width: 100%;
				height: 100%;
				background: linear-gradient(
					90deg,
					#f0f0f0 25%,
					#e0e0e0 50%,
					#f0f0f0 75%
				);
				background-size: 200% 100%;
				animation: shimmer 1.5s infinite;
			}
		}
	}

	@keyframes shimmer {
		0% {
			background-position: 200% 0;
		}
		100% {
			background-position: -200% 0;
		}
	}

	.poster-tags {
		display: flex;
		gap: 5px;
	}

	.tag {
		font-size: 10px;
		border-radius: 4px;
		padding: 2px 4px;
		display: inline-block;
	}

	.tag-aiwork {
		background-color: rgba(14, 105, 255, 0.8);
		color: white;
	}

	.tag-recommend {
		background-color: rgba(255, 255, 255, 0.8);
		color: #333;
	}

	.poster-title-overlay {
		font-size: 12px;
		color: #333;
		font-weight: 500;
		line-height: 1.4;
		max-height: 50px;
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		background-color: rgba(255, 255, 255, 0.8);
		padding: 4px;
		border-radius: 4px;
	}

	.view-more {
		display: flex;
		justify-content: center;
		align-items: center;
		color: #666;
		font-size: 14px;
		cursor: pointer;
		margin-top: 16px;

		&:hover {
			color: #0e69ff;
		}

		.arrow {
			margin-left: 4px;
			font-size: 16px;
		}
	}
}

// 模态框样式
.poster-modal-container {
	background: #fff;
	position: relative;
	padding: 0 41px;

	.close-button {
		position: absolute;
		top: 10px;
		right: 10px;
		cursor: pointer;
		color: #474747;
		width: 24px;
		height: 24px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		transition: background-color 0.3s;
		z-index: 10;

		&:hover {
			background-color: rgba(0, 0, 0, 0.05);
		}
	}

	.poster-display {
		position: relative;
		margin-bottom: 30px;
		display: flex;
		justify-content: center;
		align-items: center;

		.poster-carousel {
			width: 100%;
			height: 400px;

			:deep(.n-carousel__slides) {
				display: flex;
				align-items: center;
				height: 400px;
			}

			:deep(.n-carousel__arrow) {
				width: 38px;
				height: 38px;
				border-radius: 50%;
				background-color: rgba(61, 61, 61, 0.8);
				color: white;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				transition: background-color 0.3s;
				z-index: 10;

				&:hover {
					background-color: rgba(61, 61, 61, 1);
				}

				&--left {
					left: 20px;
				}

				&--right {
					right: 20px;
				}
			}
		}

		.carousel-item {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100%;
		}

		.poster-image-large {
			width: 100%;
			height: 100%;
			object-fit: contain;
			border-radius: 8px;
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
			transition: transform 0.3s ease;

			&:hover {
				transform: scale(1.02);
			}
		}
	}

	.poster-actions {
		display: flex;
		justify-content: center;
		gap: 20px;

		.action-button {
			min-width: 165px;
			height: 46px;
			font-size: 16px;
			border-radius: 4px;
			transition: all 0.3s ease;

			&.copy {
				border: 1px solid #3e83f9;
				color: #3e83f9;

				&:hover {
					background-color: rgba(62, 131, 249, 0.1);
				}

				&:active {
					background-color: rgba(62, 131, 249, 0.2);
				}
			}

			&.download {
				background-color: #0066fe;
				color: white;

				&:hover {
					background-color: #0057da;
				}

				&:active {
					background-color: #004ec2;
				}
			}
		}
	}
}
</style>
