<template>
  <div class="withdrawal-timeline">
    <div class="timeline-content">
      <div class="timeline-item" v-for="(item, index) in timelineItems" :key="index">
        <div class="timeline-dot"></div>
        <div class="timeline-text">{{ item }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const timelineItems = ref([
  '申请提现',
  '财务审核',
  '立即提现',
	'提现完成'
]);
</script>

<style lang="less" scoped>
.withdrawal-timeline {
  margin: 20px 0;
}

.timeline-content {
  position: relative;
  padding-left: 20px;

  &::before {
    content: '';
    position: absolute;
    left: 6px;
    top: 8px;
    bottom: 8px;
    width: 2px;
    background-color: #0E69FF;
  }
}

.timeline-item {
  position: relative;
  padding-bottom: 30px;

  &:last-child {
    padding-bottom: 0;
  }
}

.timeline-dot {
  position: absolute;
  left: -20px;
  top: 5px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: #0E69FF;
}

.timeline-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}
</style>
