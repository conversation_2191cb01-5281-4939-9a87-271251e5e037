<template>
	<div class="withdrawal-records-container">
		<n-data-table
			:columns="columns"
			:data="data"
			:pagination="pagination"
			:bordered="false"
			:row-props="rowProps"
		/>
		<div v-if="errorMessage" class="error-message">
			{{ errorMessage }}
		</div>
	</div>
</template>

<script setup lang="ts">
import { h, computed, ref, onMounted } from "vue";
import {
	NDataTable,
	NTag,
	NButton,
	useMessage,
	NText,
	NPopover,
} from "naive-ui";
import { fetchGetJsConfig, fetchWithdrawal } from "../api";

declare const WeixinJSBridge: {
	invoke: (
		method: string,
		params: {
			appId: string;
			timeStamp: string;
			nonceStr: string;
			package: string;
			signType: string;
			paySign: string;
		},
		callback: (res: { err_msg: string }) => void
	) => void;
};

const errorMessage = ref("");
import type { DataTableColumns } from "naive-ui";
import dayjs from "dayjs";
import { useRequest } from "vue-hooks-plus";
/**
 *
 */
interface WithdrawalItem {
	id: number;
	cashOrder: string;
	amount: string;
	userId: number;
	orgUserId: number;
	status: number;
	nickname: string;
	desc: string;
	out_batch_no: string | null;
	charge: string | null;
	cashAmount: string | null;
	success_time: string | null;
	createdAt: string;
	updatedAt: string;
	deletedAt: string | null;
}

interface WithdrawalResponse {
	errcode: number;
	errmsg?: string;
	data?: {
		id: number;
		appId: string;
		package: string;
		mchId: string;
	};
}
interface JsConfigResponse {
	debug: boolean;
	appId: string;
	timestamp: string;
	nonceStr: string;
	signature: string;
	jsApiList: string[];
}
const isWeixinBrowser = () => {
	const ua = navigator.userAgent.toLowerCase();
	return ua.includes("micromessenger");
};
const wxPayData = ref({});
/**
 * {
        "debug": false,
        "appId": "wx5a823f77a13370e4",
        "timestamp": "1746758766",
        "nonceStr": "h0fdon4mry",
        "signature": "1e7856db4e3846953bd378e1e24cb4707485ffa0",
        "jsApiList": [
            "onMenuShareTimeline",
            "onMenuShareAppMessage",
            "requestMerchantTransfer"
        ]
    }
 */
const { run: runFetchGetJsConfig } = useRequest<
	JsConfigResponse,
	[{ url: string }]
>((args) => fetchGetJsConfig<JsConfigResponse>(args[0]), {
	manual: true,
	onSuccess: (data) => {
		// console.log(data);
		wxPayData.value = data;
		wx.config({
			debug: true, // 调试时可开启
			appId: data.appId,
			timestamp: Number(data.timestamp),
			nonceStr: data.nonceStr,
			signature: data.signature,
			jsApiList: data.jsApiList,
		});
	},
});

onMounted(() => {
	if (isWeixinBrowser()) {
		runFetchGetJsConfig({ url: window.location.href });
	}
});

const props = defineProps({
	dateRange: {
		type: [Array, null],
		default: null,
	},
	statusFilter: {
		type: [Number, null],
		default: null,
	},
	data: {
		type: Array as () => WithdrawalItem[],
		default: () => [],
	},
});

// 表格配置
const createColumns = (): DataTableColumns => {
	return [
		{
			title: "提现单号",
			key: "cashOrder",
			align: "center",
		},
		{
			title: "提现申请时间",
			key: "createdAt",
			align: "center",
			render(row: any) {
				return dayjs(row.createdAt).format("YYYY-MM-DD HH:mm:ss");
			},
		},
		{
			title: "提现金额",
			key: "amount",
			align: "center",
			render(row: any) {
				return h("span", { style: { color: "#0E69FF" } }, row.amount);
			},
		},
		{
			title: "手续费用",
			key: "charge",
			align: "center",
			render(row: any) {
				return h("span", { style: { color: "#0E69FF" } }, row.charge);
			},
		},
		{
			title: "到账金额",
			key: "cashAmount",
			align: "center",
			render(row: any) {
				return h("span", { style: { color: "#0E69FF" } }, row.cashAmount);
			},
		},
		{
			title: "到账时间",
			key: "success_time",
			align: "center",
			render(row: any) {
				return dayjs(row.createdAt).format("YYYY-MM-DD HH:mm:ss");
			},
		},
		{
			title: "状态",
			key: "status",
			align: "center",
			render(row: any) {
				const statusMap: Record<string, { text: string }> = {
					0: { text: "审核中" },
					2: { text: "审核成功" },
					3: { text: "审核失败" },
					4: { text: "提现失败" },
					5: { text: "已提现" },
				};
				const status = statusMap[row.status as string] || { text: "未知状态" };
				let color = "#333";
				if ([2, 5].includes(Number(row.status))) color = "#4CA737";
				else if ([0, 3, 4].includes(Number(row.status))) color = "#FF0808";

				const text = h(NText, { style: { color } }, status.text);

				if (Number(row.status) === 3 && row.desc) {
					return h(
						NPopover,
						{
							trigger: "hover",
							placement: "top",
						},
						{
							trigger: () => text,
							default: () => row.desc,
						}
					);
				}
				return text;
			},
		},
		{
			title: "操作",
			key: "actions",
			align: "center",
			render(row: any) {
				if (row?.status === 2) {
					return h(
						NButton,
						{
							type: "primary",
							style: { backgroundColor: "#0079FF", color: "#fff" },
							onClick: () => handleWithdraw(row),
						},
						{ default: () => "提现至账户" }
					);
				} else {
					return "-";
				}
			},
		},
	];
};

const columns = createColumns();

// 根据筛选条件过滤数据
const tableData = computed(() => {
	let filteredData = [...props.data];
	// 根据状态筛选
	if (props.statusFilter) {
		filteredData = filteredData.filter(
			(item) => (item as WithdrawalItem).status === props.statusFilter
		);
	}

	// 根据日期筛选
	if (
		props.dateRange &&
		Array.isArray(props.dateRange) &&
		props.dateRange[0] &&
		props.dateRange[1]
	) {
		const startDate = new Date(props.dateRange[0] as Date);
		const endDate = new Date(props.dateRange[1] as Date);

		filteredData = filteredData.filter((item) => {
			const itemDate = new Date((item as WithdrawalItem).createdAt);
			return itemDate >= startDate && itemDate <= endDate;
		});
	}

	return filteredData;
});

// 分页配置
const pagination = ref({
	page: 1,
	pageSize: 10,
	showSizePicker: true,
	pageSizes: [10, 20, 30, 40],
	onChange: (page: number) => {
		pagination.value.page = page;
	},
	onUpdatePageSize: (pageSize: number) => {
		pagination.value.pageSize = pageSize;
		pagination.value.page = 1;
	},
});

// 行样式
const rowProps = (row: any) => {
	return {
		style: "cursor: pointer;",
	};
};
// 提现处理
const message = useMessage();
const handleWithdraw = async (row: WithdrawalItem) => {
	errorMessage.value = "";

	// 检查是否在微信浏览器中
	if (!/micromessenger/i.test(navigator.userAgent)) {
		message.error("若需要进行提现操作，请确保您已登录微信环境");
		return;
	}

	// 检查提现金额
	const amount = parseFloat(row.amount);
	if (isNaN(amount) || amount <= 0) {
		errorMessage.value = "请输入有效的提现金额";
		return;
	}

	try {
		// 1. 申请提现获取支付参数
		const applyRes = (await fetchWithdrawal({
			id: row?.id,
		})) as WithdrawalResponse;
		if (applyRes.errcode !== 0) {
			errorMessage.value =
				applyRes.errmsg || `提现申请失败: ${applyRes.errcode}`;
			return;
		}

		// 2. 调用微信支付
		return new Promise<void>((resolve, reject) => {
			if (typeof WeixinJSBridge === "undefined") {
				errorMessage.value = "微信支付环境初始化失败";
				return reject(new Error("WeixinJSBridge not ready"));
			}

			if (!applyRes.data?.appId || !applyRes.data?.package) {
				errorMessage.value = "获取支付参数失败";
				return reject(new Error("Missing required payment parameters"));
			}

			wx.ready(() => {
				wx.checkJsApi({
					jsApiList: ["requestMerchantTransfer"],
					success: () => {
						WeixinJSBridge.invoke(
							"requestMerchantTransfer",
							{
								mchId: applyRes?.data?.mchId, // 需要配置商户号
								appId: applyRes?.data.appId,
								package: applyRes?.data.package,
							},
							(res: { err_msg: string }) => {
								if (res.err_msg === "requestMerchantTransfer:ok") {
									message.success("提现成功");
									resolve();
								} else if (res.err_msg === "requestMerchantTransfer:cancel") {
									errorMessage.value = "用户已取消支付";
									reject(new Error("User canceled payment"));
								} else {
									errorMessage.value = res.err_msg || "微信支付失败，请重试";
									reject(new Error(res.err_msg || "Payment failed"));
								}
							}
						);
					},
					fail: () => {
						errorMessage.value = "你的微信版本过低，请更新至最新版本";
						reject(new Error("WeChat version too low"));
					},
				});
			});

			wx.error((err: any) => {
				errorMessage.value = "微信支付配置失败";
				reject(new Error(`WeChat config failed: ${JSON.stringify(err)}`));
			});
		});
	} catch (error: any) {
		errorMessage.value = error.message || "提现过程中出现错误";
		console.error("提现错误:", error);
		throw error;
	}
};
</script>

<style lang="less" scoped>
.error-message {
	color: #ff5757;
	margin-top: 10px;
	text-align: center;
	font-size: 14px;
}
.withdrawal-records-container {
	width: 100%;
}

:deep(.n-data-table .n-data-table-th) {
	background-color: #f8f9ff;
	font-weight: 500;
}

:deep(.n-pagination) {
	margin-top: 16px;
	justify-content: center;
}
</style>
