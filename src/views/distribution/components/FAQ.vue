<template>
	<div class="faq-container">
		<div class="faq-header">
			<n-icon size="20" color="#0E69FF">
				<IconQuestion1 />
			</n-icon>
			<span>相关问题</span>
		</div>
		<div class="divider"></div>

		<!-- 提现流程时间线 -->

		<div class="faq-content">
			<div class="faq-item">
				<div class="faq-question">Q: 提现流程</div>
				<div class="faq-answer">
					提交提现申请后，需等待7天才能进入财务审核阶段；财务部门对提现申请进行审核，确保申请符合规定，审核周期为7天；审核通过后，至提现记录-提现至账户-资金到账				</div>
				<WithdrawalTimeline />
			</div>

			<div class="faq-item">
				<div class="faq-question">Q：佣金提现的规则</div>
				<div class="faq-answer">单次最高可提现200元，将按提现金额1%收取手续费</div>
			</div>

		</div>
	</div>
</template>

<script setup lang="ts">
import { NIcon } from "naive-ui";
import WithdrawalTimeline from "./WithdrawalTimeline.vue";
</script>

<style lang="less" scoped>
.faq-container {
	width: 300px;
	background: #fff;
	border-radius: 8px;
	padding: 20px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}
.divider {
	width: 100%;
	height: 1px;
	background: #e8e8e8;
	margin-bottom: 15px;
}
.faq-header {
	display: flex;
	align-items: center;
	gap: 8px;
	margin-bottom: 20px;
	font-size: 16px;
	color: #3d3d3d;
}

.faq-content {
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.faq-item {
	// border-bottom: 1px solid #f0f0f0;

	&:last-child {
		border-bottom: none;
		padding-bottom: 0;
	}
}

.faq-question {
	font-size: 16px;
	color: #0e69ff;
	margin-bottom: 8px;
}

.faq-answer {
	font-size: 14px;
	color: #696969;
	line-height: 1.5;
	margin-bottom: 12px;
}

.faq-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
}

.faq-tag {
	font-size: 12px;
	color: #0e69ff;
	background-color: rgba(14, 105, 255, 0.1);
	padding: 4px 8px;
	border-radius: 4px;
}
</style>
