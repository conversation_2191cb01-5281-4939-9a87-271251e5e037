<template>
	<div class="filter-bar">
		<span class="filter-label">时间</span>
		<n-date-picker
			v-model:value="dateRange"
			type="daterange"
			clearable
			:shortcuts="dateShortcuts"
			start-placeholder="开始日期"
			end-placeholder="结束日期"
			:default-value="defaultDateRange"
		/>
		<n-select
			v-if="activeTab === 'withdrawals'"
			v-model:value="statusFilter"
			:options="statusOptions"
			placeholder="全部状态"
			style="width: 120px"
		/>
	</div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { NDatePicker, NSelect } from "naive-ui";

const props = defineProps({
	activeTab: {
		type: String,
		required: true,
	},
	statusOptions: {
		type: Array,
		required: true,
	},
	defaultDateRange: {
		type: Array,
		default: () => [
			new Date(2025, 3, 1), // 2025-4-1
			new Date(2025, 3, 30), // 2025-4-30
		],
	},
});

const emit = defineEmits(["update:dateRange", "update:statusFilter"]);

const dateRange = ref(null);
const statusFilter = ref(null);

const dateShortcuts = {
	最近一周: () => {
		const end = new Date();
		const start = new Date();
		start.setDate(start.getDate() - 7);
		return [start, end];
	},
	最近一个月: () => {
		const end = new Date();
		const start = new Date();
		start.setMonth(start.getMonth() - 1);
		return [start, end];
	},
	最近三个月: () => {
		const end = new Date();
		const start = new Date();
		start.setMonth(start.getMonth() - 3);
		return [start, end];
	},
};

watch(dateRange, (newValue) => {
	emit("update:dateRange", newValue);
});

watch(statusFilter, (newValue) => {
	emit("update:statusFilter", newValue);
});
</script>

<style lang="less" scoped>
.filter-bar {
	display: flex;
	align-items: center;
	gap: 12px;
}

.filter-label {
	font-size: 14px;
	color: #666;
}
</style>
