<template>
	<div class="flex flex-row gap-x-[53px]">
		<span
			v-for="(item, index) in list"
			:key="index"
			class="text-[#474747] text-[20px] leading-[18px] mr-[10px] cursor-pointer"
			:class="{ '!text-[#0E69FF]': item.key === active }"
			@click="navigateTo(item.key)"
			>{{ item.title }}</span
		>
	</div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useRouter } from "vue-router";

interface Props {
	active: number;
}
const { active } = defineProps<Props>();
const router = useRouter();

const list = ref([
	{
		title: "分销中心",
		key: 1,
		path: "/distribution/center"
	},
	{
		title: "用户资产",
		key: 2,
		path: "/distribution/assets"
	},
]);

const navigateTo = (key: number) => {
	const item = list.value.find(item => item.key === key);
	if (item && item.path) {
		router.push(item.path);
	}
};
</script>

<style lang="less" scoped></style>
