<template>
	<div class="distribution-orders-container">
		<n-data-table
			:columns="columns"
			:data="data"
			:pagination="pagination"
			:bordered="false"
			:row-props="rowProps"
		/>
	</div>
</template>

<script setup lang="ts">
import { h, computed, ref } from "vue";
import { NDataTable, NTag } from "naive-ui";
import type { DataTableColumns } from "naive-ui";
import dayjs from "dayjs";

const props = defineProps({
	dateRange: {
		type: [Array, null],
		default: null,
	},
	statusFilter: {
		type: [String, null],
		default: null,
	},
	data: {
		type: Array as () => OrderItem[],
		default: () => [],
	},
});

interface OrderItem {
	orderNo: string;
	alias?: string;
	amount: number;
	brokerage: number;
	percent: number;
	payDate: string;
}

// 表格配置
const createColumns = (): DataTableColumns => {
	return [
		{
			title: "订单编号",
			key: "orderNo",
			align: "center",
		},
		{
			title: "购买会员类型",
			key: "alias",
			align: "center",
		},
		{
			title: "下单时间",
			key: "payDate",
			align: "center",
			render(row: any) {
				return dayjs(row.payDate).format("YYYY-MM-DD HH:mm:ss")
			}
		},
		{
			title: "付费金额",
			key: "amount",
			align: "center",
			render(row: any) {
				return h("span", { style: { color: "#3D3D3D" } }, row.amount);
			},
		},
		{
			title: "佣金",
			key: "brokerage",
			align: "center",
			render(row: any) {
				return h("span", { style: { color: "#0E69FF" } }, row.brokerage);
			},
		},
	];
};

const columns = createColumns();

// 根据筛选条件过滤数据
const tableData = computed(() => {
	let filteredData = [...props.data];

	// 根据日期筛选
	if (
		props.dateRange &&
		Array.isArray(props.dateRange) &&
		props.dateRange[0] &&
		props.dateRange[1]
	) {
		const startDate = new Date(props.dateRange[0] as Date);
		const endDate = new Date(props.dateRange[1] as Date);
		filteredData = filteredData.filter((item) => {
			const itemDate = new Date((item as OrderItem).payDate);
			return itemDate >= startDate && itemDate <= endDate;
		});
	}
	return filteredData;
});

// 分页配置
const pagination = ref({
	page: 1,
	pageSize: 10,
	showSizePicker: true,
	pageSizes: [10, 20, 30, 40],
	onChange: (page: number) => {
		pagination.value.page = page;
	},
	onUpdatePageSize: (pageSize: number) => {
		pagination.value.pageSize = pageSize;
		pagination.value.page = 1;
	},
});

// 行样式
const rowProps = (row: any) => {
	return {
		style: "cursor: pointer;",
	};
};
</script>

<style lang="less" scoped>
.distribution-orders-container {
	width: 100%;
}

:deep(.n-data-table .n-data-table-th) {
	background-color: #f8f9ff;
	font-weight: 500;
}

:deep(.n-pagination) {
	margin-top: 16px;
	justify-content: center;
}
</style>
