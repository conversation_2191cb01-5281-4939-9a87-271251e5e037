<template>
  <div class="link-promotion-container">
    <div class="promotion-title">链接推广</div>
    <div class="promotion-content">
      <div class="link-input-container">
        <input
          type="text"
          class="link-input"
          :value="url"
          readonly
        />
        <n-button
          class="copy-button"
          @click="copyLink"
          type="primary"
					style="height: 48px;width: 112px;--n-text-color-pressed: #fff;--n-text-color-focus: #fff;"
        >
          复制链接
        </n-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useMessage, NButton } from 'naive-ui';
import { copyToClipboard } from '@/utils/clipboard';

interface Props {
	url: string
}
const props = defineProps<Props>();
const message = useMessage();

// 推广链接，实际使用时应该从API或配置中获取

// 复制链接到剪贴板
const copyLink = async () => {
  try {
    await copyToClipboard(props?.url, message);
  } catch (error) {
    console.error('复制失败:', error);
    message.error('复制失败，请手动复制');
  }
};
</script>

<style lang="less" scoped>
.link-promotion-container {
  width: 100%;
  background: #fff;
  border-radius: 4px;
  padding: 22px 26px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  margin-top: 16px;

  .promotion-title {
    font-size: 14px;
    font-weight: 400;
    color: #3D3D3D;
    margin-bottom: 16px;
  }

  .promotion-content {
    width: 100%;
  }

  .link-input-container {
    display: flex;
    align-items: center;
    width: 100%;
    height: 48px;
    background: #f5f5f5;
    border-radius: 4px;
    padding-left: 16px;
  }

  .link-input {
    flex: 1;
    height: 100%;
    border: none;
    background: transparent;
    font-size: 14px;
    color: #666;
    outline: none;
  }

  .copy-button {
    margin-left: 10px;
  }
}
</style>
