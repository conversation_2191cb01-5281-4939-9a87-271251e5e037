<template>
	<div class="assets-tabs-container">
		<div class="tabs-header">
			<div class="tabs-nav">
				<div
					v-for="tab in tabs"
					:key="tab.name"
					class="tab-item"
					:class="{ active: activeTab === tab.name }"
					@click="handleTabChange(tab.name)"
				>
					{{ tab.label }}
				</div>
			</div>

			<div class="filter-container">
				<FilterBar
					v-if="activeTab === 'withdrawals'"
					:active-tab="activeTab"
					:status-options="withdrawalStatusOptions"
					@update:date-range="updateWithdrawalDateRange"
					@update:status-filter="updateWithdrawalStatusFilter"
				/>
				<FilterBar
					v-else
					:active-tab="activeTab"
					:status-options="orderStatusOptions"
					@update:date-range="updateOrderDateRange"
					@update:status-filter="updateOrderStatusFilter"
				/>
			</div>
		</div>

		<div class="tabs-content">
			<WithdrawalRecords
				v-if="activeTab === 'withdrawals'"
				:date-range="
					withdrawalParams.startDate && withdrawalParams.endDate
						? [
								new Date(withdrawalParams.startDate),
								new Date(withdrawalParams.endDate),
						  ]
						: null
				"
				:status-filter="withdrawalParams.status"
				:data="withdrawalList"
			/>
			<DistributionOrders
				v-else
				:date-range="
					orderParams.startDate && orderParams.endDate
						? [new Date(orderParams.startDate), new Date(orderParams.endDate)]
						: null
				"
				:status-filter="orderParams.status"
				:data="orderList"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref, nextTick } from "vue";
import WithdrawalRecords from "./WithdrawalRecords.vue";
import DistributionOrders from "./DistributionOrders.vue";
import FilterBar from "./FilterBar.vue";
import { fetchWithdrawList, fetchBrokerageOrders } from "../api";

interface WithdrawalItem {
	id: string;
	applyTime: string;
	amount: string;
	receiveTime: string;
	status: "pending" | "completed";
}

interface OrderItem {
	orderNo: string;
	alias?: string;
	amount: number;
	brokerage: number;
	percent: number;
	payDate: string;
}

interface ApiResponse<T> {
	rows: T[];
	total: number;
}

const activeTab = ref("withdrawals");

const tabs = [
	{ name: "withdrawals", label: "提现记录" },
	{ name: "orders", label: "分销订单" },
];

// 提现记录筛选参数
const withdrawalParams = ref({
	page: 1,
	pageSize: 10,
	startDate: undefined as string | undefined,
	endDate: undefined as string | undefined,
	status: null,
});

// 分销订单筛选参数
const orderParams = ref({
	page: 1,
	pageSize: 10,
	startDate: undefined as string | undefined,
	endDate: undefined as string | undefined,
	status: undefined as string | undefined,
});

const loading = ref(false);
const withdrawalList = ref<WithdrawalItem[]>([]);
//提现状态 0:”审核中”,2:”审核通过”,3:”审核失败”,4:”提现失败”,5:”提现成功”
const withdrawalStatusOptions = [
	{ label: "全部状态", value: null },
	{ label: "审核中", value: 0 },
	{ label: "审核成功", value: 2 },
	{ label: "审核失败", value: 3 },
	{ label: "提现失败", value: 4 },
	{ label: "已提现", value: 5 },
];

// 分销订单筛选器状态
const orderList = ref<OrderItem[]>([]);
const orderDateRange = ref<[Date, Date] | null>(null);
const orderStatusFilter = ref<string | null>(null);
const orderStatusOptions = [
	{ label: "全部状态", value: null },
	{ label: "待付款", value: 0 },
	{ label: "待发货", value: 2 },
	{ label: "待收货", value: 3 },
	{ label: "已完成", value: 4 },
	{ label: "已取消", value: 5 },
];

// 获取提现记录
const fetchWithdrawalData = async () => {
	try {
		loading.value = true;
		const res = await fetchWithdrawList<ApiResponse<WithdrawalItem>>(
			withdrawalParams.value
		);
		withdrawalList.value = (res as ApiResponse<WithdrawalItem>).rows || [];
	} catch (error) {
		console.error("获取提现记录失败:", error);
	} finally {
		loading.value = false;
	}
};

// 获取分销订单
const fetchOrderData = async () => {
	try {
		loading.value = true;
		const res = await fetchBrokerageOrders<ApiResponse<OrderItem>>(
			orderParams.value
		);
		orderList.value = (res as ApiResponse<OrderItem>).rows || [];
	} catch (error) {
		console.error("获取分销订单失败:", error);
	} finally {
		loading.value = false;
	}
};

const formatDate = (date: any) => {
	if (!date) return undefined;
	const d = date instanceof Date ? date : new Date(date);
	if (isNaN(d.getTime())) return undefined;

	const year = d.getFullYear();
	const month = String(d.getMonth() + 1).padStart(2, "0");
	const day = String(d.getDate()).padStart(2, "0");
	return `${year}-${month}-${day}`;
};
onMounted(() => {
	fetchWithdrawalData();
});

const handleTabChange = (tabName: string) => {
	activeTab.value = tabName;
	if (tabName === "withdrawals") {
		fetchWithdrawalData();
	} else {
		fetchOrderData();
	}
};

const updateWithdrawalDateRange = (value: [Date, Date] | null) => {
	withdrawalParams.value.startDate = value?.[0]
		? formatDate(value[0])
		: undefined;
	withdrawalParams.value.endDate = value?.[1]
		? formatDate(value[1])
		: undefined;
	fetchWithdrawalData();
};

const updateWithdrawalStatusFilter = (value: string | null) => {
	withdrawalParams.value.status = value;
	fetchWithdrawalData();
};

const updateOrderDateRange = (value: [Date, Date] | null) => {
	orderParams.value.startDate = value?.[0] ? formatDate(value[0]) : undefined;
	orderParams.value.endDate = value?.[1] ? formatDate(value[1]) : undefined;
	fetchOrderData();
};

const updateOrderStatusFilter = (value: string | null) => {
	orderParams.value.status = value || undefined;
	fetchOrderData();
};
const handleWithdrawalSuccess = () => {
	activeTab.value = "withdrawals";
	fetchWithdrawalData();
};

defineExpose({
	handleWithdrawalSuccess
});
</script>

<style lang="less" scoped>
.assets-tabs-container {
	width: 100%;
	background: #fff;
	border-radius: 8px;
	padding: 24px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.tabs-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	border-bottom: 1px solid #eee;
	padding-bottom: 10px;
}

.tabs-nav {
	display: flex;
	gap: 40px;
}

.tab-item {
	font-size: 16px;
	color: #666;
	cursor: pointer;
	padding-bottom: 10px;
	position: relative;
	font-weight: 500;

	&.active {
		color: #0e69ff;
		font-weight: 600;

		&::after {
			content: "";
			position: absolute;
			bottom: -1px;
			left: 0;
			width: 100%;
			height: 2px;
			background-color: #0e69ff;
		}
	}
}

.filter-container {
	display: flex;
	align-items: center;
}

.tabs-content {
	margin-top: 20px;
}
</style>
