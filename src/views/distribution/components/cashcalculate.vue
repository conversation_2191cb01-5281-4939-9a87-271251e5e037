<template>
	<span
		class="text-[#3D3D3D] text-[16px] font-[600] mt-[40px] mb-[20px] inline-block"
		>推广身份：品牌推广大使</span
	>
	<div class="common-calc">
		<div class="title">佣金计算方式</div>
		<!-- <div class="divider"></div> -->

		<div class="calc-list">
			<div class="calc-item">
				<!-- <div class="question">1、提交论文后多久获得检测报告？</div> -->
				<div class="answer" v-for="(item, index) in rules" :key="index">
					{{ item }}
				</div>
			</div>
		</div>
		<!-- <div class="flex items-center justify-center mt-[40px] mb-[25px]">
			<n-button
				class="h-[32px] w-[136px]"
				type="primary"
				style="--n-text-color-hover: #fff; width: 136px"
				>立即加入</n-button
			>
		</div> -->

		<!-- <div class="calc-item"> -->
		<!-- <div class="question">2、如何计算查重论文的字数？</div> -->
		<!-- <div class="answer">
					对话内客由人工智能大模型输出，仅供参考，并不代表平台立场
				</div>
			</div>

			<div class="calc-item">
				<div class="question">检测语种：</div>
				<div class="answer">中文+英文</div>
			</div>
			<div class="calc-item">
				<div class="question">论文纠错：</div>
				<div class="answer">独创算法加持，避免多次返稿</div>
			</div> -->
		<!-- </div> -->
	</div>
</template>

<script setup lang="ts">
import { NButton } from "naive-ui";
interface Props {
	rules: any;
}
const props = defineProps<Props>();
</script>

<style scoped lang="less">
.common-calc {
	max-width: 286px;
	padding: 20px;
	background: #fff;
	border-radius: 4px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

	.title {
		font-size: 16px;
		color: #121519;
		font-weight: 500;
		display: flex;
		align-items: center;
		margin-bottom: 15px;

		.icon {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 20px;
			height: 20px;
			border-radius: 50%;
			background-color: #1890ff;
			color: white;
			font-size: 14px;
			margin-right: 8px;
		}
	}

	.divider {
		width: 100%;
		height: 1px;
		background: #e8e8e8;
		margin-bottom: 15px;
	}

	.calc-list {
		.calc-item {
			margin-bottom: 15px;

			&:last-child {
				margin-bottom: 0;
			}

			.question {
				font-size: 16px;
				color: #121519;
				font-weight: 400;
				margin-bottom: 8px;
			}

			.answer {
				font-size: 14px;
				color: #666666;
				line-height: 1.5;
			}
		}
	}
}
</style>
