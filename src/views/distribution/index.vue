<template>
	<div class="distribution-container">
		<BindWechat
			v-model:visible="showBindWechat"
			@bind-success="handleBindWechat"
		/>
		<BindAccount
			v-model:visible="showBindAccount"
			@bind-success="handleBindAccount"
		/>

		<div class="flex flex-col gap-y-[20px] w-full max-w-[1180px] mx-auto">
			<div class="header">
				<div class="flex items-center justify-between w-full mb-[43px]">
					<div
						class="flex items-center cursor-pointer text-[#3D3D3D] text-[14px] leading-[18px] w-[100px]"
						@click="goBack"
					>
						<IconArrowLeft />
						<span class="ml-1">返回上一级</span>
					</div>
					<span
						class="font-[600] text-[40px] leading-[53px] mt-[83px] text-center inline-block w-full"
						>AIWork365推广员</span
					>
				</div>
			</div>
		</div>
		<div class="flex flex-row content-container mx-auto">
			<div class="max-w-[624px] max-h-[653px] left">
				<div class="w-[416px] h-[280px] aspect-auto brand"></div>
				<div class="w-[534px] h-[97px] aspect-auto step"></div>
				<div class="w-[calc(100%-22px)] h-[28px]  leading-[28px]  mt-[40px] text-center">
					<ScrollText
						v-if="latestMessage.length > 0"
						:current="2"
						:autoplay="true"
						:interval="2000"
						:delayUpdate="1500"
						:height="28"
					>
						<div
							v-for="(message, index) in latestMessage"
							class="flex items-center justify-center h-[28px]"
						>
							<NIcon :size="16" class="mr-[5px]">
								<NoticeSvg />
							</NIcon>
							<span class="text-gray-500 text-[14px]">*{{
								message
							}}</span>
						</div>
					</ScrollText>
				</div>
			</div>
			<div
				class="max-w-[553px] max-h-[653px] right py-[67px] flex flex-col gap-y-[40px]"
			>
				<div
					v-for="(item, index) in rules?.descs"
					:key="index"
					class="flex flex-col px-[60px]"
				>
					<span
						class="mb-[4px] font-[600] text-[14px] text-[#3D3D3D] relative before:absolute before:top-[5px] before:left-[-15px] before:content-[' '] before:h-[13px] before:w-[3px] before:bg-[#3E83F9]"
						>{{ item.title }}</span
					>
					<span class="text-[12px] text-[#666666]">{{ item.content }}</span>
				</div>
				<div
					class="flex flex-row flex-wrap gap-x-[14px] justify-center mt-[30px]"
				>
					<n-button
						class="h-[32px] w-[181px]"
						v-if="rules?.isShare"
						@click="goToCenter"
						style="--n-border: 1px solid #0e69ff; color: #0e69ff; width: 181px"
						>分销中心</n-button
					>
					<n-button
						v-else
						class="h-[32px] w-[181px]"
						type="primary"
						@click="joinNow"
						style="
							--n-text-color-hover: #fff;
							width: 181px;
							--n-text-color-pressed: #fff;
							--n-text-color-focus: #fff;
						"
						>立即加入</n-button
					>
					<div class="text-center w-[100%] mt-[16px]" v-if="!rules?.isShare">
						<span class="text-[#868686] text=-[12px] leading-[16px]"
							>点击立即加入即默认同意</span
						><a
							class="text-[#0E69FF] text=-[12px] leading-[16px] cursor-pointer"
							@click="showAgreement"
							>《AIWork365推广大使协议》</a
						>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { NButton, useMessage } from "naive-ui";
import BindWechat from "@/views/components/BindWechat.vue";
import BindAccount from "@/views/components/BindAccount.vue";
import { useRouter } from "vue-router";
import { useRequest } from "vue-hooks-plus";
import { fetchShareApply, fetchShareDetail } from "./api";
import { ShareDetail } from "./types";
import { ScrollText } from "@/components/scrollText";

const router = useRouter();
const message = useMessage();
const showBindWechat = ref(false);
const showBindAccount = ref(false);

const latestMessage = ref([
	"1分钟前156****1756已成为品牌推广大使",
	"2分钟前157****2156已成为品牌推广大使",
	"3分钟前158****0405已成为品牌推广大使",
	"5分钟前183****7266已成为品牌推广大使",
	"7分钟前183****9466已成为品牌推广大使",
	"8分钟前158****4662已成为品牌推广大使",
	"10分钟前137****5430已成为品牌推广大使",
	"11分钟前150****1708已成为品牌推广大使",
	"13分钟前155****0902已成为品牌推广大使",
	"15分钟前135****4486已成为品牌推广大使",
	"18分钟前189****2529已成为品牌推广大使",
	"25分钟前158****8758已成为品牌推广大使",
	"30分钟前183****4096已成为品牌推广大使",
]);

const { data: rules } = useRequest<ShareDetail>(fetchShareDetail);
const { run: runShareApply } = useRequest(fetchShareApply, {
	manual: true,
	onSuccess(data) {
		message.success("申请成功");
		router.push("/distribution/center");
	},
	onError(e, params) {
		// //errcode：********，需要登录；************，需要绑定手机号，************，需要绑定微信；************，需要付费
		if (e.errcode == ************) {
			showBindWechat.value = true;
		} else if (e.errcode == ************) {
			showBindAccount.value = true;
		} else if (e.errcode == ********) {
			// 需要登录
			window.$aiwork?.openLogin?.().then(() => {
				runShareApply();
			});
		} else if (e.errcode == ************) {
			// 需要付费
			// window.$aiwork?.openRecharge?.({ type: "ai" }).then(() => {
			// 	runShareApply();
			// });
		}
	},
});

const goBack = () => {
	history.back();
};

const goToCenter = () => {
	router.push("/distribution/center");
};

const joinNow = async () => {
	runShareApply();
};
const handleBindWechat = () => {
	// 登录成功后的处理逻辑
	showBindWechat.value = false;
	runShareApply();
};
const handleBindAccount = () => {
	// 登录成功后的处理逻辑
	showBindAccount.value = false;
	runShareApply();
};

const showAgreement = () => {
	const resolved = router.resolve("/distribution/agreement");
	window.open(resolved.href, "_blank");
};
</script>

<style lang="less" scoped>
.distribution-container {
	width: 100%;
	background: #fff;

	.header {
		margin-top: 34px;
	}

	.content-container {
		height: 653px;

		.left {
			background: url("@/assets/aiwork/images/distribution-bg.png");
			background-repeat: no-repeat;
			background-size: cover;
			width: 624px;
			height: inherit;
			display: flex;
			flex-direction: column;
			align-items: center;

			.brand {
				background: url("@/assets/aiwork/images/distribution-brand.png");
				background-repeat: no-repeat;
				background-size: contain;
				margin-top: 144px;
			}

			.step {
				background: url("@/assets/aiwork/images/distribution-step.png");
				background-repeat: no-repeat;
				background-size: contain;
				margin-top: 28px;
			}
		}

		.right {
			background: #f1f8ff;
			width: 553px;
		}
	}
}
</style>
