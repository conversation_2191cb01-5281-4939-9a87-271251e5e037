<template>
	<main
		class="h-full w-full mx-auto px-[50px] py-[50px] flex flex-col items-center bg-[#f8f9ff]"
	>
		<div class="w-full !max-w-[1600px]">
			<Header :active="1" />
			<div class="flex flex-row gap-x-[20px] justify-center">
				<div class="left flex-1">
					<span
						class="text-[#3D3D3D] text-[16px] font-[600] mt-[40px] inline-block"
						>请选择您的推广方式</span
					>
					<div class="promotion-options w-full">
						<LinkPromotion :url="data?.url" />
					</div>
					<!-- 海报推广 -->
					<div class="promotion-options w-full">
						<PosterPromotion :posters="data?.posters" :url="data?.url" />
					</div>
					<!-- Q&A -->
					<div class="divider"></div>
					<div class="question">如何发展下级会员？</div>
					<div class="answer">
						① 通过论坛、贴吧、QQ、抖音、小红书等平台发布您的推广链接/推广海报，用户点击推广链接/推广海报进入本站注册后，此用户即成为您的下级会员。
					</div>
					<div class="answer">
						② 品牌推广大使可获得下级会员消费金额相对应的佣金分成，单次最高可提现佣金200元。
					</div>
					<div class="answer">
						③ 更多推广玩法，可联系客服，添加我们专业老师带你一起赚钱。
					</div>
					<div class="divider"></div>
					<div class="question">佣金结算比例：</div>
					<div class="answer">
						① 根据下级会员购买的会员类型获得相应的佣金分成。
					</div>
					<div class="divider"></div>
					<div class="question">如何查看下级的订单情况？</div>
					<div class="answer">
						① PC端后台：可通过【用户资产】—【分佣订单】，查看所有下级会员订单支付详情。
					</div>
				</div>
				<div class="right">
					<CashCalculate :rules="data?.rules" />
				</div>
			</div>
		</div>
	</main>
</template>

<script setup lang="ts">
import Header from "./components/header.vue";
import LinkPromotion from "./components/LinkPromotion.vue";
import PosterPromotion from "./components/PosterPromotion.vue";
import CashCalculate from "./components/cashcalculate.vue";
import { useRequest } from "vue-hooks-plus";
import { fetchGetShareLink } from "./api";
import { ShareLink } from "./types";
const { data } = useRequest<ShareLink>(fetchGetShareLink);
</script>

<style lang="less" scoped>
.promotion-options {
	margin-top: 20px;
}
.divider {
	width: 100%;
	height: 1px;
	background: #e8e8e8;
	margin: 20px 0;
}
.question {
	font-size: 16px;
	color: #3d3d3d;
	margin-bottom: 8px;
}
.answer {
	font-size: 14px;
	color: #666666;
	line-height: 1.5;
}
</style>
