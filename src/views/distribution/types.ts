export interface ShareDetail {
	descs: Desc[];
	range: string;
	isShare: boolean; // 是否分銷商
}

interface Desc {
	title: string;
	content: string;
}
export interface ShareLink {
  url: string;
  posters: string[];
  rules: string[];
}
export interface Distribution {
  withdrawable: number;
  pending: number;
  withdrawing: number;
  withdrawn: number;
  total_amount: number;
}

export interface WithdrawList {
  count: number;
	rows: WithdrawListRow[];
}

interface WithdrawListRow {
  id: number;
  cashOrder: string;
  amount: string;
  userId: number;
  orgUserId: number;
  status: number;
  nickname: string;
  desc: string;
  out_batch_no: null;
  charge: null;
  cashAmount: null;
  success_time: null;
  createdAt: string;
  updatedAt: string;
  deletedAt: null;
}
export interface BrokerageOrders {
  count: number;
	rows: brokerageOrdersRow[];
}

interface brokerageOrdersRow {
  orderNo: string;
  amount: string;
  payDate: string;
  percent: null;
  brokerage: string;
  alias: string;
}
