<template>
	<main
		class="h-full w-full mx-auto px-[50px] py-[50px] flex flex-col items-center bg-[#f8f9ff]"
	>
		<div class="w-full !max-w-[1600px]">
			<Header :active="2" />
			<div class="flex flex-row gap-x-[20px] justify-center">
				<div class="left flex-1 mt-[40px]">
					<AssetsSummary
						:data="data"
						:user="user || userInfo"
						@withdrawal-success="handleWithdrawalSuccess"
					/>
					<div class="mt-[20px]">
						<AssetsTabs ref="assetsTabsRef" />
					</div>
				</div>
				<div class="right">
					<FAQ />
				</div>
			</div>
		</div>
	</main>
</template>

<script setup lang="ts">
import Header from "./components/header.vue";
import AssetsSummary from "./components/AssetsSummary.vue";
import AssetsTabs from "./components/AssetsTabs.vue";
import FAQ from "./components/FAQ.vue";
import { useRequest } from "vue-hooks-plus";
import { useUserStore } from "@/store";
import { getUser } from "@/store/modules/auth/helper";
import { fetchGetDistribution } from "./api";
import { Distribution } from "./types";
import { ref } from "vue";

const { data, run } = useRequest<Distribution>(fetchGetDistribution);
const { userInfo } = useUserStore();
const user = getUser();
const assetsTabsRef = ref<InstanceType<typeof AssetsTabs>>();

const handleWithdrawalSuccess = () => {
	run()
	assetsTabsRef.value?.handleWithdrawalSuccess();
};
</script>

<style lang="less" scoped>
.left {
	width: calc(100% - 320px);
}

.right {
	width: 300px;
	margin-top: 40px;
}
</style>
