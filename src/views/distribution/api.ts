import { post } from '@/utils/request'

export function fetchShareDetail<T>(params: any) {
	return post<T>({
		url: "/api3/aiwork/personal/shareDetail",
		data: params,
	});
}

// /aiwork/personal/shareApply
export function fetchShareApply<T>() {
	return post<T>({
		url: "/api3/aiwork/personal/shareApply",
	});
}


export function fetchGetShareLink<T>(params: any) {
	return post<T>({
		url: "/api3/aiwork/personal/getShareLink",
		data: params,
	});
}
// /aiwork/personal/getDistribution
export function fetchGetDistribution<T>(params: any) {
	return post<T>({
		url: "/api3/aiwork/personal/getDistribution",
		data: params,
	});
}
// 提现列表
export function fetchWithdrawList<T>(params: { page: number; pageSize: number, startDate?: string; endDate?: string; }) {
	return post<T>({
		url: "/api3/aiwork/personal/withdrawList",
		data: params,
	});
}
// 佣金订单列表
export function fetchBrokerageOrders<T>(params: { page: number; pageSize: number, startDate?: string; endDate?: string; }) {
	return post<T>({
		url: "/api3/aiwork/personal/brokerageOrders",
		data: params,
	});
}
// /aiwork/personal/withdrawalApply
// 用户提现申请
/**
 *
 * @param params
 * @returns
 * {
				"amount": 10, //提现金额
				"charge": 0.1, //手续费
				"cashAmount": 9.9 //到账金额
		}
				参数名	类型	说明
errcode	int	0成功。非0失败
 */
export function fetchWithdrawalApply<T>(params: { amount: number }) {
	return post<T>({
		url: "/api3/aiwork/personal/withdrawalApply",
		data: params,
	});
}

// /aiwork/personal/withdrawal
// 用户提现
// 需要在微信浏览器中
/**
 *
 * @param params id: 提现申请id
 * @returns
 * {
				"mchId": "1684127229",
				"appId": "wx5a823f77a13370e4",
				"package": "ABBQO+oYAAABAAAAAAC+XOAYCunstzJEpW0daBAAAADnGpepZahT9IkJjn90+1qg+M7MK4TU6bG38/sMmnYhG+3f4o/vgmRgs3bbn48OWlS8jRzRUh+lnnz9ukwcpUZTHT67O67Pmq7AZbjz7LQeTCr4SmI="
		}
		参数名	类型	说明
		errcode	int	0成功。非0失败

 */
export function fetchWithdrawal<T>(params: { id: string }) {
	return post<T>({
		url: "/api3/aiwork/personal/withdrawal",
		data: params,
	});
}

// /aiwork/personal/getJsConfig
/**
 * 获取微信支付js配置

 * @param params
 * @returns  {
				"debug": false,
				"appId": "wx5a823f77a13370e4",
				"timestamp": "1746758766",
				"nonceStr": "h0fdon4mry",
				"signature": "1e7856db4e3846953bd378e1e24cb4707485ffa0",
				"jsApiList": [
						"onMenuShareTimeline",
						"onMenuShareAppMessage",
						"requestMerchantTransfer"
				]
		}
 */
export function fetchGetJsConfig<T>(params: { url: string }) {
	return post<T>({
		url: "/api3/aiwork/personal/getJsConfig",
		data: params,
	});
}
