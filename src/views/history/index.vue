<script lang="ts" setup>
import { delCreationHistory, getHistoryList, markdownToDocx } from '@/chatgpt';
import { useRequest } from 'vue-hooks-plus';
import { NDataTable, NInput, NImage, NPagination, NEllipsis, useMessage, NDropdown, NButton } from 'naive-ui'
import { h, nextTick, onMounted, reactive, ref } from 'vue';
import FileImg from '@/assets/images/file.png';
import TrashImg from '@/assets/trash.png';
import WordImg from '@/assets/word.png';
import Word2Img from '@/assets/images/word.png';
import CopyImg from '@/assets/images/copy.png'
import PPTImg from '@/assets/ppt.png';
import { saveAs } from "file-saver";
import { copyToClipboard } from '@/utils/clipboard';

import dayjs from 'dayjs';
import { router } from '@/router';

const message = useMessage()
const paginationReactive = reactive({
    page: 1,
    pageCount: 1,
    pageSize: 20,
    orderBy: [['updatedAt', 'DESC']],
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    prefix(data) {
        console.log('data: ', data);
        const { itemCount } = data
        return `共${itemCount}条`
    },
    onChange: (page: number) => {
        paginationReactive.page = page
        run()
    },
    onUpdatePageSize: (pageSize: number) => {
        paginationReactive.pageSize = pageSize
        paginationReactive.page = 1
        run()
    },
    onSorterChange: (sorter: any) => {
        paginationReactive.orderBy = [[sorter.columnKey, sorter.order ? sorter.order === 'ascend' ? 'ASC' : 'DESC' : 'DESC']] as unknown as []
        run()
    }
})

const queryRef = ref('')

const data = ref<any[]>([])

const { run, loading } = useRequest(() => getHistoryList({
    content: queryRef.value,
    page: paginationReactive.page,
    pageSize: paginationReactive.pageSize,
    orderBy: paginationReactive.orderBy,
}), {
    manual: true,
    onSuccess: (res: any) => {
        data.value = res.rows
        paginationReactive.itemCount = res.count
        paginationReactive.pageCount = Math.ceil(res.count / paginationReactive.pageSize)
        if (!res.rows?.length && paginationReactive.page > 1) {
            paginationReactive.page -= 1
            run()
        }
    }
})

const { run: runMarkdownToDocx } = useRequest(markdownToDocx, {
    manual: true,
    onSuccess: (fileBlob: any, params) => {
        const blob = new Blob([fileBlob], {
            type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        })
        const downloadElement = document.createElement('a') //创建a标签
        const href = window.URL.createObjectURL(blob) //创建DOMString
        const filename = params?.[0]?.name || '文件' //文件名
        downloadElement.style.display = 'none' //隐藏a标签
        downloadElement.href = href //赋值a标签的href
        downloadElement.download = filename //下载后文件名
        document.body.appendChild(downloadElement) //插入a标签
        downloadElement.click() //点击下载
        document.body.removeChild(downloadElement) //下载完成移除元素
        window.URL.revokeObjectURL(href) //释放掉blob对象
    },
    onError: e => {
        console.log('e', e);

    }
})

const { run: runDelCreationHistory } = useRequest(delCreationHistory, {
    manual: true,
    onSuccess: (data) => {
        message.success('删除成功')
        run()
    },
    onError: (err) => {
        message.error(err.message)
    }
})


onMounted(() => {
    run()
})
const handleDownload = (row: any) => {
    runMarkdownToDocx({ chatId: row.id, name: row.Createbots.name })
}
const handleDownloadMarkdown = (row: any) => {
    console.log('row', row);
    const blob = new Blob([row.text], {  // 将row.content改为row.text
        type: "text/markdown;charset=utf-8"
    })
    const downloadElement = document.createElement('a')
    const href = window.URL.createObjectURL(blob)
    const filename = `${row?.Createbots?.name || '创作'}.md`
    downloadElement.style.display = 'none'
    downloadElement.href = href
    downloadElement.download = filename
    document.body.appendChild(downloadElement)
    downloadElement.click()
    document.body.removeChild(downloadElement)
}
const handleDel = (row) => {
    runDelCreationHistory({ chatId: row.id })
}

const handleCopyMarkdown = (row: any) => {
    copyToClipboard(row.text).then((success) => success ? message.success('复制Markdown成功') : message.error('复制失败, 请稍后重试'))
}

const handleCopyTxt = (row: any) => {
    // 将Markdown转换为纯文本（简单移除Markdown标记）
    const plainText = row.text.replace(/#+\s+/g, '').replace(/\*\*/g, '').replace(/\*/g, '').replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
    copyToClipboard(plainText).then((success) => success? message.success('复制Txt成功') : message.error('复制失败, 请稍后重试'))
}
let inputEnd = ref(false);

const compositionend = (event: Event) => {
    event.preventDefault();
    inputEnd.value = true;
};
const compositionstart = (event: Event) => {
    event.preventDefault();
    inputEnd.value = false;
};
const handleInput = (e: any) => {
    queryRef.value = e.target.value
}
const handleClick = () => {
    nextTick(() => {
        run()
    })
}
const handleGoToApps = (row: any) => {
    router.push(`/apps/78?appid=${row?.Createbots?.id}`)
}
const columns = ref<any>([
    {
        title: '类目',
        key: 'id',
        render: (row: any) => {
            return h('div', { class: 'flex flex-row gap-x-[26px] items-center justify-start' }, [
                h(NImage, { src: FileImg, class: 'w-[19px] h-[25px]' },),
                h('div', { class: 'flex flex-col gap-y-[5px]' }, [
                    h('div', { class: 'text-[#0E69FF]' }, { default: () => `创作-${row?.Createbots?.name || '未知'}` }),
                    h(NEllipsis, {
                        class: 'text-[#9E9E9E] !max-w-[400px]', tooltip: {
                            width: 500
                        }
                    }, { default: () => row.content })
                ])
            ]);
        }
    },
    // {
    //     title: "字数",
    //     key: 'total_tokens',
    //     render: (row: any) => {
    //         return h('div', { class: 'text-[#676767]' }, { default: () => row.total_tokens });
    //     }
    // },
    {
        title: '修改时间',
        key: 'updatedAt',
        sortOrder: 'descend',
        sorter: true,
        render: (row: any) => {
            return h('div', { class: 'text-[#9E9E9E]' }, { default: () => dayjs(row.updatedAt).format('YYYY/MM/DD HH:mm') });
        }
    },
    {
        title: "操作",
        key: "id",
        render: (row: any) => {
            // 导出下拉菜单选项
            const exportOptions = [
                {
                    label: () => h('div', { class: 'flex flex-row gap-x-[7px] items-center' }, [
												// h(NImage, { class: 'w-[14px] h-[14px]', src: WordImg }),
												h('span', {}, { default: () => '导出为Word' })
										]),
                    key: 'word',
                    props: {
                        onClick: () => handleDownload(row)
                    }
                },
                {
                    label: () => h('div', { class: 'flex flex-row gap-x-[7px] items-center' }, [
												// h(NImage, { class: 'w-[14px] h-[14px]', src: WordImg }),
												h('span', {}, { default: () => '导出为Markdown' })
										]),
                    key: 'markdown',
                    props: {
                        onClick: () => handleDownloadMarkdown(row)
                    }
                }
            ];

            // 复制下拉菜单选项
            const copyOptions = [
                {
                    label: '复制为Markdown',
                    key: 'copy-markdown',
                    props: {
                        onClick: () => handleCopyMarkdown(row)
                    }
                },
                {
                    label: '复制为Txt',
                    key: 'copy-txt',
                    props: {
                        onClick: () => handleCopyTxt(row)
                    }
                }
            ];

            return h('div', { class: 'flex flex-row gap-x-[32px] items-center justify-start' }, [
                // 导出下拉菜单
                h('div', { class: 'text-[#1970FE] cursor-pointer' }, [
                    h(NDropdown, { options: exportOptions, trigger: 'hover' }, {
                        default: () => h('div', { class: 'flex flex-row gap-x-[7px] items-center' }, [
                            h(NImage, { class: 'w-[14px] h-[14px]', src: WordImg }),
                            h('span', {}, { default: () => '导出' })
                        ])
                    })
                ]),
                // 复制下拉菜单
                h('div', { class: 'text-[#1970FE] cursor-pointer' }, [
                    h(NDropdown, { options: copyOptions, trigger: 'hover' }, {
                        default: () => h('div', { class: 'flex flex-row gap-x-[7px] items-center' }, [
                            h(NImage, { class: 'w-[14px] h-[14px]', src: CopyImg }),
                            h('span', {}, { default: () => '复制' })
                        ])
                    })
                ]),
                // 删除按钮
                h('div', { class: 'text-[#818181] cursor-pointer flex flex-row gap-x-[7px] items-center', onClick() { handleDel(row) } }, [
                    h(NImage, { class: 'w-[14px] h-[14px]', src: TrashImg }),
                    h('span', { class: '' }, { default: () => '删除' })
                ]),
            ]);
        }
    }
])
const rowKey = (rowData: any) => {
    return rowData.id
}
</script>

<template>
    <div class="h-[100%] pt-[85px] pb-[20px] px-[20px] bg-[#F5F7FF]">
        <div class="h-full w-full rounded-[4px] bg-[#fff] p-[40px]">
            <div class=" text-[#3D3D3D] text-[20px] leading-[26px] font-bold">创作历史</div>
            <div class="flex flex-row justify-end mt-[10px] mb-[26px]">
                <n-input placeholder="搜索名称" v-model:value="queryRef" class=" !w-[240px]" @input="handleInput"
                    @keydown.enter="handleClick" @compositionend="compositionend" @compositionstart="compositionstart">
                    <template #suffix>
                        <IconSearch class="cursor-pointer" @click="handleClick"></IconSearch>
                    </template>
                </n-input>
            </div>
            <NDataTable style="--n-merged-th-color: #fff;--n-merged-border-color:#E3EDFF;--n-th-text-color:#A6A6A6;"
                :bordered="false" :row-key="rowKey" :columns="columns" :data="data" :loading="loading"
                @update:sorter="paginationReactive.onSorterChange" />
            <div class=" flex flex-row justify-end mt-[20px]">
                <n-pagination v-if="paginationReactive.itemCount >= paginationReactive.pageSize"
                    :item-count="paginationReactive.itemCount" :page="paginationReactive.page"
                    :page-size="paginationReactive.pageSize" @update:page-size="paginationReactive.onUpdatePageSize"
                    @update:page="paginationReactive.onChange">
                    <template #prefix="{ itemCount }">
                        共 {{ itemCount }} 条
                    </template>
                </n-pagination>
            </div>
        </div>
    </div>

</template>
<style lang="less">
.n-data-table .n-data-table-th {
    border-top: 1px solid var(--n-merged-border-color);
}
</style>
