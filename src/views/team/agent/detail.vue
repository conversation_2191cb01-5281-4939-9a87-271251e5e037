<template>
	<div class="p-[20px]">
		<div class="flex justify-between items-center">
			<div class="flex items-center gap-1">
				<NButton type="primary" text style="--n-icon-size: 14px;" @click="$router.back()">
					<template #icon>
						<ArrowSvg class="rotate-90 " />
					</template>
					返回
				</NButton>
				<span>| {{ formModel.name }}</span>
			</div>
			<div class="flex gap-2">
				<NButton type="primary" size="large" @click="handleSave">保存</NButton>
				<NButton type="primary" size="large" @click="handlePublish">保存并发布</NButton>
			</div>
		</div>
		<div class="mt-4 bg-white" style="box-shadow: 0px 4px 9px 0px #EBEFFF;">
			<NForm ref="formRef" :model="formModel" label-placement="left" label-align="right" label-width="auto"
				require-mark-placement="right-hanging" :rules="rules">
				<div class="flex items-stretch">
					<div class="w-1/2">
						<NCard title="基础设置">
							<NFormItem label="智能体名称" required path="name">
								<NInput v-model:value="formModel.name" placeholder="请输入智能体名称"  show-count :maxlength="8" />
							</NFormItem>
							<NFormItem label="智能体图标">
								<div>
									<NUpload list-type="image-card" accept=".jpg,.png,.jpeg" :max="1" v-model:file-list="formModel.icon"
										:default-file-list="formModel.icon" />
									<div class="text-[14px] text-[#98A2B5] mt-4">建议尺寸120*120px 支持.jpg .png格式</div>
								</div>
							</NFormItem>
							<NFormItem label="智能体介绍">
								<NInput type="textarea" v-model:value="formModel.desc" show-count :maxlength="120"
									placeholder="请填写智能体介绍" />
							</NFormItem>
						</NCard>
						<NCard title="角色资料" class="">
							<!-- <template #header-extra>
								<NButton type="primary" style="--n-icon-size: 16px;" @click="() => showAiHelp = true">
									<template #icon>
										<AiSvg />
									</template>
									AI帮我写
								</NButton>
							</template> -->
							<NFormItem label="欢迎语" path="welcome_message">
								<NInput type="textarea" v-model:value="formModel.welcome_message" show-count :maxlength="120"
									placeholder="Hi, 我是您的专属AI助理，我可以帮您写文案、写方案、生成各种视频脚本，无聊的时候还可以陪您聊天哦！" />
							</NFormItem>
							<NFormItem label="角色设定(Prompt)">
								<NInput type="textarea" v-model:value="formModel.prompt" show-count :maxlength="1000"
									placeholder="请填写智能体介绍" :rows="10" />
							</NFormItem>
						</NCard>
					</div>
					<div class="w-1/2 ">
						<NCard title="对话设置" class="h-full">
							<NFormItem>
								<template #label>
									<div class="flex justify-end gap-2">
										<span>联想能力</span>
										<NTooltip>
											<template #trigger>
												<HelpSvg class="w-4 h-4 text-primary" />
											</template>
											值越大回复内容越具多样性、创造性、随机性。若需精准答复则选择最精准，有一定可选择平衡或强创造力
										</NTooltip>
									</div>
								</template>
								<NSlider v-model:value="formModel.associate" :default-value="2" :min="0" :max="2" step="mark"
									:marks="{ 0: '最精确', 0.5: '较精确', 1: '平衡', 1.5: '强创造力', 2: '天马行空' }" />
							</NFormItem>
							<NFormItem>
								<template #label>
									<div class="flex justify-end gap-2">
										<span>搜索相似度</span>
										<NTooltip>
											<template #trigger>
												<HelpSvg class="w-4 h-4 text-primary" />
											</template>
											高相似度推荐0.8及以上
										</NTooltip>
									</div>
								</template>
								<NSlider v-model:value="formModel.similarity" :min="0" :max="1" :step="0.1"
									:marks="{ 0: '0', 1: '1' }" />
							</NFormItem>
							<NFormItem>
								<template #label>
									<div class="flex justify-end gap-2">
										<span>单次搜索</span>
										<NTooltip>
											<template #trigger>
												<HelpSvg class="w-4 h-4 text-primary" />
											</template>
											建议搜索数量5-10
										</NTooltip>
									</div>
								</template>
								<NSlider v-model:value="formModel.searchQuantity" :min="1" :max="20" :step="1"
									:marks="{ 1: '1', 20: '20' }" />
							</NFormItem>
							<NFormItem label="绑定知识库">
								<NSelect v-model:value="formModel.knowledgeId" placeholder="请选择知识库" filterable
									:options="knowledgeOptions" :loading="knowledgeLoading" clearable remote @search="getKnowledgeList"
									@update-show="(open) => open && getKnowledgeList()"></NSelect>
							</NFormItem>
							<NFormItem>
								<template #label>
									<div class="flex justify-end gap-2">
										<span>对话间隔</span>
										<NTooltip>
											<template #trigger>
												<HelpSvg class="w-4 h-4 text-primary" />
											</template>
											如果超过对话间隔时间，会开启新的上下文。
										</NTooltip>
									</div>
								</template>
								<NSelect v-model:value="formModel.interval" :default-value="5" placeholder="请选择对话间隔" :options="intervalOptions"></NSelect>
							</NFormItem>
							<NFormItem label="提问示例">
								<div class="flex items-end w-full gap-2">
									<div class="flex-1">
										<template v-for="(item, index) in formModel.examples">
											<NFormItem :show-label="false">
												<div class="flex items-center gap-2 w-full">
													<NInput class="flex-1" v-model:value="formModel.examples[index]" />
													<NButton :disabled="index === 0" class="shrink-0 grow-0" @click="() => formModel.examples.splice(index, 1)">删除</NButton>
												</div>
											</NFormItem>
										</template>
									</div>
									<div v-if="formModel.examples.length < 2" class=" shrink-0 grow-0" style="margin-bottom: var(--n-feedback-height);">
										<NButton type="primary" @click="() => formModel.examples.push('')">+添加</NButton>
									</div>
								</div>
							</NFormItem>
						</NCard>
					</div>
				</div>
			</NForm>
		</div>
		<AiHelpFill v-model:show="showAiHelp" />
	</div>
</template>

<script setup lang="ts">
import { c, FormRules, NButton, NCard, NForm, NFormItem, NInput, NSelect, NSlider, NTooltip, NUpload, useMessage } from 'naive-ui';
import ArrowSvg from "@/assets/aiwork/svg/arrow.svg"
import AiSvg from '@/assets/aiwork/svg/ai.svg'
import HelpSvg from '@/assets/aiwork/svg/help.1.svg'
import { onMounted, ref } from 'vue';
import AiHelpFill from '../components/AiHelpFill.vue';
import { useRoute, useRouter } from 'vue-router';
import request from '@/utils/request';
import defaultAvatar from '@/assets/avatar.jpg'

const message = useMessage()

const $route = useRoute()
const $router = useRouter()

const formModel = ref<any>({
	examples: ['']
})
const showAiHelp = ref(false)
const knowledgeOptions = ref<any>([])
const knowledgeLoading = ref(false)

onMounted(async () => {
	if ($route.query.id) {
		await getDetailById($route.query.id as string)
	}
	getKnowledgeList({})
})

const intervalOptions = [
	{ label: "1", value: 1 },
	{ label: "2", value: 2 },
	{ label: "3", value: 3 },
	{ label: "4", value: 4 },
	{ label: "5", value: 5 },
]

const getDetailById = (id: string) => {
	return request({
		url: '/api3/aiwork/teamAgent/detail',
		method: "POST",
		data: { id }
	}).then(data => {
		Object.keys(data).map((key) => {
			if (['id', 'name', 'desc', 'welcome_message', 'prompt', 'associate', 'similarity', 'searchQuantity', 'interval', 'knowledgeId'].includes(key)) {
				formModel.value[key] = data[key]
			}
			if (key === 'examples') formModel.value.examples = data.examples?.length ? data.examples : ['']
			if (key === 'profile') formModel.value.icon = [{ url: data.profile || defaultAvatar, name: data.profile, id: data.profile, status: 'finished' }]
			if(key === 'interval' && !formModel.value.interval) formModel.value.interval = 5
		})
	})
}

const getKnowledgeList = (params?: any) => {
	knowledgeLoading.value = true
	const _params = {
		name: '',
		...params
	}
	request({
		url: '/api3/aiwork/knowledge/listAll',
		method: "POST",
		data: _params
	}).then(data => {
		knowledgeLoading.value = false
		knowledgeOptions.value = data.map(item => ({ label: item.name, value: item.id }))
		if(formModel.value.knowledgeId && !knowledgeOptions.value.some(item => item.value === formModel.value.knowledgeId)) formModel.value.knowledgeId = null
	})
}


const formRef = ref<any>()
const rules: FormRules = {
	name: {required: true, message: '请输入智能体名称', trigger: 'blur'},
	welcome_message: {required: true, message: '请输入欢迎语' , trigger: 'blur'}
}

const handleSave = async (showMessage = true) => {
	try{
		await formRef.value?.validate()
	} catch(err:any) {
		if(!err.length) return Promise.reject(err)
		err.map(item => item.map((error: any) => message.error(error.message)))
		return Promise.reject(err)
	}

	console.log(formModel.value)
	const formData = new FormData()
	Object.keys(formModel.value).forEach(key => {
		if (key === 'icon') formData.append('file', formModel.value['icon']?.[0].file)
		else if (key === 'examples') {
			formModel.value['examples'].forEach(str => {
				formData.append('examples', str)
			})
		}
		else if (formModel.value[key] != null) formData.append(key, formModel.value[key])
	})
	await request({
		url: '/api3/aiwork/teamAgent/update',
		method: "POST",
		data: formData,
		headers: {
			"content-type": 'multipart/form-data'
		}
	}).then(data => {
		showMessage &&message.success('保存成功')
	})
}
const handlePublish = async () => {
	try {
		await handleSave(false)
	} catch(err:any) {
		return
	}
	request({
		url: '/api3/aiwork/teamAgent/publish',
		method: "POST",
		data: { id: $route.query.id }
	}).then(() => {
		message.success('发布成功', {
			onAfterLeave() {
				$router.push('/team/agent')
			},
		})
	})
}
</script>

<style>
.n-message-container.n-message-container--top {
	top: 90px;
}
</style>
