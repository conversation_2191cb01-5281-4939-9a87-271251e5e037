<template>
	<div class="flex-1 min-h-0 py-[35px] px-[100px]">
		<h1 class="text-[18px] font-bold mb-[20px] text-title">专属智能体</h1>
		<div
			class="bg-white p-[18px] border-[1px] border-solid border-[#A7C7FF] mb-[20px] flex justify-between items-center gap-[10px] rounded-[10px]">
			<div>
				<p class="text-title text-[16px]">
					智能体介绍
				</p>
				<p class="text-[14px] mt-[10px] text-[#666666]">
					您可以创建专属于自己团队的智能体，实现智能对话、AI客服、AI虚拟员工、AI学习平台等场景。每个智能体可以关联所需的知识库，通过知识库不断的学习各场景所需的知识内容，打造独一无二的AI伙伴
				</p>
			</div>
			<NButton type="primary" size="large" class="w-[120px]" @click="$router.push('/team/knowledge')">立即搭建知识库</NButton>
		</div>

		<div class="agent-list gap-[10px]">
			<div class="agent-card flex-col justify-center items-center" @click="agentModal = { show: true, type: 'create' }">
				<div class="text-primary text-center">
					<div class="flex justify-center">
						<AddSvg class="w-[14px] h-[14px]" />
					</div>
					<div class="mt-[20px]">创建智能体</div>
				</div>
			</div>
			<div v-for="item in agentList" :key="item.id" class="agent-card" @click="$router.push(`/team/agent/detail?id=${item.id}`)">
				<div class="agent-card_content">
					<div class="flex gap-2 items-center">
						<NAvatar :size="48" :src="item.profile || defaultAvatar" />
						<div class="flex flex-col">
							<div class="text-[12px] text-white px-3 rounded-full" :class="{
								'bg-primary': item.isPublish,
								'bg-secondary': !item.isPublish
							}">{{
								item.isPublish ? '已发布' : '未发布' }}</div>
							<div class="text-[12px] mt-[5px] text-secondary">ID:{{ item.id }}</div>
						</div>
					</div>
					<div class="text-[16px] mt-[6px]">{{ item.name }}</div>
					<NEllipsis class="text-[#B8B8B8] mt-[6px]" :line-clamp="2" :tooltip="{
						contentStyle: {
							maxWidth: '300px',
							whiteSpace: 'normal',
							wordBreak: 'break-all'
						}
					}">{{ item.desc || '-' }}</NEllipsis>
				</div>
				<div class="agent-card_footer" @click.stop>
					<div class="w-full h-full flex justify-between items-center" v-if="item.isPublish" @click="handleToChat(item)">
						<span>开始对话</span>
						<ArrowSvg class="w-[16px] h-[16px]" />
					</div>
					<div class="h-full w-full flex justify-center items-center" v-if="!item.isPublish" @click="handlePublish(item)">去发布</div>
				</div>
				<NPopover :trigger="'hover'" :show-arrow="false" placement="bottom" class="!px-0" @click.stop>
					<template #trigger>
						<div class=" absolute right-[20px] top-[16px] text-[#B8B8B8]" @click.stop.prevent>
							<EllipsisSvg class="w-[24px] h-[24px]" />
						</div>
					</template>
					<div class="flex flex-col">
						<div
							class="w-full flex items-center justify-start py-[5px] px-[16px] bg-white rounded-[4px] hover:bg-gray-100 cursor-pointer"
							@click="$router.push(`/team/agent/detail?id=${item.id}`)">
							<!-- <EditSvg class="w-[16px] h-[16px]" /> -->
							<img :src="EditImage" class="w-[16px] h-[16px] mr-[14px]" />
							<span>编辑</span>
						</div>
						<div
							class="w-full flex items-center justify-start py-[5px] px-[16px] bg-white rounded-[4px] hover:bg-gray-100 cursor-pointer"
							@click="handlePublish(item)">
							<!-- <PlaneSvg class="w-[16px] h-[16px]" /> -->
							<img :src="PlaneImage" class="w-[16px] h-[16px] mr-[14px]" />
							<span>发布</span>
						</div>
						<div
							class="w-full flex items-center justify-start py-[5px] px-[16px] bg-white rounded-[4px] hover:bg-gray-100 cursor-pointer"
							@click="() => handleCopy(item)">
							<CopySvg class="w-[16px] h-[16px] mr-[14px]" />
							<span>复制</span>
						</div>
						<div
							class="w-full flex items-center justify-start py-[5px] px-[16px] bg-white rounded-[4px] hover:bg-gray-100 cursor-pointer" :class="{'text-gray-400': !item.isPublish}"
							@click="() => item.isPublish && $router.push(`/team/agent/chat/history?publishAgentId=${item.releaseId}`)">
							<MessageSvg class="w-[16px] h-[16px] mr-[14px]" />
							<span>对话记录</span>
						</div>
						<div
							class="w-full flex items-center justify-start py-[5px] px-[16px] bg-white rounded-[4px] hover:bg-gray-100 cursor-pointer text-[#FF5100]"
							@click="() => handleDelete(item)">
							<DeleteSvg class="w-[16px] h-[16px] mr-[14px]" />
							<span>删除</span>
						</div>
					</div>
				</NPopover>
			</div>
		</div>
		<div class="flex justify-end mt-4">
			<NPagination v-model:page="pagination.page" :page-size="pagination.pageSize" :page-count="pagination.pageCount"
				@update-page="pagination.onChange" @update-page-size="pagination.onUpdatePageSize"
				:page-sizes="[10, 20, 50, 100]" show-size-picker />
		</div>
		<CreateAgentModal v-model:show="agentModal.show" :type="agentModal?.type" :data="agentModal?.data"
			@on-submit="handleSubmitAgenModal" />
		<PublishAgent v-model:show="showPublishAgent" />
	</div>
</template>

<script setup lang="ts">
import AddSvg from '@/assets/aiwork/svg/add.svg'
import ArrowSvg from '@/assets/aiwork/svg/arrow.1.svg'
import EllipsisSvg from '@/assets/aiwork/svg/ellipsis.svg'
import defaultAvatar from '@/assets/avatar.jpg'
import EditImage from '@/assets/aiwork/images/edit.png'
import PlaneImage from '@/assets/aiwork/images/plane.png'
import CopySvg from '@/assets/aiwork/svg/copy.svg'
import MessageSvg from '@/assets/aiwork/svg/message.3.svg'
import DeleteSvg from '@/assets/aiwork/svg/delete.svg'
import { NAvatar, NButton, NDropdown, NEllipsis, NForm, NFormItem, NInput, NModal, NPagination, NPopover, NUpload, useDialog, useMessage } from 'naive-ui';
import { onMounted, reactive, ref } from 'vue'
import CreateAgentModal from '../components/CreateAgentModal.vue'
import PublishAgent from '../components/PublishAgent.vue'
import request from '@/utils/request'
import { useChatStore } from '@/store'


const dialog = useDialog()
const message = useMessage()
const chatStore = useChatStore()

const agentModal = ref<{
	show: boolean,
	type: 'create' | 'edit'
	data?: any
}>({
	show: false,
	type: 'create'
})
const showPublishAgent = ref(false)
const pagination = reactive({
	page: 1,
	pageSize: 20,
	pageCount: 1,
	itemCount: 1,
	onChange: (page: number) => {
		pagination.page = page
		getAgentList({ page })
	},
	onUpdatePageSize: (pageSize: number) => {
		pagination.pageSize = pageSize
		pagination.page = 1
		getAgentList({ page: 1 })
	}
})

const handleCopy = (item) => {
	request({
		url: '/api3/aiwork/teamAgent/copy',
		method: 'POST',
		data: { id: item.id }
	})
		.then(data => {
			message.success('复制成功')
			getAgentList({ page: 1 })
		})
}

const agentList = ref<any[]>([])

onMounted(() => getAgentList({}))

const getAgentList = (params: any) => {
	const _params = {
		...pagination,
		...params
	}
	request({
		url: '/api3/aiwork/teamAgent/list',
		method: "POST",
		data: _params
	}).then(data => {
		const { page, pageSize, ...rest } = _params
		pagination.page = page
		pagination.pageSize = pageSize
		pagination.pageCount = Math.ceil(data.count / pageSize)
		pagination.itemCount = data.count
		agentList.value = data.rows
	})
}

const handleSubmitAgenModal = (type, form) => {
	console.log(type, form)
	const formData = new FormData()
	formData.append('name', form.name)
	if(form.description)formData.append('desc', form.description)
	if (form.icon?.[0]) formData.append('file', form.icon[0].file)
	request({
		url: '/api3/aiwork/teamAgent/add',
		method: 'POST',
		data: formData,
	}).then(res => {
		message.success('创建成功')
		agentModal.value.show = false
		getAgentList({ page: 1 })
	})
}
const handlePublish = (item) => {
	dialog.warning({
		title: '发布智能体',
		content: '确定发布智能体吗？',
		positiveText: '确定',
		negativeText: '取消',
		onPositiveClick: () => {
			request({
				url: '/api3/aiwork/teamAgent/publish',
				method: "POST",
				data: { id: item.id }
			}).then(() => {
				message.success('发布成功')
				getAgentList({ page: 1 })
			})
		}
	})
}
const handleDelete = (item) => {
	dialog.warning({
		title: '删除智能体',
		content: '确定删除智能体吗？',
		positiveText: '确定',
		negativeText: '取消',
		onPositiveClick: () => {
			request({
				url: '/api3/aiwork/teamAgent/del',
				method: 'POST',
				data: { id: item.id }
			}).then(() => {
				message.success('删除成功')
				if(pagination.itemCount % pagination.pageSize <= 1) pagination.page = Math.max(pagination.page - 1, 1)
				getAgentList({page: pagination.page})
			})
		}
	})
}
const handleToChat = (agent) => {
	const history = chatStore.getAllHistory()
	const target = history.find(item => item.agentId === agent.releaseId)
	if(!target) {
		chatStore.addHistory({
			agentId: agent.releaseId,
			teamId: agent.teamId,
			title: '新建会话',
			uuid: new Date().getTime(),
			isEdit: false,
			type: 'chat',
			createTime: new Date().getTime()
		})
	}
	else {
		chatStore.setActive(target.uuid, 'chat')
	}
}
</script>

<style lang="less">
.agent-list {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(260px, 280px));
}

.agent-card {
	flex: 1;
	min-width: 260px;
	min-height: 180px;
	background-color: rgba(255, 255, 255, .8);
	backdrop-filter: blur(10px);
	box-shadow: 0px 2.31px 13.87px 0px rgba(218, 229, 255, 0.7);
	border-radius: 10px;
	padding: 20px;
	padding-bottom: 0;
	display: flex;
	flex-direction: column;
	cursor: pointer;
	transition: all 0.3s ease-in-out;
	position: relative;

	&:hover {
		background-color: rgba(255, 255, 255, .4);
		z-index: 99;
		transform: scale(1.02);
		box-shadow: 0px 2.31px 13.87px 0px rgb(170, 192, 246, .9);

		.agent-card_footer {
			background-color: #2F82FF;
			color: #fff;
		}
	}

	.agent-card_content {
		flex: 1;
	}

	.agent-card_footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 43px;
		border-top: 1px solid #EDEDED;
		transition: all 0.3s ease-in-out;
		background-color: transparent;
		color: #8F8F8F;
		margin-left: -20px;
		margin-right: -20px;
		padding: 0 20px;
		cursor: pointer;
		border-bottom-left-radius: 10px;
		border-bottom-right-radius: 10px;
		overflow: hidden;
	}
}
</style>
