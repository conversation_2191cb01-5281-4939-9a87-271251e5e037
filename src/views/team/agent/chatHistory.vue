<template>
	<div class="p-[20px] w-full">
		<div class="flex justify-between items-center">
			<div class="flex items-center gap-1">
				<NButton type="primary" text style="--n-icon-size: 14px;" @click="$router.back()">
					<template #icon>
						<ArrowSvg class="rotate-90 " />
					</template>
					返回
				</NButton>
				<span>| 对话记录</span>
			</div>
		</div>
		<div class="mt-[20px] bg-white w-full p-[30px]" style="box-shadow: 0px 4px 9px 0px #EBEFFF;">
			<div class="flex justify-between items-center mb-[30px]">
				<div class=" text-title font-bold text-[16px]">对话记录</div>
				<div class="flex items-center">
					<span class="mr-4 text-secondary">提问时间</span>
					<NDatePicker @update:formatted-value="handleDateChange" type="daterange" clearable />
				</div>
			</div>
			<NDataTable class="w-full" :columns="columns" :data="historyList" :bordered="false" :scroll-x="800"
				:pagination="pagination" remote />
		</div>
		<ChatHistoryEdit v-model:show="showHistoryEdit" />
		<NModal v-model:show="chatRecordModal">
			<div class="bg-white rounded-md p-[20px]">
				<div>对话历史</div>
				<div class="mt-3 bg-[#F7F7F7] rounded-md w-[600px] max-h-[600px] overflow-y-auto px-3 py-6">
					<template v-for="item in chatRecordList">
						<!-- 提问 -->
						<div class="flex justify-end items-start gap-2 mb-4">
							<div class="flex flex-col items-end max-w-[80%]">
								<div class="text-secondary text-[12px]">{{ dayjs(item.createdAt).format('YYYY-MM-DD HH:mm:ss') }}</div>
								<div class="text-[14px] bg-primary px-5 py-4 text-white rounded-md">{{ item.prompt }}</div>
							</div>
							<NAvatar class=" shrink-0" size="small" rounded :src="defaultAvatar" />
						</div>
						<!-- 回答 -->
						<div class="flex justify-start items-start gap-2 mb-4">
							<NAvatar class=" shrink-0" ze="small" rounded :src="defaultAvatar" />
							<div class="flex flex-col items-start max-w-[80%]">
								<div class="text-[12px] text-secondary">{{ dayjs(item.updatedAt).format('YYYY-MM-DD HH:mm:ss') }}</div>
								<div class="text-title text-[14px] !bg-white px-5 py-4 rounded-md markdown-body" v-html="item.content"></div>
							</div>
						</div>
					</template>
				</div>
				<div class="flex justify-center mt-4">
					<NButton type="primary" round @click="chatRecordModal = false" style="--n-width: 230px;">确定</NButton>
				</div>
			</div>
		</NModal>
	</div>
</template>

<script setup lang="ts">
import { NAvatar, NButton, NDataTable, NDatePicker, NEllipsis, NModal } from 'naive-ui';
import ArrowSvg from "@/assets/aiwork/svg/arrow.svg"
import { h, onMounted, reactive, ref } from 'vue';
import { TableColumn } from 'naive-ui/es/data-table/src/interface';
import ChatHistoryEdit from '../components/ChatHistoryEdit.vue';
import request from '@/utils/request';
import dayjs from 'dayjs';
import { useRoute } from 'vue-router';
import defaultAvatar from '@/assets/avatar.jpg'
import MarkdownIt from 'markdown-it';

const showHistoryEdit = ref(false)
const route = useRoute()
const chatRecordModal = ref(false)

const pagination = reactive({
	page: 1,
	pageSize: 20,
	showSizePicker: true,
	pageSizes: [10, 20, 50, 100],
	pageCount: 1,
	itemCount: 1,
	onChange: (page: number) => {
		pagination.page = page
		getHistoryList({})
	},
	onUpdatePageSize: (pageSize: number) => {
		pagination.pageSize = pageSize
		pagination.page = 1
		getHistoryList({})
	}
})

const handleDateChange = (value: string[]) => {
	const [startDate, endDate] = value
	getHistoryList({startDate, endDate})
}

onMounted(() => getHistoryList({}))

const historyList = ref<any[]>([])
const searchParams = ref<any>({})
const getHistoryList = (params) => {
	const _params = {
		page: pagination.page,
		pageSize: pagination.pageSize,
		startTime: '',
		endTime: '',
		publishAgentId: route.query.publishAgentId,
		...searchParams.value,
		...params
	}
	request({
		url: '/api3/aiwork/teamHistory/list',
		method: 'POST',
		data: _params
	}).then(data => {
		historyList.value = data.rows
		pagination.page = _params.page
		pagination.pageSize = _params.pageSize
		pagination.pageCount = Math.ceil(data.count / _params.pageSize)
		pagination.itemCount = data.count
		searchParams.value = _params
	})
}

const columns: TableColumn[] = [
	{
		title: '提问',
		key: 'prompt',
		width: 300,
		fixed: 'left',
		ellipsis: {
			lineClamp: 2,
		}
	},
	{
		title: '回答',
		key: 'content',
		width: 450,
		ellipsis: {
			lineClamp: 2,
			tooltip:{
				contentStyle: {
					maxWidth: '1000px',
					whiteSpace: 'normal',
					wordBreak: 'break-all'
				}
			}
		}
	},
	{
		title: '提问者',
		key: 'asker',
		width: 80,
		render(row: any) {
			return h(NEllipsis, {}, row.User?.nickname || '-')
		}
	},
	{
		title: '提问时间',
		key: 'updatedAt',
		width: 150,
		render(row: any) {
			return h(NEllipsis, {}, dayjs(row.updatedAt).format('YYYY-MM-DD HH:mm:ss'))
		}
	},
	{
		title: '操作',
		key: 'actions',
		width: 150,
		fixed: 'right',
		render(row) {
			return h('div', { class: 'flex gap-3' }, [
				// h(NButton, { type: 'primary', text: true, onClick: () => showHistoryEdit.value = true }, '订正'),
				h(NButton, { type: 'primary', text: true, onClick: () => getChatRecordList(row.id) }, '对话历史')
			])
		}
	}
]

const chatRecordList = ref<any[]>([])
const getChatRecordList = (id) => {
	chatRecordModal.value = false
	chatRecordList.value = []
	request({
		url: '/api3/aiwork/teamHistory/history',
		method: 'POST',
		data: {
			publishAgentId: route.query.publishAgentId,
			id
		}
	}).then(data => {
		chatRecordModal.value = true
		chatRecordList.value = data.map(item => ({
			...item,
			content: new MarkdownIt().render(item.content)
		}))
	})
}

</script>

<style>
@import "@/views/chat/components/Message/style.less";
</style>
