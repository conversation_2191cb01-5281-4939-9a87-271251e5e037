<template>
	<Layout extend>
		<div class="relative">
			<div class="backdrop-blur-md w-screen sm:overflow-hidden">
				<div class="page-content max-w-[1400px] w-[90%] sm:w-[calc(100vw-32px)] mx-auto">

					<div class="hero pt-[50px] pb-6 flex items-center sm:flex-col">
						<div class="flex flex-col min-w-[513px] shrink-0 justify-between items-start sm:w-full sm:overflow-hidden sm:min-w-full sm:items-center">
							<h1 class="text-[44px] font-bold sm:text-[32px]">AIWork365 团队版</h1>
							<p class="mt-6 text-[24px] sm:text-[18px] sm:whitespace-pre-wrap sm:break-words">让您轻松拥有专属的AIGC系统和企业级AI平台<br class="sm:hidden" />开箱即用释放团队潜能，重塑工作流程</p>
							<div
								class="mt-[60px] gradient-btn text-[20px] px-[25px] cursor-pointer flex items-center gap-2 transition-all duration-300 ease-in-out hover:gap-4 hover-shadow sm:mt-4 sm:text-[16px] sm:!h-[40px]" @click="heroClick">
								<span>点击搭建您的专属AI团队</span>
								<Arrow1Svg class="w-[20px] h-[20px]" />
							</div>
						</div>
						<div class="flex justify-end relative sm:mt-4">
							<img :src="teamLandingImage" class="w-full max-w-[813px] grow-0 z-[1] hero_image" />
							<div class="absolute w-[200px] h-[200px] z-0 -left-[20px] bottom-0 rounded-full"
								style="background: linear-gradient(56deg, #8FB2FF 17%, rgba(250, 225, 255, 0.1) 86%);"></div>
						</div>
					</div>

					<div class="pt-[80px]">
						<h2 class="text-center text-[32px] font-bold">企业级智能体平台 构建全业务场景智能体验</h2>
						<div class="flex flex-col gap-[20px] mt-[60px]">
							<div class="flex gap-[20px] sm:flex-wrap">
								<div
									class="hover-shadow flex-1 rounded-[10px] bg-[#577EFF] p-[44px] text-white relative flex flex-col items-start gap-[26px] cursor-pointer sm:w-full" @click="heroClick">
									<img src="@/assets/aiwork/images/team-landing-2.png" class="w-[246px] absolute right-4 top-4 z-0" />
									<div class="text-[28px] font-bold z-[1]">AI+企业知识库</div>
									<div class="text-[18px] z-[1]">通过智能体不断学习所需的知识建立企业内部专属知识库，并通过聊<br />天对话模式快速查阅各种内部资料和文档</div>
									<div class="flex gap-[10px] z-[1]">
										<div class="px-2 py-3 rounded-md text-[#1C52FF]"
											style="background-color: rgba(255, 255, 255, 0.78);">多维度知识接入</div>
										<div class="px-2 py-3 rounded-md text-[#1C52FF]"
											style="background-color: rgba(255, 255, 255, 0.78);">AI自动学习</div>
										<div class="px-2 py-3 rounded-md text-[#1C52FF]"
											style="background-color: rgba(255, 255, 255, 0.78);">实时智能搜素</div>
									</div>
									<div
										class="hover-shadow w-[42px] h-[42px] rounded-full bg-[#040E2A] text-white flex justify-center items-center absolute right-[30px] bottom-[44px] cursor-pointer hover:bg-white hover:text-title">
										<ArrowSvg class="w-[16px] h-[16px] -rotate-90 cursor-pointer" />
									</div>
								</div>
								<div class="hover-shadow flex-1 rounded-[10px] p-[44px] relative flex flex-col items-start gap-[26px] cursor-pointer"
									style="background: linear-gradient(180deg, #F3F8FF 0%, #DDEBFF 100%);" @click="heroClick">
									<img src="@/assets/aiwork/images/team-landing-1.png" class="w-[128px] absolute right-4 top-4 z-0" />
									<div class="text-[28px] font-bold z-[1]">AI+营销</div>
									<div class="text-[18px] z-[1]">利用AI智能体快捷生成朋友圈文案、SE0文章、自媒体营销内容、<br />短视频脚本、电商文案等，效率开挂</div>
									<div class="flex gap-[10px] z-[1]">
										<div class="px-2 py-3 rounded-md text-title" style="background: rgba(255, 255, 255, 0.64);">提高创作效率
										</div>
										<div class="px-2 py-3 rounded-md text-title" style="background: rgba(255, 255, 255, 0.64);">
											营销内容自动化生产</div>
										<div class="px-2 py-3 rounded-md text-title" style="background: rgba(255, 255, 255, 0.64);">实时智能搜索
										</div>
									</div>
									<div
										class="hover-shadow w-[42px] h-[42px] rounded-full bg-white text-title flex justify-center items-center absolute right-[30px] bottom-[44px] cursor-pointer">
										<ArrowSvg class="w-[16px] h-[16px] -rotate-90" />
									</div>
								</div>
							</div>

							<div class="flex gap-[20px] items-stretch sm:flex-col">
								<div
									class="hover-shadow flex-1 rounded-[10px] bg-[#E3F7EA] p-[44px] text-title relative flex flex-col items-start justify-between gap-[26px] cursor-pointer" @click="heroClick">
									<div class="text-[28px] font-bold z-[1]">AI+智能化<span class="text-[#34C868]">客服</span></div>
									<div class="text-[18px] z-[1]">一键直连企业知识库实时回答，预先构建知识库<br />提供客户服务的系统，打造企业智能化客服</div>
									<div class="flex gap-[10px] z-[1]">
										<div class="px-2 py-3 rounded-md text-title" style="background: rgba(255, 255, 255, 0.8);">降低企业客服成本
										</div>
										<div class="px-2 py-3 rounded-md text-title" style="background: rgba(255, 255, 255, 0.8);">7*24小时在线
										</div>
									</div>
									<div
										class="hover-shadow w-[42px] h-[42px] rounded-full bg-white text-title flex justify-center items-center absolute right-[30px] top-[44px] cursor-pointer">
										<ArrowSvg class="w-[16px] h-[16px] -rotate-90" />
									</div>
								</div>
								<div
									class="hover-shadow flex-1 rounded-[10px] bg-[#D2EEFF] p-[44px] text-title relative flex flex-col items-start justify-between gap-[26px] cursor-pointer" @click="heroClick">
									<div class="text-[28px] font-bold z-[1]">AI+金牌<span class="text-[#1189D1]">销售</span></div>
									<div class="text-[18px] z-[1]">基于产品知识库生成更适合成单的销售话术，智能分析+精准营销，一站式探明客户意向与提升销售转化</div>
									<div class="flex gap-[10px] z-[1]">
										<div class="px-2 py-3 rounded-md text-title" style="background: rgba(255, 255, 255, 0.8);">销售话术库
										</div>
										<div class="px-2 py-3 rounded-md text-title" style="background: rgba(255, 255, 255, 0.8);">提升专业能力
										</div>
										<div class="px-2 py-3 rounded-md text-title" style="background: rgba(255, 255, 255, 0.8);">赢单提效
										</div>
									</div>
									<div
										class="hover-shadow w-[42px] h-[42px] rounded-full bg-white text-title flex justify-center items-center absolute right-[30px] top-[44px] cursor-pointer">
										<ArrowSvg class="w-[16px] h-[16px] -rotate-90" />
									</div>
								</div>
								<div
									class="hover-shadow flex-1 rounded-[10px] bg-[#DEE2FF] p-[44px] text-title relative flex flex-col items-start justify-between gap-[26px] cursor-pointer" @click="heroClick">
									<div class="text-[28px] font-bold z-[1]">AI+虚拟<span class="text-[#394EE8]">员工</span></div>
									<div class="text-[18px] z-[1]">利用AI智能体及知识库，可创建多个数字员工,TA拥有全能知识体系，大幅降低企业的员工成本，提升工作效率</div>
									<div class="flex gap-[10px] z-[1]">
										<div class="px-2 py-3 rounded-md text-title" style="background: rgba(255, 255, 255, 0.8);">职场新助手
										</div>
										<div class="px-2 py-3 rounded-md text-title" style="background: rgba(255, 255, 255, 0.8);">企业数智化转型
										</div>
									</div>
									<div
										class="hover-shadow w-[42px] h-[42px] rounded-full bg-white text-title flex justify-center items-center absolute right-[30px] top-[44px] cursor-pointer">
										<ArrowSvg class="w-[16px] h-[16px] -rotate-90" />
									</div>
								</div>

							</div>

							<div class=" relative"  @click="heroClick">
								<div class="flex absolute left-0 top-0 w-full h-full sm:hidden">
									<img src="@/assets/aiwork/images/sec5-bj-1.png" object-fit="cover" class="flex-1 h-full w-[80%]" />
									<img src="@/assets/aiwork/images/sec5-bj-2.png" class="h-full w-[119px] grow-0 shrink-0" />
								</div>
								<div class="hidden sm:block absolute left-0 top-0 w-full h-full rounded-md overflow-hidden" style="background: linear-gradient(90deg, #040E2B 0%, #24153E 100%);"></div>
								<div class="flex py-[37px] pl-[44px] text-white items-center rounded-md z-[1] relative overflow-hidden sm:flex-col whitespace-nowrap sm:p-4"
									style="">
									<div class="w-2/5 sm:w-full">
										<div class="text-[28px] font-bold">AI+更多能力</div>
										<div class="mt-[34px] sm:whitespace-pre-wrap sm:break-words">推动企业管理和业务全流程自动化，智能化地思考分解任务、处理复杂<br class="sm:hidden" />任务、解决问题，形成业务行动力，重塑企业业务模式…</div>
									</div>
									<div class="flex-1 flex flex-col gap-4 sm:mt-4 ml-[40px] sm:ml-0">
										<div class="flex gap-[20px] sm:flex-wrap sm:grid sm:grid-cols-2"
											style="animation: sec-btn-tranlate 4s ease-in-out infinite forwards alternate;">
											<div
												class="section-btn flex gap-[6px] items-center px-3 py-2 pr-5 rounded-3xl text-[16px] bg-white opacity-30 transition-all duration-300 ease-in-out hover:opacity-100 text-title cursor-pointer"
												:class="{ active: sectionCount === 1 }">
												<img src="@/assets/aiwork/images/team-landing-icon-1.png" class="w-[34px] h-[34px]" />
												<div>AI+商业服务</div>
											</div>
											<div
												class="section-btn flex gap-[6px] items-center px-3 py-2 pr-5 rounded-3xl text-[16px] bg-white opacity-30 transition-all duration-300 ease-in-out hover:opacity-100 text-title cursor-pointer"
												:class="{ active: sectionCount === 2 }">
												<img src="@/assets/aiwork/images/team-landing-icon-2.png"
													class="w-[34px] h-[34px] rotate-[19deg]" />
												<div>AI+电商</div>
											</div>
											<div
												class="section-btn flex gap-[6px] items-center px-3 py-2 pr-5 rounded-3xl text-[16px] bg-white opacity-30 transition-all duration-300 ease-in-out hover:opacity-100 text-title cursor-pointer"
												:class="{ active: sectionCount === 3 }">
												<img src="@/assets/aiwork/images/team-landing-icon-3.png"
													class="w-[34px] h-[34px] -rotate-[27deg]" />
												<div>AI+文本创作</div>
											</div>
											<div
												class="section-btn flex gap-[6px] items-center px-3 py-2 pr-5 rounded-3xl text-[16px] bg-white opacity-30 transition-all duration-300 ease-in-out hover:opacity-100 text-title cursor-pointer"
												:class="{ active: sectionCount === 4 }">
												<img src="@/assets/aiwork/images/team-landing-icon-4.png"
													class="w-[34px] h-[34px] -rotate-[27deg]" />
												<div>AI+金融</div>
											</div>
										</div>
										<div class="flex gap-[20px] translate-x-10 sm:grid sm:grid-cols-2 sm:translate-x-0"
											style="animation: sec-btn-tranlate 4s ease-in-out infinite forwards alternate; animation-delay: -2s;">
											<div
												class="section-btn flex gap-[6px] items-center px-3 py-2 pr-5 rounded-3xl text-[16px] bg-white opacity-30 transition-all duration-300 ease-in-out hover:opacity-100 text-title cursor-pointer"
												:class="{ active: sectionCount === 5 }">
												<img src="@/assets/aiwork/images/team-landing-icon-5.png" class="w-[34px] h-[34px]" />
												<div>AI+效率助手</div>
											</div>
											<div
												class="section-btn flex gap-[6px] items-center px-3 py-2 pr-5 rounded-3xl text-[16px] bg-white opacity-30 transition-all duration-300 ease-in-out hover:opacity-100 text-title cursor-pointer"
												:class="{ active: sectionCount === 6 }">
												<img src="@/assets/aiwork/images/team-landing-icon-6.png"
													class="w-[34px] h-[34px] rotate-[19deg]" />
												<div>AI+图书出版</div>
											</div>
											<div
												class="section-btn flex gap-[6px] items-center px-3 py-2 pr-5 rounded-3xl text-[16px] bg-white opacity-30 transition-all duration-300 ease-in-out hover:opacity-100 text-title cursor-pointer"
												:class="{ active: sectionCount === 7 }">
												<img src="@/assets/aiwork/images/team-landing-icon-7.png"
													class="w-[34px] h-[34px] -rotate-[27deg]" />
												<div>AI+教育学习</div>
											</div>
											<div
												class="section-btn flex gap-[6px] items-center px-3 py-2 pr-5 rounded-3xl text-[16px] bg-white opacity-30 transition-all duration-300 ease-in-out hover:opacity-100 text-title cursor-pointer"
												:class="{ active: sectionCount === 8 }">
												<img src="@/assets/aiwork/images/team-landing-icon-8.png"
													class="w-[34px] h-[34px] -rotate-[27deg]" />
												<div>AI+翻译</div>
											</div>
										</div>
										<div class="flex gap-[20px] -translate-x-4 sm:grid sm:grid-cols-2 sm:translate-x-0"
											style="--ani-x: 70px;animation: sec-btn-tranlate 4s ease-in-out infinite forwards alternate; animation-delay: -1s; animation-duration: 3s;">
											<div
												class="section-btn flex gap-[6px] items-center px-3 py-2 pr-5 rounded-3xl text-[16px] bg-white opacity-30 transition-all duration-300 ease-in-out hover:opacity-100 text-title cursor-pointer"
												:class="{ active: sectionCount === 9 }">
												<img src="@/assets/aiwork/images/team-landing-icon-9.png" class="w-[34px] h-[34px]" />
												<div>AI+法务</div>
											</div>
											<div
												class="section-btn flex gap-[6px] items-center px-3 py-2 pr-5 rounded-3xl text-[16px] bg-white opacity-30 transition-all duration-300 ease-in-out hover:opacity-100 text-title cursor-pointer"
												:class="{ active: sectionCount === 10 }">
												<img src="@/assets/aiwork/images/team-landing-icon-10.png"
													class="w-[34px] h-[34px] rotate-[19deg]" />
												<div>AI+人力资源</div>
											</div>
											<div
												class="section-btn flex gap-[6px] items-center px-3 py-2 pr-5 rounded-3xl text-[16px] bg-white opacity-30 transition-all duration-300 ease-in-out hover:opacity-100 text-title cursor-pointer"
												:class="{ active: sectionCount === 11 }">
												<img src="@/assets/aiwork/images/team-landing-icon-11.png"
													class="w-[34px] h-[34px] -rotate-[27deg]" />
												<div>AI+文旅休闲</div>
											</div>
											<div
												class="section-btn flex gap-[6px] items-center px-3 py-2 pr-5 rounded-3xl text-[16px] bg-white opacity-30 transition-all duration-300 ease-in-out hover:opacity-100 text-title cursor-pointer"
												:class="{ active: sectionCount === 12 }">
												<img src="@/assets/aiwork/images/team-landing-icon-12.png"
													class="w-[34px] h-[34px] -rotate-[27deg]" />
												<div>AI+生意经营</div>
											</div>
										</div>
									</div>
								</div>
								<div class="absolute bottom-0 right-0 z-[1]">
									<div
										class="hover-shadow w-[42px] h-[42px] rounded-full bg-[#040E2A] text-white flex justify-center items-center absolute right-[14px] bottom-[14px] cursor-pointer">
										<ArrowSvg class="w-[16px] h-[16px] -rotate-90" />
									</div>
								</div>
							</div>

						</div>

						<div class="py-[80px]">
							<div class="text-title text-[32px] font-bold text-center">AI智能体化身专业团队 打造高效协同的新工作模式</div>
							<div class="section-timeline mt-[60px] flex flex-col gap-6 rounded-[10px] overflow-hidden">
								<div class="px-8 py-6 backdrop-blur-sm flex items-center justify-between sm:flex-col"
									style="background: rgba(255, 255, 255, 0.52);">
									<div class="flex flex-col justify-start">
										<img src="@/assets/aiwork/images/team-landing-sec-icon-1.png" class="w-[88px] h-[88px]" />
										<h3 class="text-[32px] font-bold">强大的技术集成能力<br />无需开发，也可快速搭建智能体应用</h3>
										<p class="text-[16px] mt-6">AIWork365集成了自然语言处理、机器学习等核心技术<br />无需开发，也能参与到智能体的创建中</p>
										<div
											class="flex gap-4 items-center mt-6 text-[16px] text-[#0066FF] hover:text-[#6a9bec] cursor-pointer" @click="heroClick">
											<span>开始创建您的专属智能体</span>
											<Arrow1Svg class="w-[16px] h-[16px]" />
										</div>
									</div>
									<div class="w-[640px] sm:w-full sm:mt-4">
										<img src="@/assets/aiwork/images/team-landing-sec-1.png" class="w-full" />
									</div>
								</div>
							</div>
							<div class="section-timeline mt-[20px] flex flex-col gap-6 rounded-[10px] overflow-hidden">
								<div class="px-8 py-6 backdrop-blur-sm flex items-center justify-between sm:flex-col"
									style="background: rgba(255, 255, 255, 0.52);">
									<div class="flex flex-col justify-start">
										<img src="@/assets/aiwork/images/team-landing-sec-icon-2.png" class="w-[88px] h-[88px]" />
										<h3 class="text-[32px] font-bold">丰富的应用功能<br />解析能力强，高性能的知识库系统</h3>
										<p class="text-[16px] mt-6">自动解析理解企业文档知识，涵盖创意写作、文案生成、<br />跨语言交流与归纳总结等丰富的应用功能</p>
										<div
											class="flex gap-4 items-center mt-6 text-[16px] text-[#0066FF] hover:text-[#6a9bec] cursor-pointer" @click="heroClick">
											<span>点击了解更多智能化模式</span>
											<Arrow1Svg class="w-[16px] h-[16px]" />
										</div>
									</div>
									<div class="w-[640px] sm:w-full sm:mt-4">
										<img src="@/assets/aiwork/images/team-landing-sec-2.png" class="w-full" />
									</div>
								</div>
							</div>
							<div class="mt-[20px] flex flex-col gap-6 rounded-[10px] overflow-hidden">
								<div class="px-8 py-6 backdrop-blur-sm flex items-center justify-between sm:flex-col"
									style="background: rgba(255, 255, 255, 0.52);">
									<div class="flex flex-col justify-start">
										<img src="@/assets/aiwork/images/team-landing-sec-icon-3.png" class="w-[88px] h-[88px]" />
										<h3 class="text-[32px] font-bold">个性化智能体定制<br />全场景适用，高效完成您的创作需求</h3>
										<p class="text-[16px] mt-6">可根据自身特有的资料集和业务需求进行高精准度的定制化与<br />精准分析训练，以满足各种特定的任务需求</p>
										<div
											class="flex gap-4 items-center mt-6 text-[16px] text-[#0066FF] hover:text-[#6a9bec] cursor-pointer" @click="heroClick">
											<span>联系我们构建私有化定制</span>
											<Arrow1Svg class="w-[16px] h-[16px]" />
										</div>
									</div>
									<div class="w-[640px] sm:w-full sm:mt-4">
										<img src="@/assets/aiwork/images/team-landing-sec-3.png" class="w-full" />
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="absolute left-0 top-0 w-screen h-full z-[-1] page-bg bg-[#EBECFB] overflow-hidden">
				<div class="absolute left-1/2 -translate-x-1/2 top-0  max-w-[1400px] w-[90%] sm:w-[calc(100vw-32px)] h-full">
					<div
						class="page-bg-block absolute w-[500px] h-[500px] top-0 left-0 -translate-x-1/3 -translate-y-1/3 rounded-full"
						style="--bg-block: rgba(222,226,255.4)"></div>
					<div class="absolute w-[250px] h-[250px] top-[60px] right-0 rounded-full"
						style="background: linear-gradient(234deg, rgba(180, 255, 137, 0.06) -16%, rgba(87, 233, 255, 0.5733) 12%, rgba(146, 72, 255, 0.6) 53%);filter: blur(50px);">
					</div>
					<div class="absolute w-[380px] h-[380px] right-0 top-[120px] bg-[#0E69FF] rounded-full"
						style="filter: blur(200px);"></div>
					<div class="absolute w-[630px] h-[630px] -left-[108px] top-[70%] bg-[#94BFFF] rounded-full"
						style="filter: blur(300px);"></div>
					<div class="absolute w-[550px] h-[550px] -right-[60px] top-[60%] rounded-full"
						style="background: rgba(132, 239, 255, 0.76);filter: blur(300px);"></div>
				</div>

				<!-- <div class="page-bg-block absolute w-[600px] h-[600px] -top-[300px] left-[70%] rounded-full"></div> -->
			</div>
			<GlobalFloat />
		</div>
	</Layout>
</template>

<script setup lang="ts">
import Layout from '@/layouts/Layout.vue';
import teamLandingImage from '@/assets/aiwork/images/team-landing.png'
import bg_1 from '@/assets/aiwork/images/team-landing-2.png'
import ArrowSvg from '@/assets/aiwork/svg/arrow.svg'
import Arrow1Svg from '@/assets/aiwork/svg/arrow.1.svg'
import { NButton } from 'naive-ui'
import { onMounted, ref } from 'vue';
import lax from 'lax.js'
import { useBasicLayout } from '@/hooks/useBasicLayout';
import GlobalFloat from "@/components/common/GlobalFloat/index.vue";

const {isMobile} = useBasicLayout()
const sectionCount = ref(0)

onMounted(() => {
	heroAnimation()
	cardAnimation()
	randomSectionBtg()
	sectionTimeline()
})

const heroClick = () => window.$aiwork.openSelectTeam?.()

const randomSectionBtg = () => {
	// 1-12中的随机整数
	setInterval(() => {
		sectionCount.value = Math.floor(Math.random() * 11) + 1
	}, 2000)
}
const heroAnimation = () => {
	const $hero = document.querySelector('.hero') as HTMLElement
	const $hero_image = $hero.querySelector('.hero_image') as HTMLElement
	$hero.addEventListener('mousemove', (event: any) => {
		var rect = $hero.getBoundingClientRect();
		var centerX = rect.left + rect.width / 2;
		var centerY = rect.top + rect.height / 2;

		const _x = (event.clientX - centerX) / 50;
		const _y = (event.clientY - centerY) / 100;
		var rotateX = ((event.clientY - centerY) / rect.height) * 10;
		var rotateY = -((event.clientX - centerX) / rect.width) * 10;

		$hero_image.style.transform = 'perspective(1000px) translate(' + _x + 'px,0px) rotateX(' + rotateX + 'deg) rotateY(' + rotateY + 'deg)';
	})
}
const cardAnimation = () => {
	document.querySelectorAll('.card-mouse-flow').forEach((card: any, index) => {
		card.addEventListener('mousemove', (event: any) => {
			var rect = card.getBoundingClientRect();
			var centerX = rect.left + rect.width / 2;
			var centerY = rect.top + rect.height / 2;

			var rotateX = ((event.clientY - centerY) / rect.height) * 10;
			var rotateY = -((event.clientX - centerX) / rect.width) * 10;

			card.style.transform = 'perspective(1000px) rotateX(' + rotateX + 'deg) rotateY(' + rotateY + 'deg)';
		})
	})
}
const sectionTimeline = () => {
	if(isMobile.value) return
	lax.init()
	lax.addDriver('scrollY', function () {
		return window.scrollY
	})
	lax.addElements('.section-timeline', {
		scrollY: {
			opacity: [
				['elInY','elCenterY+400', 'elOutY+200'],
				[1, 1, 0]
			],
			scale: [
				['elInY','elCenterY+400', 'elOutY+200'],
				[1, 1, 0.9]
			],
			blur: [
				['elInY','elCenterY+400', 'elOutY+200'],
				[0, 0, 20]
			]

			// scale: {
			// 	from: 1,
			// 	to: 0.5,
			// 	start: 0,
			// 	end: 500,
			// 	easing: 'linear'
			// }
		}
	})
}
</script>

<style>
.page-content {
	/* width: minmax(90%, 1400px); */
}

.page-bg-block {
	background: radial-gradient(circle at center, var(--bg-block, rgba(148, 203, 255, .4)) 0, transparent 80%)
}

.gradient-btn {
	color: #fff;
	height: 60px;
	border-radius: 60px;
	display: inline-flex;
	justify-content: center;
	align-items: center;
	background: linear-gradient(90deg, #0066FF 0%, #B700FF 100%);
}

.section-bg-1 {
	background-image: url('@/assets/aiwork/images/team-landing-sec-bg.png');
	background-size: 100% 100%;
}

.card-mouse-flow {
	perspective: 500px;
}

.hover-shadow {
	transition: all 0.3s ease-in-out;

	&:hover {
		box-shadow: rgb(38, 57, 77) 0px 20px 30px -10px;
		transform: translateY(-5px);
	}
}

@keyframes sec-btn-tranlate {
	/* 0% {
		transform: translateX(0);
	}

	100% {
		transform: translateX(var(--ani-x, 100px));
	} */
}

.section-btn {
	transition: all 0.3s cubic-bezier(0.92, 0.04, 0.71, 1.82);

	&.active {
		opacity: 1;
	}
}

.section-timeline {
	position: sticky;
	top: 100px;
}
</style>
