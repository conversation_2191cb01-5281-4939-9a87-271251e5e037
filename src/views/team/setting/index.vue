<template>
	<div class="p-6">
		<div class="flex gap-6 mb-6">
			<div class="tab-item" :class="{active: tabActive === 0}" @click="tabActive = 0">团队设置</div>
			<div class="tab-item" :class="{active: tabActive === 1}" @click="tabActive = 1">成员管理</div>
		</div>

		<div class="bg-white p-8" style="box-shadow: 0px 4px 9px 0px #EBEFFF;">
			<div v-if="tabActive === 0">
				<Profile></Profile>
			</div>
			<div v-if="tabActive === 1">
				<TeamManage></TeamManage>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Profile from './Profile1.vue';
import TeamManage from './TeamManage.vue';


const tabActive = ref(0)
</script>

<style lang="less">
.tab-item {
	font-size: 18px;
	color: #333333;
	cursor: pointer;

	&.active {
		font-weight: 700;
		color: #0066FF;
	}
}
</style>
