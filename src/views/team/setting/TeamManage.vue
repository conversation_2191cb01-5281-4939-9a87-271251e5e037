<template>
	<div>
		<div class="flex justify-between items-center mb-[20px]">
			<div class="flex gap-3 items-center text-[20px]">
				<NAvatar class="w-[58px] h-[58px]" :src="userStore.curTeam?.avatar ||defaultAvatar" :size="58"></NAvatar>
				<div>{{ userStore.curTeam?.name }}</div>
			</div>
			<NButton type="primary" size="large" @click="showMemberModal = { show: true }">添加成员</NButton>
		</div>
		<NDataTable class="w-full mt-[25px]" :columns="columns" :data="memberList" :bordered="false" :scroll-x="800"
			:pagination="pagination" remote />

		<MemberModal v-if="showMemberModal.show" v-model:show="showMemberModal.show" :source="showMemberModal.source"
			@on-submit="handleOnSubmit" />
	</div>
</template>

<script setup lang="ts">
import { NAvatar, NButton, NDataTable, useDialog, useMessage } from 'naive-ui';
import { TableColumn } from 'naive-ui/es/data-table/src/interface';
import { h, onMounted, reactive, ref } from 'vue';
import defaultAvatar from '@/assets/avatar.jpg'
import MemberModal from '../components/MemberModal.vue';
import request from '@/utils/request'
import dayjs from 'dayjs';
import { useUserStore } from '@/store';

const dialog = useDialog()
const message = useMessage()
const memberList = ref<any>([])
const userStore = useUserStore()

const showMemberModal = ref<{ show: boolean, source?: any }>({
	show: false
})

const columns: TableColumn[] = [
	{
		title: '成员名称',
		key: 'nickname',
		width: 240,
		fixed: 'left',
		render(row) {
			return h('div', { class: 'flex items-center gap-4' }, [
				h(NAvatar, { src: (row.avatar ? row.avatar : defaultAvatar) as string, size: 40 }),
				h('div', { class: 'flex flex-col' }, [
					h('span', { class: 'text-title text-[16px]' }, row.nickname as string),
					h('span', { class: 'text-secondary' }, `ID: ${row.id as string}`)
				])
			])
		}
	},
	{
		title: '手机号码',
		key: 'phone',
		width: 100,
		render(row) {
			return h('div', {}, row.phone as string || '-')
		}
	},
	{
		title: '成员组',
		key: 'role',
		width: 100,
		render(row) {
			return h('div', { class: 'flex items-center gap-2' }, { 'superadmin': '超级管理员', 'admin': '管理员', 'member': '成员' }[row.role as string])
		}
	},
	{
		title: '加入时间',
		key: 'joined_at',
		width: 100,
		render(row: any) {
			return h('div', {}, dayjs(row.joined_at).format('YYYY-MM-DD HH:mm:ss'))
		}
	},

	{
		title: '操作',
		key: 'actions',
		width: 150,
		fixed: 'right',
		render(row) {
			return h('div', { class: {
				'flex': true,
				'!hidden': row.role === 'superadmin',
				'gap-3': true,
			} }, [
				h(NButton, {
					type: 'primary', text: true, onClick: () => {
						showMemberModal.value = { show: true, source: row }
					}
				}, '权限'),
				h(NButton, { type: 'error', text: true, onClick: () => { handleDelete(row) } }, '删除')
			])
		}
	}
]
const pagination = reactive({
	page: 1,
	pageSize: 20,
	showSizePicker: true,
	pageSizes: [10, 20, 50, 100],
	pageCount: 1,
	itemCount: 1,
	onChange: (page: number) => {
		pagination.page = page
		getMemberList({})
	},
	onUpdatePageSize: (pageSize: number) => {
		pagination.pageSize = pageSize
		pagination.page = 1
		getMemberList({})
	}
})

onMounted(() => {
	getMemberList({})
})

const getMemberList = (params) => {
	const _params = {
		...pagination,
		...params
	}
	request({
		url: '/api3/aiwork/team/users',
		method: "POSt",
		data: _params
	}).then(data => {
		memberList.value = data.rows
		pagination.pageCount = Math.ceil(data.count / _params.pageSize)
		pagination.page = _params.page
		pagination.itemCount = data.count
	})
}
const handleDelete = (item) => {
	dialog.warning({
		title: '确认删除',
		content: '确认删除成员吗?',
		positiveText: '确定',
		negativeText: '取消',
		onPositiveClick: () => {
			request({
				url: '/api3/aiwork/team/delUser',
				method: "POST",
				data: { userId: item.id }
			}).then(() => {
				message.success('删除成功')
				if(pagination.itemCount % pagination.pageSize <= 1) pagination.page = Math.max(pagination.page - 1, 1)
				getMemberList({page: pagination.page})
			})
		}
	})
}
const handleOnSubmit = () => {
	showMemberModal.value.show = false
	showMemberModal.value.source = null
	getMemberList({})
}
</script>
