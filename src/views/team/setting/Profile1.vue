<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useUserStore } from "@/store";
import { NAvatar, NModal } from "naive-ui";
// import dayjs from 'dayjs';
import MemberTabs from "@/views/profile/components/MemberTabs.vue";
import MemberBenefit from "@/views/profile/components/MemberBenefit.vue";
import Profile from "@/views/profile/components/Profile.vue";
import { fetchPersonal } from "@/chatgpt";
import dayjs from "dayjs";
import { useRequest } from "vue-hooks-plus";
import { CategoryType, Member, MemberEnum, MemberType } from "@/views/profile/types";
import { getUser } from "@/store/modules/auth/helper";
import MemberSvg from "@/assets/aiwork/svg/member.svg";
import { useRoute } from 'vue-router'
import defaultAvatar from '@/assets/avatar.jpg'
import { NEllipsis } from 'naive-ui'
import GlobalFloat from "@/components/common/GlobalFloat/index.vue";
import request from "@/utils/request";
import EditSvg from "@/assets/aiwork/svg/edit.svg";
import AddTeamModal from "../components/AddTeamModal.vue";
import { router } from "@/router";


const members = ref<Partial<Member>[]>([]);

const userStore = useUserStore();
const user = getUser();
const route = useRoute();
const currentMember = ref<Partial<Member>>();
const showEditNameModal = ref(false);
const showEditAvatarModal = ref(false);

const defaultMembers = ref([
	MemberEnum.TEXT,
	MemberEnum.PAPER,
	MemberEnum.PPT,
	MemberEnum.MJ,
]);

onMounted(() => {
	getTeamEquity();
})

const getTeamEquity = () => {
	request({
		url: '/api3/aiwork/team/member'
	})
		.then(data => {
			console.log('data', data)
			const { Member } = data;
			members.value = defaultMembers.value.map((m, index) => {
				const member = Member.find((item) => item.code == m);
				if (member) {
					return {
						...member,
						tab: MemberType[member.code],
						endDate: dayjs(member.endDate).format("YYYY.MM.DD"),
						isMember: member.isMember,
					};
				} else {
					return {
						tab: MemberType[m],
						endDate: "",
						isMember: false,
						categoryId: CategoryType[m],
					};
				}
			});
			console.log('members', members.value)
			currentMember.value = members.value[0];
		})
}
const handleSelectMember = (member: Member) => {
	currentMember.value = member;
};

const handleBecomeMember = () => {
	console.log('>>>route.name', route.name);
	let categoryId = 1;
	switch (route.name) {
		case "PPT":
			categoryId = 2;
			break;
		case "Paper":
			categoryId = 3;
			break;
		case "MJ":
			categoryId = 4;
			break;
		default:
			categoryId = 1;
			break;
	}
	// @ts-ignore
	window.$aiwork?.openRecharge?.({ type: "ai", categoryId }).then(() => {
		router.go(0)
	})
};

const handleEditTeam = (data) => {
	userStore.changeTeam(data.id)
}
</script>
<template>
	<main class="w-full h-[100%] overflow-y-scroll HideScrollbar flex flex-col items-start">
		<!-- 个人信息 -->
		<section
			class="3xl:w-[1180px] 2xl:w-[980px] xl:w-[780px] lg:w-[780px] md:w-[780px] sm:w-[100%] mx-[25px] flex flex-row gap-x-[20px] px-[30px] py-[30px]">
			<n-avatar round :size="60" :src="userStore.curTeam?.avatar || defaultAvatar" class="w-[60px] cursor-pointer" @click="showEditNameModal = true" />
			<div class="flex flex-col gap-y-[10px] w-full flex-1">
				<!-- 用户昵称 -->
				<div class="flex items-center gap-4">
					<NEllipsis class="text-[20px] text-[#3d3d3d] leading-[26px]">
						{{ userStore.curTeam?.name }}
					</NEllipsis>
					<EditSvg class="w-[16px] h-[16px] cursor-pointer" @click="showEditNameModal = true" />
				</div>
				<!-- UID -->
				<span class="text-[14px] text-[#818181] leading-[18px]">
					团队ID: {{ userStore.curTeam?.id }}
				</span>
				<!-- 会员信息 -->
			</div>
		</section>
		<section
			class="3xl:w-[1180px] 2xl:w-[980px] xl:w-[780px] lg:w-[780px] md:w-[780px] sm:w-[100%] mx-[25px] bg-[#ffffff] flex flex-row gap-x-[20px] pb-[30px]">
			<div class="px-[24px] w-full">
				<div v-if="members?.length">
					<MemberTabs :members="members" :hidePaper="true" @update-select-member="handleSelectMember" />
					<MemberBenefit :member="currentMember" />
				</div>
				<div v-else class="member-btn" @click="handleBecomeMember">
					<MemberSvg class="w-[16px] h-[16px]" />
					成为会员
				</div>
			</div>
		</section>
		<AddTeamModal v-model:show="showEditNameModal" :edit="true" :teamInfo="userStore.curTeam" @complete="handleEditTeam" />
	</main>
</template>
<style lang="less" scoped>
.member-btn {
	width: 110px;
	height: 36px;
	box-sizing: border-box;
	background: linear-gradient(var(--login-btn-bg-deg),
			#2079ff 0%,
			#af53ff 100%);
	border-radius: 4px;
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 7px;
	font-size: 14px;
	color: #ffffff;
	cursor: pointer;

	transition: --login-btn-bg-deg 0.3s ease-in-out;

	&:hover {
		--login-btn-bg-deg: -90deg;
	}
}
</style>
