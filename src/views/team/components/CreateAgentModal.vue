<template>
	<!-- 新建/编辑智能体 -->
	<NModal v-model:show="show" preset="dialog">
		<template #header>
			<div>新建智能体</div>
		</template>
		<NForm ref="formRef" :model="agentModalModel" label-placement="left" label-align="left" label-width="auto"
			require-mark-placement="right-hanging" class="pt-4" :rules="rules">
			<NFormItem label="智能体名称" required path="name">
				<NInput v-model:value="agentModalModel.name" show-count :maxlength="8" placeholder="请填写智能体名称" />
			</NFormItem>
			<NFormItem label="智能体图标" path="icon">
				<!-- <NUpload /> -->
				<div>
					<NUpload list-type="image-card" accept=".jpg,.png,.jpeg" :max="1" v-model:file-list="agentModalModel.icon"
						@before-upload="beforeUpload" />
					<div class="text-[14px] text-[#98A2B5] mt-4">最大2MB,建议尺寸120*120px 支持.jpg .png格式</div>
				</div>
			</NFormItem>
			<NFormItem label="智能体介绍">
				<NInput type="textarea" v-model:value="agentModalModel.description" show-count :maxlength="120"
					placeholder="请填写智能体介绍" />
			</NFormItem>

			<div class="flex justify-center gap-[18px]">
				<NButton round @click="show = false" style="--n-width: 130px;">取消</NButton>
				<NButton type="primary" round @click="submit" style="--n-width: 130px;">确认</NButton>
			</div>
		</NForm>
	</NModal>
</template>

<script setup lang="ts">
import { FormItemRule, FormRules, NAvatar, NButton, NDropdown, NForm, NFormItem, NInput, NModal, NPopover, NUpload, UploadFileInfo, useMessage } from 'naive-ui';
import { ref, watch } from 'vue';
const show = defineModel<boolean>('show')
const props = defineProps<{
	type: 'create' | 'edit',
	data?: any
}>()
const emit = defineEmits<{
	onSubmit: [type: 'create' | 'edit', agent: any]
}>()

const formRef = ref<any>()
const message = useMessage()

watch(() => show.value, (value) => {
	if (!value) agentModalModel.value = {}
})

const rules: FormRules = {
	name: { required: true, message: '请输入智能体名称' },
	// icon: {
	// 	required: true,
	// 	trigger: 'submit',
	// 	validator: (rule: FormItemRule, value: string) => {
	// 		if (!value?.length) return new Error('请上传智能体图标')
	// 		return true
	// 	},
	// }
}

const beforeUpload = (data: {
	file: UploadFileInfo
	fileList: UploadFileInfo[]
}) => {
	if ((data.file.file?.size as number) > 2 * 1024 * 1024 ) {
		message.error('仅支持2MB以下的图片')
		return false
	}
	return true
}

const agentModalModel = ref<{
	name?: string,
	icon?: UploadFileInfo[],
	description?: string
}>({})

const submit = () => {
	formRef.value?.validate().then((data) => {
		emit('onSubmit', props.type, agentModalModel.value)
	})
}
</script>
