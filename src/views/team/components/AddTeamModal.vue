<template>
	<NModal v-model:show="show" preset="dialog" @click.stop>
		<template #header>
			<div>{{ edit ? '编辑团队' : '新建团队' }}</div>
		</template>
		<NForm ref="formRef" :model="model" label-placement="left" label-align="left" label-width="auto"
			require-mark-placement="right-hanging" class="pt-4" :rules="{
				name: { required: true, message: '请输入团队名称' }, file: [
					{
						required: !edit, validator(rule, value) {
							if(props.edit) return true
							if (!value) {
								return new Error('请上传团队图标')
							}
							if (value.length <= 0) {
								return new Error('请上传团队图标')
							}
							return true
						}
					},
				]
			}">
			<NFormItem label="团队名称" required path="name">
				<NInput v-model:value="model.name" show-count :maxlength="8" placeholder="请填写团队名称" />
			</NFormItem>
			<NFormItem label="团队图标" path="file">
				<!-- <NUpload /> -->
				<div>
					<NUpload list-type="image-card" accept=".jpg,.png,.jpeg" :max="1" v-model:file-list="model.file" :default-file-list="props.teamInfo?.avatar ? [{
						status: 'finished',
						url: props.teamInfo?.avatar,
						id: props.teamInfo?.id,
						name: props.teamInfo?.name,
					}] : []" @beforeUpload="beforeUpload" />
					<div class="text-[14px] text-[#98A2B5] mt-4">2MB以下，建议尺寸120*120px 支持.jpg .png格式</div>
				</div>
			</NFormItem>
			<div class="flex justify-center gap-[18px]">
				<NButton round @click="show = false" style="--n-width: 130px;">取消</NButton>
				<NButton type="primary" round @click="submitCreateTeam" style="--n-width: 130px;">确认</NButton>
			</div>
		</NForm>
	</NModal>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store';
import { NButton, NForm, NFormItem, NInput, NModal, NUpload, UploadFileInfo, useMessage } from 'naive-ui';
import { ref, watch } from 'vue';
import request from '@/utils/request';

const show = defineModel<boolean>('show')
const props = defineProps<{
	edit?: boolean
	teamInfo?: any
}>()
const emit = defineEmits<{
	complete: [team: any]
}>()

const message = useMessage()
const userStore = useUserStore()
const formRef = ref()
const model = ref<any>({})

watch(() => show.value, (value) => {
	if(value && props.edit && props.teamInfo) {
		model.value = {
			name: props.teamInfo.name,
			// files: [props.teamInfo.avatar]
		}
	}
})

const submitCreateTeam = () => {
	formRef.value.validate().then((err) => {
		const data = new FormData()
		data.append('name', model.value.name)
		if(model.value.file?.[0]) data.append('file', model.value.file[0].file)
		if(!props.edit) data.append('type', 'add')
		request({
			url: '/api3/aiwork/team/saveTeam',
			method: "POST",
			data
		}).then(data => {
			show.value = false
			message.success('保存成功')
			emit('complete', data)
		})
	}).catch(err => {
		console.log(22222, err)
	})
}

const beforeUpload = (data: {
	file: UploadFileInfo
	fileList: UploadFileInfo[]
}) => {
	if ((data.file.file?.size as number) > 2 * 1024 * 1024 ) {
		message.error('仅支持2MB以下的图片')
		return false
	}
	return true
}

</script>
