<template>
	<!-- 新建/编辑智能体 -->
	<NModal v-model:show="show" preset="dialog">
		<template #header>
			<div>发布</div>
		</template>
		<div class="flex flex-col gap-[20px]">
			<div class="flex justify-between items-center">
				<div>
					<div class="text-[16px] text-title">开启外部访问</div>
					<div class="text-info mt-2">开启后团队外部成员可访问智能体</div>
				</div>
				<NSwitch @change="onPublishChange" />
			</div>
			<div>
				<div class="flex justify-between">
					<div class="text-[16px] text-title">链接访问</div>
					<div class="flex gap-[16px]">
						<NButton color="#2F82FF" text>复制链接</NButton>
						<NButton color="#2F82FF" text>预览</NButton>
					</div>
				</div>
				<div class="text-info mt-2">用户通过此链接可以直接与您团队的智能体聊天</div>
			</div>
			<div>
				<div class="flex justify-between">
					<div class="text-[16px] text-title">JS嵌入</div>
					<NRadioGroup name="embedType" v-model:value="embedType">
						<NRadio :value="1">页面嵌入</NRadio>
						<NRadio :value="2">聊天气泡</NRadio>
					</NRadioGroup>
				</div>
				<div class="flex justify-between">
					<div class="text-info mt-2">网页中嵌入聊天界面</div>
					<div class="flex gap-[16px]">
						<NButton color="#2F82FF" text>复制代码</NButton>
						<NButton color="#2F82FF" text>查看示例</NButton>
					</div>
				</div>
			</div>
		</div>
	</NModal>
</template>

<script setup lang="ts">
import { NButton, NModal, NRadioGroup, NSwitch, NRadio } from 'naive-ui';
import { ref } from 'vue';
const show = defineModel<boolean>('show')
const embedType = ref(1)

const onPublishChange = () => { }

</script>
