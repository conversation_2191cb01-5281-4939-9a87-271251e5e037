<template>
	<!-- 新建/编辑智能体 -->
	<NModal v-model:show="show" preset="dialog">
		<template #header>
			<div class="flex gap-2 items-center">
				<span>订正</span>
				<span class=" text-secondary text-[14px] font-thin">补充知识点以及其答案，完善知识库</span>
			</div>
		</template>
		<NForm ref="formRef" :model="agentModalModel" label-placement="left" label-align="right" label-width="auto"
			require-mark-placement="right-hanging" class="pt-4">
			<NFormItem label="匹配的知识点" path="name">
				<NInput type="textarea" v-model:value="agentModalModel.name"  placeholder="这里是问题" />
			</NFormItem>
			<NFormItem label="补充知识">
				<NInput type="textarea" v-model:value="agentModalModel.description" placeholder="这里是答案" />
			</NFormItem>
			<NFormItem label="知识库">
				<NSelect v-model:value="agentModalModel.knowledge" placeholder="请选择知识库"></NSelect>
			</NFormItem>


			<div class="flex justify-center gap-[18px]">
				<NButton round @click="show = false" style="--n-width: 130px;">取消</NButton>
				<NButton type="primary" round @click="submit" style="--n-width: 130px;">确认</NButton>
			</div>
		</NForm>
	</NModal>
</template>

<script setup lang="ts">
import { NAvatar, NButton, NDropdown, NForm, NFormItem, NInput, NModal, NPopover, NSelect, NUpload } from 'naive-ui';
import { ref } from 'vue';
const show = defineModel<boolean>('show')
const emit = defineEmits<{
	onSubmit: [agent: any]
}>()

const agentModalModel = ref<any>({})

const submit = () => {
	show.value = false
	emit('onSubmit', agentModalModel.value)
}
</script>
