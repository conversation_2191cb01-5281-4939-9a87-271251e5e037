<template>
	<!-- 新建/编辑智能体 -->
	<NModal v-model:show="show" preset="dialog">
		<template #header>
			<div>{{ props.source? '修改权限' : '添加成员' }}</div>
		</template>
		<NForm ref="formRef" :model="agentModalModel" label-placement="left" label-align="left" label-width="auto"
			require-mark-placement="right-hanging" class="pt-4">
			<NFormItem label="成员组" required path="role">
				<NRadioGroup v-model:value="agentModalModel.role">
					<NRadio label="管理员" value="admin">管理员</NRadio>
					<NRadio label="普通成员" value="member">普通成员</NRadio>
				</NRadioGroup>
			</NFormItem>
			<NFormItem v-if="!props.source" label="邀请手机号" path="phones">
				<div>
					<NInput type="textarea" v-model:value="agentModalModel.phones" placeholder="请输入需要添加的手机号，多个手机号以,(英文逗号)相隔如:18866667777,18777772222" />
					<div class="mt-2 text-secondary text-[12px]">
						输入手机号后，手机号关联的账户立即成为团队成员
					</div>
				</div>
			</NFormItem>

			<div class="flex justify-center gap-[18px]">
				<NButton round @click="show = false" style="--n-width: 130px;">取消</NButton>
				<NButton type="primary" round @click="handleSubmit" style="--n-width: 130px;">确认</NButton>
			</div>
		</NForm>
	</NModal>
</template>

<script setup lang="ts">
import { NAvatar, NButton, NDropdown, NForm, NFormItem, NInput, NModal, NPopover, NRadio, NRadioGroup, NUpload, useMessage } from 'naive-ui';
import { ref, watch } from 'vue';
import request from '@/utils/request';
const show = defineModel<boolean>('show')
const props = defineProps<{source?: any}>()
const emit = defineEmits<{
	onSubmit: [agent: any]
}>()

const agentModalModel = ref<any>({
	role: 'member'
})
const message = useMessage()

watch(() => show.value, (value) => {
	if(!value) agentModalModel.value = {}
})
watch(() => props.source, (value) => {
	if(value) agentModalModel.value = value
}, { immediate: true })

const handleSubmit = () => {
	const url = !props.source?.id ? '/api3/aiwork/team/addUserToTeam' : '/api3/aiwork/team/updateUserRole'
	const _params = {
		...agentModalModel.value
	}
	if(props.source?.id) _params.userId = props.source.id
	request({
		url: url,
		method: 'POST',
		data: _params
	}).then(() => {
		message.success('操作成功')
		emit('onSubmit', agentModalModel.value)
	})
}
</script>
