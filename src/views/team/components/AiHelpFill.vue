<template>
	<!-- 新建/编辑智能体 -->
	<NModal v-model:show="show" preset="dialog">
		<template #header>
			<div>AI帮我写</div>
		</template>
		<NForm ref="formRef" :model="agentModalModel" label-placement="left" label-align="left" label-width="auto"
			require-mark-placement="right-hanging" class="pt-4"
			:rules="{ name: { required: true, message: '请输入智能体名称' }, icon: {} }">
			<NFormItem label="智能体名称" required path="name">
				<NInput v-model:value="agentModalModel.name" show-count :maxlength="8" placeholder="请填写智能体名称" />
			</NFormItem>
			<NFormItem label="角色需求">
				<NInput type="textarea" v-model:value="agentModalModel.description" show-count :maxlength="120"
					placeholder="请填写您的智能体所承担的角色是什么，工作职责越详细最好" />
			</NFormItem>

			<div class="flex justify-center gap-[18px]">
				<NButton round @click="show = false" style="--n-width: 130px;">取消</NButton>
				<NButton type="primary" round @click="submit" style="--n-width: 130px;">确认</NButton>
			</div>
		</NForm>
	</NModal>
</template>

<script setup lang="ts">
import { NAvatar, NButton, NDropdown, NForm, NFormItem, NInput, NModal, NPopover, NUpload } from 'naive-ui';
import { ref } from 'vue';
const show = defineModel<boolean>('show')
const emit = defineEmits<{
	onSubmit: [agent: any]
}>()

const agentModalModel = ref<{
	type?: 'create' | 'edit',
	name?: string,
	icon?: File[],
	description?: string
}>({})

const submit = () => {
	show.value = false
	emit('onSubmit', agentModalModel.value)
}
</script>
