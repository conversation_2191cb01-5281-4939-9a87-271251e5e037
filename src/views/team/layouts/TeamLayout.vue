<template>
	<Layout extend>
		<div class="page-bg -z-1 fixed w-[100vw] h-[calc(100vh-64px)] top-[64px] bg-[#EBECFB] overflow-hidden">
			<div class="page-bg-block absolute w-[500px] h-[500px] -top-[100px] left-[30%] rounded-full"></div>
			<div class="page-bg-block absolute w-[600px] h-[600px] -top-[300px] left-[70%] rounded-full"></div>
		</div>
		<div class="flex relative backdrop-blur-md">
			<div class="sidebar p-4 w-[240px] bg-white h-[calc(100vh-64px)] flex flex-col sticky top-[64px] shrink-0 grow-0">
				<div class="flex-1 min-h-0 overflow-y-auto flex flex-col gap-2">
					<div class="sidebar-cell" :class="{active: $route.path.startsWith('/team/agent')}" @click="$router.push('/team/agent')">
						<div class="sidebar-cell-icon w-[20px] h-[20px]">
							<AgentSvg class="w-full h-full" />
						</div>
						<div class="sidebar-cell-text">智能体</div>
					</div>
					<div class="sidebar-cell" :class="{active: $route.path.startsWith('/team/knowledge')}" @click="$router.push('/team/knowledge')">
						<div class="sidebar-cell-icon w-[20px] h-[20px]">
							<KnowledgeSvg class="w-full h-full" />
						</div>
						<div class="sidebar-cell-text">知识库</div>
					</div>
				</div>
				<div class="">
					<NDivider />
					<div class="sidebar-cell"  :class="{active: $route.path.startsWith('/team/setting')}" @click="$router.push('/team/setting')">
						<div class="sidebar-cell-icon w-[24px] h-[24px]">
							<TeamSvg class="w-full h-full" />
						</div>
						<div class="sidebar-cell-text">团队设置</div>
					</div>
				</div>
			</div>
			<div class="flex-1 flex flex-col min-h-0 min-w-0">
				<RouterView />
			</div>
		</div>
	</Layout>

</template>

<script setup lang='ts'>
import Layout from '@/layouts/Layout.vue';
import AgentSvg from '@/assets/aiwork/svg/agent.svg'
import KnowledgeSvg from '@/assets/aiwork/svg/knowledge.svg'
import TeamSvg from '@/assets/aiwork/svg/team.svg'
import { NDivider } from 'naive-ui';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/store';
import { ref } from 'vue';

const router = useRouter()
const route = useRoute();
const isRender = ref(false)

// const userStore = useUserStore()
// if(!userStore.curTeam) router.replace('/aiteam-landing')
// if(userStore.curTeam && userStore.curTeam.TeamsUsers?.role === 'member') router.replace('/aiteam-landing')

</script>

<style>
.page-bg-block {
	background: radial-gradient(circle at center, rgba(148, 203, 255,.4) 0, rgba(148, 203, 255, 0) 50%)
}
.sidebar-cell {
	display: flex;
	align-items: center;
	gap: 15px;
	padding: 12px;
	padding-left: 35px;
	color:#3D3D3D;
	font-size: 14px;
	border-radius: 2px;
	cursor: pointer;
	transition: all 0.3s ease;
}
.sidebar-cell:hover, .sidebar-cell.active {
	background-color: #E6F1FF;
	color: #0E69FF;
}
</style>
