html,
body,
#app {
	height: 100%;
}

body {
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
}

.max-w-screen-xl {
	max-width: 1280px;
}

/* 整体滚动条样式 */
div::-webkit-scrollbar {
	width: 3px;
	/* 滚动条宽度 */
}
/* 滚动条拖动块（滑块）样式 */
div::-webkit-scrollbar-thumb {
	background-color: rgba(0, 0, 0, 0.2);
	/* 拖动块颜色 */
	border-radius: 5px;
	/* 拖动块圆角 */
}

/* 滚动条轨道样式 */
div::-webkit-scrollbar-track {
	background-color: #f1f1f1;
	/* 轨道颜色 */
	opacity: 0.7;
}

/* 滚动条按钮样式 */
div::-webkit-scrollbar-button {
	background-color: #ccc;
	/* 按钮颜色 */
	opacity: 0.7;
}

/* 水平滚动条样式 */
div::-webkit-scrollbar-horizontal {
	height: 3px;
	/* 滚动条高度 */
}

/* 竖直滚动条样式 */
div::-webkit-scrollbar-vertical {
	width: 3px;
	/* 滚动条宽度 */
}

.n-image-preview-wrapper:has(.ai-watermark-preview) {
	&::after {
		content: "";
		position: absolute;
		bottom: calc(var(--preview-bottom, 0) + 16px);
		right: calc(var(--preview-right, 0) + 16px);
		opacity: var(--preview-opacity, 0);
		width: 56px;
		height: 24px;
		background: url("https://cdn2.weimob.com/saas/saas-fe-sirius-orion-node/production/zh-CN/483/shuiyin.png") no-repeat center center / contain;
	}
}

.max-w-container {
	max-width: 85rem !important;
}

.ckButtonNew {
	position: relative;
	display: inline-flex;
	align-items: center;
	white-space: nowrap;
	font-weight: 700;
	border: none !important;
}

.ckButtonblue {
	justify-content: center;
	border-radius: 3px;
	background-color: transparent;
	--tw-text-opacity: 1;
	color: rgb(250 251 252 / var(--tw-text-opacity));
	padding: 5px 20px;
	position: relative;
	overflow: hidden;
	background: linear-gradient(
			180deg,
			rgba(51, 184, 159, 0.2),
			transparent 100%,
			rgba(54, 167, 116, 0) 0
		),
		linear-gradient(
			90deg,
			rgba(51, 184, 159, 0.8),
			rgba(51, 184, 159, 0.8) 80%,
			rgba(51, 184, 159, 0.8)
		),
		rgba(51, 184, 159, 0.8);
	transition: all 0.3s ease-in-out;
	background-size: 200% 100%;
	background-position-x: 50%;
}

.ckButtonblue:hover {
	background-position-x: 98%;
	background: rgba(34, 184, 157, 0.9);
}

.ckButtonblue:active,
.ckButtonblue:focus {
	background-position-x: 0;
}

.ckButtonblue2 {
	height: 3.2rem;
	justify-content: center;
	border-radius: 3px;
	background-color: transparent;
	line-height: 2;
	--tw-text-opacity: 1;
	color: rgb(250 251 252 / var(--tw-text-opacity));
	padding: 0 20px;
	position: relative;
	overflow: hidden;
	background: linear-gradient(
			180deg,
			rgba(51, 184, 159, 0.2),
			transparent 100%,
			rgba(54, 167, 116, 0) 0
		),
		linear-gradient(
			90deg,
			rgba(8, 58, 94, 0.8),
			rgba(8, 58, 94, 0.8) 80%,
			rgba(8, 58, 94, 0.8)
		),
		rgba(8, 58, 94, 0.8);
	transition: all 0.3s ease-in-out;
	background-size: 200% 100%;
	background-position-x: 50%;
}

.ckAnimate {
	// position: absolute;
	position: relative;
	background: linear-gradient(90deg, #33b8a0, #04aa7c) !important;
}

.ckAnimate:after {
	content: "";
	height: 40px;
	width: 30px;
	display: inline-block;
	background: hsla(0, 0%, 100%, 0.2);
	position: absolute;
	left: -60px;
	top: 0;
	z-index: 2;
	transform: skew(-12deg);
	animation: light 2.5s linear infinite;
}

.textGradient {
	background: linear-gradient(89.94deg, #33b89f 28.89%, #3f3eed 72.67%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}
css .borderline {
	position: relative;
}

.borderline::after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 1px;
	background-color: #f6f6f6;
	transform: scaleY(0.8);
	transform-origin: bottom;
}
.content-visibility-auto {
	content-visibility: auto;
}

.tab_touch_ul {
	position: absolute;
	left: 0;
	width: 100%;
	height: 40px;
	vertical-align: top;
	white-space: nowrap;
	overflow-x: auto;
	overflow-y: hidden;
	-webkit-overflow-scrolling: touch;
}
.tab_touch_li {
	color: white;
	height: 40px;
	font-size: 20px;
	display: inline-block;
	vertical-align: top;
}
@keyframes light {
	0% {
		left: -20px;
	}
	25% {
		left: -20px;
	}
	50% {
		left: 250px;
	}
	75% {
		left: 250px;
	}
	100% {
		left: 250px;
	}
}

:root {
	--navbar-height: 66px;
}

.bradge-tag {
	position: absolute;
	top: 0;
	right: 0;
	transform: translate(72%, -12%);
	width: 23px;
	height: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: url("@/assets/aiwork/nav/nav-tag-bg.png") no-repeat center center /
		contain;
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9;

	.text {
		width: 13px;
		height: 7px;
	}
	&::after {
		content: "";
		.text;
	}
	&.hot::after {
		background: url("@/assets/aiwork/nav/hot.png") no-repeat center center /
		contain;
	}
	&.new::after {
		background: url("@/assets/aiwork/nav/new.png") no-repeat center center /
			contain;
	}
}

.tag-badge {
	position: absolute;
	top: 0;
	right: 0;
	transform: translate(72%, -12%);
	width: 23px;
	height: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: url("@/assets/aiwork/nav/nav-tag-bg.png") no-repeat center center /
		contain;
	display: flex;
	justify-content: center;
	align-items: center;

	.text {
		width: 13px;
		height: 7px;
	}
	.hot {
		.text;
		background: url("@/assets/aiwork/nav/hot.png") no-repeat center center /
			contain;
	}
	.new {
		.text;
		background: url("@/assets/aiwork/nav/new.png") no-repeat center center /
			contain;
	}
}

.loader {
	display: inline-block;
	width: calc(var(--loader-size, 16px) * 3 + var(--s-loader-gap) * 2);
	--s-loader-bg: var(--loader-bg, #fff);
	--s-loader-bg-end: var(--loader-bg-end, #999);
	--s-loader-duration: var(--loader-duration, 1s);
	--s-loader-gap: var(--loader-gap, 32px);
	position: relative;
	&::before {
		content: "";
		display: block;
		width: var(--loader-size, 16px);
		height: var(--loader-size, 16px);
		border-radius: 50%;
		background-color: var(--s-loader-bg);
		box-shadow: var(--s-loader-gap) 0 var(--s-loader-bg), calc(var(--s-loader-gap) * -1) 0 var(--s-loader-bg);
		position: relative;
		left: 50%;
		transform: translateX(-50%);
		animation: flash var(--s-loader-duration) ease-out infinite alternate;
	}

}

@keyframes flash {
  0% {
    background-color: var(--s-loader-bg-end);
    box-shadow: var(--s-loader-gap) 0 var(--s-loader-bg-end), calc(var(--s-loader-gap) * -1) 0 var(--s-loader-bg);
  }
  50% {
    background-color: var(--s-loader-bg);
    box-shadow: var(--s-loader-gap) 0 var(--s-loader-bg-end), calc(var(--s-loader-gap) * -1) 0 var(--s-loader-bg-end);
  }
  100% {
    background-color: var(--s-loader-bg-end);
    box-shadow: var(--s-loader-gap) 0 var(--s-loader-bg), calc(var(--s-loader-gap) * -1) 0 var(--s-loader-bg-end);
  }
}

