@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
	.font-variation-settings-opsz-auto {
		font-variation-settings: "opsz" auto;
	}
	.bg-gradient-text {
		background: linear-gradient(103deg, #0039ff 28%, #a523ff 87%);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
		text-fill-color: transparent;
	}
	.TextAlignLastJustify {
		text-align-last: justify;
	}
	.HideScrollbar {
		-ms-overflow-style: none;
		scrollbar-width: none;
	}
	.HideScrollbar::-webkit-scrollbar {
		display: none; /* Chrome Safari */
	}
	.PaymentTabActive {
		background: linear-gradient(90deg, #0044ff 0%, #9e51f7 94%);
	}
	.PaymentTab {
		background: linear-gradient(90deg, #b9d2fd 0%, #cae9ff 100%);
	}
	.PaymentTabPlain {
		background: white;
	}

	:root {
		--ai-painting-module: 0 0% 100%;
    --ai-painting-upload-background: 0 0% 96%;
    --ai-drawing-card: 210 14% 95%;
    --scrollbar-thumb: 0 0% 49%;
    --secondary-hover: 210 10% 92%;
    --ai-writing-selection: 220 100% 90%;
    --lookup: 44 100% 80%;
    --lookup-selection: 2 96% 82%;
	}
}
