import type {
	AxiosProgressEvent,
	AxiosResponse,
	GenericAbortSignal,
} from "axios";
import request from "./axios";
import { useAuthStore, useUserStore } from "@/store";
import { useRoute } from "vue-router";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import { getToken } from "@/store/modules/auth/helper";
import { riskControl } from "@/chatgpt";
import { captchaInit } from "../functions";

export enum SelfRequestCode {
	Ok = 0,
	OFFICENEEDPAY = "50001",
	PASSWORDNEEDPAY = "50010",
	NeedLogin = 10000001,
	NeedPay = 500100100001,
	Risk = 3************,
	NoTeamPrimess = 600100100001,
}

// 忽略错误提示code
const IGNORERRORECODE = ["10004"];

export interface HttpOption {
	url: string;
	data?: any;
	method?: string;
	headers?: any;
	onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void;
	onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
	signal?: GenericAbortSignal;
	beforeRequest?: () => void;
	afterRequest?: () => void;
	responseType?:
		| "arraybuffer"
		| "blob"
		| "document"
		| "json"
		| "text"
		| "stream"
		| undefined;
}

export interface Response<T = any> {
	errmsg: string | null;
	data: T;
	message: string | null;
	status: string;
	errcode?: number | string;
}

export interface HttpExtraOption {
	level?: 0 | 1; // 0: 异常不显示, 1: 异常通知提示
	needLogin?: boolean; // 是否需要登录
	payParams?: any; // 支付参数
}

function isBlob(val) {
	return toString.call(val) === "[object Blob]";
}

function http<T = any>(options: HttpOption, extra?: HttpExtraOption) {
	const {
		url,
		data,
		method = "GET",
		headers,
		onDownloadProgress,
		onUploadProgress,
		signal,
		beforeRequest,
		afterRequest,
		responseType,
	} = options;
	const { level = 1 } = extra || {};
	const successHandler = (res: AxiosResponse<Response<T>>) => {
		const authStore = useAuthStore();
		const userStore = useUserStore();
		const result: any = res.data;
		if (result.errcode == "0") {
			return result.data;
		}
		if (isBlob(result)) {
			return result;
		}

		if (res.data.status === "Success" || typeof res.data === "string")
			return res.data;

		res.data.message = res.data?.errmsg || res.data.message;
		// 需登录
		if (res.data.status === "Unauthorized") {
			level === 1 && window.$aiwork?.openLogin?.();
			return Promise.reject(res.data);
		}
		if (result.errcode == SelfRequestCode.OFFICENEEDPAY || result.errcode == SelfRequestCode.PASSWORDNEEDPAY) {
			return Promise.reject(res.data);
		}
		if (result.errcode == SelfRequestCode.NeedLogin) {
			// authStore.removeToken();
			userStore.resetUserInfo();
			// level && window.$aiwork?.openLogin?.();
			// return Promise.reject(res.data);
			return window.$aiwork
				?.openLogin?.()
				.then(() => http(options, extra))
				.catch((err) =>
					Promise.reject({
						...res.data,
						loginErr: err,
					})
				);
		}

		// 需充值
		if (result.errcode == SelfRequestCode.NeedPay) {
			// // 需要接口自行处理
			// rechargeErrorHandler();
			// return Promise.reject(res.data);
			return window.$aiwork
				?.openRecharge?.({
					type: "ai",
					paperId: result?.data?.paperId || undefined,
					...extra?.payParams,
				})
				.then((res) => http(options, extra))
				.catch((err) =>
					Promise.reject({
						...res.data,
						payErr: err,
					})
				);
		}

		level &&
			!IGNORERRORECODE.includes(res.data.errcode as string) &&
			window.$notification?.error({
				title: "请求失败",
				content: res.data.message as string,
				duration: 3000,
				onClose: () => {
					if(result.errcode == SelfRequestCode.NoTeamPrimess) window.location.href = '/'
				}
			});
		return Promise.reject(res.data);
	};

	const failHandler = (error: Response<Error>) => {
		afterRequest?.();
		const errors: any = error;

		if (
			errors?.response?.status === 429 &&
			errors?.response?.data.errcode === "************"
		) {
			return Promise.reject(errors?.response?.data);
		} else if (errors.code == "ERR_CANCELED") {
			return Promise.reject(errors);
		} else {
			window.$notification?.error({
				// title: "404",
				content: "请求失败, 请稍后重试",
				duration: 3000,
			});
			throw new Error(
				errors?.response?.data?.errmsg ||
					errors?.response?.data?.message ||
					error?.message
			);
		}
	};

	const userStore = useUserStore();
	if (extra?.needLogin && !userStore.userInfo.uid) {
		return window.$aiwork
			?.openLogin?.()
			.then(() => http(options, extra))
			.catch((err) =>
				Promise.reject({
					message: "取消登录",
					loginErr: err,
				})
			);
	}

	beforeRequest?.();

	const params = Object.assign(
		typeof data === "function" ? data() : data || {},
		{}
	);
	return method === "GET"
		? request
				.get(url, {
					params,
					signal,
					onDownloadProgress,
					onUploadProgress,
					responseType,
				})
				.then(successHandler, failHandler)
		: request
				.post(url, params, {
					headers,
					signal,
					onDownloadProgress,
					onUploadProgress,
					responseType,
				})
				.then(successHandler, failHandler);
}

export function get<T = any>({
	url,
	data,
	method = "GET",
	onDownloadProgress,
	onUploadProgress,
	signal,
	beforeRequest,
	afterRequest,
}: HttpOption): Promise<Response<T>> {
	return http<T>({
		url,
		method,
		data,
		onDownloadProgress,
		onUploadProgress,
		signal,
		beforeRequest,
		afterRequest,
	});
}

export function post<T = any>(
	{
		url,
		data,
		method = "POST",
		headers,
		onDownloadProgress,
		onUploadProgress,
		signal,
		beforeRequest,
		afterRequest,
		responseType,
	}: HttpOption,
	extra?: HttpExtraOption
): Promise<T> {
	return http<T>(
		{
			url,
			method,
			data,
			headers,
			onDownloadProgress,
			onUploadProgress,
			signal,
			beforeRequest,
			afterRequest,
			responseType,
		},
		extra
	);
}
class RetriableError extends Error {}
class FatalError extends Error {}

export async function postSse({
	url,
	data,
	signal,
	payParams = {},
	onDownloadProgress,
	onError,
	onRepay,
}) {
	const token = getToken();
	const userStore = useUserStore();
	const authStore = useAuthStore();

	try {
		const isCaptcha: any = await riskControl('long');
		await new Promise((resolve, reject) => {
			if(!isCaptcha?.captcha) return resolve(true)
			captchaInit(authStore.captchaAppId, async function (captchaData: any) {
				if (captchaData && captchaData.randstr) {
					data.randstr = captchaData.randstr;
					data.ticket = captchaData.ticket;
					resolve(true)
				} else {
					reject(captchaData)
				}
			});
		})
	} catch (captchaData) {
		return onError(captchaData)
	}
	fetchEventSource(url, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Bearer ${getToken()}`,
			"ai-team": (userStore.curTeam?.id) || ''
		},
		body: JSON.stringify(data),
		signal: signal.signal,
		openWhenHidden: true,
		onopen: async (response) => {
			console.log("onopen:", response);
			if (response.ok) {
				return; // everything's good
			} else if (
				response.status >= 400 &&
				response.status < 500 &&
				response.status !== 429
			) {
				// client-side errors are usually non-retriable:
				throw new FatalError();
			} else {
				throw new RetriableError();
			}
		},
		onmessage(msg) {
			const eventName = msg.event.trim();
			if (eventName === "Buy") {
				onError?.();
				return window.$aiwork
					?.openRecharge?.({
						type: "ai",
						...(data?.paperId ? { paperId: data.paperId } : {}),
						...payParams,
					})
					.then(() => onRepay?.({ type: "buy" }))
					.catch(
						(err) => {}
						// Promise.reject({
						// 	...res.data,
						// 	payErr: err,
						// })
					);
			} else if (eventName === "Login") {
				onError?.();
				return window.$aiwork?.openLogin?.().catch((err) =>
					Promise.reject({
						message: "取消登录",
						loginErr: err,
					})
				);
			}
			// if the server emits an error message, throw an exception
			// so it gets handled by the onerror callback below:
			else if (msg.event === "Error") {
				onError?.();
				throw new FatalError(msg.data);
			} else {
				onDownloadProgress(msg);
			}
		},
		onerror(err) {
			throw new FatalError(err);
		},
	});
}
export default post;
