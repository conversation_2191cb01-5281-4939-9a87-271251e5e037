import axios, { type AxiosResponse } from 'axios'
import { getToken } from '@/store/modules/auth/helper'
import { useUserStore, useAuthStore } from '@/store';
axios.defaults.timeout = 300000;

const service = axios.create({
  baseURL: import.meta.env.VITE_GLOB_API_URL,
})

service.interceptors.request.use(
  (config) => {
		const token = getToken()
		const userStore = useUserStore()
		const authStore = useAuthStore()
		  if (token)
		    config.headers.Authorization = `Bearer ${token}`
		// @ts-ignore
		config.headers['landing-page'] = window.landingUrl
		config.headers['ai-team'] = userStore.curTeam?.id || ''

		  // 添加shareId到header
		  const shareId = authStore.getValidShareId();
		  if (shareId) {
		    config.headers['ai-share'] = shareId;
		  }

    return config
  },
  (error) => {
    return Promise.reject(error.response)
  },
)

service.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => {
    if (response.status === 200)
      return response

    throw new Error(response.status.toString())
  },
  (error) => {
    return Promise.reject(error)
  },
)

export default service
