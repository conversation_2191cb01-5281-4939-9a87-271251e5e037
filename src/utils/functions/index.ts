export function getCurrentDate() {
  const date = new Date()
  const day = date.getDate()
  const month = date.getMonth() + 1
  const year = date.getFullYear()
  return `${year}-${month}-${day}`
}
export function captchaInit(captchaAppId:string,callback:any){
  const captcha = new (window as any).TencentCaptcha(captchaAppId, function(res:any){
    if (res && res.ticket) {
      callback&&callback({
        ticket:res.ticket,
        randstr:res.randstr

      })
    }else{
      callback && callback({
        errcode:'1051',
        message:"请完成拼图！",
        errmsg:"请完成拼图！"
      })
    }
    
  });
  captcha.show();
}

export function downloadIamge(imgSrc: string, fileName?: string) {
	const xhr = new XMLHttpRequest()
	xhr.open('GET',imgSrc)
	xhr.responseType = 'blob'
	xhr.send()
	xhr.onload = function () {
			const fileBlob = xhr.response;
			const fileUrl = URL.createObjectURL(fileBlob)
			console.log(fileUrl)
			const ele = document.createElement('a')
			ele.setAttribute('href',fileUrl)
			ele.setAttribute('download',"")
			ele.click()
			ele.innerHTML = '下载'
			document.body.appendChild(ele)
	}
}