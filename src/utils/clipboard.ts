/**
 * 高可用的剪贴板操作工具函数
 */

import { MessageApi } from 'naive-ui';

/**
 * 复制文本到剪贴板的高可用实现
 *
 * 该函数尝试多种方法复制文本到剪贴板：
 * 1. 首先尝试使用现代的 navigator.clipboard API
 * 2. 如果失败，回退到传统的 document.execCommand 方法
 * 3. 如果仍然失败，创建一个临时的文本区域元素进行复制
 *
 * @param text 要复制的文本
 * @param message 可选的消息API，用于显示成功/失败通知
 * @returns Promise<boolean> 表示复制是否成功
 */
export const copyToClipboard = async (
  text: string,
  message?: MessageApi
): Promise<boolean> => {
  // 记录是否成功复制
  let success = false;

  try {
    // 方法1: 使用现代的 Clipboard API (最佳方法，但需要安全上下文)
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(text);
      success = true;
    } else {
      throw new Error('Clipboard API not available');
    }
  } catch (error) {
    console.warn('Clipboard API failed, trying fallback methods', error);

    try {
      // 方法2: 使用传统的 document.execCommand 方法
      const textArea = document.createElement('textarea');
      textArea.value = text;

      // 防止滚动到底部
      textArea.style.position = 'fixed';
      textArea.style.top = '0';
      textArea.style.left = '0';
      textArea.style.width = '2em';
      textArea.style.height = '2em';
      textArea.style.padding = '0';
      textArea.style.border = 'none';
      textArea.style.outline = 'none';
      textArea.style.boxShadow = 'none';
      textArea.style.background = 'transparent';
      textArea.style.opacity = '0';

      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      // 执行复制命令
      success = document.execCommand('copy');

      // 清理
      document.body.removeChild(textArea);

      if (!success) {
        throw new Error('execCommand copy failed');
      }
    } catch (fallbackError) {
      console.error('All clipboard methods failed', fallbackError);

      // 所有方法都失败了
      if (message) {
        message.error('复制失败，请手动复制');
      }
      return false;
    }
  }

  // 如果成功并且提供了消息API，显示成功消息
  if (success && message) {
    message.success('复制成功');
  }

  return success;
};

/**
 * 复制密码到剪贴板的专用函数
 *
 * @param password 要复制的密码
 * @param message 消息API，用于显示通知
 * @returns Promise<boolean> 表示复制是否成功
 */
export const copyPassword = async (
  password: string,
  message: MessageApi
): Promise<boolean> => {
  const success = await copyToClipboard(password, message);

  if (!success) {
    // 提供额外的指导
    message.warning('无法自动复制密码，请记住密码: ' + password);
  }

  return success;
};
