import { markdown2tree } from "./index";

/**
 * 将Markdown文本转换为大纲格式。
 *
 * @example
 * 输入
 *
  # 图书管理系统
  ## 1. 引言
  ### 1.1 介绍研究背景与动机
	- xxxxx
	- xxxxx
  ### 1.2 明确研究问题与目的
  ### 1.3 研究方法概述与论文结构
 * @example
  // 输出
  {
   title: "图书管理系统",
   topic: "",
   level: 1,
   children: [
    {
     title: "1. 引言",
     level: 2,
     children: [
      {
       title: "1.1 介绍研究背景与动机",
       level: 3,
       children: [
        // text
       ],
      },
      {
       title: "1.2 明确研究问题与目的",
       level: 3,
       children: [],
      },
      {
       title: "1.3 研究方法概述与论文结构",
       level: 3,
       children: [],
      },
     ],
    },
   ],
 */
export const markdown2outline = (content: string) => {
	// 去除markdown代码块标识， 转换成树状结构
	const mdTree = markdown2tree(
		content.replace(/^```(markdown)?/, "").replace(/```$/, "")
	);
	// 树状结构转换为符合大纲的树
	const mdContentList = mdTreeFormatAsOutline(mdTree);

	// 最多4级， 也就是包含level:3以及children的节点
	// 一级
	const _outline: any = {
		title: "",
		topic: "",
		level: 1,
		children: [],
	};

	mdContentList.forEach((item: any, index: any) => {
		if (index === 0) return (_outline.title = item.text); // 标题
		if (index === 1 && item.type !== "heading")
			return (_outline.topic = item.text); // 题目

		if (item.type === "heading") {
			// 二级
			if (item.depth === 2) {
				const current = _outline;
				current.children.push({
					title: item.text,
					level: 2,
					children: [],
				});
			}
			// 三级
			if (item.depth === 3) {
				const current = _outline.children.at(-1);

				current.children.push({
					chartType: null, // mind:思维导图,flow：流程图
					table: false, // 数据表
					content: item?.text ? item?.text + "\n    " : null,
					//  +
					// _outline.children.map((x: any) => x.text).join("\n") +
					// "\n"
					title: item.text,
					level: 3,
					children: [],
				});
			}
			// 当作四级文本
			if (item.depth === 4) {
				const current = _outline.children.at(-1).children.at(-1);

				current.children.push(item.text);
			}
		}
		// 四级文本
		if (item.type === "list") {
			const current =
				_outline.children.at(-1)?.children.at(-1) ?? _outline.children.at(-1);
			const childText = item.children.map((x: any) => x.text);
			const childTitle = item.children.map((x: any) => " - " + x.text);
			current.children = current.children.concat(childText);
			if (current.content) {
				current.content += childTitle.join("\n") + "\n";
			}
		}
	});
	console.log(_outline);

	return _outline;
};

/**
 * 将Markdown文本转换为树形结构。
 *
 * @param markdownText 输入的Markdown文本
 * @returns 树形结构的对象
 */
export function markdown2TreeOutline(markdownText: string) {
	try {
		// 将输入的 Markdown 文本按行分割
		const lines = markdownText.split("\n");
		// 初始化树结构的根节点
		const root = { key: "root", label: "Root", children: [] };
		const stack: any = [root]; // 使用栈来跟踪当前节点
		let currentLevel = 0; // 当前节点的级别
		let counter = 0; // 计数器，用于生成唯一键

		// 创建唯一键的函数
		function createKey(label) {
			return `${label.toLowerCase().replace(/[^a-z0-9]+/g, "-")}-${counter++}`;
		}

		// 遍历每一行
		lines.forEach((line) => {
			// 使用正则表达式匹配标题行
			const match = line.match(/^(#+)\s*(.*)/);
			if (match) {
				const level = match[1].length; // 标题的级别
				const label = match[2]; // 标题的文本
				const node = { key: createKey(label), label, children: [] }; // 创建新节点

				// 根据当前级别调整栈
				while (currentLevel >= level) {
					stack.pop(); // 弹出栈中顶层节点
					currentLevel--; // 更新当前级别
				}

				// 将新节点添加到当前栈顶节点的子节点中
				stack[stack.length - 1].children.push(node);
				stack.push(node); // 将新节点压入栈
				currentLevel = level; // 更新当前级别
			} else if (currentLevel > 0 && line.trim() !== "") {
				// 如果是普通文本行，则添加到当前节点的子节点
				const lastNode = stack[stack.length - 1];
				if (!lastNode.children) {
					lastNode.children = [];
				}
				lastNode.children.push({
					key: createKey(line), // 为普通文本行创建唯一键
					label: line,
				});
			}
		});

		// 清理节点的函数，去掉没有子节点的空节点
		function cleanNode(node) {
			if (node.children && node.children.length === 0) {
				delete node.children; // 删除没有子节点的属性
			} else if (node.children) {
				node.children.forEach(cleanNode); // 递归清理子节点
			}
		}
		cleanNode(root); // 从根节点开始清理

		// 返回根节点的子节点
		return root.children;
	} catch (error) {
		// 捕捉异常并输出警告信息
		console.warn("markdownText", markdownText);
	}
}

/**
 * 将大纲对象转换为Markdown格式的字符串。
 *
 * @param outline 大纲对象
 * @param res 累加的Markdown字符串
 * @returns 转换后的Markdown字符串
 */
export const outline2markdown = (outline: any, res: string = "") => {
	// 将大纲对象转换为 Markdown 格式的字符串
	if (typeof outline === "string") return res + "- " + outline + "\n";
	else if (outline.level)
		res += `${"#".repeat(outline.level)} ${outline.title}\n`;
	if (outline.children?.length) {
		// 遍历子项并递归调用转换函数
		outline.children.map((item) => (res += outline2markdown(item)));
		res += "\n";
	}
	return res;
};

interface OutlineRes {
	title: string;
	child: OutlineChild[];
}

interface OutlineChild {
	chartType?: string | null;
	table?: boolean;
	content: string | null;
}

interface Outline {
	title: string;
	topic: string;
	level: number;
	children: Child2[];
}

interface Child2 {
	title: string;
	level: number;
	children: (Child | Children2)[];
}

interface Children2 {
	chartType: null | string;
	table: boolean;
	content: string;
	title: string;
	level: number;
	children: string[];
}

interface Child {
	chartType: null;
	table: boolean;
	content: string;
	title: string;
	level: number;
	children: string[];
}

/**
 * level: 1 的数据不要 只拿2 3层的 转成OutlineRes
 * @param outline
 * @param res
 */
export const newOutline2markdown = (outline: Outline): OutlineRes => {
	const tempOutline = outline.children; // 这是去除第一层后的数据
	return tempOutline.map((item) => {
		const { title, children } = item;
		const child = children.map(
			({ chartType = null, table = false, title = null, content }) => {
				return {
					chartType,
					table,
					content: title ? title + "\n" : content,
				};
			}
		);
		return {
			title,
			child,
		};
	});
};

/**
 * 将Markdown树结构格式化为大纲格式。
 *
 * @param tree Markdown树节点
 * @returns 大纲格式的结构
 */
const mdTreeFormatAsOutline = (tree: any) => {
	// 根据树的类型对其进行格式化并返回相应的输出
	switch (tree.type) {
		case "root":
			return tree.children.map((x: any) => mdTreeFormatAsOutline(x));
		case "text":
			return tree.value;
		case "heading":
			// 创建一个包含文本和深度的标题对象
			return {
				text: tree.children.map((x: any) => mdTreeFormatAsOutline(x)).join(""),
				depth: tree.depth,
				type: "heading",
			};
		case "list":
			// 创建一个列表对象，包含有序性、类型、展开状态及子项
			return {
				ordered: tree.ordered,
				type: "list",
				spread: tree.spread,
				children: tree.children.map((x: any) => mdTreeFormatAsOutline(x)),
			};
		case "listItem":
			// 创建一个列表项对象，包含类型、展开状态、检查状态及文本
			return {
				type: "listItem",
				spread: tree.spread,
				checked: tree.checked,
				text: tree.children
					.map((x: any) => mdTreeFormatAsOutline(x))
					.map((x: any) => x?.text || x)
					.join(""),
			};
		case "paragraph":
			// 创建一个段落对象，包含子项和文本内容
			return {
				type: "paragraph",
				children: tree.children.map((x: any) => mdTreeFormatAsOutline(x)),
				text: tree.children.map((x: any) => mdTreeFormatAsOutline(x)).join(""),
			};
		case "strong":
			// 返回加粗文本的内容
			return tree.children.map((x: any) => mdTreeFormatAsOutline(x)).join("");
	}
};
