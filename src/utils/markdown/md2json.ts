import { marked } from "marked";

export interface ITokens {
  type: string;
  row?: string;
  depth: number;
  text?: string;
  parent?:  any;
  tokens?: ITokens[];
  children?: any[];
}

const formatJson = (list: ITokens[]) => {
	for(let item of list) {
			delete item.parent
			delete item.tokens
			if(Array.isArray(item.children)) {
					formatJson(item.children)
			}
	}
	return list
}

export const markdownToJson = (markdown: string) => {
	const tokens: any = marked.lexer(markdown);
	let result = [] as any[];
	let prev: any;
	let parent: any;

	console.log(tokens);

	for (let j of tokens) {
		if(j.type === 'heading') {
				let i = {
					depth: j.depth,
					text: j.text,
					parent: j.parent,
					type: j.type
				};
				if (prev) {
					if (prev.depth - j.depth < 0) {
						i.parent = prev;
						parent = prev;
						if (!parent.children) {
							parent.children = [i];
						} else {
							parent.children.push(i);
						}
					} else if (prev.depth - j.depth === 0) {
						i.parent = parent;
						if (prev.parent) {
							parent = prev.parent;
							parent.children.push(i);
						} else {
							result.push(i);
						}
					} else {
						let current = prev;

						while (current.parent) {
							if (current.parent.depth === i.depth) {
								if (current.parent.parent) {
									parent = current.parent.parent;
									i.parent = parent;
									parent.children.push(i);
								} else {
									// 到达根节点
									parent = null;
									i.parent = null;
									result.push(i);
								}
								break;
							}
							current = current.parent;
						}

						if (!current.parent) {
							parent = null;
							i.parent = null;
							result.push(i);
						}
					}
				} else {
					result.push(i);
				}
				prev = i;
		} else if(j.type === 'list' && j.items?.length>0) {
			if(prev.children) {
				prev.children.push(j.items[0]);
			} else {
				prev.children = []
				prev.children.push(j.items[0]);
			}
		}
	}
	const newResult = formatJson(result)
	return newResult;
};
