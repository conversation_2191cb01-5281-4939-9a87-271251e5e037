import type { AxiosProgressEvent, GenericAbortSignal } from "axios";
import { post, get, postSse } from "@/utils/request";
import { useSettingStore, useUserStore } from "@/store";

export function fetchChatAPI<T = any>(
	prompt: string,
	options?: { conversationId?: string; parentMessageId?: string },
	signal?: GenericAbortSignal
) {
	return post<T>({
		url: "/api3/aiwork/core/chat",
		data: { prompt, options },
		signal,
	});
}

export function fetchChatConfig<T = any>() {
	return post<T>({
		url: "/api3/aiwork/core/config",
	});
}

export function fetchChatAPIProcess2(params: {
	signal?: GenericAbortSignal;
	onDownloadProgress?: (response: any) => void; // Provide an initializer for onDownloadProgress
	onError: (error: any) => void; // Provide an initializer for onError
	onRepay: (response: any) => void; // Provide an initializer for onRepay
	data: any; // Add an initializer for the 'data' property
}) {
	return postSse({
		url: "/api3/aiwork/long/generateContent2",
		data: params.data, // Use the provided initializer for the 'data' property
		signal: params.signal,
		onDownloadProgress: params.onDownloadProgress,
		onError: params.onError, // Use the provided initializer for onError
		onRepay: params.onRepay, // Use the provided initializer for onRepay
	});
}

// TODO准备废弃
export function fetchChatAPIProcess<T = any>(params: {
	prompt: string;
	ticket?: string;
	randstr?: string;
	chatbotId?: number;
	createbotId?: number;
	formData?: any[];
	options?: { conversationId?: string; parentMessageId?: string };
	signal?: GenericAbortSignal;
	type?: "expand" | "rewrite" | "simplify" | "continue";
	onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void;
	data?: any;
	responseType?: any;
	isDeepseek?: boolean;
}) {
	const settingStore = useSettingStore();
	const publishAgentId = params.data?.publishAgentId || null
	const url = publishAgentId ? '/api3/aiwork/teamAgent/chat-agent' : '/api3/aiwork/core/chat-process'
	return post<T>(
		{
			url,
			data: {
				prompt: params.prompt,
				randstr: params.randstr,
				ticket: params.ticket,
				options: params.options,
				systemMessage: settingStore.systemMessage,
				chatbotId: params.chatbotId,
				createbotId: params.createbotId,
				formData: params.formData,
				type: params.type,
				isDeepseek: params.isDeepseek,
				...(params.data || {}),
			},
			signal: params.signal,
			onDownloadProgress: params.onDownloadProgress,
			responseType: params.responseType,
		},
		{ payParams: { type: "ai" } }
	);
}

export function fetchSession<T>() {
	return post<T>({
		url: "/api3/aiwork/core/session",
	});
}

export function fetchVerify<T>(username: string, password: string) {
	return post<T>({
		url: "/api3/aiwork/sign/login",
		data: { username, password },
	});
}
export function fetchRegister<T>(username: string, password: string) {
	return post<T>({
		url: "/api3/aiwork/sign/register",
		data: { username, password },
	});
}
export function fetchGetCode<T>(phone: string) {
	return post<T>({
		url: "/api3/aiwork/sign/code",
		data: { phone },
	});
}
export function fetchCodeLogin<T>(phone: string, code: string) {
	return post<T>({
		url: "/api3/aiwork/sign/codeLogin",
		data: { phone, code },
	});
}

// 商品列表
export function fetchGoodsList<T>(goodsType?: string) {
	return post<T>({
		url: "/api3/aiwork/goods/list",
		data: {
			goodsType,
		},
	});
}

// 支付
export function fetchAlipay<T>(params: any) {
	return post<T>({
		url: "/api3/aiwork/alipay/prepay",
		data: {
			url: window.landingUrl || "",
			teamId: useUserStore().curTeam?.id || "",
			...params,
		},
	});
}

export function fetchAlipayPrepay<T>(params: any) {
	return get<T>({
		url: `/api3/aiwork/alipay/prepay/${params.goodsId}`,
		data: {
			teamId: useUserStore().curTeam?.id || "",
		}
	});
}

export function fetchPayIspayy<T>() {
	return post<T>({
		url: `/api3/aiwork/pay/ispay`,
	});
}
export function fetchGetBindCode<T>(phone: string) {
	return post<T>({
		url: "/api3/aiwork/sign/bindCode",
		data: { phone },
	});
}
export function fetchGetBind<T>(
	phone: string,
	code: number,
	payOrderNo: string
) {
	return post<T>({
		url: "/api3/aiwork/sign/bind",
		data: { phone, code, payOrderNo },
	});
}

export function fetchImagine<T = any>(params: {
	prompt: string;
	options: any;
	signal?: GenericAbortSignal;
	onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void;
}) {
	return post<T>({
		url: "/api3/aiwork/mj/imagine",
		data: { prompt: params.prompt, params: params.options },
		signal: params.signal,
		onDownloadProgress: params.onDownloadProgress,
	});
}

export function fetchVariation<T = any>(params: {
	content: string;
	index: number;
	msgId: string;
	msgHash: string;
	options: any;
	signal?: GenericAbortSignal;
	onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void;
}) {
	return post<T>({
		url: "/api3/aiwork/mj/variation",
		data: {
			content: params.content,
			index: params.index,
			msgId: params.msgId,
			msgHash: params.msgHash,
			params: params.options,
		},
		signal: params.signal,
		onDownloadProgress: params.onDownloadProgress,
	});
}

export function fetchUpscale<T = any>(params: {
	content: string;
	index: number;
	msgId: string;
	msgHash: string;
	options: any;
	signal?: GenericAbortSignal;
	onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void;
}) {
	return post<T>({
		url: "/api3/aiwork/mj/upscale",
		data: {
			content: params.content,
			index: params.index,
			msgId: params.msgId,
			msgHash: params.msgHash,
			params: params.options,
		},
		signal: params.signal,
		onDownloadProgress: params.onDownloadProgress,
	});
}

// 获取messageid
export function fetchMjByMessageId<T = any>(params: { messageId: string }) {
	return post<T>({
		url: "/api3/aiwork/mj/getMjByMessageId",
		data: {
			messageId: params.messageId,
		},
	});
}

export function fetchPersonal<T>() {
	return post<T>({
		url: "/api3/aiwork/personal/detail",
		data: {},
	});
}
export function fetchAlipayPrepay2<T>(params: any) {
	return post<T>({
		url: `/api3/aiwork/alipay/prepay/${params.goodsId}`,
		data: {
			url: window.landingUrl || "",
			teamId: useUserStore().curTeam?.id || "",
			...params,
		},
	});
}

export function getAuthUrl<T>() {
	return post<T>({
		url: "/api3/aiwork/sign/getAuthUrl",
	});
}

export function checkAuth<T>(params: { messageId: string; type?: string }) {
	return post<T>({
		url: "/api3/aiwork/sign/checkAuth",
		data: params,
	});
}

export function bindPhone<T>(phone: number | string, code: number) {
	return post<T>({
		url: "/api3/aiwork/sign/bindPhone",
		data: { phone, code },
	});
}

export function bindPay<T>(payOrderNo: string) {
	return post<T>({
		url: "/api3/aiwork/sign/bindPay",
		data: { payOrderNo },
	});
}

export function upload<T>() {
	return post<T>({
		url: "/api3/aiwork/file/upload",
	});
}
export function riskControl<T>(type) {
	return post<T>({
		url: "/api3/aiwork/core/riskControl",
		data: { type },
	});
}
export function chatbotsList<T>(keywords: string) {
	return post<T>({
		url: "/api3/aiwork/chatbots/list",
		data: { keywords },
	});
}

export function chatbotShortcuts<T>(params: any) {
	return post<T>({
		url: "/api3/aiwork/core/shortcuts",
		data: params,
	});
}

export function mjList<T>(params: any) {
	return post<T>({
		url: "/api3/aiwork/material/mjlist",
		data: params,
	});
}

export function getAppsCategory<T>() {
	return post<T>({
		url: "/api3/aiwork/chatbots/category",
	});
}
export function getAppsCategoryList<T>(params: any) {
	return post<T>({
		url: "/api3/aiwork/chatbots/creates",
		data: params,
	});
}
export function getAppDetailByAppId<T>(params: any) {
	return post<T>({
		url: "/api3/aiwork/chatbots/detail",
		data: params,
	});
}
export function getCategoryAndCreatesByCategoryId<T>(params?: any) {
	return post<T>({
		url: "/api3/aiwork/chatbots/categoryAndCreates",
		data: params,
	});
}

export function getHistoryList<T>(params: any) {
	return post<T>({
		url: "/api3/aiwork/core/list",
		data: params,
	});
}

export function collect<T>(params: any) {
	return post<T>({
		url: "/api3/aiwork/core/collect",
		data: params,
	});
}

export function getCollectList<T>(params: any) {
	return post<T>({
		url: "/api3/aiwork/core/collects",
		data: params,
	});
}

// 收藏应用
export function collectApp<T>(params: any) {
	return post<T>({
		url: "/api3/aiwork/favorite/createbot",
		data: params,
	});
}
// 取消收藏应用
export function unCollectApp<T>(params: any) {
	return post<T>({
		url: "/api3/aiwork/favorite/unCreatebot",
		data: params,
	});
}
// 收藏应用列表
export function getCollectedCreatebotList<T>(params: any) {
	return post<T>({
		url: "/api3/aiwork/favorite/createbotList",
		data: params,
	});
}

// AI论文
export function fetchPaperList<T = any>(params: {
	page: number;
	pageSize: number;
	type: string;
	title?: string;
}) {
	return post<T>({
		url: "/api3/paper/core/list",
		data: {
			page: params.page,
			pageSize: params.pageSize,
			title: params.title,
			type: params.type,
		},
	});
}
export function fetchPaperByMessageId<T = any>(params: { messageId: string }) {
	return post<T>({
		url: "/api3/paper/core/getByMessageId",
		data: {
			messageId: params.messageId,
		},
	});
}

export function fetchPaperDelete<T = any>(params: { id: number }) {
	return post<T>({
		url: "/api3/paper/core/del",
		data: {
			id: params.id,
		},
	});
}

export function fetchGenerateCommonPaper<T = any>(params: {
	id: number;
	formData: object;
}) {
	return post<T>({
		url: "/api3/paper/core/generateCommon",
		data: {
			id: params.id,
			formData: params.formData,
		},
	});
}

export function fetchPaperTemplates<T = any>() {
	return post<T>({
		url: "/api3/paper/template/listAll",
		data: {},
	});
}

export function fetchPaperTemplate<T = any>(params: { type: string }) {
	return post<T>({
		url: "/api3/paper/template/detail",
		data: {
			type: params.type,
		},
	});
}
// 首页详情
export function homeFetch<T>(params: {
	count?: number;
	hotCount?: number;
}): Promise<any> {
	return post<T>({
		url: "/api3/aiwork/home/<USER>",
		data: params,
	});
}
// 首页搜索
export function homeSearch<T>(params: { keywords?: string }) {
	return post<T>({
		url: "/api3/aiwork/home/<USER>",
		data: {
			...params,
		},
	});
}
// 长文生成大纲
export function generateLongArticleOutline<T>(params: {
	createbotId: number;
	formData: any;
}) {
	return post<T>({
		url: "/api3/aiwork/long/generateOutline",
		data: params,
	});
}
// 长文流式生成大纲2
// export function generateLongArticleOutline2<T>(params: {
// 	createbotId: number;
// 	formData: any;
// }) {
// 	return post<T>({
// 		url: "/api3/aiwork/long/generateOutline2",
// 		data: params,
// 	});
// }

export function generateLongArticleOutline2<T = any>(params: {
	ticket?: string;
	randstr?: string;
	chatbotId?: number;
	createbotId?: number;
	formData?: any[];
	isNetwork?: boolean
	options?: { conversationId?: string; parentMessageId?: string };
	signal?: GenericAbortSignal;
	type?: "expand" | "rewrite" | "simplify" | "continue";
	onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void;
	data?: any;
	responseType?: any;
}) {
	const settingStore = useSettingStore();
	return post<T>(
		{
			url: "/api3/aiwork/long/generateOutline2",
			data: {
				randstr: params.randstr,
				ticket: params.ticket,
				options: params.options,
				systemMessage: settingStore.systemMessage,
				chatbotId: params.chatbotId,
				createbotId: params.createbotId,
				formData: params.formData,
				isNetwork: params.isNetwork,
				type: params.type,
				...(params.data || {}),
			},
			signal: params.signal,
			onDownloadProgress: params.onDownloadProgress,
			responseType: params.responseType,
		},
		{
			payParams: { type: "ai" },
		}
	);
}

// 长文生成内容
export function generateLongArticleContent<T>(params: {
	outlineId: any;
	outline?: any;
}) {
	return post<T>(
		{
			url: "/api3/aiwork/long/generateContent",
			data: params,
		},
		{ payParams: { type: "ai" } }
	);
}
// 获取长文内容结果
export function getLongArticleOutlineByMessageId<T>(params: {
	messageId: any;
}) {
	return post<T>(
		{
			url: "/api3/aiwork/long/getByMessageId",
			data: params,
		},
		{ payParams: { type: "ai" } }
	);
}

// 上报反馈
export function sendFeedbackReport<T = any>(params: {
	desc: string;
	phone?: string;
	files?: string[];
}) {
	return post<T>({
		url: "/api3/aiwork/feedback/report",
		data: params,
	});
}

export function markdownToDocx(params: { chatId: string; name?: string }) {
	return post({
		url: "/api3/paper/core/todocx",
		data: params,
		responseType: "blob",
	});
}

export function delCreationHistory(params: { chatId: string }) {
	return post({
		url: "/api3/aiwork/core/delHistory",
		data: params,
	});
}
export function getContentByUrl(params: { url: string }) {
	return post({
		url: "/api3/aiwork/search/getContentByUrl",
		data: params,
	});
}

export function generatePPT(params: { chatId: string, templateId: string }) {
	return post({
		url: "/api3/aiwork/long/generatePPT",
		data: params,
	});
}




/**
 * 获取ppt模板列表
 * @param params 
 * @returns 
 */
export function fetchPPTListV2<T>(params: any) {
	return post<T>({
		url: "/api3/ppt/v2/template/list",
		data: params,
	});
}

/**
 * 获取ppt模板目录
 * @param params 
 * @returns 
 */
export function fetchPPTTemplateCatalogV2<T>(params: any) {
	return post<T>({
		url: "/api3/ppt/v2/template/catalog",
		data: params,
	});
}



// export function feedbackUpload<T>(params: {
// 	desc: string;
// 	phone?: string;
// 	files?: string[];
// }) {
// 	return post<T>({
// 		url: "/api3/aiwork/feedback/upload",
// 		data: params,
// 	});
// }
