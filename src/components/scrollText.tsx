import {
 defineComponent,
 watch,
 ref,
 onMounted,
 nextTick,
 VNodeArrayChildren,
 VNode,
 VNodeChild,
 onUnmounted,
} from "vue";

// 创建一个返回 Promise 的 sleep 函数，用于延迟执行
const sleep = (ms?: number) => {
 return new Promise<void>((resolve) => {
  if (!ms) nextTick(resolve);
  else setTimeout(resolve, ms);
 });
};

// 定义 ScrollText 组件
export const ScrollText = defineComponent(
 (props, { slots }) => {
  const $ele = ref<any>(null);
  const $list = ref<any>(null);
  // const $target = ref<any>(null)
  const styles = ref<any>("height: 1.5em;white-space: nowrap;");
  const _current = ref(0);
  const $prev = ref<any>(null);
  const timer = ref<any>();

  // 监视 props.current 的变化并相应更新 _current
  watch(
   () => props.current,
   (v) => {
    if (props.delayUpdate) {
     setTimeout(() => (_current.value = v), props.delayUpdate);
    } else _current.value = v;
   }
  );

  // 组件挂载后执行初始化操作
  onMounted(() => {
   if (!$ele.value) return;
   if ($ele.value.children.length > 0) return init();
	//  监听子节点加载, 才进行初始化, 不然获取不到子节点
   const observer = new MutationObserver((mutationsList, observer) => {
    for (const mutation of mutationsList) {
     if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
      console.log("子节点已添加:", mutation.addedNodes);
      // 在第一次检测到子节点添加后断开观察器
      observer.disconnect();

      init();
      break;
     }
    }
   });
   observer.observe($ele.value, { childList: true });

	//  监听页面显示或隐藏
	 document.addEventListener('visibilitychange', handleTogglePageView);
  });

  // 组件卸载时清除定时器
  onUnmounted(() => {
		timer.value && clearInterval(timer.value);
		document.removeEventListener('visibilitychange', handleTogglePageView);
	});

  // 初始化函数，设置 $list 并处理下一项
  const init = () => {
   $list.value = $ele.value.children;
   handleNext();

   if (props.autoplay) {
    timer.value = setInterval(() => {
     _current.value++;
     if (_current.value >= $list.value.length) _current.value = 0;
    }, props.interval || 2000);
   }
  };

  // 监视 _current 的变化并处理下一项
  watch(
   () => _current.value,
   (v) => handleNext()
  );
  // 监视 props.current 的变化并更新 _current
  watch(
   () => props.current,
   (v) => (_current.value = v)
  );

  // 处理下一个项的动画效果
  const handleNext = async () => {
   nextTick(async () => {
    $list.value = $ele.value.children;
    const target = $list.value[_current.value];
    if (!target) return;

    const _transition = "top 0.3s ease-in-out";
    const rect = target.firstChild?.getBoundingClientRect();
    const height = props.height || rect.height
    target.style.transition = "none";
    if ($prev.value) $prev.value.style.transition = _transition;

    target.style.top = "100%";
    if ($prev.value) $prev.value.style.top = "-100%";

    await sleep(50);
    styles.value = `height: ${height}px;min-width: ${Math.max(
     rect.width,
     100
    )}px`;
    target.style.transition = _transition;
    target.style.top = "0%";
    const _prev = $prev.value;

    await sleep(300);
    if (_prev) _prev.style.transition = "none";
    $prev.value = target;
   });
  };

	const handleTogglePageView = () => {
		if (document.visibilityState === 'visible') {
			console.log('页面变得可见');
			// 在页面变得可见时执行的操作
			init()

		} else if (document.visibilityState === 'hidden') {
			console.log('页面变得不可见');
			// 在页面变得不可见时执行的操作
			timer.value && clearInterval(timer.value);
		}
	}

  // 渲染每个项的函数
  const renderItem = (el: VNode | VNodeChild) => (
   <div class="min-w-full absolute left-0 top-full">{el}</div>
  );

  // 渲染组件的 JSX
  return () => (
   <div
    ref={(el) => ($ele.value = el)}
    class="overflow-hidden relative"
    style={styles.value}
    data-current={_current.value}
   >
    {slots.default?.().map((item) => {
     if (Array.isArray(item.children))
      return item.children.map((el) => renderItem(el));
     return renderItem(item);
    })}
   </div>
  );
 },
 {
  props: {
   current: {
    type: Number,
    default: 0,
   },
   autoplay: Boolean,
   height: {
    type: Number,
    default: 0,
   },
   interval: {
    type: Number,
    default: 2000,
   },
   delayUpdate: Number,
  },
 }
);
