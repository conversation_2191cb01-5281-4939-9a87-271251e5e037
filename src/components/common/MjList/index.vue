<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { downloadIamge } from '@/utils/functions'
import ClipboardJS from 'clipboard'
import {
    NModal,
    NInput,
    NButton,
    NList,
    NListItem,
    NThing,
    NImage,
    NSpin,
    useMessage,
    NPagination
} from 'naive-ui'

import {
  mjList
} from '@/chatgpt'

interface Props {
  visible: boolean
  searchQuery?:string
  changeFn?: (visible: boolean) => void
}

interface Emit {
  (e: 'update:visible', visible: boolean): void
  (e: 'changeFn', visible: boolean): void
}
const props = defineProps<Props>()
const emit = defineEmits<Emit>()
const mjImageList = ref()
const currentPage = ref(1)
const searchQuery = ref()
const loading = ref(true)
const pageSize = ref(6)
const message = useMessage()

const isMobile = computed(() =>
  /iPhone|Android|Mobile/.test(navigator.userAgent),
)
const show = computed({
  get: () => props.visible,
  set: (visible: boolean) => emit('update:visible', visible),
})

onMounted(async () => {
  await fetchMjList();
  copyLink("")
})



onUnmounted(() => {
 
})

async function fetchMjList() {
  loading.value=true;
  const response: any = await mjList({pageSize:pageSize.value,page:currentPage.value,keywords:searchQuery.value})
  mjImageList.value = response
  loading.value=false;

}
async function handlePageChange(e:number){
  currentPage.value=e;
  await fetchMjList();
}
async function handleSearch(e:any){
  currentPage.value=1;
  await fetchMjList();
}

async function  copyLink(url:string) {
  var clipboard = new ClipboardJS('.copyLink');

clipboard.on('success', function (e) {
  message.info("复制成功!")
  e.clearSelection();
});

clipboard.on('error', function (e) {
  message.error("复制失败！")
});
   //copyText({ text: url, origin: true })        
}
const handClose = () => {
  show.value = false
}


</script>

<template>
  <NModal title="MJ图片管理"
    v-model:show="show"
    :trap-focus="false"
    style="width: 90%; max-width:700px;"
    preset="card"
    @close="handClose">
   
   <div>
    <div class="search-bar">
      <n-input v-model:value="searchQuery" placeholder="请输入关键词搜索"></n-input>
      <n-button type="success" @click="handleSearch" >搜索</n-button>
    </div>
    <div v-if="loading" style="min-height:350px;text-align:center;">
     <n-spin size="medium" />
   </div>
   <div v-if="!isMobile">
    <div class="image-list" v-if="!loading&&mjImageList.rows.length" style="min-height:350px">
      <div v-for="image in mjImageList.rows" :key="image.id" class="image-item">
        <n-image  :src="image.url+'?imageMogr2/thumbnail/!20p'" :preview-src="image.url" alt="Image" />
        <div class="image-title" :title="image.title">{{ image.title||image.content }}</div>
        <div class="image-button">
          <n-button @:click="downloadIamge(image.url)" size="small" type="primary" style="margin-right:5px;margin-bottom:5px;">下载</n-button>
          <n-button :data-clipboard-text="image.url"  class="copyLink" size="small" type="primary">复制</n-button></div>
      </div>
    </div>
    <div v-if="!loading&&!mjImageList.rows.length" style="height:350px;text-align:center;line-height:350px;">暂无数据</div>
   </div>
   <div v-if="isMobile">
     <n-list v-if="!loading">
    <n-list-item v-for="image in mjImageList.rows" :key="image.id">
      <template #prefix>
        <div style="height:50px;width:50px">
        <n-image :src="image.url+'?imageMogr2/thumbnail/!20p'" :preview-src="image.url" alt="Image" />
        </div>
      </template>
      <template #suffix>
          <n-button @:click="downloadIamge(image.url)" size="small" type="primary" style="margin-bottom:5px;">下载</n-button>
          <n-button :data-clipboard-text="image.url"  class="copyLink" size="small" type="primary">复制</n-button>
      </template>
      <n-thing>
        <div class="img-title" :title="image.title||image.content">
       {{ image.title||image.content }}
        </div>
      </n-thing>
    </n-list-item>
  </n-list>
   </div>
    <div v-if="!loading" class="pagination">
      <n-pagination
        :item-count="mjImageList.count"
        :simple="true"
        :page.sync="currentPage"
        :page-size="pageSize"
        @change="handlePageChange"
      ></n-pagination>
    </div>

     </div>
  </NModal>
</template>
<style scoped>
.img-title{
  overflow: hidden;
text-overflow: ellipsis;
display: -webkit-box;
-webkit-line-clamp: 3;
-webkit-box-orient: vertical;
}

.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.image-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.image-item {
  width: 31%;
  margin: 1%;
  text-align: center;
  cursor: pointer;
  position: relative;
  padding-bottom: 40px;
}

.image-button{
  position:absolute;
  width: 100%;
  text-align: center;
  bottom:0
}
.image-title{
    width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}


</style>