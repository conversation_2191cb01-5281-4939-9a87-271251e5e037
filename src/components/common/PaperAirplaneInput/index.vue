<script lang="ts" setup>
import { NImage } from 'naive-ui';
import PaperAirPlane from '@/assets/images/paper_airplane.png'
import { debounce } from '@/utils/functions/debounce';
import { nextTick, ref } from 'vue';
interface Props {
    value?: any
    placeholder?: string
    onChange?: (value: string) => void;
    onSubmit?: (value: string) => void;
}

interface Emits {
    (e: 'update:show', value: boolean): void
}
const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {
    value: "",
    placeholder: "",
    onChange: () => { }
})

let inputEnd = ref(false);

const compositionend = (event) => {
    event.preventDefault();
    inputEnd.value = true;
};
const compositionstart = (event) => {
    event.preventDefault();
    inputEnd.value = false;
};
const inputValue = ref(props.value)
const handleInput = debounce((e: any) => {
    const value = e.target.value
    inputValue.value = value
    props.onChange(value)
}, 300)
const handleSubmit = () => {
    if (inputValue.value) {
        nextTick(() => {
            props?.onSubmit?.(inputValue.value)
        })
    }
}
</script>
<template>
    <div class="h-[53px] min-w-[80%] w- relative flex flex-row items-center bg-white border border-[#D8D8D8] rounded-[5px] cursor-pointer ">
        <input class="search-input flex-1 ml-[21px] outline-none" :placeholder="placeholder" @input="handleInput"
            @focus="emit('update:show', true)" @blur="emit('update:show', false)" :value="inputValue"
            @keypress.enter="handleSubmit" @compositionend="compositionend"
            @compositionstart="compositionstart" />
        <div class="w-[40px] h-[40px] mr-[10px] rounded-[2px] flex items-center justify-center bg-[#D8D8D8] cursor-not-allowed"
            :class="{ '!bg-[#0E69FF] !cursor-pointer': inputValue?.length > 0 }"
            @click="handleSubmit">
            <NImage :src="PaperAirPlane" class=" w-[22px] h-[22px]" previewDisabled />
        </div>
    </div>
</template>
<style lang="less" scoped>
@import './index.less';
</style>
