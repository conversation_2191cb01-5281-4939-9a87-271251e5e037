<script setup lang="ts">
import { NImage } from 'naive-ui'
import { replaceText } from '@/plugins/directive';
interface Props {
    title: string;
    imgSrc: string;
    imgActiveSrc: string;
    markUrl?: string
    active: boolean;
}
const props = withDefaults(defineProps<Props>(), {
    title: '',
    imgSrc: '',
    imgActiveSrc: '',
    active: false
})
</script>

<template>
    <div class="tab-container flex flex-row items-center pl-[20px] cursor-pointer bg-[#ffffff] relative"
        :class="active ? 'active' : ''">
        <NImage v-if="markUrl" :src="markUrl" width="16px" height="16px" preview-disabled class=" absolute top-[-6px] right-[10px]" />
        <NImage v-if="active" preview-disabled :src="imgActiveSrc" class="w-[24px] h-[24px] text-[#ffffff]" />
        <NImage v-else preview-disabled :src="imgSrc" class="w-[24px] h-[24px] text-[#ffffff]" />
        <span class=" text-[14px] leading-[42px] pl-[6px]" :class="active ? 'text-[#ffffff]' : 'text-[#3d3d3d]'">{{ replaceText(title) }}</span>
    </div>
</template>

<style lang="less" scoped>
@import './index.less';
</style>