<script lang="ts" setup>
import { NImage, NFloatButton, NModal, NBackTop } from "naive-ui";
import FeedbackImg from "@/assets/images/feedback.png";
import AngleArrowUpImg from "@/assets/images/angle-arrow-up.png";
import Double11Img from "@/assets/images/double11.png";
import Double12Img from "@/assets/images/double12.png";
import Back from "@/assets/images/back.png";
import FeedBack from "../FeedBack/index.vue";
import { ref, inject, onMounted } from "vue";
import { getActivity, getActivityImageShow } from "@/store/modules/auth/helper";
import { useBasicLayout } from "@/hooks/useBasicLayout";
import { useRouter } from "vue-router";
const router = useRouter();

const { isPC } = useBasicLayout();

const { showTop, showActive, showBottom } = defineProps({
	position: {
		type: String,
		default: "140px",
	},
	showTop: {
		type: Boolean,
		default: true,
	},
	showActive: {
		type: Boolean,
		default: true,
	},
	showBottom: {
		type: Boolean,
		default: false,
	},
});

const scrollPageTo: any = inject("scrollPageTo");

const backUpRef = ref<HTMLElement | null>(null);
const showRef = ref(false);
const backTopShowRef = ref(false);
const activityRef = ref<any>({});
const activityIcon = ref<string>("");

onMounted(() => {
	const activity = getActivity();
	if (activity) activityRef.value = activity;
	if (activity.activityIcon) activityIcon.value = activity.activityIcon;
});

const handleFeedback = () => {
	showRef.value = true;
};
const handleActivityShow = () => {
	window.$aiwork.openRecharge?.({ type: "ai" });
};
const handleDistribution = () => {
	router.push({
		name: "Distribution",
	});
};
const handleUpdateShow = (show: boolean) => {
	backTopShowRef.value = show;
};
const pageHeight = document.documentElement.clientHeight / 4;

const toBottom = () => {
	scrollPageTo && scrollPageTo(10000000);
};
</script>

<template>
	<div
		class="fixed z-10 right-[9px] sm:right-0 bottom-[calc(110px+119px+32px+82px)] h-[82px] cursor-pointer sm:z-[2]"
		@click="handleDistribution"
	>
		<NImage
			src="//cdn2.weimob.com/saas/saas-fe-sirius-orion-node/production/zh-CN/503/distribution_float.png"
			width="82px"
			height="82px"
			preview-disabled
		/>
		<span class="absolute left-[17px] bottom-[12px] text-[#E54545] text-[12px]">点击查看</span>
	</div>
	<div
		v-if="showActive && activityIcon"
		class="fixed z-10 right-[9px] sm:right-0 bottom-[calc(110px+119px+32px)] h-[82px] cursor-pointer sm:z-[2]"
		@click="handleActivityShow"
	>
		<NImage :src="activityIcon" width="82px" height="82px" preview-disabled />
	</div>
	<n-float-button
		:right="isPC ? 25 : 10"
		:bottom="position"
		:width="50"
		:height="119"
		shape="square"
		type="default"
		class="text-[#333333] sm:z-[2]"
		style="--n-color-hover: #fff; --n-box-shadow: 0 2px 8px 0 #dce4f6"
	>
		<div
			class="float-container flex flex-col items-center justify-center w-full"
		>
			<div
				class="text-[12px] flex flex-col justify-center items-center hover:text-[#0E69FF]"
				@click="handleFeedback"
			>
				<NImage
					:src="FeedbackImg"
					width="22px"
					height="22px"
					preview-disabled
				/>
				<span class="leading-[16px] pt-[1px]">反馈</span>
			</div>
			<div class="divider mt-[12px] mb-[5px]"></div>
			<template v-if="showTop">
				<div
					v-if="!backTopShowRef"
					class="flex flex-col justify-center items-center text-[#A2A2A2] text-[12px] h-[40px] hover:text-[#0E69FF]"
				>
					<NImage
						:src="AngleArrowUpImg"
						width="24px"
						height="24px"
						preview-disabled
						class="pt-[-1px]"
					/>
					<span class="pt-[1px] leading-[16px]">顶部</span>
				</div>

				<div
					v-else
					ref="backUpRef"
					class="text-[#333333] text-[12px] h-[40px] w-[24px] flex items-center justify-start"
				></div>
			</template>

			<template v-if="showBottom">
				<div
					class="flex flex-col justify-center items-center text-[12px] h-[40px] text-center hover:text-[#0E69FF]"
					@click="toBottom"
				>
					<!-- <NImage :src="Back" width="18px" height="18px" preview-disabled class="pt-[-1px] border-[1px] border-dashed" /> -->
					<span class="iconfont icon-return border-[1px] border-dashed"></span>
					<span class="pt-[2px] leading-[16px]">返回</span>
				</div>
			</template>
		</div>
	</n-float-button>
	<n-back-top
		v-if="showTop"
		:visibility-height="pageHeight"
		bottom="10px"
		right="3px"
		:to="backUpRef"
		@update:show="handleUpdateShow"
		class="flex flex-col justify-start items-start"
		style="
			--n-border-radius: none;
			--n-box-shadow: none;
			--n-text-color: #a2a2a2;
			--n-height: 40px;
		"
	>
		<NImage
			:src="AngleArrowUpImg"
			width="24px"
			height="24px"
			preview-disabled
		/>
		<span class="pt-[1px] leading-[17px]">顶部</span>
	</n-back-top>
	<FeedBack :show-ref="showRef" @update:show="() => (showRef = false)" />
</template>

<style lang="less" scoped>
.float-container {
	cursor: pointer;
	border-radius: 10px;
	background: #ffffff;
}

:deep(.n-float-button--square-shape) {
	border-radius: 10px !important;
}

:deep(.n-float-button__body) {
	&:hover {
		background: #ffffff;
	}
}

:deep(.n-back-top) {
	height: 40px !important;
	box-shadow: unset !important;
	transition: unset !important;
	box-shadow: unset !important;
	background-color: unset !important;
}

.divider {
	width: 100%;
	height: 1px;
	background-color: #dcdcdc;
}
</style>
