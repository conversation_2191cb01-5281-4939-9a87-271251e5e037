<script setup lang="ts">
import { computed, ref } from 'vue'
import {
	NEllipsis,
	NImage
} from 'naive-ui'
import { useRoute } from 'vue-router'
import { useSuggestionStore } from '@/store'
import eye from '@/assets/eye.svg'

interface Props {
	data: any
}

interface ItemProps {
	id: number,
	name: string,
	categoryId: number,
	categoryName: string,
	uuid: number
}

interface Emit {
	(ev: 'update-suggest', item: ItemProps): void
}

const route = useRoute()
const { uuid } = route.params as { uuid: string }

const store = useSuggestionStore()
const props = defineProps<Props>()
const emit = defineEmits<Emit>()

const tabIndex = ref(0)
const tabName = ref('')
const currentChildList = ref<any[]>([])

const list = computed({
	get: () => {
		if (props.data && props.data.length) {
			current.value = props.data[0].id
			tabName.value = props.data[0].name
			currentChildList.value = props.data[0].Chatbots
		}
		return props.data
	},
	set: () => { }
})

const current = ref(-1)

const handleClick = (item: any, categoryName: string) => {
	const data: ItemProps = {
		id: item.id,
		name: item.name,
		categoryId: item.categoryId,
		categoryName: categoryName,
		uuid: Number(+uuid)
	}
	store.updateSetting(data, +uuid)
	emit('update-suggest', data)
}

const imageLink = (url: string) => {
	return `${url}?v=1`
}

const handleTab = (index: number, name: string, curList: any[]) => {
	tabIndex.value = index
	tabName.value = name
	currentChildList.value = curList
}

</script>

<template>

	<ul class="grid grid-flow-col text-center  bg-gray-100 rounded-full p-1 mt-[10px]" v-if="list">
		<li v-for="(item, index) in list" :key="index" @click="handleTab(index, item.name, item.Chatbots)">
			<a class="flex justify-center py-2 cursor-pointer"
				:class="tabIndex === index ? 'bg-white rounded-full shadow text-[#36ad6a]' : ''">{{ item.name }}</a>
		</li>
	</ul>
	<div class="grid grid-cols-3 gap-5 sm:grid-cols-2 sm:gap-2 mt-5">
		<div
			class="min-h-[130px] rounded-[5px] border border-[#e4e9e9] shadow-inner transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-105 duration-300 cursor-pointer px-[15px] py-[15px] relative"
			v-for="(item_child, i) in currentChildList" :key="i" @click="handleClick(item_child, tabName)">
			<div class="flex items-center">
				<div class="flex-none flex mt-[-5px]" v-if="item_child.profile != 'undefined'">
					<NImage :src="imageLink(item_child.profile)" width="20" height="20" />
				</div>
				<h1 class="text-[14px] font-semibold inline-block pl-[3px]">
					<NEllipsis :line-clamp="1">
						{{ item_child.name }}
					</NEllipsis>
				</h1>
			</div>
			<div class="w-full h-[1px] bg-[#EAEAEA] mt-[5px] mb-[5px]"></div>
			<div class="text-[13px] mb-[5px]">
				<NEllipsis :line-clamp="3">
					{{ item_child.welcome_message }}
					<template #tooltip>
						<div style="text-align: center">
							{{ item_child.welcome_message }}
						</div>
					</template>
				</NEllipsis>
			</div>
			<div class=" absolute bottom-[3px] right-3 flex ">
				<NImage :src="eye" width="15" height="5" />
				<span class="pl-[5px] text-[#414755] text-[12px]">{{ item_child.hot_num }}</span>
			</div>
		</div>
	</div>
</template>
<style lang="less">
@import url(./index.less);
</style>
