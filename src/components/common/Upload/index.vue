<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { NModal, NButton, NSpace, NSlider,NUpload,NUploadDragger,NText,NP,useMessage,NImage} from 'naive-ui'
import type { UploadFileInfo } from 'naive-ui'
import { FileInfo } from 'naive-ui/es/upload/src/interface'
import { useDrawSettingStore, useUserStore } from '@/store'
import { ClipboardImage24Regular } from '@vicons/fluent'
import { getToken } from '@/store/modules/auth/helper'
import del from '@/assets/circle-cross.svg'

interface Props {
  visible: boolean
}
interface Emit {
  (e: 'update:visible', visible: boolean): void
}
const message = useMessage()
const settingStore = useDrawSettingStore()
const props = defineProps<Props>()
const emit = defineEmits<Emit>()

// 定义ref
const marks = ref({
  0.5: '低',
	1: '默认',
  1.5: '中',
  2: '高',
})
const fileList = ref<UploadFileInfo[]>([])
const show = computed({
  get() {
    return props.visible
  },
  set(visible: boolean) {
    emit('update:visible', visible)
  },
})


let defaultOptions: any = ref(settingStore.getSetting())
const onUpdate = (value: number) => {
  defaultOptions.value.iw = value
}
const maxFileSize = 2
const beforeUpload = (data: { file: FileInfo }): any => {
	const fileInfo = data.file.file

	const size : number | undefined = fileInfo?.size
	if(size) {
		const isLtMax = size / 1024 / 1024 < (maxFileSize as number);
		if(!isLtMax) {
			message.warning(`图片大小超过${maxFileSize}M限制`)
		}
		return new Promise((resolve, reject) => {
			if (!isLtMax) {
					reject(fileInfo);
			} else {
					resolve(fileInfo);
			}
    });
	}
};
const handleRemove = (data: { file: UploadFileInfo; fileList: UploadFileInfo[],e: Event } ) => {
	fileList.value = []
	defaultOptions.value.url = ''
	return new Promise((resolve) => {
		resolve(false)
	})
}
const handleUploadChange = (data: { file: UploadFileInfo; fileList: UploadFileInfo[],event?: ProgressEvent }) => {
	fileList.value = data.fileList
	const file = data.file
	const event: any = data.event
	const status = file?.status

	if(status === 'finished') {
		const curEvent: any = event.currentTarget
		const response = JSON.parse(curEvent.response)
		if(response && response.errcode == 0) {
			defaultOptions.value.url = response.data?.url
		} else {
			message.error(response.errmsg || `上传图片失败`);
			return
		}
	} else if(status === 'error') {
		message.error(`上传图片失败`);
		return
	}
}
const token = getToken()
const userStore = useUserStore()
const params: any = {
	action: import.meta.env.VITE_GLOB_API_URL + '/chatgpt/file/upload',
	max: 1,
	accept: 'image/jpeg,image/bmp,image/png',
	headers: {
		Authorization: `Bearer ${token}`,
		'ai-team': userStore.curTeam?.id || ''
	},
	onBeforeUpload: beforeUpload,
	onChange: handleUploadChange,
	onRemove: handleRemove
}
const handleIconRemove = (e: Event) => {
	e.stopPropagation()
	fileList.value = []
	defaultOptions.value.url = ''
	params.disabled = false
}

onMounted(()=> {
	if(defaultOptions.value.url) {
		params.disabled = true
	} else {
		params.disabled = false
	}
})


const handleSubmit = () => {
  settingStore.updateSetting(defaultOptions.value)
	show.value = false
}
</script>

<template>
  <NModal
    v-model:show="show"
    :auto-focus="false"
    preset="card"
    style="width: 90%; max-width: 540px;"
  >
    <template #header>
      <div>以图生图</div>
    </template>
		<div>
				<NUpload
					v-bind="params"
					v-model:file-list="fileList"
					directory-dnd
				>
					<NUploadDragger>
						<div class="mb-[12px]" v-if="!defaultOptions.url">
							<n-icon size="48" :depth="3">
								<ClipboardImage24Regular class="w-[30px] h-[30px] mx-auto"/>
							</n-icon>
						</div>
						<div v-if="defaultOptions.url" class="mb-[12px] rounded-[5px] relative flex justify-center">
							<NImage :src="defaultOptions.url" class="max-w-[100%] max-h-[300px] rounded-[5px] mx-auto"/>
							<NImage :src="del" review-disabled @click="handleIconRemove" class="w-[30px] absolute top-[-20px] right-[-20px] z-[3000]"/>
						</div>
						<NText class="text-[16px]">
							点击或者拖动文件到该区域来上传
						</NText>
						<NP depth="3" style="margin: 8px 0 0 0">
							请上传参考图片, 上传完成后回到输入框输入提示词开始
						</NP>
					</NUploadDragger>
				</NUpload>
		</div>
		<div class="w-[98%]">
        <div class="mt-[20px]">参考图片权重</div>
        <div class="mt-[5px]">
          <NSpace vertical>
            <NSlider
              v-model:value="defaultOptions.iw"
              :marks="marks"
              step="mark"
              :min="0.5"
              :max="2"
              @update:value="onUpdate"
            />
          </NSpace>
        </div>
    </div>
    <template #footer>
      <NButton type="primary" color="#3dbaa1" style="width: 100%;" @click="handleSubmit">
        确认
      </NButton>
    </template>
  </NModal>
</template>
