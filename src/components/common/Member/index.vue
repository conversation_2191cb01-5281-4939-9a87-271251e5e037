<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import {
	NButton,
	NModal,
	NImage,
	NDivider
} from 'naive-ui'
import QrcodeVue from 'qrcode.vue'
import {
	fetchAlipayPrepay2,
	fetchGoodsList,
	fetchPayIspayy,
	fetchSession
} from '@/chatgpt'
import CountUp from 'vue-countup-v3'
import { getUser, setUser } from '@/store/modules/auth/helper'
import { ss } from '@/utils/storage'
import { useUserStore } from '@/store'
import alipay from '@/assets/alipay.png'
import wxpayImg from '@/assets/wxpay.png'
import bgTop from '@/assets/bg_top.png'
interface Props {
	visible: boolean
	changeFn?: (visible: boolean) => void
}

interface Emit {
	(e: 'update:visible', visible: boolean): void
	(e: 'changeFn', visible: boolean): void
}
const chats = ['无限次数的问答', 'Plus付费账号接入', '急速响应', '使用新的特征']
const mjs = ['一键AI智能绘图', '解锁多元风格', '快速文生图', '高性价比专业模式']

const route = useRoute()
const pathname = ref(route.name)
const props = defineProps<Props>()
const emit = defineEmits<Emit>()
const goods = ref()
const currentGoods = ref()
const userStore = useUserStore()

const currentPayType = ref('wxpay')
const currentIndex = ref(0)
const qrcodeUrl = ref()
const timer = ref()
const loading = ref(true) //chat.18age.cn
const loadingQ = ref(true)
const isMobile = computed(() =>
	/iPhone|Android|Mobile/.test(navigator.userAgent),
)

const isWx = computed(() => /MicroMessenger/.test(navigator.userAgent))
const isH5Wx = computed(() => {
	return true
})

let type = ''
if (pathname.value == 'Chat') {
	type = 'chat'
} else if (pathname.value == 'Mj') {
	type = 'mj'
} else if (pathname.value == 'Paper') {
	type = 'paper'
} else {
	type = 'chat'
}
const curType = ref(type)

const show = computed({
	get: () => props.visible,
	set: (visible: boolean) => emit('update:visible', visible),
})

onMounted(async () => {
	InitSelect()
	await fetchGoods()
	fetchIsPay()
})

function InitSelect() {
	if (!isH5Wx.value && isMobile.value) {
		currentPayType.value = 'alipay'
	}
}

onUnmounted(() => {
	clearInterval(timer.value)
})
async function getPayUrl() {
	const langPages =
		ss.get('LANDING_PAGE') || ss.get('LANDING_NOCHANNEL_PAGE') || ''
	loadingQ.value = false
	const store = localStorage.getItem('SECRET_TOKEN');
	if (currentPayType.value == 'wxpay') {
		try {
			const goodsId = currentGoods?.value.id

			if (isMobile.value) {
				if (store) {
					const token = JSON.parse(store).data;
					if (isWx.value) {
						qrcodeUrl.value = `https://api.aiwork365.cn/wxpay/pay?weixinpay=1&goodsId=${goodsId}&access_token=${token}&url=${encodeURIComponent(langPages)}`
					} else {
						qrcodeUrl.value = `https://api.aiwork365.cn/wxpay/pay?weixinpay=1&goodsId=${goodsId}&access_token=${token}&url=${encodeURIComponent(langPages)}`
					}
				}
			} else {
				const wx: any = await fetchAlipayPrepay2({
					goodsId: goodsId,
					url: langPages,
				})
				if (wx) {
					qrcodeUrl.value = wx.url
				}
			}

			loadingQ.value = true
		} catch (error) {
			loadingQ.value = true
		}
	} else {
		showQrcode(langPages)
		loadingQ.value = true
	}
}
async function fetchGoods() {
	let type = "ai";
	if (curType.value === 'mj') {
		type = "mj"
	} else if (curType.value === 'paper') {
		type = "aipaper-offset"
	}
	chatGoods(type)
}
async function chatGoods(type: string) {
	loading.value = true
	const response: any = await fetchGoodsList(type)
	goods.value = response
	currentIndex.value = response.length - 1
	// 默认选择最后
	if (response) {
		loading.value = false
		currentGoods.value = response[response.length - 1]
		await getPayUrl()
	}
}

async function fetchIsPay() {
	const user = getUser()
	timer.value = setInterval(async () => {
		const response: any = await fetchPayIspayy().catch((res) => {
			if (res.errcode == 10000001) clearInterval(timer.value)
		})
		if (response?.status) {
			//if (true) {
			show.value = false
			clearInterval(timer.value)

			const data: any = await fetchSession()
			const userStore = useUserStore();
			const users: any = data.data.user;
			if (users) {
				setUser(users)
				userStore.updateUserInfo({
					name: users.nickname || users.username || users.phone,
					description: "用户信息",
					member: users.MemberLevels,
					mjmember: users.mjMember,
					uid: users.uid

				})
			}
			// 弹出绑定界面, 如果是临时用户
			if (user && user.type === 'temp') emit('changeFn', true)
		}
	}, 2000)
}

const handClose = () => {
	clearInterval(timer.value)
	show.value = false
}

const onTab = (value: string) => {
	curType.value = value
	currentPayType.value = 'wxpay'
	InitSelect()
	let type = "ai";
	if (value === 'chat') {
		type = "ai"
	} else if (value === 'mj') {
		type = "mj"
	} else if (value === 'paper') {
		type = "aipaper-offset"
	}
	chatGoods(type)
}

const payh5 = () => {
	window.open(qrcodeUrl.value)
}
const showQrcode = (urls: any) => {
	const store = localStorage.getItem('SECRET_TOKEN')
	if (store) {
		try {
			const token = JSON.parse(store).data
			let goodsId = currentGoods?.value.id
			const url = `https://data.aiwork365.cn/aiwokr/alipay/prepay/${goodsId}?access_token=${token}&url=${encodeURIComponent(urls)}&teamId=${userStore.curTeam?.id || ""}`
			qrcodeUrl.value = url
		} catch (error) {
			console.error(error)
		}
	}
}
const handleGoods = async (item: any, index: number) => {
	currentGoods.value = item
	currentIndex.value = index
	loadingQ.value = false
	await getPayUrl()
}

const handePayType = async (type: string) => {
	loadingQ.value = false
	currentPayType.value = type
	await getPayUrl()
}
</script>

<template>
	<NModal title="" v-model:show="show" style="width:99%; max-width: 700px; border-radius: 10px; background: #fff;"
		@close="handClose">
		<div class="relative">
			<div class=" absolute top-[3px] right-2 w-[30px] h-[30px] z-50 cursor-pointer" @click="handClose">
				<IconCloseOne class=" text-[32px]" />
			</div>
			<div class="w-full h-[187px] relative overflow-hidden" id="banner">
				<NImage :src="bgTop" class="w-full h-full absolute top-0 right-0 left-0 bottom-0" style="width: 100%"
					preview-disabled />
				<div class="absolute top-[37px] w-full">
					<h1 class="text-[20px] text-[#333] text-center">一键升级解锁更多会员权益</h1>
					<ul class="grid grid-cols-2 gap-2 ml-[30px] mt-[15px]">
						<li class="flex items-center" v-for="(item, index) in chats" :key="index"
							v-if="curType == 'chat'">
							<IconCheck class=" text-[#3ebba2]" />
							<span class="text-[12px] text-[#4D4D4D] pl-2 ">{{ item }}</span>
						</li>
						<li class="flex items-center" v-for="(item, index) in mjs" :key="index" v-if="curType == 'mj'">
							<IconCheck class=" text-[#3ebba2]" />
							<span class="text-[12px] text-[#4D4D4D] pl-2 ">{{ item }}</span>
						</li>
					</ul>
				</div>
				<div id="tab" class="absolute bottom-0 left-0 right-0">
					<ul class="grid grid-cols-3" v-if="!isMobile">
						<li @click="onTab('chat')">
							<div class="h-[43px] w-full flex items-center cursor-pointer relative rounded-tr-[100px] rounded-tl-[8px] justify-center"
								:class="[curType == 'chat' ? 'bg-[#01B085]' : 'bg-[#39524A]']">
								<span class="text-[15px] text-[#fff] sm:text-[12px]">文字会员</span>
								<div :class="[curType == 'chat' ? 'bg-[#01B085]' : 'bg-[#39524A]']"
									class="absolute right-0 top-0 h-[43px] w-[20px] -translate-x-[10px] skew-x-[19deg] rounded-tr-[8px] bg-[#01B085] z-50">
								</div>
								<div class="absolute top-0 right-0 h-[43px] w-[20px] bg-[#e5fbf0]"></div>
							</div>
						</li>
						<li @click="onTab('mj')">
							<div class="h-[43px] w-full flex items-center cursor-pointer relative rounded-tr-[8px] justify-center"
								:class="[curType == 'mj' ? 'bg-[#01B085]' : 'bg-[#39524A]']">
								<span class="text-[15px] text-[#fff] sm:text-[12px]">MJ会员</span>
								<div :class="[curType == 'mj' ? 'bg-[#01B085]' : 'bg-[#39524A]']"
									class="absolute left-0 top-0 h-[43px] w-[20px] translate-x-[7px] -skew-x-[19deg] rounded-tl-[8px] z-50">
								</div>
								<div class=" absolute top-0 left-0 h-[43px] w-[20px] bg-[#e5fbf0]"></div>

								<div :class="[curType == 'mj' ? 'bg-[#01B085]' : 'bg-[#39524A]']"
									class="absolute right-0 top-0 h-[43px] w-[20px] translate-x-[-8px] skew-x-[19deg] rounded-tr-[8px] z-50">
								</div>
								<div class=" absolute top-0 right-0 h-[43px] w-[20px] bg-[#e5fbf0]"></div>

							</div>
						</li>
						<li @click="onTab('paper')">
							<div class="h-[43px] w-full flex items-center  justify-center cursor-pointer relative rounded-tr-[8px]"
								:class="[curType == 'paper' ? 'bg-[#01B085]' : 'bg-[#4e9369]']">
								<span class="text-[15px] text-[#fff] sm:text-[12px]">论文算力包</span>
								<div :class="[curType == 'paper' ? 'bg-[#01B085]' : 'bg-[#4e9369]']"
									class="absolute left-0 top-0 h-[43px] w-[20px] translate-x-[7px] -skew-x-[19deg] rounded-tl-[8px] z-50">
								</div>
								<div class=" absolute top-0 left-0 h-[43px] w-[20px] bg-[#e5fbf0]"></div>
							</div>
						</li>
					</ul>
					<ul class="grid grid-cols-3" v-if="isMobile">
						<li @click="onTab('chat')">
							<div class="h-[43px] w-full flex items-center cursor-pointer relative rounded-tr-[12px] rounded-tl-[12px] justify-center"
								:class="[curType == 'chat' ? 'bg-[#01B085]' : 'bg-[#39524A]']">
								<span class="text-[15px] text-[#fff] sm:text-[12px]">文字会员</span>

							</div>
						</li>
						<li @click="onTab('mj')">
							<div class="h-[43px] ml-[2px] w-full flex items-center cursor-pointer relative rounded-tr-[12px] rounded-tl-[12px] justify-center"
								:class="[curType == 'mj' ? 'bg-[#01B085]' : 'bg-[#39524A]']">
								<span class="text-[15px] text-[#fff] sm:text-[12px]">MJ会员</span>

							</div>
						</li>
						<li @click="onTab('paper')">
							<div class="h-[43px] w-full flex items-center  justify-center cursor-pointer relative rounded-tr-[12px] ml-[8px] rounded-tl-[12px]"
								:class="[curType == 'paper' ? 'bg-[#01B085]' : 'bg-[#4e9369]']">
								<span class="text-[15px] text-[#fff] sm:text-[12px]">论文算力包</span>

							</div>
						</li>
					</ul>
				</div>
			</div>
			<div id="tabpanel">
				<div v-if="curType == 'chat'" class="px-[20px] mt-[23px]">
					<ul v-if="!loading" class="grid sm:grid-cols-2 gap-3 gap-x-3" :class="`grid-cols-${goods.length}`">
						<li v-for="(item, index) of goods" :key="index" :class="` sm:h-[95px] h-[160px] border rounded-[8px] cursor-pointer flex flex-col pt-[18px] pl-[15px] relative overflow-hidden ${currentIndex == index
							? 'border-[#01b085] border-[1px] bg-[#F2FAF6]'
							: 'border-[#cfcfcf] border-[1px]'
							}`" @click="handleGoods(item, index)">
							<div
								class="text-[16px] sm:text-[14px] text-[#333] text-left leading-[16px] sm:pt-0 pt-3 font-medium">
								{{ item.name }}
							</div>
							<div class="sm:text-[18px] text-[20px] text-[#01b085] font-bold sm:pt-0 pt-3 -ml-1">
								<span class="flex" v-if="parseFloat(item.discount) == 0">￥{{ item.price
									}}</span>
								<span class="flex" v-if="parseFloat(item.discount) > 0">
									￥
									<CountUp :start-val="parseFloat(item.originalPrice)"
										:end-val="parseFloat(item.originalPrice) - parseFloat(item.discount)"
										:decimal-places="2" :duration="2.5" :loop="1" />
								</span>

							</div>
							<div class="text-[12px] text-[#999] sm:pt-0 pt-3">
								{{ item.description || '--' }}
							</div>
							<div v-if="item.discountDesc"
								class="text-[12px] text-[#573B18] absolute right-0 top-0 rounded-[8px] py-[2px] px-[6px] rounded-r-none truncate"
								style="background: linear-gradient(90deg, #F9E5BD, #E2B460);">
								{{ item.discountDesc }}
							</div>
						</li>
					</ul>
				</div>
				<div v-if="curType == 'mj'" class="px-[20px] mt-[23px]">
					<ul class="grid sm:grid-cols-2 gap-3 gap-x-2 grid-cols-3" v-if="!loading">
						<li v-for="(item, index) of goods" :key="index" :class="`sm:h-[95px] h-[120px] border rounded-[8px] cursor-pointer flex flex-col pt-[18px] pl-[15px] relative overflow-hidden ${currentIndex == index
							? 'border-[#01b085] border-[1px] bg-[#F2FAF6]'
							: 'border-[#cfcfcf] border-[1px]'
							}`" @click="handleGoods(item, index)">
							<div
								class="sm:text-[14px] text-[16px] text-[#333] text-left leading-[16px] pt-4 font-medium">
								{{ item.name }}
							</div>
							<div class="flex">
								<div class="text-[18px] text-[#01b085] font-bold pt-3 sm:pt-0 -ml-1">
									￥{{ item.price }}
								</div>
							</div>
							<div class="text-[12px] text-[#573B18] absolute right-0 top-0 rounded-[8px] py-[2px] px-[6px] rounded-r-none truncate"
								style="background: linear-gradient(90deg, #F9E5BD, #E2B460);">
								{{ item.description || '--' }}
							</div>
						</li>
					</ul>
				</div>
				<div v-if="curType == 'paper'" class="px-[20px] mt-[23px]">
					<ul class="grid sm:grid-cols-2 gap-3 gap-x-2 grid-cols-4" v-if="!loading">
						<li v-for="(item, index) of goods" :key="index" :class="`sm:h-[110px] h-[120px] border rounded-[8px] cursor-pointer flex flex-col pt-[18px] pl-[15px] relative overflow-hidden ${currentIndex == index
							? 'border-[#01b085] border-[1px] bg-[#F2FAF6]'
							: 'border-[#cfcfcf] border-[1px]'
							}`" @click="handleGoods(item, index)">
							<div
								class="sm:text-[14px] text-[16px] text-[#333] text-left leading-[16px] pt-4 font-medium">
								{{ item.name }}
							</div>
							<div class="flex">
								<div class="text-[18px] text-[#01b085] font-bold pt-3 sm:pt-0 -ml-1">
									￥{{ item.originalPrice }}
								</div>

							</div>
							<div v-if="item.discountDesc" class="text-[12px] text-[#999] py-[5px] ">
								{{ item.discountDesc }}
							</div>
							<div class="text-[12px] text-[#573B18] absolute right-0 top-0 rounded-[8px] py-[2px] px-[6px] rounded-r-none truncate"
								style="background: linear-gradient(90deg, #F9E5BD, #E2B460);">
								{{ item.description || '--' }}
							</div>

						</li>
					</ul>
				</div>
				<div class="px-[20px] py-[20px]">
					<h1 class="text-[15px] text-[#333] py-[12px] font-medium">选择支付方式：</h1>
					<div class="flex flex-col justify-center items-center">
						<div class="w-full flex">
							<div v-if="qrcodeUrl && !isMobile && loadingQ" class="w-[120px] h-[120px]">
								<QrcodeVue :value="qrcodeUrl" :size="300" style="width: 120px; height: 120px;" />
							</div>
							<div class="ml-5 w-full sm:ml-0">
								<div :class="`w-full h-[40px] mx-auto ${currentPayType == 'alipay'
									? 'text-[#009973]'
									: 'text-[#323232]'
									}  rounded-[7px] flex justify-between items-center mt-0 pr-[15px]`" @click="handePayType('alipay')">
									<div class="flex">
										<img :src="alipay" class="w-[25px] h-[25px]" alt="" />
										<span class="pl-[8px] text-[15px] font-medium">
											{{ $t('vip.payAlipay') }}
										</span>
									</div>
									<div>
										<i v-if="currentPayType == 'alipay'"
											class="fi fi-rr-dot-circle text-[#01B085] text-[18px]"></i>
										<i v-if="currentPayType == 'wxpay'"
											class="fi fi-rr-circle text-[#E6E6E6] text-[18px]"></i>
									</div>
								</div>
								<NDivider />
								<div :class="`w-full h-[40px] mx-auto ${currentPayType == 'wxpay'
									? ' text-[#009973]'
									: ' text-[#323232]'
									} rounded-[7px] flex justify-between items-center mt-[7px] pr-[15px]`" @click="handePayType('wxpay')">
									<div class="flex">
										<img :src="wxpayImg" class="w-[25px] h-[25px]" alt="" />
										<span class="pl-[8px] text-[15px] font-medium">
											{{ $t('vip.wxpay') }}
										</span>
									</div>
									<div>
										<i v-if="currentPayType == 'alipay'"
											class="fi fi-rr-circle text-[#E6E6E6] text-[18px]"></i>
										<i v-if="currentPayType == 'wxpay'"
											class="fi fi-rr-dot-circle text-[#01B085] text-[18px]"></i>
									</div>
								</div>
							</div>
						</div>
						<div class="text-center mt-[25px] sm:mt-[10px] mb-[10px] sm:mb-0 text-[#999]">
							我已阅读并同意<span class="text-[#01B085]">《会员服务协议》</span>
						</div>
						<div class="sm:mr-0 sm:mt-[15px] mb-[25px] w-full" v-if="qrcodeUrl && isMobile && loadingQ">
							<div v-if="qrcodeUrl && isMobile && loadingQ" class="m-auto w-full text-center ">
								<NButton type="info" color="#01B085" text-color="#fff" size="large" style="width: 100%"
									@click="payh5()">
									立即支付
								</NButton>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</NModal>
</template>
<style>
#banner .n-image img {
	width: 100%
}
</style>
