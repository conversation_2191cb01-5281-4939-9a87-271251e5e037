<script setup lang="ts">
import { NImage } from 'naive-ui'
interface Props {
    iconSrc: string
    title: string
}
const props = withDefaults(defineProps<Props>(), {
    iconSrc: '',
    title: ''
})
</script>
<template>
    <div class=" flex flex-row gap-x-[9px] items-end justify-start">
        <NImage preview-disabled :src="iconSrc" review-disabled class="w-[24px] h-[24px]" />
        <span class=" font-bold text-[#333333] text-[20px] leading-[20px]">{{ title }}</span>
    </div>
</template>
