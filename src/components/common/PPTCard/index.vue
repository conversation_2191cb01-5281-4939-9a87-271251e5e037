<script setup lang="ts">
import { NImage } from 'naive-ui';
import testSrc from '@/assets/images/sec2-pic-1.png';
import { useRouter } from 'vue-router';
const router = useRouter();

interface Props {
    src: string;
    id: number | null;
    title: string;
}
interface Emit {
    (e: 'update:search', value: string): void
}
const emit = defineEmits<Emit>()
const props = withDefaults(defineProps<Props>(), {
    src: testSrc,
    id: null,
    title: '扁平简约互联网报告ppt模板'
})
const handleSubmit = () => {
    // router.push({ name: 'PPTGenerate', query: { prompt: props.title } });
    // window.location.href = `/ppt/generate?outlineId=${props.id}&m=1`
    emit('update:search', props.title)
}
</script>

<template>
    <div class="ppt-card-container flex flex-col items-center justify-start gap-y-[19px] w-[325px] cursor-pointer"
        @click="handleSubmit">
        <NImage :src="src" preview-disabled width="305" height="186" class="pt-[10px]" />
        <span class=" text-[16px] font-normal text-[#3d3d3d] leading-[21px]">{{ title }}</span>
    </div>
</template>

<style lang="less" scoped>
@import './index.less';
</style>