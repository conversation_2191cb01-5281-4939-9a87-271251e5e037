<script lang="ts" setup>
import { ref } from 'vue';
import { NFormItem, NForm, NUpload, NButton, NModal, NInput } from 'naive-ui'
import { useRequest } from 'vue-hooks-plus';
import { sendFeedbackReport } from '@/chatgpt';
import { useMessage } from 'naive-ui';
import { useAuthStore, useUserStore } from '@/store';

interface Props {
    showRef: boolean
}
interface Emit {
    (e: "update:show", show: boolean): void;
}
defineProps<Props>()
const message = useMessage()




const emit = defineEmits<Emit>()
const list = ref([
    {
        label: '请输入使用意见或建议反馈',
        field: 'desc',
        isRequired: true,
        type: 'textarea',
        placeholder: '请输入你的问题'
    },
    {
        label: '请输入上传问题截图(非必填)',
        field: 'files',
        isRequired: false,
        type: 'upload',
    },
    {
        label: '联系方式(非必填)',
        field: 'phone',
        isRequired: false,
        type: 'input',
        placeholder: '请输入您的手机/邮箱/微信号，方便我们与您联系'
    },
])

const authStore = useAuthStore()
const userStore = useUserStore()
const form = ref();
const headers = ref({
    Authorization: `Bearer ${authStore.token}`,
		'ai-team': userStore.curTeam?.id || ''
})
const formData = ref({
    desc: '',
    files: [],
    phone: ''
})
const previewFileList = ref([])
const maxSize = 3 * 1024 * 1024

const { run, loading } = useRequest(sendFeedbackReport, {
    manual: true,
    onSuccess: (data) => {
        if (data?.feedback) {
            message.success('反馈提交成功')
            emit('update:show', false)
            formData.value = {
                desc: '',
                files: [],
                phone: ''
            }
        } else {
            message.error('反馈提交失败')
        }
    }
})


const handleSubmit = () => {
    form.value.validate((errors) => {
        if (!errors) {
            run(formData.value)
        } else {
            message.error('请填写必填项')
            return false;
        }
    });
}
const handleClose = () => {
    emit('update:show', false)
}
const handleUploadFinish = (event, field) => {
    const res = JSON.parse(event.currentTarget.response)
    if (res?.errcode === 0) {
        const key = res?.data?.key
        formData.value[field].push(key)
    }
}
const handleRemove = (index, field) => {
    const isExist = formData.value[field].find((item, i) => i === index)
    if (!isExist) return false
    formData.value[field].splice(index, 1)
}
const handleBeforeUpload = ({ file }) => {
    if (file.size > maxSize) {
        message.error('图片大小不能超过3M')
        return false
    }
    return true
}
</script>

<template>
    <n-modal v-bind:show="showRef" style="width: 532px; border-radius: 20px;" close-on-esc preset="card"
        class=" feedback-container relative">
        <div class=" leading-[24px] text-[18px] text-[#333333] mt-[-24px] mb-[27px] text-center">意见反馈</div>
        <template #header-extra>
            <div class="absolute top-[-30px] right-[-40px] cursor-pointer" @click="handleClose">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none"
                    version="1.1" width="30" height="30.000001907348633" viewBox="0 0 30 30.000001907348633">
                    <g>
                        <path
                            d="M15,0C6.85714,0,0,6.85714,0,15C0,23.1429,6.85714,30,15,30C23.1429,30,30,23.1429,30,15C30,6.85714,23.1429,0,15,0ZM21.4286,19.2857C22.0714,19.9286,22.0714,20.7857,21.4286,21.2143C20.7857,21.8571,19.9286,21.8571,19.5,21.2143L15.2143,16.9286L10.7143,21.4286C10.0714,22.0714,9.21429,22.0714,8.57143,21.4286C7.92857,20.7857,7.92857,19.7143,8.57143,19.2857L13.0714,14.7857L8.78571,10.5C8.14286,10.0714,8.14286,9.21429,8.78571,8.57143C9.42857,7.92857,10.2857,7.92857,10.7143,8.57143L15,12.8571L19.5,8.35714C20.1429,7.71429,21,7.71429,21.6429,8.35714C22.2857,9,22.2857,9.85714,21.6429,10.5L17.1429,15L21.4286,19.2857Z"
                            fill="#FFFFFF" fill-opacity="0.6000000238418579" style="mix-blend-mode: passthrough" />
                    </g>
                </svg>
            </div>
        </template>
        <NForm ref="form" :model="formData" label-placement="top" label-width="auto">
            <template v-for="(field, index) in list || []" :key="index">
                <template v-if="field.type === 'textarea'">
                    <NFormItem :label="field.label" :path="field.field" :rule="{
                        required: field.isRequired,
                        message: `请输入${field.label}`,
                    }" :class="`${!field.isRequired ? 'no-required' : ''}`">
                        <NInput type="textarea" v-model:value="formData[field.field]"
                            :placeholder="field.placeholder" />
                    </NFormItem>
                </template>
                <template v-else-if="field.type === 'upload'">
                    <NFormItem :label="field.label" :path="field.field" style="--n-feedback-height:0px"
                        class=" relative">
                        <n-upload action="/api3/aiwork/feedback/upload" accept=".png, .jpg, .jpeg"
                            class="feed-back-upload-container" @before-upload="({ file }) => handleBeforeUpload(file)"
                            @remove="({ index }) => handleRemove(index, field.field)"
                            @finish="({ event }) => handleUploadFinish(event, field.field)" :headers="headers"
                            :default-file-list="previewFileList" list-type="image-card" :max="5" />
                        <span class="absolute right-[0] top-[0] text-[#98A2B5] text-[16px]">{{formData?.[field?.field]?.length}}/5</span>
                    </NFormItem>
                    <div class="text-[#98A2B5] text-[14px] leading-[14px] mt-[10px] mb-[18px]">
                        支持上传5张图片，格式为png、jpg、jpeg，3M以内</div>
                </template>
                <template v-else-if="field.type === 'input'">
                    <NFormItem :label="field.label" :path="field.field" :rule="{
                        required: field.isRequired,
                        message: `请输入${field.label}`,
                    }" :class="`${!field.isRequired ? 'no-required' : ''}`" class=" w-[328px]">
                        <NInput v-model:value="formData[field.field]" :placeholder="field.placeholder" />
                    </NFormItem>
                </template>
            </template>
            <div class=" w-full flex flex-row items-center justify-center gap-x-[18px]">
                <n-button circle style="--n-width:131px;" @click="handleClose">关闭</n-button>
                <n-button type="info" circle style="--n-width:131px;" @click="handleSubmit" :disabled="loading"
                    :loading="loading">提交</n-button>
            </div>
        </NForm>
    </n-modal>
</template>

<style lang="less">
.feedback-container {
    .n-card-header__main {
        display: none !important;
    }

    .n-base-close {
        display: none !important;
    }

}

.feed-back-upload-container {
    .n-form-item-feedback-wrapper {
        display: none !important;
    }

    .n-upload-file-list.n-upload-file-list--grid {
        grid-template-columns: repeat(auto-fill, 60px);
    }
    .n-upload-trigger.n-upload-trigger--image-card {
        width: 60px;
        height: 60px
    }

    .n-upload-file-list .n-upload-file.n-upload-file--image-card-type {
        width: 60px;
        height: 60px
    }
}
</style>
