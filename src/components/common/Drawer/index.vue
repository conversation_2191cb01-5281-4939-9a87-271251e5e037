<script setup lang="ts">
import { computed, ref, nextTick, onMounted } from 'vue'
import {
	<PERSON><PERSON><PERSON><PERSON>,
	NDrawerContent,
	NTabs,
	NTabPane,
	TabsInst,
	NEllipsis,
	NImage,
	NInput,
	NAutoComplete,
	NEmpty,
	NTab
} from 'naive-ui'
import { useRoute } from 'vue-router'
import { useSuggestionStore } from '@/store'
import eye from '@/assets/images/eye.png'
import search from '@/assets/search.svg'
import apps from '@/assets/apps.svg'

interface Props {
	visible: boolean,
	data: any
}

interface ItemProps {
	id: number,
	name: string,
	categoryId: number,
	categoryName: string,
	uuid: number
}

interface Emit {
	(e: 'update:visible', visible: boolean): void
	(ev: 'update-suggest', item: ItemProps): void
	(ev: 'search', keyword: string): void
	(ev: 'close'): void
}

const route = useRoute()
const { uuid } = route.params as { uuid: string }

const store = useSuggestionStore()
const props = defineProps<Props>()
const emit = defineEmits<Emit>()
const keyword = ref<string>('')

const list = computed({
	get: () => {
		if (props.data && props.data.length) {
			current.value = props.data[0].id
		}
		nextTick(() => tabsInstRef.value?.syncBarPosition())
		return props.data
	},
	set: () => { }
})

const inputRef = ref()

const current = ref(-1)
const tabsInstRef = ref<TabsInst | null>(null)

const show = computed({
	get: () => {
		return props.visible
	},
	set: (visible: boolean) => emit('update:visible', visible)
})

const handleClick = (item: any, categoryName: string) => {
	show.value = false
	const data: ItemProps = {
		id: item.id,
		name: item.name,
		categoryId: item.categoryId,
		categoryName: categoryName,
		uuid: Number(+uuid)
	}
	store.updateSetting(data, +uuid)
	emit('update-suggest', data)
}

const handleSearch = () => {
	emit('search', keyword.value)
}

const handleClose = (showFlag: boolean) => {
	show.value = showFlag
	emit('close')
}

function handleEnter(event: KeyboardEvent) {
	if (event.key === 'Enter' && !event.shiftKey) {
		event.preventDefault()
		handleSearch()
	}
}
const chatbots = computed(() => {
	const v = list.value?.filter((item) => item.id === current.value)[0] || {}
	console.log('chatbots', v.Chatbots);
	return v
})
</script>

<template>
	<div id="drawer-target"
		class=" absolute w-[600px] h-[79vh] bottom-[12.5%] right-[30px] ai-agent-container rounded-[8px] sm:w-full sm:right-0 sm:bottom-0">
	</div>
	<NDrawer v-model:show="show" :auto-focus="false" style="width: 100%;" @update-show="handleClose"
		to="#drawer-target">
		<NDrawerContent title="AI助手" :native-scrollbar="true" :closable="false">
			<template #header>
				<div
					class="flex flex-col items-center justify-between w-full bg-gradient-to-r to-purple-500 from-blue-500">
					<div class="flex flex-row w-full h-[50px] justify-between items-center px-[30px]">
						<div class="flex items-center  flex-1">
							<span class="pl-[10px] sm:text-[14px] text-white">AI助手</span>
						</div>
						<div class="">
							<NAutoComplete v-model:value="keyword" :input-props="{
								autocomplete: 'disabled'
							}">
								<template #default="{ handleInput, handleBlur, handleFocus }">
									<NInput ref="inputRef" class=" w-[184px]" placeholder="请输入关键字" @input="handleInput"
										@focus="handleFocus" @blur="handleBlur" @keypress.enter="handleSearch">
										<template #prefix>
											<NIcon :size="16">
												<svg xmlns="http://www.w3.org/2000/svg"
													xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1"
													width="15" height="15" viewBox="0 0 15 15">
													<g>
														<g>
															<path
																d="M7.00496,1.3333740234375C9.31058,1.3333787918075,11.4123,2.6545340234375,12.4118,4.7322240234374995C13.4113,6.8099140234375,13.1319,9.276574023437501,11.693,11.0780440234375L13.2715,12.7999740234375C13.5396,13.0588740234375,13.5432,13.4871740234375,13.2797,13.7507740234375C13.0161,14.0142740234375,12.5878,14.0105740234375,12.3289,13.7425740234375L10.74963,12.0213740234375C7.51966,14.6013740234375,2.70933,13.2186740234375,1.342432,9.3173040234375C-0.024469999999999992,5.4159240234375,2.87104,1.3333742618565,7.00496,1.3333740234375ZM7.00496,2.6667040234375C4.427630000000001,2.6667040234375,2.3382899999999998,4.7560440234375,2.3382899999999998,7.3333740234375C2.3382899999999998,9.9107040234375,4.427630000000001,12.0000740234375,7.00496,12.0000740234375C9.58166,11.9991740234375,11.67,9.9100740234375,11.67,7.3333740234375C11.67,4.756674023437499,9.58166,2.6675940234375,7.00496,2.6667040234375Z"
																fill="#525252" fill-opacity="1"
																style="mix-blend-mode: passthrough" />
														</g>
													</g>
												</svg>
											</NIcon>
										</template>
									</NInput>
								</template>
							</NAutoComplete>
						</div>
						<IconClose class=" font-[14px] w-[16px] h-[16px] ml-[19px] cursor-pointer text-[#fff]"
							@click="handleClose(false)" />
					</div>
					<NTabs ref="tabsInstRef" v-model:value="current" v-if="list?.length > 0"
						class=" px-[30px] h-[50px] !pb-[10px]" animated>
						<NTab :name="item.id" :tab="item.name" :key="index" v-for="(item, index) in list"
							class=" text-[#fff]"> </NTab>
					</NTabs>
				</div>
			</template>
			<div class="grid grid-cols-2 gap-5 sm:grid-cols-2 sm:gap-2  HideScrollbar">
				<div title=""
					class=" HideScrollbar flex flex-col min-h-[160px] rounded-[10px] border border-[#DEE2EE] shadow-inner transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-105 duration-300 cursor-pointer pl-[20px] pr-[16px] pt-[20px] pb-[9px] relative"
					v-for="(item_child, i) in chatbots?.Chatbots || []" :key="i"
					@click="handleClick(item_child, chatbots.name)">
					<div class="flex items-center mb-[10px]">
						<h1
							class="text-[14px] font-semibold pl-[3px] flex flex-row items-center justify-start gap-x-[10px]">
							<div
								class=" w-[30px] h-[30px] rounded-[50%] border-[1px] bg-[#ffffff] flex items-center justify-center text-[#0E69FF]">
								<NImage :src="item_child.profile" width="30" height="30" class=" rounded-[50%]"
									preview-disabled />
							</div>
							<NEllipsis :line-clamp="1" class=" inline-block max-w-[120px]">
								{{ item_child.name }}
							</NEllipsis>
							<!-- <span
								class=" block bg-[#0E69FF] text-[#fff] text-[11px] leading-16px px-[6px] rounded-[2px] font-normal">智能体</span> -->
						</h1>
					</div>
					<div class="text-[13px] mb-[10px] flex-1">
						<NEllipsis :line-clamp="3" class=" text-[#818181]">
							{{ item_child.welcome_message }}
							<template #tooltip>
								<div style="text-align: center">
									{{ item_child.welcome_message }}
								</div>
							</template>
						</NEllipsis>
					</div>
					<div class=" w-full bottom-[3px] right-3 flex flex-row gap-x-[10px] items-center justify-end">
						<NImage :src="eye" width="14" height="9" preview-disabled />
						<span class=" text-[#A6A6A6] text-[12px]">{{ item_child.hot_num }}</span>
					</div>
				</div>
			</div>
			<NEmpty description="你什么也查不到，再换个关键字查下！" size="large" class="mt-[100px]" v-if="list && list.length == 0" />
		</NDrawerContent>
	</NDrawer>
</template>
<style lang="less">
.n-drawer-header__main {
	width: 95%
}

.ai-agent-container {
	.n-drawer-mask {
		background-color: transparent;
	}

	.n-drawer-content-wrapper {
		border-radius: 12px !important;
	}

	.n-drawer-container {
		border-radius: 12px !important;
	}

	.n-drawer {
		border-radius: 12px !important;
	}

	.n-drawer-header {
		padding: unset !important;
	}

	.n-drawer-header__main {
		width: 100%;
	}

	.n-tabs-tab {
		padding-bottom: 4px !important;
	}

	.n-tabs-tab__label {
		color: #fff;
	}

	.n-tabs-bar {
		background-color: #fff !important;
	}

	.n-drawer-body-content-wrapper {
		-ms-overflow-style: none;
		scrollbar-width: none;
	}
}

@import url(./index.less);
</style>
