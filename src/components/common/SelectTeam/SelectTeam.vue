<template>
	<div>
		<div class="flex flex-col max-h-[200px] overflow-y-auto overflow-x-hidden w-full">
			<div v-for="(item, index) in userStore.teamList" :key="item.id" @click="handleChangeTeam(item)"
				class="w-full flex items-center justify-start px-[14px] py-[5px] bg-white rounded-[4px] hover:bg-gray-100 cursor-pointer">
				<NAvatar :src="imageMogr2(item.avatar,'thumbnail/100x100/quality/90/interlace')" :size="26" class="shrink-0" />
				<NEllipsis class="flex-1 ml-[10px] max-w-[140px] text-nowrap">{{ item.name }}</NEllipsis>
				<span class="w-[18px] h-[18px] shrink-0">
					<SelectSvg v-if="item.id == currentId" class="text-primary w-full h-full" />
				</span>
			</div>
			<div v-if="userStore.teamList?.length == 0 && !userStore.teamListLoading" class="w-full flex items-center justify-center text-gray-400">
				<span>暂无团队</span>
			</div>
		</div>
		<div class="pt-[10px]">
			<NButton block class="h-9 text-primary"
				style="--n-text-color: #3363FF; --n-color:#E6F1FF;--n-height: 36px;--n-text-color-hover: #fff; --n-color-hover: #3363FF;--n-text-color-press: #3363FF; --n-color-press:#E6F1FF;--n-text-color-focus: #3363FF; --n-color-focus:#E6F1FF;--n-border-focus:none;--n-border:none;"
				@click.stop.prevent="showTeamModal = true" :disabled="!userStore.canCreateTeam">
				+ 新增团队
			</NButton>
		</div>
		<AddTeamModal v-model:show="showTeamModal" @complete="handleAddTeam" />
	</div>
</template>

<script setup lang="ts">
import { NAvatar, NButton, NCheckbox, NEllipsis, NForm, NFormItem, NInput, NModal, NUpload, useMessage } from 'naive-ui';
import defaultAvatar from '@/assets/avatar.jpg'
import { onMounted, ref } from 'vue';
import request from '@/utils/request';
import { useUserStore } from '@/store';
import SelectSvg from '@/assets/aiwork/svg/selected.svg';
import AddTeamModal from '@/views/team/components/AddTeamModal.vue';
import { imageMogr2 } from '@/utils/imageDeal/imageMogr2';

const selected = defineModel('value')
defineProps<{
	currentId?: string
}>()
const emit = defineEmits<{
	select: [selected: any]
}>()

const showTeamModal = ref(false)
const userStore = useUserStore()
// const teamList = ref<any[]>([])
// const canCreateTeam = ref(true)

onMounted(() => userStore.getTeamList())

// const getTeamList = () => {
// 	request({
// 		url: '/api3/aiwork/team/listAll',
// 	}).then(data => {
// 		teamList.value = data.teams
// 		canCreateTeam.value = data.isCreated
// 	})
// }

const handleChangeTeam = (item: any) => {
	selected.value = item
	emit('select', item)
}

const handleAddTeam = async (data: any) => {
	await userStore.getTeamList()
	userStore.changeTeam(data.id)
	emit('select', data)
}

</script>
