<template>
	<div>
		<NModal v-model:show="show" preset="dialog" @click.stop>
			<template #header>
				<div>选择团队</div>
			</template>
			<div class="flex flex-col max-h-[200px] overflow-y-auto overflow-x-hidden w-full mt-4">
				<div class="flex gap-[7px] items-center justify-start cursor-pointer hover:bg-gray-100" @click="handleChangePersonal">
					<NAvatar :src="defaultAvatar" :size="30" class="shrink-0" />
					<NEllipsis>{{ userStore.userInfo?.name }}</NEllipsis>
					<div class="text-[12px] bg-[#A061FF] rounded-[2px] text-white px-1 shrink-0">个人</div>
				</div>
				<NDivider />
				<div v-for="(item, index) in userStore.teamList" :key="item.id" @click="handleChangeTeam(item)"
					class="flex items-center justify-start px-[14px] -mx-[14px] mb-3 bg-white rounded-[4px] hover:bg-gray-100 cursor-pointer">
					<NAvatar :src="imageMogr2(item.avatar, 'thumbnail/100x100/quality/90/interlace')" :size="30"
						class="shrink-0" />
					<NEllipsis class="flex-1 ml-[10px] max-w-[140px] text-nowrap">{{ item.name }}</NEllipsis>
					<span class="w-[18px] h-[18px] shrink-0">
						<SelectSvg v-if="item.id == currentId" class="text-primary w-full h-full" />
					</span>
				</div>
				<div v-if="userStore.teamList?.length == 0 && !userStore.teamListLoading"
					class="w-full flex items-center justify-center text-gray-400">
					<span>暂无团队</span>
				</div>
			</div>
			<div class="pt-[10px]">
				<NButton block class="h-9 text-primary"
					style="--n-text-color: #3363FF; --n-color:#E6F1FF;--n-height: 36px;--n-text-color-hover: #fff; --n-color-hover: #3363FF;--n-text-color-press: #3363FF; --n-color-press:#E6F1FF;--n-text-color-focus: #3363FF; --n-color-focus:#E6F1FF;--n-border-focus:none;--n-border:none;"
					@click.stop.prevent="showTeamModal = true" :disabled="!userStore.canCreateTeam">
					+ 新增团队
				</NButton>
			</div>
		</NModal>
		<AddTeamModal v-model:show="showTeamModal" @complete="handleAddTeam" />
	</div>
</template>

<script setup lang="ts">
import { NAvatar, NButton, NCheckbox, NDivider, NEllipsis, NForm, NFormItem, NInput, NModal, NUpload, useMessage } from 'naive-ui';
import defaultAvatar from '@/assets/avatar.jpg'
import { inject, onMounted, ref } from 'vue';
import { useUserStore } from '@/store';
import SelectSvg from '@/assets/aiwork/svg/selected.svg';
import AddTeamModal from '@/views/team/components/AddTeamModal.vue';
import { imageMogr2 } from '@/utils/imageDeal/imageMogr2';

defineProps<{
	currentId?: string
}>()
const emit = defineEmits<{
	select: [selected: any]
}>()
const changeTeamProvide = inject<any>('changeTeamProvide')

const showTeamModal = ref(false)
const userStore = useUserStore()
const show = ref(false)

window.$aiwork.openSelectTeam = async (afterLogin = true) => {
	if(afterLogin && !userStore.userInfo?.uid) await window.$aiwork.openLogin()
	show.value = true
}

onMounted(() => userStore.getTeamList())

const handleChangeTeam = (item: any) => {
	// selected.value = item
	// emit('select', item)
	userStore.changeTeam(item.id)
	show.value = false
	changeTeamProvide()
}
const handleChangePersonal = () => {
	userStore.changeTeam(null)
	show.value = false
	changeTeamProvide()
}

const handleAddTeam = async (data: any) => {
	await userStore.getTeamList()
	userStore.changeTeam(data.id)
	emit('select', data)
}

</script>
