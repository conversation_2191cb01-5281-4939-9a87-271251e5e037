<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useChatStore, useUserStore } from '@/store'
import {
	NDropdown
} from "naive-ui";
const route = useRoute()
const router = useRouter()
const chatStore = useChatStore()
const userStore = useUserStore();
import { getUser } from '@/store/modules/auth/helper'
const user = getUser()
const { uuid } = route.params as { uuid: string }
const curIndex = ref(0)
const memberType = ref(0)
const pathname = ref(route.name)
const options=ref([
		{
          label: 'AI PPT',
          key: "ppt"
        },
        {
          label: 'AI写作',
          key: '/apps'
        },
        {
          label: '个人中心',
          key: '/profile'
        }
])
if (userStore && userStore.userInfo && userStore.userInfo.member) {
	const member: any = userStore.userInfo.member
	 //code 100001
	memberType.value = member
} else if (userStore && userStore.userInfo) {
	//code 100001
	memberType.value = 4
}
const curSource: any = computed(() => chatStore.getHistory(+uuid))
onMounted(() => {
	if (curSource.value?.type && curSource.value?.type === 'mj') {
		curIndex.value = 1
	} else {
		curIndex.value = 0
	}
})
const handleMdRoute = (type='tweet') => {
	const mapUrl:any = {
		"ppt":"https://ppt.aiwork365.cn"
	}
	if(user.type!=='temp' && user.token) {
		window.open(`${mapUrl[type]}?token=${user.token}`)
	} else {
		window.open(`${mapUrl[type]}`)
	}
}
const handleSelect =(key: string)=> {
	if(!key.includes("/")){
		handleMdRoute(key);
	}else{
		handleRoute(key)
	}

}

const handleRoute = (path: string) => {
	if (path === '/chat') {
		const chats: any = chatStore.history.filter(item=> !item.type || item.type == 'chat')
		if (chats && chats?.length === 0) {
			chatStore.addHistory({
				title: '新建会话',
				uuid: 1002,
				isEdit: false,
				type: 'chat',
				createTime: new Date().getTime()
			})
		}
	}

	if (path === '/mj') {
		const chats: any = chatStore.history.filter(item=> item.type == 'mj')
		if (chats && chats?.length === 0) {
			chatStore.addHistory({
				title: '新建绘图',
				uuid: 1001,
				type: 'mj',
				isEdit: false,
				createTime: new Date().getTime()
			})
		}
	}
	const urlObj = new URL(window.location.href);
	router.push(path + urlObj.search)
}
</script>
<template>
	<footer
		class="bg-white dark:bg-[#25272c] hidden sm:block pb-[env(safe-area-inset-bottom)] fixed bottom-0 right-0 left-0 z-[1001]">
		<div class="grid py-2 border-t dark:border-t-neutral-800 grid-cols-4">
			<a @click="handleRoute('/chat')" class="leading-4 text-center cursor-pointer" :class="pathname == 'Chat'
					? 'text-[#3ebba2] dark:text-[#86dfba]'
					: 'text-slate-500'
				">
				<span class="inline-block text-xl w-[18px] h-[18px]">
					<i class="fi fi-rr-comment-alt"></i>
				</span>
				<p>AI聊天</p>
			</a>
			<a class="leading-4 text-center cursor-pointer" @click="handleRoute('/mj')" :class="pathname == 'Mj'
					? 'text-[#3ebba2] dark:text-[#86dfba]'
					: 'text-slate-500'
				">
				<span class="inline-block text-xl w-[18px] h-[18px]">
					<i class="fi fi-rr-pen-swirl"></i>
				</span>
				<p>AI绘图</p>
			</a>
			<a class="leading-4 text-center cursor-pointer" @click="handleRoute('/Paper')" :class="pathname == 'Paper'
					? 'text-[#3ebba2] dark:text-[#86dfba]'
					: 'text-slate-500'
				">
				<span class="inline-block text-xl w-[18px] h-[18px]">
					<i class="fi fi-rr-pencil"></i>
				</span>
				<p>论文助手</p>
			</a>
			<a class="leading-4 text-center cursor-pointer" :class="(pathname == 'Profile'||pathname == 'Apps')
					? 'text-[#3ebba2] dark:text-[#86dfba]'
					: 'text-slate-500'
				" >

				<n-dropdown
					trigger="click"
					:options="options"
					:show-arrow="true"
					@select="handleSelect"
				><div>
					<span class="inline-block text-xl w-[18px] h-[18px]">
						<i class="fi fi-rr-apps"></i>
					</span>
					<p>更多</p></div>
				</n-dropdown>
				<!-- <p>我的</p> -->
			</a>
		</div>
	</footer>
</template>
