<script setup lang="ts">
import { computed, ref } from 'vue'
import { NModal, NButton, NSpace, NSlider } from 'naive-ui'
import { useDrawSettingStore } from '@/store'

interface Props {
  visible: boolean
}
interface Emit {
  (e: 'update:visible', visible: boolean): void
}
const settingStore = useDrawSettingStore()

const sizes = [
  {
    label: '默认',
    value: '1:1',
  },
  {
    label: '电脑壁纸',
    value: '16:9'
  },
  {
    label: '手机壁纸',
    value: '9:16'
  },
  {
    label: '长手机壁纸',
    value: '9:18'
  },
]
const model = [
  {
    key: '通用',
    value: '5',
  },
  {
    key: '二次元',
    value: 'n',
  },
]
const props = defineProps<Props>()

const emit = defineEmits<Emit>()

// 定义ref
const lpMarks = ref({
  0: '默认',
  10: '低',
  50: '中',
  75: '高',
  100: '极致',
})
const ysMarks = ref({
  0: '默认',
  100: '低',
  500: '中',
  750: '高',
  1000: '极致',
})

const show = computed({
  get() {
    return props.visible
  },
  set(visible: boolean) {
    emit('update:visible', visible)
  },
})

let defaultOptions: any = ref(settingStore.getSetting())
const handleModel = (item: any) => {
  defaultOptions.value.v = item.value
}
const handleSize = (item: any) => {
  defaultOptions.value.ar = item.value
}
const onUpdate = (value: number) => {
  defaultOptions.value.c = value
}
const onArtUpdate = (value: number) => {
  defaultOptions.value.s = value
}
const handleSubmit = () => {
  settingStore.updateSetting(defaultOptions.value)
	show.value = false
}
</script>

<template>
  <NModal
    v-model:show="show"
    :auto-focus="false"
    preset="card"
    style="width: 90%; max-width: 540px;"
  >
    <template #header>
      <div>设置</div>
    </template>
		<div class="w-[98%]">
        <div>模型</div>
        <div class="mt-[5px]">
          <ul class="flex">
            <li
              class="w-[70px] h-[34px] rounded-[3px] border border-[#e0e0e5] flex justify-center items-center mr-[10px] outline-none cursor-pointer"
              v-for="(item, index) of model"
              :key="index"
              :class="
                item.value == defaultOptions.v
                  ? 'bg-[#3dbaa1] text-[#fff] border-none'
                  : ''
              "
              @click="handleModel(item)"
            >
              {{ item.key }}
            </li>
          </ul>
        </div>

        <div class="mt-[20px]">图片比例</div>
        <div class="mt-[5px]">
          <ul class="flex">
            <li
              class="w-[90px] h-[80px] text-[12px] rounded-sm mr-[10px] flex flex-col justify-center items-center cursor-pointer hover:border-[#3dbaa1] hover:text-[#3dbaa1]"
              v-for="(item, index) of sizes"
              :key="index"
              :class="
                item.value == defaultOptions.ar
                  ? 'border border-[#3dbaa1] text-[#3dbaa1]'
                  : 'border border-[#e5e7eb] text-[#777d83]'
              "
              @click="handleSize(item)"
            >
              <div class="block">
                {{ item.value }}
                <br />
              </div>
              <div class="block">{{ item.label }}</div>
            </li>
          </ul>
        </div>
        <div class="mt-[20px]">离谱程度</div>
        <div class="mt-[5px]">
          <NSpace vertical>
            <NSlider
              v-model:value="defaultOptions.c"
              :marks="lpMarks"
              step="mark"
              :min="0"
              :max="100"
              @update:value="onUpdate"
            />
          </NSpace>
        </div>

        <div class="mt-[20px]">艺术性</div>
        <div class="mt-[5px]">
          <NSpace vertical>
            <NSlider
              v-model:value="defaultOptions.s"
              :marks="ysMarks"
              step="mark"
              :min="0"
              :max="1000"
              @update:value="onArtUpdate"
            />
          </NSpace>
        </div>
    </div>
    <template #footer>
      <NButton type="primary" color="#3dbaa1" style="width: 100%;" @click="handleSubmit">
        确认
      </NButton>
    </template>
  </NModal>
</template>
