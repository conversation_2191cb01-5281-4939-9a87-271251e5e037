.nav-badge {
	position: absolute;
	top: 0;
	right: 0;
	transform: translate(72%, -12%);
	width: 23px;
	height: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: url("@/assets/aiwork/nav/nav-tag-bg.png") no-repeat center center /
		contain;
	display: flex;
	justify-content: center;
	align-items: center;

	.text {
		width: 13px;
		height: 7px;
	}
	.hot {
		.text;
		background: url("@/assets/aiwork/nav/hot.png") no-repeat center center /
			contain;
	}
	.new {
		.text;
		background: url("@/assets/aiwork/nav/new.png") no-repeat center center /
			contain;
	}
}

@property --login-btn-bg-deg {
	syntax: "<angle>";
	inherits: false;
	initial-value: 90deg;
}

.login-btn {
	width: 100px;
	height: 36px;
	padding: 0 10px;
	border-radius: 4px;
	opacity: 1;
	// background: linear-gradient(var(--login-btn-bg-deg), #2079ff 0%, #af53ff 100%);
	border: 1px solid #0e69ff;
	background-color: #f9faff;
	display: flex;
	justify-content: center;
	align-items: center;

	font-size: 14px;
	color: #0e69ff;
	cursor: pointer;

	transition: --login-btn-bg-deg 0.3s ease-in-out;

	&:hover {
		--login-btn-bg-deg: -90deg;
	}
}
.member-btn {
	width: 110px;
	height: 36px;
	box-sizing: border-box;
	background: linear-gradient(90deg, #2079ff 0%, #af53ff 100%);
	border-radius: 4px;
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 7px;
	font-size: 14px;
	color: #ffffff;
	cursor: pointer;

	transition: all 0.3s ease-in-out;

	&:hover {
		background: linear-gradient(-90deg, #2079ff 0%, #af53ff 100%);
	}
}

.activity-tag {
	background: linear-gradient(
		var(--login-btn-bg-deg),
		#ff0000 0%,
		#ff5900 100%
	);
	border-radius: 10px;
	line-height: 12px;
	padding: 2px 5px;
	font-size: 12px;
	font-weight: 400;
	color: #ffffff;
	z-index: 999;
	// 画一个三角形 在底部
	&::before {
		content: "";
		position: absolute;
		width: 0;
		height: 0;
		border-left: 5px solid transparent;
		border-bottom: 5px solid transparent;
		border-top: 5px solid #ff0000;
		bottom: -9px;
		left: 35%;
		transform: translateX(-50%);
	}
}
