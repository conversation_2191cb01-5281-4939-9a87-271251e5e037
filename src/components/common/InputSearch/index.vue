<script setup lang="ts">
import { ref,  nextTick } from 'vue'
import { SvgIcon } from '@/components/common'
import { replaceText } from '@/plugins/directive';
interface Props {
    onChange: (value: string) => void;
    onClick: () => void;
}
const inputVal = ref('小红书');
const props = withDefaults(defineProps<Props>(), {
    onChange: () => { },
    onClick: () => { }
})
const handleInput = (e: any) => {
    const value = e.target.value
    if (!value) {
        inputVal.value = '小红书'
    } else {
        inputVal.value = value
    }
    props.onChange(value)
}

const handleClick = () => {
    nextTick(() => {
        props.onChange(inputVal.value)
        props.onClick()
    })
}

let inputEnd = ref(false);

const compositionend = (event) => {
    event.preventDefault();
    inputEnd.value = true;
};
const compositionstart = (event) => {
    event.preventDefault();
    inputEnd.value = false;
};
const replacePlaceholder = (text: string) => {

	return replaceText(text)
}


</script>

<template>
    <div
        class="h-[50px] w-[80%] max-w-[700px] relative flex flex-row items-center justify-center p-[5px] border rounded-[10px] opacity-100 bg-white">
        <SvgIcon icon="ri:search-2-line" class="w-[18px] h-[18px] ml-[10px] cursor-pointer" />
        <input class="search-input flex-1" :placeholder="replacePlaceholder('请输入关键词搜索，如：文章创作、毕业文稿、商业计划书；')" @input="handleInput"
            @keydown.enter="handleClick" @compositionend="compositionend" @compositionstart="compositionstart" />
        <button class="search-btn" @click="handleClick">搜索</button>
    </div>
</template>

<style lang="less" scoped>
@import './index.less';
</style>
