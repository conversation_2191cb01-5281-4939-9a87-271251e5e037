<script lang="ts" setup>
import { ref ,onMounted, computed} from 'vue'
import { NImage,NButton } from 'naive-ui'
import type { Theme } from '@/store/modules/app/helper'
import { useAppStore } from '@/store'
import { fetchPersonal } from '@/chatgpt'
import dayjs from 'dayjs';
import wechat from '@/assets/wechat.png'
const appStore = useAppStore()

const userInfos:any = ref({})
const membersInfo:any = ref({})
async function fetchPersoanl() {
  try {
    const response: any = await fetchPersonal()
    userInfos.value = response
    membersInfo.value = response?.Members || []

  } catch (error) {
  }
}
onMounted(async () => {
   await fetchPersoanl();
})
const theme = computed(() => appStore.theme)
const themeOptions: { label: string; key: Theme; icon: string }[] = [
  {
    label: 'Auto',
    key: 'auto',
    icon: 'ri:contrast-line',
  },
  {
    label: 'Light',
    key: 'light',
    icon: 'ri:sun-foggy-line',
  },
  {
    label: 'Dark',
    key: 'dark',
    icon: 'ri:moon-foggy-line',
  },
]
</script>
<template>
  <div class="p-4 space-y-5 min-h-[200px]">
    <div class="space-y-6">
      <div class="flex items-center space-x-4" >
        <span class="flex-shrink-0 w-[150px]">手机号</span>
        <div class="flex-1">
          {{userInfos.phone||"--"}}
        </div>

      </div>
      <template  v-for="(item) in membersInfo" :key="item.id">
            <div class="flex items-center space-x-4">
        <span class="flex-shrink-0 w-[150px] font-medium">会员信息</span>
        <div class="flex-1">
          {{ item.name || '--' }}
        </div>
      </div>
			<div class="flex items-center space-x-4" v-if="!item.permanent" >
        <span class="flex-shrink-0 w-[150px]">会员有效期</span>
        <div class="flex-1">
          {{ dayjs(item.endDate).format('YYYY-MM-DD HH:mm:ss') }}
        </div>
      </div>
			<div class="flex items-center space-x-4" v-if="item.limitCount">
        <span class="flex-shrink-0 w-[150px]">{{item.name}}总计</span>
        <div class="flex-1">
          {{ item.count }}
        </div>
      </div>
      <div class="flex items-center space-x-4" v-if="item.limitCount">
        <span class="flex-shrink-0 w-[150px]">{{item.name}}已使用</span>
        <div class="flex-1">
          {{ item.use }}
        </div>
      </div>
      </template>
			<div class="flex items-center space-x-4" v-if="userInfos.openId">
        <span class="flex-shrink-0 w-[150px] flex items-end">
					<span>微信标识</span>
					<NImage :src="wechat" class="w-[20px] h-[20px] ml-[5px]" preview-disabled />
				</span>
        <div class="flex-1">
					{{  userInfos.openId || '--' }}
        </div>
      </div>
			<div class="hidden items-center space-x-4">
        <span class="flex-shrink-0 w-[150px]">{{ $t('setting.theme') }}</span>
        <div class="flex flex-wrap items-center gap-4">
          <template v-for="item of themeOptions" :key="item.key">
            <NButton
              size="small"
              :type="item.key === theme ? 'primary' : undefined"
              @click="appStore.setTheme(item.key)"
            >
              <template #icon>
                <SvgIcon :icon="item.icon" />
              </template>
            </NButton>
          </template>
        </div>
      </div>
    </div>
    <div class="space-y-6" v-if="!userInfos.openId&&!userInfos.phone">
      <div class="flex items-center space-x-4" >
        <h3>暂无用户信息</h3>
      </div>
    </div>
  </div>
</template>
