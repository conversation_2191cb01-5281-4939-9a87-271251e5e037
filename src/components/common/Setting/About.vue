<script setup lang='ts'>
import { computed, onMounted, ref } from 'vue'
import { NSpin } from 'naive-ui'
import { fetchChatConfig } from '@/chatgpt'
import pkg from '@/../package.json'
// import { useAuthStore } from '@/store'

interface ConfigState {
  timeoutMs?: number
  reverseProxy?: string
  apiModel?: string
  socksProxy?: string
  httpsProxy?: string
  balance?: string
}

// const authStore = useAuthStore()

const loading = ref(false)

const config = ref<ConfigState>()

const isChatGPTAPI = computed<boolean>(() => false)

async function fetchConfig() {
  try {
    loading.value = true
    const data = await fetchChatConfig<ConfigState>()
    config.value = data
  }
  finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchConfig()
})
</script>

<template>
  <NSpin :show="loading">
    <div class="p-4 space-y-4">
      <h2 class="text-xl font-bold">
        Version - {{ pkg.version }}
      </h2>

      <p>{{ $t("setting.api") }}：{{ config?.apiModel || '-' }}</p>
      <p v-if="isChatGPTAPI">
        {{ $t("setting.balance") }}：{{ config?.balance || '-' }}
      </p>
      <!--<p v-if="!isChatGPTAPI">
        {{ $t("setting.reverseProxy") }}：{{ config?.reverseProxy ?? '-' }}
      </p>-->
      <p>{{ $t("setting.timeout") }}：{{ config?.timeoutMs || '-' }}</p>
      <p>{{ $t("setting.socks") }}：{{ config?.socksProxy || '-' }}</p>
      <p>{{ $t("setting.httpsProxy") }}：{{ config?.httpsProxy || '-' }}</p>
    </div>
  </NSpin>
</template>
