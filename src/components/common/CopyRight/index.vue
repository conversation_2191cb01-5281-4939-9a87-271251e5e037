<script lang="ts" setup>
import { computed } from 'vue'
import { NText } from 'naive-ui'
import LogoImage from '@/assets/aiwork/logo.png'
const isMobile = computed(() =>
	/iPhone|Android|Mobile/.test(navigator.userAgent),
)

const icpNumber = computed(() => {
	const hostname = window.location.hostname;
	if (hostname.includes(HostnameEnum.AIWORK365_NET)) {
		return '沪ICP备16035216号-24';
	} else if (hostname.includes(HostnameEnum.AIWORK365_COM_CN)) {
		return '沪ICP备14044897号-113';
	} else if (hostname.includes(HostnameEnum.AIWORK365_CC)) {
		return '沪ICP备2023040053号-4';
	} else if (hostname.includes(HostnameEnum.AIWORK365_CN)) {
		return '沪ICP备16023798号-18';
	}
	return '';
});

const companyName = computed(() => {
	const hostname = window.location.hostname;
	if (hostname.includes(HostnameEnum.AIWORK365_NET)) {
		return '上海盟耀信息科技有限公司';
	}
	return '上海盟聚信息科技有限公司'
})

enum HostnameEnum {
	AIWORK365_NET = 'aiwork365.net',
	AIWORK365_COM_CN = 'aiwork365.com.cn',
	AIWORK365_CC = 'aiwork365.cc',
	AIWORK365_CN = 'aiwork365.cn'
}

// 网站地址是动态计算出来的
// local环境默认是aiwork365.com
// qa环境默认是chat-qa.mjmobi.com
// 其他环境默认是根域名 而且由于会发布多个网站 所以需要动态计算
const website = computed(() => {
	const hostname = window.location.hostname;
	if (hostname.indexOf('localhost') > -1) {
		return HostnameEnum.AIWORK365_CN;
	} else if (hostname.indexOf('chat-qa.mjmobi.com') > -1) {
		return 'chat-qa.mjmobi.com';
	}
	// 一共只有4个域名 根据indexOf判断
	return hostname.indexOf(HostnameEnum.AIWORK365_NET) > -1 ? HostnameEnum.AIWORK365_NET :
		hostname.indexOf(HostnameEnum.AIWORK365_COM_CN) > -1 ? HostnameEnum.AIWORK365_COM_CN :
			hostname.indexOf(HostnameEnum.AIWORK365_CC) > -1 ? HostnameEnum.AIWORK365_CC :
				HostnameEnum.AIWORK365_CN;
})
</script>

<template>
	<div class="py-[40px] px-[70px]  text-[#6b7280] flex items-center justify-center gap-[50px] leading-[30px] text-left sm:flex-col sm:gap-4 sm:p-4"
		v-if="true">
		<img :src="LogoImage" class="!h-[40px]" />
		<div class="text-[12px] text-[#999]">
			<p>Copyright © 2024-2024 <NText type="primary"><a :href="`//${website}`">{{ website }}</a>
				</NText> All Rights Reserved <br class="hidden " />{{companyName}} <br class="hidden" /> <span
					class="ml-[20px] sm:ml-0"><a href="https://beian.miit.gov.cn/" target="_blank">{{ icpNumber
						}}</a></span></p>
		</div>
	</div>
</template>
