<script setup lang="ts">
import Tab from '@/components/common/Tab/index.vue'
import MoreIcon from '@/assets/images/more.png'
interface Option {
    id: number;
    name: string;
    icon: string;
    icon2: string;
    active: boolean;
}

interface Props {
    tabs: Option[]
}
const handleTab = function () {
}
</script>

<template>
    <div class="flex flex-row flex-wrap gap-x-[20px] gap-y-[20px] items-center justify-start">
        <Tab key="more" :img-src="MoreIcon" :img-active-src="MoreIcon" title="更多" :active="false" @click="handleTab" />
    </div>
</template>

<style lang="less" scoped></style>