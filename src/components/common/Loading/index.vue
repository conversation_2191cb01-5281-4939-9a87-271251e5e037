<script setup lang='ts'>
import { ref } from 'vue'

interface Props {
  title: string
}

const props = defineProps<Props>()
const title = ref(props.title)

</script>

<template>
	<div>
		<div class="container">
			<div class="cube">
				<div class="sides">
					<div class="top"></div>
					<div class="right"></div>
					<div class="bottom"></div>
					<div class="left"></div>
					<div class="front"></div>
					<div class="back"></div>
				</div>
		</div>
		<div class="mt-[260px] text-[28px]">{{ title }}</div>
	</div>
</div>

</template>

<style lang="less">
	@import url(./index.less);
</style>

