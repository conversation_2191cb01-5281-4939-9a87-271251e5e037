<script setup lang="ts">
import Tab from '@/components/common/Tab/index.vue'
import MoreTab from '@/components/common/MoreTab/index.vue'

import { computed } from 'vue';
interface Option {
    id: number;
    name: string;
    icon: string;
    icon2: string;
    active: boolean;
    markUrl?: string
}
interface Props {
    options: Option[],
    onClick: (id: number) => void
    containerWidth: number | undefined
}
const props = withDefaults(defineProps<Props>(), {
    options: undefined
})
const handleTab = function (id: number) {
    props.onClick(id)
}

// 因为tab只能展示1行 多余的tab都要放到最后的更多tab下 通过点击下拉来展示
// 所以要精确的切割出能展示的tabs
// 每个tab的宽度计算是固定的20px * 2 + icon的宽度(24px) + 间隔(6px) + 每个文字的宽度(14px)
// 然后最后一个更多的tab宽度是20px * 2 + icon的宽度(24px) + 间隔(6px) + 28px
// 更多不是必须展示的 如果所有tab的长度刚好小于等于containerWidth 那么就不展示更多

const calcTabWidth = (count: number) => {
    return 20 * 2 + 24 + 6 + 20 * count;
}

const allTabs = computed(() => {
    const tabs = props.options;
    const containerWidth = props.containerWidth || 0;
    const moreTabWidth = 20 * 2 + 24 + 6 + 28;
    const showTabs: Option[] = [];
    const hideTabs: Option[] = [];
    let totalWidth = 0;
    for (let i = 0; i < tabs.length; i++) {
        const tab = tabs[i];
        const tabWidth = calcTabWidth(tab.name.length);
        totalWidth += tabWidth;
        if (totalWidth <= containerWidth - moreTabWidth) {
            showTabs.push(tab);
        } else {
            hideTabs.push(tab);
        }
    }
    return { showTabs, hideTabs };
})

</script>
<template>
    <div class="flex flex-row flex-wrap gap-x-[20px] gap-y-[20px] items-center justify-start">
        <Tab v-for="item in options" :key="item.id" :img-src="item.icon" :img-active-src="item.icon2 || item.icon" :title="item.name"
            :active="item.active" @click="handleTab(item.id)" :mark-url="item.markUrl" />
        <!-- <MoreTab v-if="allTabs.hideTabs?.length" :tabs="allTabs.hideTabs" /> -->
    </div>
</template>
<style lang="less"></style>