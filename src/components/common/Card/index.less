.card-container {
	height: 130px;
	background: #ffffff;
	box-sizing: border-box;
	border: 1.15px solid #ffffff;
	box-shadow: 0px 2.3px 11.51px 0px #dee2ee;
	&:hover {
		.uncollect {
			display: flex;
		}
	}
}
.title {
	display: -webkit-box;
	overflow: hidden;
	text-overflow: ellipsis;
	word-break: break-all;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
}
.desc {
	display: -webkit-box;
	overflow: hidden;
	text-overflow: ellipsis;
	word-break: break-all;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}
.uncollect {
	display: none;
	box-shadow: 0px 1px 10px 0px #e1e9ff;
}
