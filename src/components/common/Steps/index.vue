<template>
    <div class="step-progress__wrapper">
        <div class="step-progress__bar">
            <div v-for="(step, index) in steps" :key="index" :class="index !== steps.length - 1 ? 'flex-1' : ''">
                <div :style="{
                    '--activeColor': activeColor,
                    '--passiveColor': passiveColor,
                    '--activeBorder': activeThickness + 'px',
                    '--passiveBorder': passiveThickness + 'px',
                    '--lineWidth': lineWidth + 'px',
                    '--lineLength': (100 / (steps.length - 1)) + '%'
                }" :class="{
                    'step-progress__step': true,
                    'step-progress__step--active': index === currentStep,
                    'step-progress__step--valid': index < currentStep
                }" @click="jumpToStep(index)">
                    <div class="step-progress__step-content cursor-pointer">
                        <span class="step-progress__step-icon">{{ index + 1 }}</span>
                        <div class="step-progress__step-label">{{ step }}</div>
                    </div>
                    <div class="step-progress__step-line" :class="{
                        'step-progress_step-line-active': index < currentStep
                    }" v-if="index !== steps.length - 1"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">

export default {
    name: 'StepProgress',
    props: {
        steps: {
            type: Array,
            default() {
                return [];
            },
            validator(val: any[]) {
                return val && val.length > 0;
            }
        },
        currentStep: {
            type: Number,
            default: 0
        },
        iconClass: {
            type: String,
            default: 'fa fa-check'
        },
        activeColor: {
            type: String,
            default: '#0E69FF'
        },
        passiveColor: {
            type: String,
            default: '#BBD5FD'
        },
        activeThickness: {
            type: Number,
            default: 5
        },
        passiveThickness: {
            type: Number,
            default: 5
        },
        lineWidth: {
            type: Number,
            default: 1
        }
    },
    computed: {
        scaleX() {
            let step = this.currentStep;
            if (step < 0) {
                step = 0;
            } else if (step >= this.steps.length) {
                step = this.steps.length - 1;
            }
            return step / (this.steps.length - 1);
        }
    },
    methods: {
        jumpToStep(index: number) {
            this.$emit('update-current', index + 1);
        }
    }
};
</script>

<style lang="less" scoped>
.step-progress__wrapper {
    position: relative;
}

.step-progress__bar {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.step-progress__step {
    z-index: 2;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    color: #fff;
    --activeColor: #0E69FF;
    --passiveColor: #BBD5FD;
    --activeBorder: 5px;
    --passiveBorder: 5px;

    .step-progress__step-content {
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .step-progress__step-icon {
        width: 21px;
        height: 21px;
        position: relative;
        text-align: center;
        line-height: 21px;

        &:after {
            content: '';
            position: absolute;
            z-index: -1;
            left: -50%;
            top: 50%;
            transform: translate(50%, -50%);
            width: 21px;
            height: 21px;
            background-color: var(--passiveColor);
            border-radius: 50%;
            transition: 0.3s ease;
        }
    }

    .step-progress__step-label {
        white-space: nowrap;
        font-size: 16px;
        color: #3D3D3D;
        transition: 0.3s ease;
        margin-left: 4px;
    }

    .step-progress__step-line {
        width: 100%;
        margin: 0 10px 0 5px;
        height: 1px;
        background: linear-gradient(to left,
                transparent 0%,
                transparent 50%,
                #ccc 50%,
                #ccc 100%);
        background-size: 10px 1px;
    }
}

.step-progress__step--valid {
    .step-progress__step-icon {
        width: 21px;
        height: 21px;
        position: relative;
        text-align: center;
        line-height: 21px;

        &:after {
            content: '';
            position: absolute;
            z-index: -1;
            left: -50%;
            top: 50%;
            transform: translate(50%, -50%);
            width: 21px;
            height: 21px;
            background-color: var(--activeColor);
            border-radius: 50%;
            transition: 0.3s ease;
        }
    }
}


.step-progress_step-line-active {
    width: 100%;
    margin: 0 10px 0 5px;
    height: 1px;
    background: linear-gradient(to left,
            transparent 0%,
            transparent 50%,
            #ccc 50%,
            #ccc 100%);
    background-size: 10px 1px;
}

// .step-progress__step--active:before {
//     border: var(--activeBorder) dashed var(--activeColor);
// }

// .step-progress__step--valid:before {
//     background-color: var(--activeColor);
//     border: var(--activeBorder) solid var(--activeColor);
// }</style>
