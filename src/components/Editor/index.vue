<template>
	<editor-content :editor="editor" v-model="value" />
	<bubble-menu :editor="editor" v-if="editor">
		<div class="bubble-menu">
			<div class="bubble-menu-item" @click="editor.chain().focus().toggleBold().run()"
				:class="{ 'is-active': editor.isActive('bold') }">
				Bold
			</div>
			<div class="bubble-menu-item" @click="editor.chain().focus().toggleItalic().run()"
				:class="{ 'is-active': editor.isActive('italic') }">
				Italic
			</div>
			<div class="bubble-menu-item" @click="editor.chain().focus().toggleStrike().run()"
				:class="{ 'is-active': editor.isActive('strike') }">
				Strike
			</div>
		</div>
	</bubble-menu>
	<div class="custom-drag-handle"></div>
</template>

<script setup lang="ts">
import { useEditor, EditorContent, AnyExtension, BubbleMenu } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import { watch } from 'vue'
import GlobalDragHandle from 'tiptap-extension-global-drag-handle'
import AutoJoiner from 'tiptap-extension-auto-joiner'
import Slash from './slash/slash'
import suggestion from './slash/suggestion'
import { suggestionList } from './slash/suggestionList'
import { Markdown } from 'tiptap-markdown';

const value = defineModel('modelValue', {
	type: String,
	default: '',
})

const editor = useEditor({
	content: value.value,
	extensions: [
		StarterKit.configure({
		heading: {
			levels: [1, 2, 3, 4, 5, 6],
		},
	}) as AnyExtension,
	Markdown,
	GlobalDragHandle.configure({
		dragHandleWidth: 20, // 默认值

		// scrollTreshold 指定了用户需要将元素拖动到屏幕上/下边缘多近的位置时，才会触发自动滚动。
		// 例如，scrollTreshold = 100 表示当用户将元素拖动到距离屏幕边缘最多99px的位置时，会自动开始滚动。
		// 你可以将此值设为0以防止此扩展引起的自动滚动。
		scrollTreshold: 100, // 默认值

		// 用于查询拖动句柄的CSS选择器（例如：'.custom-handle'）。
		// 如果找到对应的句柄元素，则会使用该元素作为拖动句柄。
		// 如果未找到，则会创建一个默认句柄。
		dragHandleSelector: ".custom-drag-handle", // 默认值为 undefined


		// 需要排除拖动句柄的HTML标签
		// 如果你想为特定HTML标签隐藏全局拖动句柄，可以使用此选项。
		// 例如，将此选项设置为 ['p', 'hr'] 会隐藏 <p> 和 <hr> 标签的全局拖动句柄。
		excludedTags: [], // 默认值

		// 需要包含拖动句柄的自定义节点
		// 例如，假设有一个自定义Alert组件。在节点组件的包装器上添加 data-type="alert"。
		// 然后将 ['alert'] 添加到这个列表中。
		customNodes: [],
	}),
	AutoJoiner.configure({
		elementsToJoin: ["bulletList", "orderedList"] // default
	}),
	Slash.configure({
		suggestion: suggestion(suggestionList),
	})
	],
	onUpdate: ({ editor, transaction }) => {
		value.value = editor.getHTML()
	}
})

watch(value, (newVal, oldVal) => {
	editor.value?.commands.setContent(newVal)
})

</script>

<style lang="less">
@import "./index.css";

.bubble-menu {
	display: flex;
	align-items: center;
	gap: 2px;
	border-radius: 8px;
	border: 1px solid rgb(229, 229, 229);
	box-shadow: rgba(0, 0, 0, 0) 0px 0px;
	padding: 4px;
	height: 43px;
	background: #fff;

	.bubble-menu-item {
		color: hsl(0 0% 9.8%);
		font-weight: 600;
		padding: 0 8px;
		font-size: 14px;
		line-height: 32px;
		height: 32px;

		background-color: transparent;
		border-radius: 6px;
		transition: all 0.3s ease-in-out;
		cursor: pointer;

		&:hover {
			background-color: rgba(0, 0, 0, 0.05);
		}

		&.is-active {
			background-color: rgba(0, 0, 0, 0.05);
		}
	}
}

.drag-handle {
	position: fixed;
	opacity: 1;
	transition: opacity ease-in 0.2s;
	border-radius: 0.25rem;

	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 10' style='fill: rgba(0, 0, 0, 0.5)'%3E%3Cpath d='M3,2 C2.44771525,2 2,1.55228475 2,1 C2,0.44771525 2.44771525,0 3,0 C3.55228475,0 4,0.44771525 4,1 C4,1.55228475 3.55228475,2 3,2 Z M3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 C3.55228475,4 4,4.44771525 4,5 C4,5.55228475 3.55228475,6 3,6 Z M3,10 C2.44771525,10 2,9.55228475 2,9 C2,8.44771525 2.44771525,8 3,8 C3.55228475,8 4,8.44771525 4,9 C4,9.55228475 3.55228475,10 3,10 Z M7,2 C6.44771525,2 6,1.55228475 6,1 C6,0.44771525 6.44771525,0 7,0 C7.55228475,0 8,0.44771525 8,1 C8,1.55228475 7.55228475,2 7,2 Z M7,6 C6.44771525,6 6,5.55228475 6,5 C6,4.44771525 6.44771525,4 7,4 C7.55228475,4 8,4.44771525 8,5 C8,5.55228475 7.55228475,6 7,6 Z M7,10 C6.44771525,10 6,9.55228475 6,9 C6,8.44771525 6.44771525,8 7,8 C7.55228475,8 8,8.44771525 8,9 C8,9.55228475 7.55228475,10 7,10 Z'%3E%3C/path%3E%3C/svg%3E");
	background-size: calc(0.5em + 0.375rem) calc(0.5em + 0.375rem);
	background-repeat: no-repeat;
	background-position: center;
	width: 1.2rem;
	height: 1.5rem;
	z-index: 50;
	cursor: grab;

	&:hover {
		background-color: var(--novel-stone-100);
		transition: background-color 0.2s;
	}

	&:active {
		background-color: var(--novel-stone-200);
		transition: background-color 0.2s;
		cursor: grabbing;
	}

	&.hide {
		opacity: 0;
		pointer-events: none;
	}

	@media screen and (max-width: 600px) {
		display: none;
		pointer-events: none;
	}
}

.dark .drag-handle {
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 10' style='fill: rgba(255, 255, 255, 0.5)'%3E%3Cpath d='M3,2 C2.44771525,2 2,1.55228475 2,1 C2,0.44771525 2.44771525,0 3,0 C3.55228475,0 4,0.44771525 4,1 C4,1.55228475 3.55228475,2 3,2 Z M3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 C3.55228475,4 4,4.44771525 4,5 C4,5.55228475 3.55228475,6 3,6 Z M3,10 C2.44771525,10 2,9.55228475 2,9 C2,8.44771525 2.44771525,8 3,8 C3.55228475,8 4,8.44771525 4,9 C4,9.55228475 3.55228475,10 3,10 Z M7,2 C6.44771525,2 6,1.55228475 6,1 C6,0.44771525 6.44771525,0 7,0 C7.55228475,0 8,0.44771525 8,1 C8,1.55228475 7.55228475,2 7,2 Z M7,6 C6.44771525,6 6,5.55228475 6,5 C6,4.44771525 6.44771525,4 7,4 C7.55228475,4 8,4.44771525 8,5 C8,5.55228475 7.55228475,6 7,6 Z M7,10 C6.44771525,10 6,9.55228475 6,9 C6,8.44771525 6.44771525,8 7,8 C7.55228475,8 8,8.44771525 8,9 C8,9.55228475 7.55228475,10 7,10 Z'%3E%3C/path%3E%3C/svg%3E");
}
</style>
